# Enhanced Azure DevOps Pipeline for SFDC Function App - Staging Environment
# This pipeline is specifically for staging environment deployments

trigger:
  branches:
    include:
    - staging
    - release/*
  paths:
    include:
    - atomsec-func-sfdc/*

pr: none

pool:
  vmImage: ubuntu-latest

variables:
  # Service connections
  sfdcServiceConnection: 'sc-atomsec-staging-backend'
  
  # Build configuration
  pythonVersion: '3.12'
  buildConfiguration: 'Release'
  
  # Deployment configuration
  functionAppName: 'func-atomsec-sfdc-staging'
  resourceGroupName: 'atomsec-staging-backend'
  stagingSlotName: 'staging'
  
  # Enhanced testing for staging
  enableSecurityScanning: true
  enablePerformanceTesting: true
  enableIntegrationTesting: true
  enableLoadTesting: true

stages:
# ============================================================================
# STAGE 1: BUILD AND ENHANCED QUALITY ASSURANCE
# ============================================================================
- stage: BuildAndQuality
  displayName: 'Build and Enhanced Quality Assurance'
  jobs:
  
  - job: EnhancedSecurityScanning
    displayName: 'Enhanced Security Scanning'
    steps:
    
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '$(pythonVersion)'
      displayName: 'Set up Python $(pythonVersion)'

    # Install security scanning tools
    - script: |
        pip install bandit safety semgrep
        pip install -r requirements.txt
      displayName: 'Install Security Tools'
      workingDirectory: 'atomsec-func-sfdc'

    # Advanced security scanning with Semgrep
    - script: |
        echo "Running Semgrep security analysis..."
        semgrep --config=auto --json --output=semgrep-report.json . || true
        semgrep --config=auto . || true
      displayName: 'Advanced Security Scan (Semgrep)'
      workingDirectory: 'atomsec-func-sfdc'
      continueOnError: true

    # OWASP dependency check
    - task: dependency-check-build-task@6
      inputs:
        projectName: 'AtomSec-SFDC'
        scanPath: 'atomsec-func-sfdc'
        format: 'ALL'
        additionalArguments: '--enableRetired --enableExperimental'
      displayName: 'OWASP Dependency Check'
      continueOnError: true

  - job: ComprehensiveTesting
    displayName: 'Comprehensive Testing'
    steps:
    
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '$(pythonVersion)'
      displayName: 'Set up Python $(pythonVersion)'

    - script: |
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-xdist pytest-mock pytest-asyncio
        pip install locust requests
      displayName: 'Install Test Dependencies'
      workingDirectory: 'atomsec-func-sfdc'

    # Comprehensive unit tests
    - script: |
        python -m pytest tests/unit/ \
          --cov=. \
          --cov-report=xml:coverage.xml \
          --cov-report=html:htmlcov \
          --cov-report=term \
          --junitxml=unit-test-results.xml \
          --verbose \
          --durations=10 \
          -n auto
      displayName: 'Comprehensive Unit Tests'
      workingDirectory: 'atomsec-func-sfdc'

    # Contract tests
    - script: |
        if [ -d "tests/contract" ]; then
          echo "Running contract tests..."
          python -m pytest tests/contract/ \
            --junitxml=contract-test-results.xml \
            --verbose
        else
          echo "No contract tests found"
        fi
      displayName: 'Contract Tests'
      workingDirectory: 'atomsec-func-sfdc'
      continueOnError: true

# ============================================================================
# STAGE 2: STAGING DEPLOYMENT WITH CANARY
# ============================================================================
- stage: CanaryDeployment
  displayName: 'Canary Deployment'
  dependsOn: BuildAndQuality
  jobs:
  
  - deployment: CanaryDeploy
    displayName: 'Deploy Canary Version'
    environment: 'SFDC-Staging-Canary'
    strategy:
      canary:
        increments: [10, 25, 50, 100]
        deploy:
          steps:
          - download: current
            artifact: sfdc-drop
            displayName: 'Download Build Artifacts'

          # Deploy to canary slot
          - task: AzureFunctionApp@2
            inputs:
              connectedServiceNameARM: '$(sfdcServiceConnection)'
              appType: 'functionAppLinux'
              appName: '$(functionAppName)'
              package: '$(Pipeline.Workspace)/sfdc-drop/sfdc-build-$(Build.BuildNumber).zip'
              runtimeStack: 'PYTHON|3.12'
              deploymentMethod: 'zipDeploy'
              deployToSlotOrASE: true
              resourceGroupName: '$(resourceGroupName)'
              slotName: 'canary'
            displayName: 'Deploy to Canary Slot'

          # Configure traffic routing
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Configuring traffic routing for canary deployment..."
                
                # Set traffic distribution
                az functionapp traffic-routing set \
                  --name "$(functionAppName)" \
                  --resource-group "$(resourceGroupName)" \
                  --distribution canary=$(strategy.increment)
                
                echo "✅ Traffic routing configured: $(strategy.increment)% to canary"
            displayName: 'Configure Traffic Routing'

        routeTraffic:
          steps:
          # Monitor canary deployment
          - script: |
              echo "Monitoring canary deployment with $(strategy.increment)% traffic..."
              
              # Wait for metrics to stabilize
              sleep 120
              
              # Check error rates and performance metrics
              # This would typically integrate with Application Insights
              echo "✅ Canary monitoring completed for $(strategy.increment)% traffic"
            displayName: 'Monitor Canary Metrics'

        postRouteTraffic:
          steps:
          # Validate canary performance
          - script: |
              echo "Validating canary performance..."
              
              canary_url="https://$(functionAppName)-canary.azurewebsites.net"
              
              # Run health checks
              curl -f "$canary_url/api/health" || exit 1
              
              # Run performance validation
              echo "✅ Canary validation passed for $(strategy.increment)% traffic"
            displayName: 'Validate Canary Performance'

# ============================================================================
# STAGE 3: FULL STAGING DEPLOYMENT
# ============================================================================
- stage: FullStagingDeployment
  displayName: 'Full Staging Deployment'
  dependsOn: CanaryDeployment
  jobs:
  
  - deployment: FullDeploy
    displayName: 'Full Staging Deployment'
    environment: 'SFDC-Staging-Full'
    strategy:
      runOnce:
        deploy:
          steps:
          # Promote canary to full deployment
          - task: AzureAppServiceManage@0
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              Action: 'Swap Slots'
              WebAppName: '$(functionAppName)'
              ResourceGroupName: '$(resourceGroupName)'
              SourceSlot: 'canary'
            displayName: 'Promote Canary to Production'

          # Reset traffic routing
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Resetting traffic routing..."
                
                az functionapp traffic-routing clear \
                  --name "$(functionAppName)" \
                  --resource-group "$(resourceGroupName)"
                
                echo "✅ Traffic routing reset to 100% production"
            displayName: 'Reset Traffic Routing'

# ============================================================================
# STAGE 4: COMPREHENSIVE STAGING VALIDATION
# ============================================================================
- stage: ComprehensiveValidation
  displayName: 'Comprehensive Staging Validation'
  dependsOn: FullStagingDeployment
  jobs:
  
  - job: LoadTesting
    displayName: 'Load Testing'
    steps:
    
    - script: |
        pip install locust requests
      displayName: 'Install Load Testing Tools'

    # Extended load testing
    - script: |
        echo "Running extended load testing..."
        
        staging_url="https://$(functionAppName).azurewebsites.net"
        
        # Run comprehensive load test
        locust -f tests/performance/comprehensive_load_test.py \
          --host="$staging_url" \
          --users=100 \
          --spawn-rate=10 \
          --run-time=300s \
          --headless \
          --html=load-test-report.html \
          --csv=load-test-results || true
      displayName: 'Extended Load Testing'
      workingDirectory: 'atomsec-func-sfdc'

  - job: SecurityValidation
    displayName: 'Security Validation'
    steps:
    
    # DAST scanning
    - task: AzureKeyVault@2
      inputs:
        azureSubscription: '$(sfdcServiceConnection)'
        KeyVaultName: 'akv-atomsec-staging'
        SecretsFilter: 'dast-api-key'
        RunAsPreJob: false
      displayName: 'Get DAST API Key'

    - script: |
        echo "Running DAST security scanning..."
        
        staging_url="https://$(functionAppName).azurewebsites.net"
        
        # Run OWASP ZAP baseline scan
        docker run -t owasp/zap2docker-stable zap-baseline.py \
          -t "$staging_url" \
          -J zap-report.json \
          -r zap-report.html || true
      displayName: 'DAST Security Scan'
      continueOnError: true

# ============================================================================
# STAGE 5: APPROVAL FOR PRODUCTION
# ============================================================================
- stage: ProductionApproval
  displayName: 'Production Approval'
  dependsOn: ComprehensiveValidation
  jobs:
  
  - job: waitForValidation
    displayName: 'Wait for Manual Validation'
    pool: server
    timeoutInMinutes: 4320 # 3 days
    steps:
    - task: ManualValidation@0
      timeoutInMinutes: 4320
      inputs:
        notifyUsers: |
          <EMAIL>
          <EMAIL>
        instructions: |
          Please review the staging deployment and approve for production:
          
          Staging URL: https://$(functionAppName).azurewebsites.net
          
          Validation checklist:
          - [ ] Functional testing completed
          - [ ] Performance testing passed
          - [ ] Security scanning completed
          - [ ] Load testing results acceptable
          - [ ] Business stakeholder approval
          
          Build: $(Build.BuildNumber)
          Commit: $(Build.SourceVersion)
      displayName: 'Manual Validation Gate'