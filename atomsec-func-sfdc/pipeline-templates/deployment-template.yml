# Reusable deployment template for SFDC Function App
parameters:
- name: environment
  type: string
- name: serviceConnection
  type: string
- name: functionAppName
  type: string
- name: resourceGroupName
  type: string
- name: stagingSlotName
  type: string
  default: 'staging'
- name: enableHealthCheck
  type: boolean
  default: true
- name: enableBlueGreenDeployment
  type: boolean
  default: true
- name: healthCheckRetries
  type: number
  default: 10
- name: healthCheckDelay
  type: number
  default: 30

steps:
- download: current
  artifact: sfdc-drop
  displayName: 'Download Build Artifacts'

# Deploy to staging slot
- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: '${{ parameters.serviceConnection }}'
    appType: 'functionAppLinux'
    appName: '${{ parameters.functionAppName }}'
    package: '$(Pipeline.Workspace)/sfdc-drop/sfdc-build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.12'
    deploymentMethod: 'zipDeploy'
    deployToSlotOrASE: true
    resourceGroupName: '${{ parameters.resourceGroupName }}'
    slotName: '${{ parameters.stagingSlotName }}'
  displayName: 'Deploy to Staging Slot'

# Wait for deployment to stabilize
- script: |
    echo "Waiting for deployment to stabilize..."
    sleep 60
  displayName: 'Wait for Deployment Stabilization'

# Health check on staging slot
- ${{ if eq(parameters.enableHealthCheck, true) }}:
  - script: |
      echo 'Running health check on staging deployment...'
      staging_url="https://${{ parameters.functionAppName }}-${{ parameters.stagingSlotName }}.azurewebsites.net"
      echo "Testing staging URL: $staging_url"
      
      # Health check with retries
      for i in {1..${{ parameters.healthCheckRetries }}}; do
        if curl -f "$staging_url/api/health" > /dev/null 2>&1; then
          echo "✅ Health check passed on attempt $i"
          break
        else
          echo "⏳ Health check failed on attempt $i, retrying in ${{ parameters.healthCheckDelay }} seconds..."
          sleep ${{ parameters.healthCheckDelay }}
        fi
        
        if [ $i -eq ${{ parameters.healthCheckRetries }} ]; then
          echo "❌ Health check failed after ${{ parameters.healthCheckRetries }} attempts"
          exit 1
        fi
      done
    displayName: 'Health Check on Staging'

# Blue-Green deployment (swap slots)
- ${{ if eq(parameters.enableBlueGreenDeployment, true) }}:
  - task: AzureAppServiceManage@0
    inputs:
      azureSubscription: '${{ parameters.serviceConnection }}'
      Action: 'Swap Slots'
      WebAppName: '${{ parameters.functionAppName }}'
      ResourceGroupName: '${{ parameters.resourceGroupName }}'
      SourceSlot: '${{ parameters.stagingSlotName }}'
    displayName: 'Blue-Green Deployment (Swap Slots)'

# Post-deployment health check
- ${{ if eq(parameters.enableHealthCheck, true) }}:
  - script: |
      echo 'Running post-deployment health check...'
      production_url="https://${{ parameters.functionAppName }}.azurewebsites.net"
      echo "Testing production URL: $production_url"
      
      # Wait for production to be ready
      for i in {1..${{ parameters.healthCheckRetries }}}; do
        if curl -f "$production_url/api/health" > /dev/null 2>&1; then
          echo "✅ Production health check passed on attempt $i"
          break
        else
          echo "⏳ Production health check failed on attempt $i, retrying in ${{ parameters.healthCheckDelay }} seconds..."
          sleep ${{ parameters.healthCheckDelay }}
        fi
        
        if [ $i -eq ${{ parameters.healthCheckRetries }} ]; then
          echo "❌ Production health check failed after ${{ parameters.healthCheckRetries }} attempts"
          exit 1
        fi
      done
      
      echo "✅ Post-deployment validation completed successfully"
    displayName: 'Post-deployment Health Check'

# Deployment summary
- script: |
    echo "=== DEPLOYMENT SUMMARY ==="
    echo "Environment: ${{ parameters.environment }}"
    echo "Function App: ${{ parameters.functionAppName }}"
    echo "Resource Group: ${{ parameters.resourceGroupName }}"
    echo "Build Number: $(Build.BuildNumber)"
    echo "Production URL: https://${{ parameters.functionAppName }}.azurewebsites.net"
    echo "Staging URL: https://${{ parameters.functionAppName }}-${{ parameters.stagingSlotName }}.azurewebsites.net"
    echo "=========================="
  displayName: 'Deployment Summary'