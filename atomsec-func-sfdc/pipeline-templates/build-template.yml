# Reusable build template for SFDC Function App
parameters:
- name: pythonVersion
  type: string
  default: '3.12'
- name: enableSecurityScanning
  type: boolean
  default: true
- name: enableCodeQuality
  type: boolean
  default: true
- name: workingDirectory
  type: string
  default: 'atomsec-func-sfdc'

steps:
- task: UsePythonVersion@0
  inputs:
    versionSpec: '${{ parameters.pythonVersion }}'
  displayName: 'Set up Python ${{ parameters.pythonVersion }}'

# Install dependencies
- script: |
    python -m pip install --upgrade pip
    
    echo 'Installing ODBC driver for SQL Server...'
    sudo apt-get update
    sudo apt-get install -y curl
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
    sudo curl -fsSL https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/prod.list -o /etc/apt/sources.list.d/mssql-release.list
    sudo apt-get update
    sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17 unixodbc-dev

    echo 'Installing Python dependencies...'
    if [ -f requirements.txt ]; then
      pip install -r requirements.txt -t .
    else
      echo 'No requirements.txt found'
      exit 1
    fi
  displayName: 'Install Dependencies'
  workingDirectory: '${{ parameters.workingDirectory }}'

# Code quality checks
- ${{ if eq(parameters.enableCodeQuality, true) }}:
  - script: |
      pip install black isort flake8 pylint
      
      echo "Checking code formatting with Black..."
      black --check --diff . || echo "Code formatting issues found"
      
      echo "Checking import sorting with isort..."
      isort --check-only --diff . || echo "Import sorting issues found"
      
      echo "Running Flake8 style check..."
      flake8 . || echo "Style issues found"
      
      echo "Running Pylint analysis..."
      pylint **/*.py || echo "Pylint issues found"
    displayName: 'Code Quality Checks'
    workingDirectory: '${{ parameters.workingDirectory }}'
    continueOnError: true

# Security scanning
- ${{ if eq(parameters.enableSecurityScanning, true) }}:
  - script: |
      pip install bandit safety
      
      echo "Running Bandit security analysis..."
      bandit -r . -f txt || echo "Security issues found"
      
      echo "Running Safety dependency check..."
      safety check || echo "Dependency vulnerabilities found"
    displayName: 'Security Scanning'
    workingDirectory: '${{ parameters.workingDirectory }}'
    continueOnError: true

# Create build info
- script: |
    echo "Creating build information file..."
    cat > build-info.json << EOF
    {
      "buildNumber": "$(Build.BuildNumber)",
      "buildId": "$(Build.BuildId)",
      "sourceVersion": "$(Build.SourceVersion)",
      "sourceBranch": "$(Build.SourceBranchName)",
      "buildDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
      "repository": "$(Build.Repository.Name)",
      "pythonVersion": "${{ parameters.pythonVersion }}"
    }
    EOF
    cat build-info.json
  displayName: 'Create Build Info'
  workingDirectory: '${{ parameters.workingDirectory }}'

# Archive application
- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '${{ parameters.workingDirectory }}'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/sfdc-build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true
  displayName: 'Archive Application'

# Publish artifacts
- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)/sfdc-build-$(Build.BuildNumber).zip'
    ArtifactName: 'sfdc-drop'
    publishLocation: 'Container'
  displayName: 'Publish Build Artifacts'