"""
Test Enhanced Error Handling and Resilience

This test file verifies the enhanced error handling framework implementation.
"""

import pytest
import time
from datetime import datetime
from src.shared.error_handler import (
    get_enhanced_error_handler,
    get_error_response_builder,
    ErrorResponse,
    Error<PERSON>ategory,
    RetryConfig,
    RetryStrategy
)
from src.shared.task_sequence_failure_handler import (
    get_task_sequence_failure_handler,
    TaskSequenceState,
    TaskState,
    FailureStrategy
)

def test_enhanced_circuit_breaker():
    """Test enhanced circuit breaker functionality"""
    error_handler = get_enhanced_error_handler()
    
    # Get circuit breaker for salesforce service
    cb = error_handler.get_circuit_breaker('salesforce')
    
    # Initially should be closed
    assert cb.can_execute() == True
    assert cb.state == "closed"
    
    # Record failures to open circuit
    for _ in range(3):  # Salesforce config has failure_threshold=3
        cb.record_failure()
    
    # Circuit should now be open
    assert cb.state == "open"
    assert cb.can_execute() == False
    
    # Test status reporting
    status = cb.get_status()
    assert status['service_name'] == 'salesforce'
    assert status['state'] == 'open'
    assert status['failure_count'] == 3

def test_enhanced_retry_policy():
    """Test enhanced retry policy functionality"""
    error_handler = get_enhanced_error_handler()
    
    # Get retry policy for salesforce API
    retry_policy = error_handler.get_retry_policy('salesforce_api')
    
    # Test retry decision
    transient_error = ConnectionError("Connection timeout")
    assert retry_policy.should_retry(transient_error, 1) == True
    
    permanent_error = ValueError("Invalid parameter")
    assert retry_policy.should_retry(permanent_error, 1) == False
    
    # Test delay calculation
    delay = retry_policy.calculate_delay(1)
    assert delay > 0
    
    # Test metrics
    metrics = retry_policy.get_metrics()
    assert 'total_operations' in metrics
    assert 'success_rate' in metrics

def test_error_categorization():
    """Test enhanced error categorization"""
    error_handler = get_enhanced_error_handler()
    
    # Test different error types
    auth_error = PermissionError("Authentication failed")
    assert error_handler.categorize_error(auth_error) == ErrorCategory.PERMANENT
    
    connection_error = ConnectionError("Connection refused")
    assert error_handler.categorize_error(connection_error) == ErrorCategory.TRANSIENT
    
    validation_error = ValueError("Invalid JSON format")
    assert error_handler.categorize_error(validation_error) == ErrorCategory.NON_RETRYABLE
    
    timeout_error = TimeoutError("Request timeout")
    assert error_handler.categorize_error(timeout_error) == ErrorCategory.TRANSIENT

def test_error_response_builder():
    """Test error response builder functionality"""
    builder = get_error_response_builder()
    
    # Test basic error response
    test_error = ValueError("Test validation error")
    response = builder.build_error_response(test_error, {'user_id': 'test123'})
    
    assert isinstance(response, ErrorResponse)
    assert response.error_type == 'ValueError'
    assert response.error_message == 'Test validation error'
    assert response.correlation_id is not None
    
    # Test response formatting
    response_dict = response.to_dict()
    assert response_dict['success'] == False
    assert 'error' in response_dict
    assert 'correlation_id' in response_dict['error']
    
    # Test user-friendly message
    user_message = response.to_user_friendly_message()
    assert isinstance(user_message, str)
    assert len(user_message) > 0

def test_task_sequence_failure_handler():
    """Test task sequence failure handler"""
    failure_handler = get_task_sequence_failure_handler()
    
    # Test sequence initialization
    execution_log_id = "test-execution-123"
    sequence_result = failure_handler.start_sequence(
        "sfdc_scan", 
        execution_log_id, 
        {"org_id": "test-org"}
    )
    
    assert sequence_result.execution_log_id == execution_log_id
    assert sequence_result.sequence_type == "sfdc_scan"
    assert sequence_result.state == TaskSequenceState.PENDING
    
    # Test sequence status
    status = failure_handler.get_sequence_status(execution_log_id)
    assert status is not None
    assert status['execution_log_id'] == execution_log_id
    assert status['state'] == TaskSequenceState.PENDING.value

def test_operation_specific_retry_configs():
    """Test operation-specific retry configurations"""
    error_handler = get_enhanced_error_handler()
    
    # Test different operation types have different configs
    sf_policy = error_handler.get_retry_policy('salesforce_api')
    db_policy = error_handler.get_retry_policy('database')
    queue_policy = error_handler.get_retry_policy('queue_operation')
    
    # Verify they have different configurations
    assert sf_policy.config.max_attempts == 4  # Salesforce config
    assert db_policy.config.max_attempts == 3   # Database config
    assert queue_policy.config.max_attempts == 5 # Queue config
    
    # Verify different strategies
    assert sf_policy.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF
    assert queue_policy.config.strategy == RetryStrategy.LINEAR_BACKOFF

def test_circuit_breaker_service_configs():
    """Test service-specific circuit breaker configurations"""
    error_handler = get_enhanced_error_handler()
    
    # Test different services have different circuit breaker configs
    sf_cb = error_handler.get_circuit_breaker('salesforce')
    db_cb = error_handler.get_circuit_breaker('database')
    
    # Verify different thresholds
    assert sf_cb.config.failure_threshold == 3  # Salesforce config
    assert db_cb.config.failure_threshold == 5  # Database config
    
    # Verify different recovery timeouts
    assert sf_cb.config.recovery_timeout == 120  # Salesforce config
    assert db_cb.config.recovery_timeout == 60   # Database config

def test_error_context_enrichment():
    """Test error context enrichment"""
    error_handler = get_enhanced_error_handler()
    
    # Test context enrichment
    test_context = {'user_id': 'test123', 'operation': 'test_op'}
    enriched = error_handler._enrich_error_context(test_context)
    
    # Verify original context is preserved
    assert enriched['user_id'] == 'test123'
    assert enriched['operation'] == 'test_op'
    
    # Verify system information is added
    assert 'python_version' in enriched
    assert 'process_id' in enriched
    assert 'environment' in enriched

def test_error_severity_assessment():
    """Test error severity assessment"""
    error_handler = get_enhanced_error_handler()
    
    # Test different severity levels
    critical_error = MemoryError("Out of memory")
    assert error_handler._determine_error_severity(critical_error) == 'critical'
    
    high_error = RuntimeError("Internal server error")
    assert error_handler._determine_error_severity(high_error) == 'high'
    
    medium_error = ValueError("Validation failed")
    assert error_handler._determine_error_severity(medium_error) == 'medium'

if __name__ == "__main__":
    # Run basic tests
    print("Testing Enhanced Error Handling Framework...")
    
    try:
        test_enhanced_circuit_breaker()
        print("✓ Circuit breaker tests passed")
        
        test_enhanced_retry_policy()
        print("✓ Retry policy tests passed")
        
        test_error_categorization()
        print("✓ Error categorization tests passed")
        
        test_error_response_builder()
        print("✓ Error response builder tests passed")
        
        test_task_sequence_failure_handler()
        print("✓ Task sequence failure handler tests passed")
        
        test_operation_specific_retry_configs()
        print("✓ Operation-specific retry config tests passed")
        
        test_circuit_breaker_service_configs()
        print("✓ Circuit breaker service config tests passed")
        
        test_error_context_enrichment()
        print("✓ Error context enrichment tests passed")
        
        test_error_severity_assessment()
        print("✓ Error severity assessment tests passed")
        
        print("\n🎉 All enhanced error handling tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()