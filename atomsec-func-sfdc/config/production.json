{"security": {"require_https": true, "rate_limit_requests_per_minute": 100, "enable_audit_logging": true, "session_timeout_minutes": 30}, "performance": {"connection_pool_size": 20, "request_timeout_seconds": 60, "cache_ttl_seconds": 600, "max_concurrent_requests": 200, "memory_limit_mb": 1024, "cpu_limit_percent": 70}, "monitoring": {"log_level": "INFO", "enable_custom_metrics": true, "enable_distributed_tracing": true, "health_check_interval_seconds": 60, "metrics_retention_days": 90}, "database": {"connection_pool_size": 20, "connection_timeout_seconds": 60, "command_timeout_seconds": 120, "retry_attempts": 5, "retry_delay_seconds": 2}, "storage": {"enable_encryption": true, "retention_days": 365, "max_file_size_mb": 500}, "db_service": {"base_url": "https://atomsec-func-db-r.azurewebsites.net/api/db", "timeout_seconds": 120, "retry_attempts": 5}}