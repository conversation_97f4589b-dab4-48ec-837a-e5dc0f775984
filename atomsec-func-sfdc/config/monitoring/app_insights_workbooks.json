{"workbooks": [{"name": "SFDC Service - System Performance", "description": "Comprehensive system performance monitoring for SFDC service", "template": {"version": "Notebook/1.0", "items": [{"type": 1, "content": {"json": "# SFDC Service - System Performance Dashboard\n\nThis dashboard provides comprehensive monitoring of system performance metrics for the SFDC service."}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"system.cpu_percent\"\n| where timestamp > ago(4h)\n| summarize avg(value) by bin(timestamp, 5m)\n| render timechart with (title=\"CPU Usage %\")", "size": 0, "title": "CPU Usage Trend", "timeContext": {"durationMs": 14400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"system.memory_percent\"\n| where timestamp > ago(4h)\n| summarize avg(value) by bin(timestamp, 5m)\n| render timechart with (title=\"Memory Usage %\")", "size": 0, "title": "Memory Usage Trend", "timeContext": {"durationMs": 14400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"system.health_score\"\n| where timestamp > ago(1h)\n| summarize arg_max(timestamp, value)\n| project HealthScore = value\n| extend Status = case(\n    HealthScore >= 90, \"Healthy\",\n    HealthScore >= 70, \"Warning\", \n    \"Critical\"\n)", "size": 3, "title": "Current Health Status", "timeContext": {"durationMs": 3600000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"traces.active_count\"\n| where timestamp > ago(2h)\n| summarize avg(value) by bin(timestamp, 2m)\n| render timechart with (title=\"Active Traces\")", "size": 0, "title": "Active Traces", "timeContext": {"durationMs": 7200000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}]}}, {"name": "SFDC Service - Task Sequence Monitoring", "description": "Task sequence execution monitoring and performance analytics", "template": {"version": "Notebook/1.0", "items": [{"type": 1, "content": {"json": "# SFDC Service - Task Sequence Monitoring\n\nMonitoring dashboard for SFDC task sequence execution, including success rates, duration trends, and failure analysis."}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"task_sequence.completed\"\n| where timestamp > ago(24h)\n| summarize \n    Total = count(),\n    Successful = countif(tostring(customDimensions.status) == \"completed\"),\n    Failed = countif(tostring(customDimensions.status) == \"failed\"),\n    Partial = countif(tostring(customDimensions.status) == \"partial\")\n| extend SuccessRate = round((Successful * 100.0) / Total, 2)\n| project SuccessRate, Total, Successful, Failed, Partial", "size": 3, "title": "24h Task Sequence Success Rate", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"task_sequence.total_duration_seconds\"\n| where timestamp > ago(12h)\n| summarize \n    AvgDuration = avg(value),\n    P50Duration = percentile(value, 50),\n    P95Duration = percentile(value, 95),\n    P99Duration = percentile(value, 99)\n by bin(timestamp, 30m)\n| render timechart with (title=\"Task Sequence Duration Trends\")", "size": 0, "title": "Task Sequence Duration Trends", "timeContext": {"durationMs": 43200000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"tasks.duration_seconds\"\n| where timestamp > ago(6h)\n| summarize \n    AvgDuration = round(avg(value), 2),\n    P95Duration = round(percentile(value, 95), 2),\n    Count = count(),\n    SuccessRate = round((countif(tostring(customDimensions.success) == \"true\") * 100.0) / count(), 2)\nby TaskType = tostring(customDimensions.task_type)\n| order by AvgDuration desc", "size": 0, "title": "Task Performance by Type (6h)", "timeContext": {"durationMs": 21600000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"task_sequence.progress_percent\"\n| where timestamp > ago(1h)\n| summarize arg_max(timestamp, value) by ExecutionLogId = tostring(customDimensions.execution_log_id)\n| where value < 100\n| summarize ActiveSequences = count()\n| extend Status = case(ActiveSequences == 0, \"No Active Sequences\", strcat(ActiveSequences, \" Active Sequences\"))", "size": 3, "title": "Currently Active Sequences", "timeContext": {"durationMs": 3600000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"tasks.completed\"\n| where timestamp > ago(24h)\n| where tostring(customDimensions.success) == \"false\"\n| summarize FailureCount = count() by \n    TaskType = tostring(customDimensions.task_type),\n    ExecutionLogId = tostring(customDimensions.execution_log_id)\n| order by FailureCount desc\n| take 20", "size": 0, "title": "Recent Task Failures (24h)", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}]}}, {"name": "SFDC Service - Business Metrics & KPIs", "description": "Business metrics, KPIs, and operational insights", "template": {"version": "Notebook/1.0", "items": [{"type": 1, "content": {"json": "# SFDC Service - Business Metrics & KPIs\n\nBusiness-focused metrics and key performance indicators for operational insights."}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"tasks.completed\"\n| where timestamp > ago(7d)\n| summarize TaskCount = count() by bin(timestamp, 1d), Success = tostring(customDimensions.success)\n| render columnchart with (title=\"Daily Task Completions (7 days)\")", "size": 0, "title": "Daily Task Completion Trends", "timeContext": {"durationMs": 604800000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "requests\n| where timestamp > ago(24h)\n| summarize \n    RequestCount = count(),\n    AvgDuration = round(avg(duration), 2),\n    P95Duration = round(percentile(duration, 95), 2),\n    SuccessRate = round((countif(success == true) * 100.0) / count(), 2)\nby bin(timestamp, 1h)\n| render timechart with (title=\"API Performance (24h)\")", "size": 0, "title": "API Performance Metrics", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customEvents\n| where name == \"user_activity\"\n| where timestamp > ago(24h)\n| summarize UniqueUsers = dcount(tostring(customDimensions.user_id))\n| extend Metric = \"Active Users (24h)\"", "size": 3, "title": "User Activity", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "customMetrics\n| where name == \"tasks.completed\"\n| where timestamp > ago(24h)\n| summarize \n    Total = count(),\n    Errors = countif(tostring(customDimensions.success) == \"false\")\nby TaskType = tostring(customDimensions.task_type)\n| extend ErrorRate = round((Errors * 100.0) / Total, 2)\n| order by ErrorRate desc\n| take 10", "size": 0, "title": "Error Rate by Task Type (24h)", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}, {"type": 3, "content": {"version": "KqlItem/1.0", "query": "exceptions\n| where timestamp > ago(24h)\n| summarize ExceptionCount = count() by bin(timestamp, 2h), ExceptionType = type\n| render timechart with (title=\"Exception Trends (24h)\")", "size": 0, "title": "Exception Trends", "timeContext": {"durationMs": 86400000}, "queryType": 0, "resourceType": "microsoft.insights/components"}}]}}], "alert_rules": [{"name": "High CPU Usage Alert", "description": "Alert when CPU usage exceeds 80% for 5 minutes", "query": "customMetrics | where name == 'system.cpu_percent' | where value > 80", "frequency": "PT5M", "severity": 2, "threshold": {"operator": "GreaterThan", "value": 80}}, {"name": "Task Sequence Failure Rate Alert", "description": "Alert when task sequence failure rate exceeds 10%", "query": "customMetrics | where name == 'task_sequence.completed' | summarize FailureRate = (countif(tostring(customDimensions.status) == 'failed') * 100.0) / count() | where FailureRate > 10", "frequency": "PT10M", "severity": 2, "threshold": {"operator": "GreaterThan", "value": 10}}, {"name": "Low Health Score Alert", "description": "Alert when system health score drops below 50", "query": "customMetrics | where name == 'system.health_score' | where value < 50", "frequency": "PT3M", "severity": 1, "threshold": {"operator": "<PERSON><PERSON><PERSON>", "value": 50}}]}