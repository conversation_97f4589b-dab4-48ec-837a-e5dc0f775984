{"security": {"require_https": false, "rate_limit_requests_per_minute": 1000, "enable_audit_logging": false}, "performance": {"enable_caching": false, "cache_ttl_seconds": 60}, "monitoring": {"log_level": "DEBUG", "enable_custom_metrics": false, "enable_distributed_tracing": false}, "database": {"connection_pool_size": 5, "retry_attempts": 1}, "db_service": {"base_url": "http://localhost:7072/api/db", "timeout_seconds": 30}, "storage": {"enable_encryption": false}}