{"enhanced_security": {"description": "Enable enhanced security features including advanced authentication and authorization", "status": "active", "default_value": true, "rules": [{"strategy": "boolean", "parameters": {"enabled": true}}], "tags": ["security", "authentication"], "metadata": {"owner": "security-team", "impact": "high"}}, "performance_monitoring": {"description": "Enable advanced performance monitoring and metrics collection", "status": "active", "default_value": true, "rules": [{"strategy": "boolean", "parameters": {"enabled": true}}], "tags": ["monitoring", "performance"], "metadata": {"owner": "devops-team", "impact": "medium"}}, "advanced_caching": {"description": "Enable advanced caching mechanisms for improved performance", "status": "active", "default_value": false, "rules": [{"strategy": "percentage", "parameters": {"percentage": 50, "seed": "caching_rollout_2024"}}], "tags": ["performance", "caching"], "metadata": {"owner": "backend-team", "impact": "medium"}}, "new_ui_components": {"description": "Enable new UI components and interface improvements", "status": "testing", "default_value": false, "rules": [{"strategy": "user_list", "parameters": {"users": ["admin", "test_user", "ui_tester"]}}, {"strategy": "percentage", "parameters": {"percentage": 10, "seed": "ui_rollout_2024"}}], "tags": ["ui", "frontend", "beta"], "metadata": {"owner": "frontend-team", "impact": "low"}}, "beta_features": {"description": "Enable beta features for testing and feedback", "status": "testing", "default_value": false, "rules": [{"strategy": "attribute", "parameters": {"attribute": "role", "values": ["admin", "beta_tester", "power_user"]}}], "tags": ["beta", "testing"], "metadata": {"owner": "product-team", "impact": "low"}}, "enhanced_error_handling": {"description": "Enable enhanced error handling and recovery mechanisms", "status": "active", "default_value": false, "rules": [{"strategy": "canary", "parameters": {"percentage": 25, "organizations": ["test-org", "pilot-org"], "users": ["admin", "error_handler_tester"]}}], "tags": ["error-handling", "reliability"], "metadata": {"owner": "backend-team", "impact": "high"}}, "task_sequence_optimization": {"description": "Enable optimized task sequence processing for SFDC operations", "status": "active", "default_value": true, "rules": [{"strategy": "boolean", "parameters": {"enabled": true}}], "tags": ["performance", "task-processing", "sfdc"], "metadata": {"owner": "backend-team", "impact": "high"}}, "execution_log_coordination": {"description": "Enable enhanced execution log coordination across task sequences", "status": "active", "default_value": true, "rules": [{"strategy": "boolean", "parameters": {"enabled": true}}], "tags": ["logging", "coordination", "task-processing"], "metadata": {"owner": "backend-team", "impact": "medium"}}, "maintenance_mode": {"description": "Enable maintenance mode to disable certain features during updates", "status": "inactive", "default_value": false, "rules": [{"strategy": "time_window", "parameters": {"start_time": "2024-01-01T02:00:00", "end_time": "2024-01-01T04:00:00"}}], "tags": ["maintenance", "system"], "metadata": {"owner": "devops-team", "impact": "high"}}, "debug_logging": {"description": "Enable debug logging for troubleshooting", "status": "active", "default_value": false, "rules": [{"strategy": "attribute", "parameters": {"attribute": "role", "values": ["admin", "developer", "support"]}}], "tags": ["logging", "debug", "troubleshooting"], "metadata": {"owner": "devops-team", "impact": "low"}}}