{"security": {"jwt_algorithm": "HS256", "jwt_access_token_expire_minutes": 30, "jwt_refresh_token_expire_days": 7, "rate_limit_requests_per_minute": 100, "require_https": true, "enable_audit_logging": true, "max_request_size_mb": 10, "session_timeout_minutes": 60, "allowed_origins": ["http://localhost:3000", "https://app-atomsec-dev01.azurewebsites.net"]}, "performance": {"connection_pool_size": 10, "request_timeout_seconds": 30, "cache_ttl_seconds": 300, "max_concurrent_requests": 100, "enable_compression": true, "enable_caching": true, "memory_limit_mb": 512, "cpu_limit_percent": 80}, "monitoring": {"log_level": "INFO", "enable_custom_metrics": true, "health_check_interval_seconds": 30, "enable_distributed_tracing": true, "metrics_retention_days": 30, "enable_performance_counters": true, "alert_thresholds": {"error_rate_percent": 5.0, "response_time_ms": 2000, "cpu_usage_percent": 80.0, "memory_usage_percent": 85.0}}, "database": {"connection_pool_size": 10, "connection_timeout_seconds": 30, "command_timeout_seconds": 60, "retry_attempts": 3, "retry_delay_seconds": 1, "enable_connection_pooling": true}, "storage": {"container_name": "default", "enable_encryption": true, "retention_days": 90, "max_file_size_mb": 100}, "db_service": {"timeout_seconds": 60, "retry_attempts": 3, "retry_delay_seconds": 2, "user_agent": "atomsec-func-sfdc/1.0.0"}, "pmd": {"enabled": true, "default_categories": ["security", "performance"], "max_findings": 10000, "scan_timeout": 300, "temp_dir": "/tmp"}}