# Task Management API Examples

This document provides comprehensive examples for the Task Management API endpoints, demonstrating sequential task processing and execution log coordination.

## 🔄 Sequential Task Processing Overview

The SFDC service processes tasks in a specific sequence, all linked by the same `execution_log_id`:

1. **sfdc_authenticate** - Authenticate with Salesforce API
2. **health_check** - Perform comprehensive health checks  
3. **metadata_extraction** - Extract Salesforce metadata
4. **pmd_apex_security** - Run PMD scanning (final step)

## 📋 Task Management Endpoints

### 1. Create Task

Create a new task for sequential processing.

#### Request
```bash
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {
      "access_token": "00D5j000006P6ly!AQgAQM9ts3lBUQ31FN780NESscn0OQyfQfFuK_sV60pEIbUjqWKjgQfhuf0XGluiqDvnx1s27RG_kICYPkYCXoo5ioLFq7UQ",
      "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "environment": "production"
    },
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "priority": "high"
  }'
```

#### Response (201 Created)
```json
{
  "success": true,
  "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
  "message": "Task created successfully"
}
```

#### Error Response (400 Bad Request)
```json
{
  "success": false,
  "error": "Validation failed",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-123",
  "details": {
    "task_type": "Invalid task type: invalid_type",
    "org_id": "Invalid UUID format",
    "execution_log_id": "Execution log ID is required for sequential task processing"
  }
}
```

### 2. List Tasks

Retrieve tasks with filtering options.

#### Basic Request
```bash
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .
```

#### Advanced Filtering
```bash
# Filter by status and task type
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&status=completed&task_type=metadata_extraction" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .

# Filter by execution log ID (sequential tasks)
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&execution_log_id=e5cc4b65-d0ce-4570-9425-9619af407db0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .

# Limit results
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": [
    {
      "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
      "task_type": "metadata_extraction",
      "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "user_id": "2123",
      "status": "completed",
      "priority": "high",
      "progress": 100,
      "retry_count": 0,
      "created_at": "2025-01-24T10:16:17.275408Z",
      "updated_at": "2025-01-24T10:18:45.123456Z",
      "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
      "params": {
        "access_token": "[REDACTED]",
        "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
        "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
        "environment": "production"
      },
      "message": "Task completed successfully",
      "result": "{\"blob_path\": \"/metadata/org_d432f9f2/metadata_20250124.zip\", \"extracted_objects\": 145}"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 100,
    "has_more": false
  }
}
```

### 3. Get Task by ID

Retrieve detailed information about a specific task.

#### Request
```bash
curl -s "http://localhost:7071/api/tasks/183c990b-c389-4ce5-900f-e13ddd1e0f0a" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "status": "completed",
    "priority": "high",
    "progress": 100,
    "retry_count": 0,
    "created_at": "2025-01-24T10:16:17.275408Z",
    "updated_at": "2025-01-24T10:18:45.123456Z",
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "params": {
      "access_token": "[REDACTED]",
      "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "environment": "production"
    },
    "message": "Task completed successfully",
    "result": "{\"blob_path\": \"/metadata/org_d432f9f2/metadata_20250124.zip\", \"extracted_objects\": 145, \"processing_time\": \"2m 28s\"}"
  }
}
```

#### Error Response (404 Not Found)
```json
{
  "success": false,
  "error": "Task not found",
  "error_code": "NOT_FOUND",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-456"
}
```

### 4. Get Latest Task by Type

Retrieve the most recent task of a specific type.

#### Request
```bash
curl -s "http://localhost:7071/api/tasks/latest?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&task_type=metadata_extraction" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .
```

#### With Execution Log Filter
```bash
curl -s "http://localhost:7071/api/tasks/latest?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&task_type=metadata_extraction&execution_log_id=e5cc4b65-d0ce-4570-9425-9619af407db0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq .
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "status": "completed",
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "created_at": "2025-01-24T10:16:17.275408Z"
  }
}
```

### 5. Update Task Status

Update the status and progress of a task (typically used by internal services).

#### Request
```bash
curl -X PUT "http://localhost:7071/api/tasks/183c990b-c389-4ce5-900f-e13ddd1e0f0a/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "status": "completed",
    "progress": 100,
    "message": "Metadata extraction completed successfully",
    "result": "{\"blob_path\": \"/metadata/org_d432f9f2/metadata_20250124.zip\", \"extracted_objects\": 145}"
  }'
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Task status updated successfully"
}
```

#### Update to Failed Status
```bash
curl -X PUT "http://localhost:7071/api/tasks/183c990b-c389-4ce5-900f-e13ddd1e0f0a/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "status": "failed",
    "progress": 75,
    "message": "Task failed due to authentication error",
    "error": "Invalid Salesforce credentials"
  }'
```

### 6. Delete Task

Delete a completed or failed task.

#### Request
```bash
curl -X DELETE "http://localhost:7071/api/tasks/183c990b-c389-4ce5-900f-e13ddd1e0f0a" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Task deleted successfully"
}
```

#### Error Response (409 Conflict)
```json
{
  "success": false,
  "error": "Cannot delete running task",
  "error_code": "CONFLICT",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-789"
}
```

## 🔄 Sequential Task Processing Examples

### Complete Sequential Task Flow

Here's how to create and monitor a complete sequential task flow:

#### 1. Create Authentication Task
```bash
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "task_type": "sfdc_authenticate",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {
      "access_token": "YOUR_SECURE_TOKEN",
      "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "environment": "production"
    },
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "priority": "high"
  }'
```

#### 2. Monitor All Tasks in Sequence
```bash
# Monitor all tasks with the same execution_log_id
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75&execution_log_id=e5cc4b65-d0ce-4570-9425-9619af407db0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.data[] | {task_type, status, progress, created_at}'
```

#### Expected Sequential Output
```json
[
  {
    "task_type": "sfdc_authenticate",
    "status": "completed",
    "progress": 100,
    "created_at": "2025-01-24T10:16:17.275408Z"
  },
  {
    "task_type": "health_check",
    "status": "completed",
    "progress": 100,
    "created_at": "2025-01-24T10:16:45.123456Z"
  },
  {
    "task_type": "metadata_extraction",
    "status": "running",
    "progress": 65,
    "created_at": "2025-01-24T10:17:12.987654Z"
  },
  {
    "task_type": "pmd_apex_security",
    "status": "pending",
    "progress": 0,
    "created_at": "2025-01-24T10:17:15.456789Z"
  }
]
```

### Task Status Monitoring

#### Monitor Task Progress
```bash
# Check specific task progress
TASK_ID="183c990b-c389-4ce5-900f-e13ddd1e0f0a"
while true; do
  STATUS=$(curl -s "http://localhost:7071/api/tasks/$TASK_ID" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq -r '.data.status')
  PROGRESS=$(curl -s "http://localhost:7071/api/tasks/$TASK_ID" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq -r '.data.progress')
  
  echo "Task Status: $STATUS, Progress: $PROGRESS%"
  
  if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
    break
  fi
  
  sleep 5
done
```

#### Monitor Sequential Task Completion
```bash
# Monitor entire sequence completion
EXECUTION_LOG_ID="e5cc4b65-d0ce-4570-9425-9619af407db0"
ORG_ID="d432f9f2-c257-49c9-a5d0-1f11784d0f75"

while true; do
  COMPLETED_COUNT=$(curl -s "http://localhost:7071/api/tasks?org_id=$ORG_ID&execution_log_id=$EXECUTION_LOG_ID&status=completed" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.data | length')
  
  TOTAL_COUNT=$(curl -s "http://localhost:7071/api/tasks?org_id=$ORG_ID&execution_log_id=$EXECUTION_LOG_ID" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.data | length')
  
  echo "Sequential Tasks Progress: $COMPLETED_COUNT/$TOTAL_COUNT completed"
  
  if [ "$COMPLETED_COUNT" = "$TOTAL_COUNT" ]; then
    echo "All sequential tasks completed!"
    break
  fi
  
  sleep 10
done
```

## 🐍 Python Examples

### Task Management Client
```python
import requests
import json
import time
from typing import Dict, List, Optional

class SFDCTaskClient:
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def create_task(self, task_type: str, org_id: str, user_id: str, 
                   params: Dict, execution_log_id: str, priority: str = 'medium') -> Dict:
        """Create a new task"""
        data = {
            'task_type': task_type,
            'org_id': org_id,
            'user_id': user_id,
            'params': params,
            'execution_log_id': execution_log_id,
            'priority': priority
        }
        
        response = requests.post(
            f'{self.base_url}/tasks',
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def get_tasks(self, org_id: str, status: Optional[str] = None, 
                 task_type: Optional[str] = None, execution_log_id: Optional[str] = None,
                 limit: int = 100) -> Dict:
        """Get tasks with filtering"""
        params = {'org_id': org_id, 'limit': limit}
        if status:
            params['status'] = status
        if task_type:
            params['task_type'] = task_type
        if execution_log_id:
            params['execution_log_id'] = execution_log_id
        
        response = requests.get(
            f'{self.base_url}/tasks',
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def get_task(self, task_id: str) -> Dict:
        """Get task by ID"""
        response = requests.get(
            f'{self.base_url}/tasks/{task_id}',
            headers=self.headers
        )
        return response.json()
    
    def update_task_status(self, task_id: str, status: str, progress: int = None,
                          message: str = None, result: str = None, error: str = None) -> Dict:
        """Update task status"""
        data = {'status': status}
        if progress is not None:
            data['progress'] = progress
        if message:
            data['message'] = message
        if result:
            data['result'] = result
        if error:
            data['error'] = error
        
        response = requests.put(
            f'{self.base_url}/tasks/{task_id}/status',
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def delete_task(self, task_id: str) -> Dict:
        """Delete task"""
        response = requests.delete(
            f'{self.base_url}/tasks/{task_id}',
            headers=self.headers
        )
        return response.json()
    
    def monitor_sequential_tasks(self, org_id: str, execution_log_id: str, 
                               poll_interval: int = 5) -> List[Dict]:
        """Monitor sequential task completion"""
        print(f"Monitoring sequential tasks for execution_log_id: {execution_log_id}")
        
        while True:
            tasks_response = self.get_tasks(
                org_id=org_id,
                execution_log_id=execution_log_id
            )
            
            if not tasks_response.get('success'):
                print(f"Error getting tasks: {tasks_response.get('error')}")
                break
            
            tasks = tasks_response['data']
            completed_tasks = [t for t in tasks if t['status'] == 'completed']
            failed_tasks = [t for t in tasks if t['status'] == 'failed']
            
            print(f"Progress: {len(completed_tasks)}/{len(tasks)} completed, {len(failed_tasks)} failed")
            
            # Print task status
            for task in sorted(tasks, key=lambda x: x['created_at']):
                print(f"  {task['task_type']}: {task['status']} ({task['progress']}%)")
            
            # Check if all tasks are completed or failed
            if len(completed_tasks) + len(failed_tasks) == len(tasks):
                print("Sequential task processing completed!")
                return tasks
            
            time.sleep(poll_interval)

# Usage example
if __name__ == "__main__":
    client = SFDCTaskClient(
        base_url="http://localhost:7071/api",
        auth_token="YOUR_JWT_TOKEN"
    )
    
    # Create a metadata extraction task
    task_params = {
        "access_token": "YOUR_SECURE_TOKEN",
        "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
        "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
        "environment": "production"
    }
    
    result = client.create_task(
        task_type="metadata_extraction",
        org_id="d432f9f2-c257-49c9-a5d0-1f11784d0f75",
        user_id="2123",
        params=task_params,
        execution_log_id="e5cc4b65-d0ce-4570-9425-9619af407db0",
        priority="high"
    )
    
    if result.get('success'):
        print(f"Task created: {result['task_id']}")
        
        # Monitor the task
        task_id = result['task_id']
        while True:
            task_info = client.get_task(task_id)
            if task_info.get('success'):
                task = task_info['data']
                print(f"Task {task['task_type']}: {task['status']} ({task['progress']}%)")
                
                if task['status'] in ['completed', 'failed']:
                    break
            
            time.sleep(5)
    else:
        print(f"Error creating task: {result.get('error')}")
```

## 🔍 Error Handling Examples

### Common Error Scenarios

#### 1. Invalid Task Type
```bash
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "task_type": "invalid_task_type",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {},
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0"
  }'
```

Response:
```json
{
  "success": false,
  "error": "Validation failed",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-123",
  "details": {
    "task_type": "Invalid task type: invalid_task_type. Must be one of: sfdc_authenticate, health_check, metadata_extraction, pmd_apex_security"
  }
}
```

#### 2. Missing Required Parameters
```bash
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
  }'
```

Response:
```json
{
  "success": false,
  "error": "Validation failed",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-124",
  "details": {
    "user_id": "Field is required",
    "params": "Field is required",
    "execution_log_id": "Field is required for sequential task processing"
  }
}
```

#### 3. Authentication Error
```bash
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {},
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0"
  }'
```

Response:
```json
{
  "success": false,
  "error": "Authentication required",
  "error_code": "UNAUTHORIZED",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-125"
}
```

---

This comprehensive guide covers all aspects of the Task Management API with practical examples for sequential task processing and execution log coordination.