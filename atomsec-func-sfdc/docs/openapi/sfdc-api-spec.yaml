openapi: 3.0.3
info:
  title: AtomSec SFDC Service API
  description: |
    Comprehensive API documentation for the AtomSec Salesforce (SFDC) Service.
    
    This service provides Salesforce integration capabilities including:
    - Sequential task processing (authenticate → health check → metadata extraction → PMD scanning)
    - Security analysis and health checks
    - Integration management
    - User and organization management
    - Policy and rule management
    - PMD (Programming Mistake Detector) scanning
    
    ## Architecture
    
    The SFDC service follows a microservices architecture:
    - **Service Role**: Processes scan requests from DB service or direct POST requests
    - **Task Processing**: Sequential task execution with execution_log_id tracking
    - **Security**: Validates access tokens and parameters before processing
    - **Communication**: Internal service-to-service communication with DB service
    
    ## Authentication
    
    The API supports multiple authentication methods:
    - JWT Bearer tokens for user authentication
    - API keys for service-to-service communication
    - Azure AD integration for enterprise authentication
    
    ## Rate Limiting
    
    API endpoints are rate limited to ensure service stability:
    - Standard endpoints: 100 requests per minute per user
    - Task creation endpoints: 10 requests per minute per user
    - Health check endpoints: 200 requests per minute per user
    
  version: 1.0.0
  contact:
    name: AtomSec Development Team
    email: <EMAIL>
    url: https://atomsec.com
  license:
    name: Proprietary
    url: https://atomsec.com/license
  termsOfService: https://atomsec.com/terms

servers:
  - url: http://localhost:7071/api
    description: Local development server
  - url: https://atomsec-func-sfdc-dev.azurewebsites.net/api
    description: Development environment
  - url: https://atomsec-func-sfdc-staging.azurewebsites.net/api
    description: Staging environment
  - url: https://atomsec-func-sfdc.azurewebsites.net/api
    description: Production environment

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  # Health Check Endpoints
  /health:
    get:
      tags:
        - Health Check
      summary: Get service health status
      description: |
        Returns the current health status of the SFDC service including:
        - Service status and uptime
        - Database connectivity
        - External service dependencies
        - System metrics (CPU, memory, disk usage)
        - Task processing statistics
      operationId: getHealth
      responses:
        '200':
          description: Service health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                status: "healthy"
                service: "atomsec-func-sfdc"
                environment: "production"
                version: "1.0.0"
                timestamp: "2025-01-24T10:30:00Z"
                checks:
                  database: "connected"
                  service_bus: "connected"
                  key_vault: "connected"
                  sfdc_service: "connected"
                metrics:
                  pending_tasks: 5
                  completed_tasks: 150
                  failed_tasks: 2
                  total_tasks: 157
                uptime: "2h 15m 30s"
                memory_usage: "45.2%"
                cpu_usage: "12.8%"
        '503':
          description: Service unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /health/detailed:
    get:
      tags:
        - Health Check
      summary: Get detailed health information
      description: |
        Returns comprehensive health information including:
        - Detailed dependency status with response times
        - Task processing metrics and performance data
        - System resource utilization
        - Error statistics and trends
      operationId: getDetailedHealth
      responses:
        '200':
          description: Detailed health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealthResponse'

  # Task Management Endpoints
  /tasks:
    get:
      tags:
        - Task Management
      summary: List tasks with filtering
      description: |
        Retrieve a list of tasks with optional filtering by:
        - Organization ID (required)
        - Task status (pending, running, completed, failed)
        - Task type (metadata_extraction, health_check, etc.)
        - Execution log ID for tracking sequential tasks
        
        **Sequential Task Processing**: Tasks are processed in sequence using the same execution_log_id:
        1. sfdc_authenticate - Authenticate with Salesforce
        2. health_check - Perform health checks
        3. metadata_extraction - Extract Salesforce metadata
        4. pmd_apex_security - PMD scanning (final step)
      operationId: getTasks
      parameters:
        - name: org_id
          in: query
          required: true
          description: Organization ID to filter tasks
          schema:
            type: string
            format: uuid
          example: "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
        - name: status
          in: query
          required: false
          description: Filter tasks by status
          schema:
            type: string
            enum: [pending, running, completed, failed]
          example: "completed"
        - name: task_type
          in: query
          required: false
          description: Filter tasks by type
          schema:
            type: string
            enum: [sfdc_authenticate, health_check, metadata_extraction, pmd_apex_security]
          example: "metadata_extraction"
        - name: execution_log_id
          in: query
          required: false
          description: Filter tasks by execution log ID for sequential task tracking
          schema:
            type: string
            format: uuid
          example: "e5cc4b65-d0ce-4570-9425-9619af407db0"
        - name: limit
          in: query
          required: false
          description: Maximum number of tasks to return
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
          example: 50
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskListResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - Task Management
      summary: Create a new task
      description: |
        Create a new task for processing. The task will be added to the sequential processing queue.
        
        **Required Parameters**:
        - Secure access tokens for Salesforce authentication
        - Valid execution_log_id for tracking across task sequence
        - Integration ID and organization details
        
        **Security Validation**:
        - Access tokens are validated for security
        - Parameters are checked for required fields
        - Execution log ID is validated for proper tracking
      operationId: createTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
            example:
              task_type: "metadata_extraction"
              org_id: "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
              user_id: "2123"
              params:
                access_token: "00D5j000006P6ly!AQgAQM9ts3lBUQ31FN780NESscn0OQyfQfFuK_sV60pEIbUjqWKjgQfhuf0XGluiqDvnx1s27RG_kICYPkYCXoo5ioLFq7UQ"
                instance_url: "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/"
                integration_id: "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
                environment: "production"
              execution_log_id: "e5cc4b65-d0ce-4570-9425-9619af407db0"
      responses:
        '201':
          description: Task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTaskResponse'
              example:
                success: true
                task_id: "183c990b-c389-4ce5-900f-e13ddd1e0f0a"
                message: "Task created successfully"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{task_id}:
    get:
      tags:
        - Task Management
      summary: Get task by ID
      description: Retrieve detailed information about a specific task including its current status, progress, and results.
      operationId: getTaskById
      parameters:
        - name: task_id
          in: path
          required: true
          description: Unique task identifier
          schema:
            type: string
            format: uuid
          example: "183c990b-c389-4ce5-900f-e13ddd1e0f0a"
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '404':
          description: Task not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - Task Management
      summary: Delete task
      description: Delete a task from the system. Only completed or failed tasks can be deleted.
      operationId: deleteTask
      parameters:
        - name: task_id
          in: path
          required: true
          description: Unique task identifier
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Task deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Task not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Task cannot be deleted (still running)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{task_id}/status:
    put:
      tags:
        - Task Management
      summary: Update task status
      description: |
        Update the status of a task. This endpoint is typically used by internal services
        to update task progress and completion status.
      operationId: updateTaskStatus
      parameters:
        - name: task_id
          in: path
          required: true
          description: Unique task identifier
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTaskStatusRequest'
      responses:
        '200':
          description: Task status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Task not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/latest:
    get:
      tags:
        - Task Management
      summary: Get latest task by type
      description: |
        Retrieve the most recent task of a specific type for an organization.
        Useful for checking the status of the latest sequential task execution.
      operationId: getLatestTask
      parameters:
        - name: org_id
          in: query
          required: true
          description: Organization ID
          schema:
            type: string
            format: uuid
        - name: task_type
          in: query
          required: true
          description: Task type to search for
          schema:
            type: string
            enum: [sfdc_authenticate, health_check, metadata_extraction, pmd_apex_security]
        - name: status
          in: query
          required: false
          description: Filter by task status
          schema:
            type: string
            enum: [pending, running, completed, failed]
        - name: execution_log_id
          in: query
          required: false
          description: Filter by execution log ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Latest task found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '404':
          description: No task found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Integration Management Endpoints
  /integrations:
    get:
      tags:
        - Integration Management
      summary: List integrations
      description: |
        Retrieve a list of Salesforce integrations with optional filtering.
        Integrations represent connected Salesforce organizations.
      operationId: getIntegrations
      parameters:
        - name: type
          in: query
          required: false
          description: Filter by integration type
          schema:
            type: string
            enum: [Salesforce]
            default: Salesforce
        - name: environment
          in: query
          required: false
          description: Filter by environment
          schema:
            type: string
            enum: [production, sandbox, development]
        - name: is_active
          in: query
          required: false
          description: Filter by active status
          schema:
            type: boolean
      responses:
        '200':
          description: List of integrations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationListResponse'

    post:
      tags:
        - Integration Management
      summary: Create integration
      description: Create a new Salesforce integration configuration.
      operationId: createIntegration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIntegrationRequest'
      responses:
        '201':
          description: Integration created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateIntegrationResponse'

  /integrations/{integration_id}:
    get:
      tags:
        - Integration Management
      summary: Get integration by ID
      description: Retrieve detailed information about a specific integration.
      operationId: getIntegrationById
      parameters:
        - name: integration_id
          in: path
          required: true
          description: Unique integration identifier
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Integration details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationResponse'
        '404':
          description: Integration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Integration Management
      summary: Update integration
      description: Update an existing integration configuration.
      operationId: updateIntegration
      parameters:
        - name: integration_id
          in: path
          required: true
          description: Unique integration identifier
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIntegrationRequest'
      responses:
        '200':
          description: Integration updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

    delete:
      tags:
        - Integration Management
      summary: Delete integration
      description: Soft delete an integration (marks as inactive).
      operationId: deleteIntegration
      parameters:
        - name: integration_id
          in: path
          required: true
          description: Unique integration identifier
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Integration deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Security Analysis Endpoints
  /security/health-score:
    get:
      tags:
        - Security Analysis
      summary: Get security health score
      description: |
        Calculate and return the security health score for a Salesforce organization.
        The score is based on various security metrics and best practices.
      operationId: getSecurityHealthScore
      parameters:
        - name: integration_id
          in: query
          required: true
          description: Integration ID to analyze
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Security health score
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityHealthScoreResponse'

  /security/health-risks:
    get:
      tags:
        - Security Analysis
      summary: Get security health risks
      description: |
        Retrieve detailed security risks and recommendations for a Salesforce organization.
      operationId: getSecurityHealthRisks
      parameters:
        - name: integration_id
          in: query
          required: true
          description: Integration ID to analyze
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Security health risks
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityHealthRisksResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for service-to-service authentication

  schemas:
    # Health Check Schemas
    HealthResponse:
      type: object
      required:
        - status
        - service
        - environment
        - version
        - timestamp
        - checks
        - metrics
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
          description: Overall service health status
        service:
          type: string
          description: Service name
          example: "atomsec-func-sfdc"
        environment:
          type: string
          description: Current environment
          example: "production"
        version:
          type: string
          description: Service version
          example: "1.0.0"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
        checks:
          type: object
          description: Individual service dependency checks
          properties:
            database:
              type: string
              enum: [connected, disconnected, error]
            service_bus:
              type: string
              enum: [connected, disconnected, error]
            key_vault:
              type: string
              enum: [connected, disconnected, error]
            sfdc_service:
              type: string
              enum: [connected, disconnected, error]
        metrics:
          type: object
          description: Task processing metrics
          properties:
            pending_tasks:
              type: integer
              minimum: 0
            completed_tasks:
              type: integer
              minimum: 0
            failed_tasks:
              type: integer
              minimum: 0
            total_tasks:
              type: integer
              minimum: 0
        uptime:
          type: string
          description: Service uptime
          example: "2h 15m 30s"
        memory_usage:
          type: string
          description: Memory usage percentage
          example: "45.2%"
        cpu_usage:
          type: string
          description: CPU usage percentage
          example: "12.8%"

    DetailedHealthResponse:
      allOf:
        - $ref: '#/components/schemas/HealthResponse'
        - type: object
          properties:
            checks:
              type: object
              description: Detailed dependency checks with response times
              properties:
                database:
                  $ref: '#/components/schemas/DetailedHealthCheck'
                service_bus:
                  $ref: '#/components/schemas/DetailedHealthCheck'
                key_vault:
                  $ref: '#/components/schemas/DetailedHealthCheck'
                sfdc_service:
                  $ref: '#/components/schemas/DetailedHealthCheck'
            disk_usage:
              type: string
              description: Disk usage percentage
              example: "23.1%"

    DetailedHealthCheck:
      type: object
      required:
        - status
        - response_time
        - last_check
      properties:
        status:
          type: string
          enum: [connected, disconnected, error]
        response_time:
          type: string
          description: Response time in milliseconds
          example: "45ms"
        last_check:
          type: string
          format: date-time
          description: Last health check timestamp
        error_message:
          type: string
          description: Error message if status is error

    # Task Management Schemas
    Task:
      type: object
      required:
        - task_id
        - task_type
        - org_id
        - user_id
        - status
        - priority
        - progress
        - retry_count
        - created_at
        - updated_at
        - execution_log_id
      properties:
        task_id:
          type: string
          format: uuid
          description: Unique task identifier
        task_type:
          type: string
          enum: [sfdc_authenticate, health_check, metadata_extraction, pmd_apex_security]
          description: Type of task to execute
        org_id:
          type: string
          format: uuid
          description: Organization identifier
        user_id:
          type: string
          description: User identifier who created the task
        status:
          type: string
          enum: [pending, running, completed, failed]
          description: Current task status
        priority:
          type: string
          enum: [low, medium, high, critical]
          description: Task priority level
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Task completion percentage
        retry_count:
          type: integer
          minimum: 0
          description: Number of retry attempts
        created_at:
          type: string
          format: date-time
          description: Task creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
        execution_log_id:
          type: string
          format: uuid
          description: Execution log ID for tracking sequential tasks
        params:
          type: object
          description: Task-specific parameters
          properties:
            access_token:
              type: string
              description: Salesforce access token (secure)
            instance_url:
              type: string
              format: uri
              description: Salesforce instance URL
            integration_id:
              type: string
              format: uuid
              description: Integration identifier
            environment:
              type: string
              enum: [production, sandbox, development]
              description: Target environment
        message:
          type: string
          description: Status message or error description
        result:
          type: string
          description: Task execution result (JSON string)

    CreateTaskRequest:
      type: object
      required:
        - task_type
        - org_id
        - user_id
        - params
        - execution_log_id
      properties:
        task_type:
          type: string
          enum: [sfdc_authenticate, health_check, metadata_extraction, pmd_apex_security]
          description: Type of task to create
        org_id:
          type: string
          format: uuid
          description: Organization identifier
        user_id:
          type: string
          description: User identifier
        params:
          type: object
          required:
            - access_token
            - instance_url
            - integration_id
          properties:
            access_token:
              type: string
              description: Secure Salesforce access token
            instance_url:
              type: string
              format: uri
              description: Salesforce instance URL
            integration_id:
              type: string
              format: uuid
              description: Integration identifier
            environment:
              type: string
              enum: [production, sandbox, development]
              default: production
              description: Target environment
        execution_log_id:
          type: string
          format: uuid
          description: Execution log ID for sequential task tracking
        priority:
          type: string
          enum: [low, medium, high, critical]
          default: medium
          description: Task priority level

    CreateTaskResponse:
      type: object
      required:
        - success
        - task_id
        - message
      properties:
        success:
          type: boolean
          description: Operation success status
        task_id:
          type: string
          format: uuid
          description: Created task identifier
        message:
          type: string
          description: Success message

    TaskResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          $ref: '#/components/schemas/Task'

    TaskListResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          type: array
          items:
            $ref: '#/components/schemas/Task'
        pagination:
          type: object
          properties:
            total:
              type: integer
              description: Total number of tasks
            page:
              type: integer
              description: Current page number
            limit:
              type: integer
              description: Items per page
            has_more:
              type: boolean
              description: Whether more pages exist

    UpdateTaskStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [pending, running, completed, failed]
          description: New task status
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Task completion percentage
        message:
          type: string
          description: Status message
        result:
          type: string
          description: Task execution result (JSON string)
        error:
          type: string
          description: Error message if status is failed

    # Integration Management Schemas
    Integration:
      type: object
      required:
        - integration_id
        - name
        - type
        - tenant_url
        - environment
        - is_active
        - created_at
        - updated_at
      properties:
        integration_id:
          type: string
          format: uuid
          description: Unique integration identifier
        name:
          type: string
          description: Integration display name
        type:
          type: string
          enum: [Salesforce]
          description: Integration type
        tenant_url:
          type: string
          format: hostname
          description: Salesforce tenant URL
        environment:
          type: string
          enum: [production, sandbox, development]
          description: Environment type
        is_active:
          type: boolean
          description: Whether integration is active
        health_score:
          type: integer
          minimum: 0
          maximum: 100
          description: Security health score
        last_scan:
          type: string
          format: date-time
          description: Last scan timestamp
        last_health_check_scan:
          type: string
          format: date-time
          description: Last health check timestamp
        last_metadata_scan_time:
          type: string
          format: date-time
          description: Last metadata scan timestamp
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp

    CreateIntegrationRequest:
      type: object
      required:
        - name
        - type
        - tenant_url
        - environment
      properties:
        name:
          type: string
          description: Integration display name
        type:
          type: string
          enum: [Salesforce]
          description: Integration type
        tenant_url:
          type: string
          format: hostname
          description: Salesforce tenant URL
        environment:
          type: string
          enum: [production, sandbox, development]
          description: Environment type
        is_active:
          type: boolean
          default: true
          description: Whether integration should be active

    CreateIntegrationResponse:
      type: object
      required:
        - success
        - integration_id
        - message
      properties:
        success:
          type: boolean
          description: Operation success status
        integration_id:
          type: string
          format: uuid
          description: Created integration identifier
        message:
          type: string
          description: Success message

    UpdateIntegrationRequest:
      type: object
      properties:
        name:
          type: string
          description: Integration display name
        environment:
          type: string
          enum: [production, sandbox, development]
          description: Environment type
        is_active:
          type: boolean
          description: Whether integration is active

    IntegrationResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          $ref: '#/components/schemas/Integration'

    IntegrationListResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          type: array
          items:
            $ref: '#/components/schemas/Integration'

    # Security Analysis Schemas
    SecurityHealthScoreResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          type: object
          required:
            - integration_id
            - health_score
            - calculated_at
          properties:
            integration_id:
              type: string
              format: uuid
              description: Integration identifier
            health_score:
              type: integer
              minimum: 0
              maximum: 100
              description: Overall security health score
            calculated_at:
              type: string
              format: date-time
              description: Score calculation timestamp
            categories:
              type: object
              description: Score breakdown by category
              properties:
                authentication:
                  type: integer
                  minimum: 0
                  maximum: 100
                password_policy:
                  type: integer
                  minimum: 0
                  maximum: 100
                session_management:
                  type: integer
                  minimum: 0
                  maximum: 100
                user_permissions:
                  type: integer
                  minimum: 0
                  maximum: 100

    SecurityHealthRisksResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          description: Operation success status
        data:
          type: object
          required:
            - integration_id
            - risks
            - analyzed_at
          properties:
            integration_id:
              type: string
              format: uuid
              description: Integration identifier
            risks:
              type: array
              items:
                $ref: '#/components/schemas/SecurityRisk'
            analyzed_at:
              type: string
              format: date-time
              description: Analysis timestamp

    SecurityRisk:
      type: object
      required:
        - risk_id
        - category
        - severity
        - title
        - description
      properties:
        risk_id:
          type: string
          description: Unique risk identifier
        category:
          type: string
          enum: [authentication, authorization, data_protection, configuration]
          description: Risk category
        severity:
          type: string
          enum: [low, medium, high, critical]
          description: Risk severity level
        title:
          type: string
          description: Risk title
        description:
          type: string
          description: Detailed risk description
        recommendation:
          type: string
          description: Recommended remediation action
        affected_objects:
          type: array
          items:
            type: string
          description: List of affected Salesforce objects

    # Common Response Schemas
    SuccessResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          description: Operation success status
        message:
          type: string
          description: Success message

    ErrorResponse:
      type: object
      required:
        - success
        - error
        - error_code
        - timestamp
      properties:
        success:
          type: boolean
          description: Operation success status (always false)
        error:
          type: string
          description: Error message
        error_code:
          type: string
          description: Machine-readable error code
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        request_id:
          type: string
          description: Request correlation ID
        details:
          type: object
          description: Additional error details

  responses:
    BadRequest:
      description: Bad request - invalid parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Validation failed"
            error_code: "VALIDATION_ERROR"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"
            details:
              task_type: "Invalid task type: invalid_type"

    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Authentication required"
            error_code: "UNAUTHORIZED"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Access denied"
            error_code: "FORBIDDEN"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Task not found"
            error_code: "NOT_FOUND"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Task already exists"
            error_code: "CONFLICT"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Internal server error"
            error_code: "INTERNAL_ERROR"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

    ServiceUnavailable:
      description: Service temporarily unavailable
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Database service unavailable"
            error_code: "SERVICE_UNAVAILABLE"
            timestamp: "2025-01-24T10:30:00Z"
            request_id: "req-123"

tags:
  - name: Health Check
    description: Service health monitoring and status endpoints
  - name: Task Management
    description: Sequential task processing and management
  - name: Integration Management
    description: Salesforce integration configuration and management
  - name: Security Analysis
    description: Security health scoring and risk analysis
  - name: Policy Management
    description: Security policy and rule management
  - name: User Management
    description: User and organization management
  - name: PMD Scanning
    description: Programming Mistake Detector scanning