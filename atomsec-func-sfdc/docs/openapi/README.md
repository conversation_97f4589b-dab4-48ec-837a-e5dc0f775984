# AtomSec SFDC Service API Documentation

This directory contains comprehensive API documentation for the AtomSec Salesforce (SFDC) Service.

## 📁 Documentation Structure

```
docs/openapi/
├── README.md                    # This file - documentation overview
├── sfdc-api-spec.yaml          # Complete OpenAPI 3.0 specification
├── swagger-ui.html             # Interactive API documentation portal
├── examples/                   # Request/response examples
│   ├── task-management.md      # Task management examples
│   ├── integration-management.md # Integration examples
│   └── security-analysis.md    # Security analysis examples
├── schemas/                    # JSON schema definitions
│   ├── task-schemas.json       # Task-related schemas
│   ├── integration-schemas.json # Integration schemas
│   └── common-schemas.json     # Common response schemas
└── postman/                    # Postman collections
    ├── sfdc-api.postman_collection.json
    └── sfdc-environments.postman_environment.json
```

## 🚀 Quick Start

### 1. View Interactive Documentation

Open `swagger-ui.html` in your browser to access the interactive API documentation portal:

```bash
# From the project root
open atomsec-func-sfdc/docs/openapi/swagger-ui.html
```

### 2. Environment Setup

The API supports multiple environments:

| Environment | Base URL | Description |
|-------------|----------|-------------|
| Local | `http://localhost:7071/api` | Local development server |
| Development | `https://atomsec-func-sfdc-dev.azurewebsites.net/api` | Development environment |
| Staging | `https://atomsec-func-sfdc-staging.azurewebsites.net/api` | Staging environment |
| Production | `https://atomsec-func-sfdc.azurewebsites.net/api` | Production environment |

### 3. Authentication

The API supports multiple authentication methods:

#### JWT Bearer Token
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     https://atomsec-func-sfdc.azurewebsites.net/api/health
```

#### API Key
```bash
curl -H "X-API-Key: YOUR_API_KEY" \
     https://atomsec-func-sfdc.azurewebsites.net/api/health
```

## 📋 API Overview

### Core Endpoints

| Endpoint Category | Description | Key Features |
|------------------|-------------|--------------|
| **Health Check** | Service monitoring | Status, metrics, dependency checks |
| **Task Management** | Sequential task processing | SFDC authentication, metadata extraction, PMD scanning |
| **Integration Management** | Salesforce org configuration | CRUD operations, connection testing |
| **Security Analysis** | Security scoring and risk analysis | Health scores, risk identification |
| **Policy Management** | Security policies and rules | Policy CRUD, rule management |
| **User Management** | User and organization management | User profiles, permissions |
| **PMD Scanning** | Code quality analysis | Apex code scanning, issue reporting |

### Sequential Task Processing Flow

The SFDC service processes tasks in a specific sequence, all linked by `execution_log_id`:

```mermaid
graph LR
    A[sfdc_authenticate] --> B[health_check]
    B --> C[metadata_extraction]
    C --> D[pmd_apex_security]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

1. **sfdc_authenticate** - Authenticate with Salesforce API
2. **health_check** - Perform comprehensive health checks
3. **metadata_extraction** - Extract Salesforce metadata
4. **pmd_apex_security** - Run PMD scanning (final step)

## 🔧 Usage Examples

### Health Check
```bash
# Basic health check
curl -s "http://localhost:7071/api/health" | jq .

# Detailed health information
curl -s "http://localhost:7071/api/health/detailed" | jq .
```

### Task Management
```bash
# List tasks for an organization
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75" | jq .

# Create a new task
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {
      "access_token": "YOUR_SECURE_TOKEN",
      "instance_url": "https://your-org.my.salesforce.com/services/data/v59.0/",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "environment": "production"
    },
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0"
  }' | jq .

# Get task status
curl -s "http://localhost:7071/api/tasks/TASK_ID" | jq .
```

### Integration Management
```bash
# List integrations
curl -s "http://localhost:7071/api/integrations" | jq .

# Create integration
curl -X POST "http://localhost:7071/api/integrations" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Salesforce Org",
    "type": "Salesforce",
    "tenant_url": "my-org.my.salesforce.com",
    "environment": "production"
  }' | jq .
```

### Security Analysis
```bash
# Get security health score
curl -s "http://localhost:7071/api/security/health-score?integration_id=INTEGRATION_ID" | jq .

# Get security risks
curl -s "http://localhost:7071/api/security/health-risks?integration_id=INTEGRATION_ID" | jq .
```

## 🔒 Security Considerations

### Parameter Validation
- All requests undergo comprehensive parameter validation
- Access tokens are validated for security before processing
- Execution log IDs are required for sequential task tracking

### Rate Limiting
- Standard endpoints: 100 requests/minute per user
- Task creation: 10 requests/minute per user
- Health checks: 200 requests/minute per user

### Authentication Requirements
- JWT tokens must be valid and not expired
- API keys must have appropriate permissions
- Service-to-service calls require internal authentication

## 📊 Response Formats

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error description",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-01-24T10:30:00Z",
  "request_id": "req-123",
  "details": {
    "field": "Additional error information"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `NOT_FOUND` | 404 | Resource not found |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Access denied |
| `CONFLICT` | 409 | Resource conflict |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## 🧪 Testing

### Using Swagger UI
1. Open `swagger-ui.html` in your browser
2. Select the appropriate environment
3. Set authentication token if required
4. Use the "Try it out" feature for each endpoint

### Using Postman
1. Import the Postman collection from `postman/sfdc-api.postman_collection.json`
2. Import the environment from `postman/sfdc-environments.postman_environment.json`
3. Set up authentication variables
4. Run individual requests or the entire collection

### Using cURL
See the examples above or check the `examples/` directory for comprehensive cURL examples.

## 🔄 API Versioning

The API uses header-based versioning:

```bash
curl -H "API-Version: v1" \
     -H "Accept: application/json" \
     "http://localhost:7071/api/tasks"
```

Supported versions:
- `v1` (current) - Full feature set
- `v2` (planned) - Enhanced security features

## 📈 Monitoring and Analytics

### Request Tracking
All API requests include correlation IDs for tracking:
- `X-Correlation-ID` header in responses
- Request/response logging in Application Insights
- Performance metrics collection

### Health Monitoring
- Service health endpoints for monitoring systems
- Dependency health checks (database, service bus, key vault)
- Performance metrics (response times, throughput, error rates)

## 🤝 Support and Contribution

### Getting Help
- Check the interactive documentation in Swagger UI
- Review the examples in the `examples/` directory
- Contact the development team for API-specific questions

### Reporting Issues
- API bugs: Create an issue with request/response details
- Documentation issues: Suggest improvements or corrections
- Feature requests: Describe the use case and expected behavior

### Contributing
- Follow the existing API patterns and conventions
- Update documentation when adding new endpoints
- Include comprehensive examples and error handling
- Test all changes against multiple environments

---

**Last Updated**: January 24, 2025  
**API Version**: 1.0.0  
**Documentation Version**: 1.0.0