{"id": "sfdc-api-environments", "name": "AtomSec SFDC API Environments", "values": [{"key": "base_url", "value": "http://localhost:7071/api", "description": "Base URL for the SFDC API", "enabled": true}, {"key": "auth_token", "value": "", "description": "JWT authentication token", "enabled": true, "type": "secret"}, {"key": "api_key", "value": "", "description": "API key for service-to-service authentication", "enabled": true, "type": "secret"}, {"key": "salesforce_access_token", "value": "", "description": "Salesforce access token for API calls", "enabled": true, "type": "secret"}, {"key": "salesforce_instance_url", "value": "https://your-org.my.salesforce.com/services/data/v59.0/", "description": "Salesforce instance URL", "enabled": true}, {"key": "org_id", "value": "d432f9f2-c257-49c9-a5d0-1f11784d0f75", "description": "Organization ID for testing", "enabled": true}, {"key": "user_id", "value": "2123", "description": "User ID for testing", "enabled": true}, {"key": "integration_id", "value": "d432f9f2-c257-49c9-a5d0-1f11784d0f75", "description": "Integration ID for testing", "enabled": true}, {"key": "execution_log_id", "value": "e5cc4b65-d0ce-4570-9425-9619af407db0", "description": "Execution log ID for sequential task tracking", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-24T10:30:00.000Z", "_postman_exported_using": "Postman/10.0.0"}