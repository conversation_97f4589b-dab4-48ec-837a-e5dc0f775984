{"info": {"name": "AtomSec SFDC Service API", "description": "Comprehensive API collection for the AtomSec Salesforce (SFDC) Service including sequential task processing, integration management, and security analysis.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "{{base_url}}", "type": "string"}, {"key": "org_id", "value": "d432f9f2-c257-49c9-a5d0-1f11784d0f75", "type": "string"}, {"key": "user_id", "value": "2123", "type": "string"}, {"key": "execution_log_id", "value": "e5cc4b65-d0ce-4570-9425-9619af407db0", "type": "string"}, {"key": "integration_id", "value": "d432f9f2-c257-49c9-a5d0-1f11784d0f75", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Get Service Health", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Get basic service health status including dependency checks and metrics."}, "response": [{"name": "Healthy Service", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"healthy\",\n  \"service\": \"atomsec-func-sfdc\",\n  \"environment\": \"local\",\n  \"version\": \"1.0.0\",\n  \"timestamp\": \"2025-01-24T10:30:00Z\",\n  \"checks\": {\n    \"database\": \"connected\",\n    \"service_bus\": \"connected\",\n    \"key_vault\": \"connected\",\n    \"sfdc_service\": \"connected\"\n  },\n  \"metrics\": {\n    \"pending_tasks\": 5,\n    \"completed_tasks\": 150,\n    \"failed_tasks\": 2,\n    \"total_tasks\": 157\n  },\n  \"uptime\": \"2h 15m 30s\",\n  \"memory_usage\": \"45.2%\",\n  \"cpu_usage\": \"12.8%\"\n}"}]}, {"name": "Get Detailed Health", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}, "description": "Get comprehensive health information with detailed dependency status and performance metrics."}}], "description": "Health check endpoints for monitoring service status and dependencies."}, {"name": "Task Management", "item": [{"name": "Create Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has task_id\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task_id');", "    pm.collectionVariables.set('created_task_id', jsonData.task_id);", "});", "", "pm.test(\"Response indicates success\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"metadata_extraction\",\n  \"org_id\": \"{{org_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"params\": {\n    \"access_token\": \"{{salesforce_access_token}}\",\n    \"instance_url\": \"{{salesforce_instance_url}}\",\n    \"integration_id\": \"{{integration_id}}\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"{{execution_log_id}}\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}, "description": "Create a new task for sequential processing. This will add the task to the processing queue."}, "response": [{"name": "Task Created Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"metadata_extraction\",\n  \"org_id\": \"d432f9f2-c257-49c9-a5d0-1f11784d0f75\",\n  \"user_id\": \"2123\",\n  \"params\": {\n    \"access_token\": \"00D5j000006P6ly!AQgAQM...\",\n    \"instance_url\": \"https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/\",\n    \"integration_id\": \"d432f9f2-c257-49c9-a5d0-1f11784d0f75\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"e5cc4b65-d0ce-4570-9425-9619af407db0\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"task_id\": \"183c990b-c389-4ce5-900f-e13ddd1e0f0a\",\n  \"message\": \"Task created successfully\"\n}"}]}, {"name": "List Tasks", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has data array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Tasks have required fields\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        var task = jsonData.data[0];", "        pm.expect(task).to.have.property('task_id');", "        pm.expect(task).to.have.property('task_type');", "        pm.expect(task).to.have.property('status');", "        pm.expect(task).to.have.property('execution_log_id');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tasks?org_id={{org_id}}&limit=50", "host": ["{{base_url}}"], "path": ["tasks"], "query": [{"key": "org_id", "value": "{{org_id}}"}, {"key": "status", "value": "completed", "disabled": true}, {"key": "task_type", "value": "metadata_extraction", "disabled": true}, {"key": "execution_log_id", "value": "{{execution_log_id}}", "disabled": true}, {"key": "limit", "value": "50"}]}, "description": "Retrieve tasks with optional filtering by status, type, and execution log ID."}}, {"name": "Get Task by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 404\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Response has task data\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('data');", "        pm.expect(jsonData.data).to.have.property('task_id');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tasks/{{created_task_id}}", "host": ["{{base_url}}"], "path": ["tasks", "{{created_task_id}}"]}, "description": "Get detailed information about a specific task by its ID."}}, {"name": "Get Latest Task by Type", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tasks/latest?org_id={{org_id}}&task_type=metadata_extraction", "host": ["{{base_url}}"], "path": ["tasks", "latest"], "query": [{"key": "org_id", "value": "{{org_id}}"}, {"key": "task_type", "value": "metadata_extraction"}, {"key": "status", "value": "completed", "disabled": true}, {"key": "execution_log_id", "value": "{{execution_log_id}}", "disabled": true}]}, "description": "Get the most recent task of a specific type for an organization."}}, {"name": "Update Task Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\",\n  \"progress\": 100,\n  \"message\": \"Task completed successfully\",\n  \"result\": \"{\\\"blob_path\\\": \\\"/metadata/org_d432f9f2/metadata_20250124.zip\\\", \\\"extracted_objects\\\": 145}\"\n}"}, "url": {"raw": "{{base_url}}/tasks/{{created_task_id}}/status", "host": ["{{base_url}}"], "path": ["tasks", "{{created_task_id}}", "status"]}, "description": "Update the status and progress of a task. Typically used by internal services."}}, {"name": "Delete Task", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/tasks/{{created_task_id}}", "host": ["{{base_url}}"], "path": ["tasks", "{{created_task_id}}"]}, "description": "Delete a completed or failed task from the system."}}], "description": "Task management endpoints for creating, monitoring, and managing sequential task processing."}, {"name": "Integration Management", "item": [{"name": "List Integrations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/integrations", "host": ["{{base_url}}"], "path": ["integrations"], "query": [{"key": "type", "value": "Salesforce", "disabled": true}, {"key": "environment", "value": "production", "disabled": true}, {"key": "is_active", "value": "true", "disabled": true}]}, "description": "Retrieve a list of Salesforce integrations with optional filtering."}}, {"name": "Create Integration", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has integration_id\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('integration_id');", "    pm.collectionVariables.set('created_integration_id', jsonData.integration_id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Salesforce Org\",\n  \"type\": \"Salesforce\",\n  \"tenant_url\": \"test-org.my.salesforce.com\",\n  \"environment\": \"sandbox\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/integrations", "host": ["{{base_url}}"], "path": ["integrations"]}, "description": "Create a new Salesforce integration configuration."}}, {"name": "Get Integration by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/integrations/{{integration_id}}", "host": ["{{base_url}}"], "path": ["integrations", "{{integration_id}}"]}, "description": "Retrieve detailed information about a specific integration."}}, {"name": "Update Integration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Salesforce Org\",\n  \"environment\": \"production\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/integrations/{{created_integration_id}}", "host": ["{{base_url}}"], "path": ["integrations", "{{created_integration_id}}"]}, "description": "Update an existing integration configuration."}}, {"name": "Delete Integration", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/integrations/{{created_integration_id}}", "host": ["{{base_url}}"], "path": ["integrations", "{{created_integration_id}}"]}, "description": "Soft delete an integration (marks as inactive)."}}], "description": "Integration management endpoints for configuring and managing Salesforce connections."}, {"name": "Security Analysis", "item": [{"name": "Get Security Health Score", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/security/health-score?integration_id={{integration_id}}", "host": ["{{base_url}}"], "path": ["security", "health-score"], "query": [{"key": "integration_id", "value": "{{integration_id}}"}]}, "description": "Calculate and return the security health score for a Salesforce organization."}, "response": [{"name": "Security Health Score", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/security/health-score?integration_id={{integration_id}}", "host": ["{{base_url}}"], "path": ["security", "health-score"], "query": [{"key": "integration_id", "value": "{{integration_id}}"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"integration_id\": \"d432f9f2-c257-49c9-a5d0-1f11784d0f75\",\n    \"health_score\": 85,\n    \"calculated_at\": \"2025-01-24T10:30:00Z\",\n    \"categories\": {\n      \"authentication\": 90,\n      \"password_policy\": 80,\n      \"session_management\": 85,\n      \"user_permissions\": 85\n    }\n  }\n}"}]}, {"name": "Get Security Health Risks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/security/health-risks?integration_id={{integration_id}}", "host": ["{{base_url}}"], "path": ["security", "health-risks"], "query": [{"key": "integration_id", "value": "{{integration_id}}"}]}, "description": "Retrieve detailed security risks and recommendations for a Salesforce organization."}}], "description": "Security analysis endpoints for health scoring and risk assessment."}, {"name": "Sequential Task Processing Workflow", "item": [{"name": "1. Create Authentication Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Authentication task created\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.collectionVariables.set('auth_task_id', jsonData.task_id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"sfdc_authenticate\",\n  \"org_id\": \"{{org_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"params\": {\n    \"access_token\": \"{{salesforce_access_token}}\",\n    \"instance_url\": \"{{salesforce_instance_url}}\",\n    \"integration_id\": \"{{integration_id}}\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"{{execution_log_id}}\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}, "description": "Step 1: Create Salesforce authentication task"}}, {"name": "2. Create Health Check Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Health check task created\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.collectionVariables.set('health_task_id', jsonData.task_id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"health_check\",\n  \"org_id\": \"{{org_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"params\": {\n    \"access_token\": \"{{salesforce_access_token}}\",\n    \"instance_url\": \"{{salesforce_instance_url}}\",\n    \"integration_id\": \"{{integration_id}}\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"{{execution_log_id}}\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}, "description": "Step 2: Create health check task"}}, {"name": "3. Create Metadata Extraction Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Metadata extraction task created\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.collectionVariables.set('metadata_task_id', jsonData.task_id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"metadata_extraction\",\n  \"org_id\": \"{{org_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"params\": {\n    \"access_token\": \"{{salesforce_access_token}}\",\n    \"instance_url\": \"{{salesforce_instance_url}}\",\n    \"integration_id\": \"{{integration_id}}\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"{{execution_log_id}}\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}, "description": "Step 3: Create metadata extraction task"}}, {"name": "4. Create PMD Security Scan Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"PMD security scan task created\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.collectionVariables.set('pmd_task_id', jsonData.task_id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_type\": \"pmd_apex_security\",\n  \"org_id\": \"{{org_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"params\": {\n    \"access_token\": \"{{salesforce_access_token}}\",\n    \"instance_url\": \"{{salesforce_instance_url}}\",\n    \"integration_id\": \"{{integration_id}}\",\n    \"environment\": \"production\"\n  },\n  \"execution_log_id\": \"{{execution_log_id}}\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/tasks", "host": ["{{base_url}}"], "path": ["tasks"]}, "description": "Step 4: Create PMD security scan task (final step)"}}, {"name": "5. Monitor Sequential Tasks", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Sequential tasks retrieved\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"All tasks have same execution_log_id\", function () {", "    var jsonData = pm.response.json();", "    var executionLogId = pm.collectionVariables.get('execution_log_id');", "    ", "    jsonData.data.forEach(function(task) {", "        pm.expect(task.execution_log_id).to.equal(executionLogId);", "    });", "});", "", "pm.test(\"Tasks are in correct sequence\", function () {", "    var jsonData = pm.response.json();", "    var expectedSequence = ['sfdc_authenticate', 'health_check', 'metadata_extraction', 'pmd_apex_security'];", "    ", "    var taskTypes = jsonData.data.map(task => task.task_type).sort();", "    // Note: This assumes all tasks are created, actual sequence may vary", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tasks?org_id={{org_id}}&execution_log_id={{execution_log_id}}", "host": ["{{base_url}}"], "path": ["tasks"], "query": [{"key": "org_id", "value": "{{org_id}}"}, {"key": "execution_log_id", "value": "{{execution_log_id}}"}]}, "description": "Step 5: Monitor all tasks in the sequential processing workflow"}}], "description": "Complete workflow for sequential task processing with execution log coordination."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic variables if not already set", "if (!pm.collectionVariables.get('execution_log_id')) {", "    pm.collectionVariables.set('execution_log_id', pm.variables.replaceIn('{{$guid}}'));", "}", "", "// Add timestamp to requests", "pm.collectionVariables.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for all requests", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"Response has proper content type\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Log response for debugging", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');"]}}]}