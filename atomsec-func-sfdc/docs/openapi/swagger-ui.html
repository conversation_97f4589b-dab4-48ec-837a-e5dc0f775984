<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AtomSec SFDC Service API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.10.5/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.10.5/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *, *:before, *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
        }

        .swagger-ui .topbar {
            background-color: #1f4e79;
            border-bottom: 1px solid #d4d4d4;
        }

        .swagger-ui .topbar .download-url-wrapper {
            display: none;
        }

        .swagger-ui .info {
            margin: 50px 0;
        }

        .swagger-ui .info .title {
            color: #1f4e79;
        }

        .swagger-ui .scheme-container {
            background: #fff;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.15);
        }

        .custom-header {
            background: linear-gradient(135deg, #1f4e79 0%, #2d5aa0 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .environment-selector {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }

        .environment-selector h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .environment-selector select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .quick-links {
            background: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }

        .quick-links h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .quick-links ul {
            margin: 0;
            padding-left: 20px;
        }

        .quick-links li {
            margin: 5px 0;
        }

        .quick-links a {
            color: #1f4e79;
            text-decoration: none;
        }

        .quick-links a:hover {
            text-decoration: underline;
        }

        .architecture-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }

        .architecture-note h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }

        .architecture-note p {
            margin: 0;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="custom-header">
        <h1>AtomSec SFDC Service API</h1>
        <p>Comprehensive API Documentation for Salesforce Integration Service</p>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // Build a system
            const ui = SwaggerUIBundle({
                url: './sfdc-api-spec.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                tryItOutEnabled: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {
                    // Add custom content after Swagger UI loads
                    addCustomContent();
                },
                requestInterceptor: function(request) {
                    // Add custom headers or modify requests
                    request.headers['X-Requested-With'] = 'SwaggerUI';
                    return request;
                },
                responseInterceptor: function(response) {
                    // Log responses for debugging
                    console.log('API Response:', response);
                    return response;
                }
            });

            function addCustomContent() {
                // Add environment selector
                const infoSection = document.querySelector('.swagger-ui .info');
                if (infoSection) {
                    const environmentSelector = document.createElement('div');
                    environmentSelector.className = 'environment-selector';
                    environmentSelector.innerHTML = `
                        <h3>Environment Selection</h3>
                        <select id="environment-select" onchange="changeEnvironment(this.value)">
                            <option value="http://localhost:7071/api">Local Development</option>
                            <option value="https://atomsec-func-sfdc-dev.azurewebsites.net/api">Development</option>
                            <option value="https://atomsec-func-sfdc-staging.azurewebsites.net/api">Staging</option>
                            <option value="https://atomsec-func-sfdc.azurewebsites.net/api">Production</option>
                        </select>
                    `;
                    infoSection.appendChild(environmentSelector);

                    // Add quick links
                    const quickLinks = document.createElement('div');
                    quickLinks.className = 'quick-links';
                    quickLinks.innerHTML = `
                        <h3>Quick Links</h3>
                        <ul>
                            <li><a href="#/Health%20Check/getHealth">Health Check</a> - Check service status</li>
                            <li><a href="#/Task%20Management/getTasks">List Tasks</a> - View all tasks</li>
                            <li><a href="#/Task%20Management/createTask">Create Task</a> - Start new task</li>
                            <li><a href="#/Integration%20Management/getIntegrations">List Integrations</a> - View integrations</li>
                            <li><a href="#/Security%20Analysis/getSecurityHealthScore">Security Score</a> - Get health score</li>
                        </ul>
                    `;
                    infoSection.appendChild(quickLinks);

                    // Add architecture note
                    const architectureNote = document.createElement('div');
                    architectureNote.className = 'architecture-note';
                    architectureNote.innerHTML = `
                        <h4>🏗️ Architecture Note</h4>
                        <p>
                            This SFDC service processes tasks sequentially: authenticate → health check → metadata extraction → PMD scanning.
                            All tasks in a sequence share the same execution_log_id for proper tracking and coordination.
                        </p>
                    `;
                    infoSection.appendChild(architectureNote);
                }
            }

            window.changeEnvironment = function(baseUrl) {
                // Update the Swagger UI to use the selected environment
                ui.specActions.updateUrl(baseUrl);
                ui.specActions.download(baseUrl);
            };

            // Add authentication helper
            window.setAuthToken = function(token) {
                ui.preauthorizeApiKey('BearerAuth', 'Bearer ' + token);
            };

            // Add example data helper
            window.loadExampleData = function() {
                const examples = {
                    createTask: {
                        task_type: "metadata_extraction",
                        org_id: "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
                        user_id: "2123",
                        params: {
                            access_token: "00D5j000006P6ly!AQgAQM9ts3lBUQ31FN780NESscn0OQyfQfFuK_sV60pEIbUjqWKjgQfhuf0XGluiqDvnx1s27RG_kICYPkYCXoo5ioLFq7UQ",
                            instance_url: "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
                            integration_id: "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
                            environment: "production"
                        },
                        execution_log_id: "e5cc4b65-d0ce-4570-9425-9619af407db0"
                    }
                };
                
                console.log('Example data loaded:', examples);
                return examples;
            };
        };

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+/ or Cmd+/ to show shortcuts
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                showShortcuts();
            }
        });

        function showShortcuts() {
            alert(`
Keyboard Shortcuts:
- Ctrl+/ (Cmd+/): Show this help
- Ctrl+F (Cmd+F): Search in documentation
- Escape: Close modals
- Tab: Navigate between fields
            `);
        }

        // Add copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                console.log('Copied to clipboard:', text);
            });
        }

        // Add request/response logging
        function logRequest(method, url, data) {
            console.group(`🚀 API Request: ${method} ${url}`);
            console.log('Request Data:', data);
            console.groupEnd();
        }

        function logResponse(status, data) {
            console.group(`📥 API Response: ${status}`);
            console.log('Response Data:', data);
            console.groupEnd();
        }
    </script>
</body>
</html>