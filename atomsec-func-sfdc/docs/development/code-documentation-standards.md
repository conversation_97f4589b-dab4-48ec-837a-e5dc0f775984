# AtomSec SFDC Service Code Documentation Standards

This document defines the code documentation standards for the AtomSec SFDC Service to ensure consistency, maintainability, and ease of understanding across the codebase.

## 📋 Documentation Principles

### Core Principles
1. **Clarity**: Documentation should be clear and easy to understand
2. **Completeness**: All public APIs and complex logic should be documented
3. **Consistency**: Follow established patterns and conventions
4. **Currency**: Keep documentation up-to-date with code changes
5. **Conciseness**: Be thorough but avoid unnecessary verbosity

### Documentation Hierarchy
1. **Module/File Level**: Purpose, dependencies, and usage overview
2. **Class Level**: Responsibility, relationships, and usage patterns
3. **Function/Method Level**: Parameters, return values, and behavior
4. **Inline Comments**: Complex logic, business rules, and edge cases

## 🐍 Python Documentation Standards

### Module Documentation

Every Python module should start with a comprehensive docstring:

```python
"""
Enhanced Task Management Endpoints

This module provides enhanced task management functionality for the AtomSec SFDC Service,
implementing sequential task processing with security validation and execution log coordination.

Key Features:
- Sequential task processing (authenticate → health check → metadata extraction → PMD)
- Enhanced security validation for Salesforce credentials
- Execution log coordination across task sequences
- Comprehensive error handling and retry mechanisms

Architecture Notes:
- Follows blueprint pattern for modular API organization
- Integrates with enhanced security framework for request validation
- Uses execution_log_id for tracking sequential task processing
- Implements proper CORS and authentication middleware

Dependencies:
- shared.security_middleware: Security validation and authentication
- shared.enhanced_parameter_validator: Parameter validation framework
- shared.enhanced_auth_service: Salesforce authentication service
- shared.data_access: Database repository pattern
- shared.event_publisher: Service Bus event publishing

Usage Example:
    from api.enhanced_task_endpoints import bp as enhanced_task_bp
    app.register_blueprint(enhanced_task_bp)

Author: AtomSec Development Team
Created: January 2025
Last Modified: January 2025
Version: 1.0.0
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import statements with comments for complex dependencies
from shared.security_middleware import (
    secure_endpoint,  # Decorator for endpoint security validation
    validate_sequential_task_request  # Sequential task request validation
)
```

### Class Documentation

Classes should have comprehensive docstrings with examples:

```python
class EnhancedTaskProcessor:
    """
    Enhanced task processor for sequential SFDC task execution.
    
    This class manages the sequential processing of SFDC tasks, ensuring proper
    execution order, security validation, and error handling. Tasks are processed
    in the following sequence:
    1. sfdc_authenticate - Authenticate with Salesforce API
    2. health_check - Perform comprehensive health checks
    3. metadata_extraction - Extract Salesforce metadata
    4. pmd_apex_security - Run PMD scanning (final step)
    
    All tasks in a sequence share the same execution_log_id for proper tracking
    and coordination.
    
    Attributes:
        execution_log_id (str): Unique identifier for task sequence tracking
        org_id (str): Organization identifier for task context
        security_validator (SecurityValidator): Security validation service
        task_coordinator (TaskCoordinator): Task sequence coordination service
        
    Example:
        >>> processor = EnhancedTaskProcessor(
        ...     execution_log_id="e5cc4b65-d0ce-4570-9425-9619af407db0",
        ...     org_id="d432f9f2-c257-49c9-a5d0-1f11784d0f75"
        ... )
        >>> result = processor.process_task_sequence(task_params)
        >>> print(f"Sequence completed: {result.success}")
        
    Security Considerations:
        - All access tokens are validated before processing
        - Parameters undergo comprehensive security validation
        - Execution logs are tracked for audit purposes
        - Failed authentication attempts are logged and monitored
        
    Performance Notes:
        - Tasks are processed sequentially, not in parallel
        - Connection pooling is used for database operations
        - Caching is implemented for frequently accessed data
        - Retry logic with exponential backoff for transient failures
    """
    
    def __init__(self, execution_log_id: str, org_id: str) -> None:
        """
        Initialize the enhanced task processor.
        
        Args:
            execution_log_id: Unique identifier for tracking task sequence
            org_id: Organization identifier for task context
            
        Raises:
            ValueError: If execution_log_id or org_id is invalid
            SecurityValidationError: If security validation fails
            
        Example:
            >>> processor = EnhancedTaskProcessor(
            ...     execution_log_id="e5cc4b65-d0ce-4570-9425-9619af407db0",
            ...     org_id="d432f9f2-c257-49c9-a5d0-1f11784d0f75"
            ... )
        """
        self.execution_log_id = self._validate_execution_log_id(execution_log_id)
        self.org_id = self._validate_org_id(org_id)
        self.security_validator = SecurityValidator()
        self.task_coordinator = TaskCoordinator(execution_log_id)
        self.logger = logging.getLogger(__name__)
```

### Function Documentation

Functions should have detailed docstrings with type hints:

```python
def process_sequential_task(
    task_type: str,
    task_params: Dict[str, Any],
    execution_log_id: str,
    user_context: Optional[Dict[str, Any]] = None
) -> TaskProcessingResult:
    """
    Process a single task in the sequential task processing workflow.
    
    This function handles the processing of individual tasks within the sequential
    SFDC task workflow. It validates parameters, authenticates with Salesforce,
    executes the task, and updates the execution log with results.
    
    The function implements comprehensive error handling, retry logic, and
    security validation to ensure reliable task processing.
    
    Args:
        task_type: Type of task to process. Must be one of:
            - 'sfdc_authenticate': Authenticate with Salesforce API
            - 'health_check': Perform health checks
            - 'metadata_extraction': Extract Salesforce metadata
            - 'pmd_apex_security': Run PMD security scanning
        task_params: Dictionary containing task-specific parameters:
            - access_token (str): Secure Salesforce access token
            - instance_url (str): Salesforce instance URL
            - integration_id (str): Integration identifier
            - environment (str): Target environment ('production', 'sandbox', 'development')
        execution_log_id: Unique identifier for tracking task sequence
        user_context: Optional user context for authorization and auditing
        
    Returns:
        TaskProcessingResult: Result object containing:
            - success (bool): Whether task completed successfully
            - task_id (str): Unique task identifier
            - message (str): Status message or error description
            - result_data (Dict): Task-specific result data
            - processing_time (float): Task execution time in seconds
            - retry_count (int): Number of retry attempts made
            
    Raises:
        ParameterValidationError: If required parameters are missing or invalid
        SecurityValidationError: If security validation fails
        SalesforceAuthenticationError: If Salesforce authentication fails
        TaskProcessingError: If task processing fails after all retries
        
    Example:
        >>> task_params = {
        ...     'access_token': 'secure_token_here',
        ...     'instance_url': 'https://org.my.salesforce.com/services/data/v59.0/',
        ...     'integration_id': 'd432f9f2-c257-49c9-a5d0-1f11784d0f75',
        ...     'environment': 'production'
        ... }
        >>> result = process_sequential_task(
        ...     task_type='metadata_extraction',
        ...     task_params=task_params,
        ...     execution_log_id='e5cc4b65-d0ce-4570-9425-9619af407db0'
        ... )
        >>> if result.success:
        ...     print(f"Task completed in {result.processing_time:.2f}s")
        ... else:
        ...     print(f"Task failed: {result.message}")
        
    Security Notes:
        - Access tokens are validated and never logged
        - All parameters undergo security validation
        - Failed attempts are logged for security monitoring
        - Rate limiting is applied to prevent abuse
        
    Performance Notes:
        - Function implements connection pooling for database operations
        - Caching is used for frequently accessed configuration data
        - Retry logic with exponential backoff for transient failures
        - Timeout handling to prevent hanging operations
        
    Architecture Notes:
        - Function follows the repository pattern for data access
        - Uses dependency injection for testability
        - Implements proper separation of concerns
        - Follows SOLID principles for maintainability
    """
    # Validate input parameters
    if not task_type or task_type not in SUPPORTED_TASK_TYPES:
        raise ParameterValidationError(f"Invalid task type: {task_type}")
    
    if not execution_log_id or not is_valid_uuid(execution_log_id):
        raise ParameterValidationError(f"Invalid execution_log_id: {execution_log_id}")
    
    # Security validation
    security_result = validate_task_security(task_params, user_context)
    if not security_result.is_valid:
        raise SecurityValidationError(security_result.error_message)
    
    # Initialize task processing
    start_time = time.time()
    task_id = str(uuid.uuid4())
    retry_count = 0
    
    logger.info(
        f"Starting task processing",
        extra={
            'task_id': task_id,
            'task_type': task_type,
            'execution_log_id': execution_log_id,
            'user_id': user_context.get('user_id') if user_context else None
        }
    )
    
    try:
        # Task-specific processing logic
        result_data = _execute_task_by_type(task_type, task_params, task_id)
        
        processing_time = time.time() - start_time
        
        # Log successful completion
        logger.info(
            f"Task completed successfully",
            extra={
                'task_id': task_id,
                'task_type': task_type,
                'processing_time': processing_time,
                'execution_log_id': execution_log_id
            }
        )
        
        return TaskProcessingResult(
            success=True,
            task_id=task_id,
            message="Task completed successfully",
            result_data=result_data,
            processing_time=processing_time,
            retry_count=retry_count
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        
        # Log error with context
        logger.error(
            f"Task processing failed",
            extra={
                'task_id': task_id,
                'task_type': task_type,
                'error': str(e),
                'processing_time': processing_time,
                'execution_log_id': execution_log_id
            },
            exc_info=True
        )
        
        return TaskProcessingResult(
            success=False,
            task_id=task_id,
            message=f"Task failed: {str(e)}",
            result_data={},
            processing_time=processing_time,
            retry_count=retry_count
        )
```

### Inline Comments

Use inline comments for complex logic and business rules:

```python
def validate_salesforce_credentials(access_token: str, instance_url: str) -> bool:
    """Validate Salesforce credentials and check token expiration."""
    
    # Extract token payload without verification for expiration check
    # This is safe as we're only reading the expiration claim
    try:
        # Split JWT token into header, payload, signature
        header, payload, signature = access_token.split('.')
        
        # Decode payload (base64url decoding with padding)
        # Add padding if needed for proper base64 decoding
        payload_padded = payload + '=' * (4 - len(payload) % 4)
        decoded_payload = base64.urlsafe_b64decode(payload_padded)
        token_data = json.loads(decoded_payload)
        
        # Check token expiration (exp claim is in Unix timestamp format)
        current_time = int(time.time())
        token_expiry = token_data.get('exp', 0)
        
        if token_expiry <= current_time:
            logger.warning(
                "Salesforce token expired",
                extra={'token_expiry': token_expiry, 'current_time': current_time}
            )
            return False
            
    except (ValueError, json.JSONDecodeError, KeyError) as e:
        # Token format is invalid - this could indicate a security issue
        logger.error(f"Invalid token format detected: {str(e)}")
        return False
    
    # Validate token with Salesforce by making a test API call
    # Use the tokeninfo endpoint for efficient validation
    try:
        response = requests.get(
            f"{instance_url}/services/oauth2/tokeninfo",
            headers={'Authorization': f'Bearer {access_token}'},
            timeout=SALESFORCE_API_TIMEOUT  # Prevent hanging requests
        )
        
        # Salesforce returns 200 for valid tokens, 401 for invalid
        if response.status_code == 200:
            token_info = response.json()
            
            # Additional validation: check if token has required scopes
            required_scopes = ['api', 'refresh_token']
            token_scopes = token_info.get('scope', '').split()
            
            missing_scopes = set(required_scopes) - set(token_scopes)
            if missing_scopes:
                logger.warning(
                    f"Token missing required scopes: {missing_scopes}",
                    extra={'available_scopes': token_scopes}
                )
                return False
                
            return True
            
    except requests.RequestException as e:
        # Network or API errors - treat as validation failure
        logger.error(f"Salesforce token validation failed: {str(e)}")
        return False
    
    return False
```

## 📁 File Organization and Naming

### Directory Structure Documentation

```python
"""
AtomSec SFDC Service Directory Structure

atomsec-func-sfdc/
├── api/                          # API endpoint modules (blueprint pattern)
│   ├── __init__.py              # Blueprint registration and common utilities
│   ├── enhanced_task_endpoints.py  # Enhanced task management with security
│   ├── integration_endpoints.py    # Salesforce integration management
│   ├── security_endpoints.py      # Security analysis and health scoring
│   └── ...
├── shared/                       # Shared utilities and services
│   ├── __init__.py              # Common imports and utilities
│   ├── security_middleware.py   # Security validation and authentication
│   ├── enhanced_auth_service.py # Enhanced authentication services
│   ├── data_access.py           # Database repository pattern
│   └── ...
├── config/                       # Configuration files
│   ├── common.json              # Common configuration across environments
│   ├── local.json               # Local development configuration
│   ├── production.json          # Production environment configuration
│   └── ...
├── docs/                         # Documentation
│   ├── openapi/                 # API documentation
│   ├── operations/              # Operational procedures
│   ├── development/             # Development guides
│   └── architecture/            # Architecture documentation
├── tests/                        # Test suites
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   ├── performance/             # Performance tests
│   └── security/                # Security tests
├── infrastructure/               # Infrastructure as Code
│   ├── main.bicep               # Main infrastructure template
│   ├── modules/                 # Reusable infrastructure modules
│   └── parameters/              # Environment-specific parameters
└── scripts/                      # Utility scripts
    ├── deployment/              # Deployment automation
    ├── monitoring/              # Monitoring and alerting setup
    └── maintenance/             # Maintenance and cleanup scripts

Naming Conventions:
- Modules: snake_case (e.g., enhanced_task_endpoints.py)
- Classes: PascalCase (e.g., EnhancedTaskProcessor)
- Functions: snake_case (e.g., process_sequential_task)
- Constants: UPPER_SNAKE_CASE (e.g., SALESFORCE_API_TIMEOUT)
- Private members: _leading_underscore (e.g., _validate_token)
"""
```

### Import Organization

```python
"""
Import organization standards for AtomSec SFDC Service.

Imports should be organized in the following order:
1. Standard library imports
2. Third-party library imports
3. Azure Functions imports
4. Local application imports

Each group should be separated by a blank line and sorted alphabetically.
"""

# Standard library imports
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# Third-party library imports
import requests
from pydantic import BaseModel, ValidationError

# Azure Functions imports
import azure.functions as func
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

# Local application imports - organized by module type
# Shared utilities (most commonly used)
from shared.common import get_environment_config, is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Task, Organization

# Security and authentication
from shared.security_middleware import secure_endpoint, validate_request
from shared.enhanced_auth_service import authenticate_salesforce_api
from shared.enhanced_parameter_validator import validate_sfdc_request

# Business logic services
from shared.task_coordination_service import TaskCoordinator
from shared.event_publisher import publish_task_event

# Configuration and utilities
from shared.config import get_configuration_manager
from shared.monitoring import get_telemetry_service
```

## 🧪 Testing Documentation Standards

### Test Documentation

```python
"""
Test module for enhanced task processing functionality.

This module contains comprehensive tests for the enhanced task processing
system, covering security validation, sequential task execution, and
error handling scenarios.

Test Categories:
- Unit tests for individual functions and methods
- Integration tests for end-to-end workflows
- Security tests for authentication and authorization
- Performance tests for load and stress scenarios

Test Data:
- Uses mock data for external dependencies (Salesforce, database)
- Implements test fixtures for consistent test setup
- Uses factory patterns for test data generation

Coverage Requirements:
- Minimum 90% code coverage for all modules
- 100% coverage for critical security functions
- Edge case testing for error conditions
"""

import pytest
import unittest.mock as mock
from datetime import datetime, timedelta
from typing import Dict, Any

# Test utilities and fixtures
from tests.fixtures.task_fixtures import create_test_task, create_test_execution_log
from tests.mocks.salesforce_mock import MockSalesforceClient
from tests.utils.test_helpers import assert_task_sequence_valid

class TestEnhancedTaskProcessing:
    """
    Test suite for enhanced task processing functionality.
    
    This test class covers all aspects of enhanced task processing including:
    - Parameter validation and security checks
    - Sequential task execution and coordination
    - Error handling and retry mechanisms
    - Performance and scalability scenarios
    
    Test Fixtures:
        - mock_salesforce_client: Mocked Salesforce API client
        - test_task_params: Standard test parameters for task creation
        - test_execution_log: Mock execution log for task tracking
    """
    
    @pytest.fixture
    def mock_salesforce_client(self):
        """Mock Salesforce client for testing without external dependencies."""
        return MockSalesforceClient()
    
    @pytest.fixture
    def test_task_params(self) -> Dict[str, Any]:
        """Standard test parameters for task creation."""
        return {
            'access_token': 'test_token_12345',
            'instance_url': 'https://test-org.my.salesforce.com/services/data/v59.0/',
            'integration_id': 'd432f9f2-c257-49c9-a5d0-1f11784d0f75',
            'environment': 'sandbox'
        }
    
    def test_sequential_task_processing_success(self, mock_salesforce_client, test_task_params):
        """
        Test successful sequential task processing workflow.
        
        This test verifies that tasks are processed in the correct sequence:
        1. sfdc_authenticate
        2. health_check
        3. metadata_extraction
        4. pmd_apex_security
        
        Expected Behavior:
        - All tasks complete successfully
        - Tasks are processed in correct order
        - execution_log_id is consistent across all tasks
        - Proper result data is returned for each task
        """
        # Arrange
        execution_log_id = str(uuid.uuid4())
        org_id = 'd432f9f2-c257-49c9-a5d0-1f11784d0f75'
        
        with mock.patch('shared.enhanced_auth_service.SalesforceClient', return_value=mock_salesforce_client):
            processor = EnhancedTaskProcessor(execution_log_id, org_id)
            
            # Act
            results = processor.process_task_sequence(test_task_params)
            
            # Assert
            assert len(results) == 4, "Should process all 4 tasks in sequence"
            assert all(result.success for result in results), "All tasks should succeed"
            
            # Verify task order
            expected_sequence = ['sfdc_authenticate', 'health_check', 'metadata_extraction', 'pmd_apex_security']
            actual_sequence = [result.task_type for result in results]
            assert actual_sequence == expected_sequence, f"Tasks processed in wrong order: {actual_sequence}"
            
            # Verify execution log consistency
            assert all(result.execution_log_id == execution_log_id for result in results), \
                "All tasks should have same execution_log_id"
    
    def test_parameter_validation_failure(self, test_task_params):
        """
        Test parameter validation with invalid inputs.
        
        This test verifies that proper validation errors are raised for:
        - Missing required parameters
        - Invalid parameter formats
        - Security validation failures
        
        Expected Behavior:
        - ParameterValidationError raised for invalid parameters
        - Detailed error messages provided
        - No tasks are processed when validation fails
        """
        # Test missing access token
        invalid_params = test_task_params.copy()
        del invalid_params['access_token']
        
        with pytest.raises(ParameterValidationError) as exc_info:
            process_sequential_task('metadata_extraction', invalid_params, str(uuid.uuid4()))
        
        assert 'access_token' in str(exc_info.value), "Error should mention missing access_token"
        
        # Test invalid execution_log_id format
        with pytest.raises(ParameterValidationError) as exc_info:
            process_sequential_task('metadata_extraction', test_task_params, 'invalid-uuid')
        
        assert 'execution_log_id' in str(exc_info.value), "Error should mention invalid execution_log_id"
```

## 📚 Architecture Decision Records (ADRs)

### ADR Template

```markdown
# ADR-001: Sequential Task Processing Architecture

## Status
Accepted

## Context
The AtomSec SFDC Service needs to process multiple related tasks in a specific sequence for each Salesforce organization scan. These tasks include authentication, health checks, metadata extraction, and PMD security scanning. The system must ensure proper ordering, error handling, and tracking across the entire sequence.

## Decision
We will implement a sequential task processing architecture using execution_log_id for coordination and tracking.

### Key Components:
1. **Task Coordination Service**: Manages task sequence and dependencies
2. **Execution Log Tracking**: Uses execution_log_id to link related tasks
3. **Enhanced Security Validation**: Validates parameters and credentials
4. **Retry and Error Handling**: Implements robust error recovery

### Task Sequence:
1. `sfdc_authenticate` - Authenticate with Salesforce API
2. `health_check` - Perform comprehensive health checks
3. `metadata_extraction` - Extract Salesforce metadata
4. `pmd_apex_security` - Run PMD security scanning (final step)

## Consequences

### Positive:
- **Reliable Processing**: Ensures tasks are processed in correct order
- **Better Tracking**: execution_log_id provides end-to-end traceability
- **Error Recovery**: Failed tasks can be retried without affecting completed tasks
- **Security**: Enhanced validation prevents unauthorized access
- **Monitoring**: Clear visibility into task sequence progress

### Negative:
- **Complexity**: More complex than parallel processing
- **Performance**: Sequential processing may be slower than parallel
- **Dependencies**: Tasks are tightly coupled to sequence order

### Risks and Mitigations:
- **Risk**: Task sequence failure affects entire workflow
  - **Mitigation**: Implement partial completion handling and recovery
- **Risk**: Long-running sequences may timeout
  - **Mitigation**: Implement proper timeout handling and progress tracking

## Implementation Details

### Database Schema:
```sql
-- Tasks table with execution_log_id for sequence tracking
CREATE TABLE tasks (
    task_id UNIQUEIDENTIFIER PRIMARY KEY,
    execution_log_id UNIQUEIDENTIFIER NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    sequence_order INT NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at DATETIME2 NOT NULL,
    INDEX IX_tasks_execution_log_id (execution_log_id),
    INDEX IX_tasks_sequence (execution_log_id, sequence_order)
);
```

### API Design:
```python
# Task creation with execution_log_id
POST /api/tasks
{
    "task_type": "metadata_extraction",
    "org_id": "uuid",
    "execution_log_id": "uuid",
    "params": { ... }
}

# Task sequence monitoring
GET /api/tasks?execution_log_id=uuid&org_id=uuid
```

## Alternatives Considered

### 1. Parallel Task Processing
- **Pros**: Faster execution, simpler individual task logic
- **Cons**: Complex dependency management, harder to track failures
- **Rejected**: Dependencies between tasks require sequential processing

### 2. Workflow Engine (Azure Logic Apps)
- **Pros**: Built-in orchestration, visual workflow design
- **Cons**: Additional complexity, vendor lock-in, cost
- **Rejected**: Overkill for current requirements, adds external dependency

### 3. Message Queue Orchestration
- **Pros**: Decoupled processing, scalable
- **Cons**: Complex error handling, eventual consistency issues
- **Rejected**: Too complex for current scale and requirements

## References
- [Task Processing Requirements](../requirements/task-processing.md)
- [Security Validation Design](../design/security-validation.md)
- [Database Schema Design](../design/database-schema.md)

---
**Date**: January 24, 2025  
**Author**: AtomSec Development Team  
**Reviewers**: Engineering Team, Security Team  
**Status**: Implemented in v1.0.0
```

## 🚀 Developer Onboarding Documentation

Let me create the developer onboarding guide:

<function_calls>
<invoke name="fsWrite">
<parameter name="path">atomsec-func-sfdc/docs/development/developer-onboarding.md AtomSec SFDC Service Developer Onboarding Guide

Welcome to the AtomSec SFDC Service development team! This guide will help you get up to speed with our codebase, development practices, and contribution workflow.

## 🎯 Service Overview

### What is the SFDC Service?
The AtomSec SFDC Service is a specialized Azure Function App that provides Salesforce integration capabilities for the AtomSec security platform. It handles:

- **Sequential Task Processing**: Processes tasks in a specific order (authenticate → health check → metadata extraction → PMD scanning)
- **Security Analysis**: Performs security health scoring and risk analysis for Salesforce organizations
- **Integration Management**: Manages Salesforce organization connections and configurations
- **PMD Scanning**: Runs Programming Mistake Detector scans on Apex code

### Architecture Overview

```mermaid
graph TB
    subgraph "AtomSec Platform"
        A[Frontend] --> B[DB Service]
        B --> C[SFDC Service]
    end
    
    subgraph "SFDC Service Components"
        C --> D[API Endpoints]
        D --> E[Security Middleware]
        D --> F[Task Processing]
        D --> G[Integration Management]
    end
    
    subgraph "External Dependencies"
        C --> H[Salesforce API]
        C --> I[Azure SQL Database]
        C --> J[Service Bus]
        C --> K[Key Vault]
        C --> L[Application Insights]
    end
```

### Key Concepts

#### Sequential Task Processing
Tasks are processed in a specific sequence, all linked by `execution_log_id`:
1. **sfdc_authenticate** - Authenticate with Salesforce API
2. **health_check** - Perform comprehensive health checks
3. **metadata_extraction** - Extract Salesforce metadata
4. **pmd_apex_security** - Run PMD scanning (final step)

#### Security Framework
- Enhanced parameter validation for all requests
- Secure handling of Salesforce access tokens
- Comprehensive audit logging and monitoring
- Rate limiting and abuse prevention

## 🛠️ Development Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.9+**: Primary development language
- **Azure CLI**: For Azure resource management
- **Azure Functions Core Tools**: For local development
- **Git**: Version control
- **Visual Studio Code**: Recommended IDE with Azure Functions extension
- **Docker**: For containerized development (optional)

### Local Development Setup

#### 1. Clone the Repository
```bash
git clone https://github.com/atomsec/atomsec-func-sfdc.git
cd atomsec-func-sfdc
```

#### 2. Set Up Python Environment
```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

#### 3. Configure Local Settings
```bash
# Copy local settings template
cp local.settings.json.example local.settings.json

# Edit local.settings.json with your configuration
# See "Configuration" section below for details
```

#### 4. Set Up Local Dependencies

##### Azurite (Local Storage Emulator)
```bash
# Install Azurite globally
npm install -g azurite

# Start Azurite
azurite --silent --location c:\azurite --debug c:\azurite\debug.log
```

##### Local Database Setup
```bash
# Create local SQL Server database (using Docker)
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=YourStrong@Passw0rd" \
   -p 1433:1433 --name atomsec-sql-local \
   -d mcr.microsoft.com/mssql/server:2019-latest

# Run database migrations
python scripts/init_database.py --connection-string "Server=localhost,1433;Database=atomsec-local;User Id=sa;Password=YourStrong@Passw0rd;"
```

#### 5. Start Local Development Server
```bash
# Start the Function App locally
func start --python

# The service will be available at http://localhost:7071
```

### Configuration

#### local.settings.json Configuration
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "ENVIRONMENT": "local",
    "LOG_LEVEL": "DEBUG",
    
    // Database Configuration
    "DATABASE_CONNECTION_STRING": "Server=localhost,1433;Database=atomsec-local;User Id=sa;Password=YourStrong@Passw0rd;",
    
    // Service Bus Configuration (use Azurite for local development)
    "SERVICE_BUS_CONNECTION_STRING": "UseDevelopmentStorage=true",
    
    // Application Insights (optional for local development)
    "APPINSIGHTS_INSTRUMENTATIONKEY": "your-local-insights-key",
    
    // Salesforce Configuration (for testing)
    "SALESFORCE_CLIENT_ID": "your-salesforce-client-id",
    "SALESFORCE_CLIENT_SECRET": "your-salesforce-client-secret",
    
    // Feature Flags
    "ENABLE_DEBUG_LOGGING": "true",
    "ENABLE_PERFORMANCE_MONITORING": "true"
  }
}
```

## 📁 Codebase Structure

### Directory Organization
```
atomsec-func-sfdc/
├── api/                          # API endpoint modules (blueprint pattern)
│   ├── enhanced_task_endpoints.py  # Enhanced task management
│   ├── integration_endpoints.py    # Integration management
│   ├── security_endpoints.py      # Security analysis
│   └── ...
├── shared/                       # Shared utilities and services
│   ├── security_middleware.py   # Security validation
│   ├── enhanced_auth_service.py # Authentication services
│   ├── data_access.py           # Database access layer
│   └── ...
├── config/                       # Configuration files
├── docs/                         # Documentation
├── tests/                        # Test suites
├── infrastructure/               # Infrastructure as Code
└── scripts/                      # Utility scripts
```

### Key Modules

#### API Layer (`api/`)
- **Blueprint Pattern**: Each module is an Azure Functions blueprint
- **Endpoint Organization**: Grouped by functionality (tasks, integrations, security)
- **Middleware Integration**: Security and validation middleware applied consistently

#### Shared Services (`shared/`)
- **Security Middleware**: Request validation and authentication
- **Data Access**: Repository pattern for database operations
- **Configuration Management**: Environment-aware configuration loading
- **Monitoring**: Telemetry and logging services

#### Configuration (`config/`)
- **Environment-Specific**: Separate configs for local, dev, staging, prod
- **Feature Flags**: Dynamic feature toggling
- **Security Settings**: Authentication and authorization configuration

## 🧪 Testing Strategy

### Test Organization
```
tests/
├── unit/                         # Unit tests for individual components
│   ├── test_enhanced_auth_service.py
│   ├── test_security_middleware.py
│   └── ...
├── integration/                  # Integration tests for workflows
│   ├── test_sequential_task_processing.py
│   ├── test_deployment_integration.py
│   └── ...
├── performance/                  # Performance and load tests
│   ├── test_load_testing.py
│   └── ...
├── security/                     # Security-focused tests
│   ├── test_authentication_security.py
│   └── ...
└── fixtures/                     # Test data and utilities
```

### Running Tests

#### Unit Tests
```bash
# Run all unit tests
python -m pytest tests/unit/ -v

# Run specific test file
python -m pytest tests/unit/test_enhanced_auth_service.py -v

# Run with coverage
python -m pytest tests/unit/ --cov=shared --cov-report=html
```

#### Integration Tests
```bash
# Run integration tests (requires local services)
python -m pytest tests/integration/ -v

# Run specific integration test
python -m pytest tests/integration/test_sequential_task_processing.py -v
```

#### Performance Tests
```bash
# Run performance tests
python -m pytest tests/performance/ -v

# Run load tests with Locust
cd tests/performance
locust -f locustfile.py --host=http://localhost:7071
```

### Test Writing Guidelines

#### Unit Test Example
```python
import pytest
from unittest.mock import Mock, patch
from shared.enhanced_auth_service import authenticate_salesforce_api, SalesforceAuthenticationError

class TestEnhancedAuthService:
    """Test suite for enhanced authentication service."""
    
    @pytest.fixture
    def mock_salesforce_response(self):
        """Mock successful Salesforce authentication response."""
        return {
            'access_token': 'mock_token_12345',
            'instance_url': 'https://test.my.salesforce.com',
            'token_type': 'Bearer',
            'expires_in': 3600
        }
    
    def test_authenticate_salesforce_api_success(self, mock_salesforce_response):
        """Test successful Salesforce API authentication."""
        # Arrange
        client_id = 'test_client_id'
        client_secret = 'test_client_secret'
        
        with patch('requests.post') as mock_post:
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = mock_salesforce_response
            
            # Act
            result = authenticate_salesforce_api(client_id, client_secret)
            
            # Assert
            assert result.success is True
            assert result.access_token == 'mock_token_12345'
            assert result.instance_url == 'https://test.my.salesforce.com'
            
            # Verify API call was made correctly
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert 'grant_type=client_credentials' in call_args[1]['data']
    
    def test_authenticate_salesforce_api_failure(self):
        """Test Salesforce API authentication failure."""
        # Arrange
        client_id = 'invalid_client_id'
        client_secret = 'invalid_client_secret'
        
        with patch('requests.post') as mock_post:
            mock_post.return_value.status_code = 401
            mock_post.return_value.json.return_value = {
                'error': 'invalid_client',
                'error_description': 'Invalid client credentials'
            }
            
            # Act & Assert
            with pytest.raises(SalesforceAuthenticationError) as exc_info:
                authenticate_salesforce_api(client_id, client_secret)
            
            assert 'Invalid client credentials' in str(exc_info.value)
```

## 🔄 Development Workflow

### Git Workflow

We follow a modified GitFlow workflow:

#### Branch Structure
- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Feature development branches
- **hotfix/**: Critical production fixes
- **release/**: Release preparation branches

#### Feature Development Process
```bash
# 1. Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/enhanced-task-validation

# 2. Develop your feature
# ... make changes ...

# 3. Run tests and ensure quality
python -m pytest tests/
python -m flake8 .
python -m black .

# 4. Commit changes with descriptive messages
git add .
git commit -m "feat: add enhanced parameter validation for task endpoints

- Implement comprehensive parameter validation
- Add security checks for access tokens
- Include execution_log_id validation
- Add unit tests for validation logic

Closes #123"

# 5. Push and create pull request
git push origin feature/enhanced-task-validation
# Create PR through GitHub/Azure DevOps
```

### Code Review Process

#### Pull Request Requirements
- [ ] All tests pass
- [ ] Code coverage meets minimum threshold (90%)
- [ ] Security scan passes
- [ ] Documentation updated
- [ ] Breaking changes documented
- [ ] Performance impact assessed

#### Review Checklist
- **Functionality**: Does the code work as intended?
- **Security**: Are there any security vulnerabilities?
- **Performance**: Will this impact system performance?
- **Maintainability**: Is the code readable and well-structured?
- **Testing**: Are there adequate tests?
- **Documentation**: Is documentation updated?

### Coding Standards

#### Python Style Guide
We follow PEP 8 with some modifications:

```python
# Line length: 100 characters (not 79)
# Use Black for automatic formatting
# Use type hints for all function parameters and return values

def process_task(
    task_type: str,
    params: Dict[str, Any],
    execution_log_id: str
) -> TaskProcessingResult:
    """Process a task with proper type hints and documentation."""
    pass
```

#### Naming Conventions
- **Variables/Functions**: `snake_case`
- **Classes**: `PascalCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private members**: `_leading_underscore`
- **Modules**: `snake_case`

#### Documentation Requirements
- All public functions must have docstrings
- Complex logic requires inline comments
- API endpoints need comprehensive documentation
- Architecture decisions documented in ADRs

## 🚀 Deployment Process

### Development Deployment
```bash
# Deploy to development environment
az functionapp deployment source config-zip \
  --resource-group rg-atomsec-sfdc-dev \
  --name atomsec-func-sfdc-dev \
  --src deployment.zip
```

### CI/CD Pipeline
Our deployment pipeline includes:

1. **Build Stage**
   - Code compilation and packaging
   - Dependency installation
   - Static analysis and security scanning

2. **Test Stage**
   - Unit test execution
   - Integration test execution
   - Performance test execution
   - Security test execution

3. **Deploy Stage**
   - Infrastructure deployment (Bicep)
   - Application deployment
   - Configuration deployment
   - Health check validation

### Environment Promotion
- **Development**: Automatic deployment on merge to develop
- **Staging**: Manual promotion from development
- **Production**: Manual promotion with approval gates

## 🔍 Debugging and Troubleshooting

### Local Debugging

#### VS Code Configuration
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Attach to Python Functions",
            "type": "python",
            "request": "attach",
            "port": 9091,
            "preLaunchTask": "func: host start"
        }
    ]
}
```

#### Common Issues and Solutions

##### Issue: Function App won't start locally
```bash
# Check Python version
python --version  # Should be 3.9+

# Check Azure Functions Core Tools
func --version  # Should be 4.x

# Clear local cache
rm -rf .azure-functions-core-tools/
func start --python
```

##### Issue: Database connection fails
```bash
# Test database connectivity
python -c "import pyodbc; print(pyodbc.connect('your-connection-string'))"

# Check firewall rules
az sql server firewall-rule list --server your-server --resource-group your-rg
```

##### Issue: Salesforce authentication fails
```bash
# Test Salesforce credentials
curl -X POST https://login.salesforce.com/services/oauth2/token \
  -d "grant_type=client_credentials" \
  -d "client_id=your-client-id" \
  -d "client_secret=your-client-secret"
```

### Production Debugging

#### Application Insights Queries
```kusto
// Find errors in the last hour
exceptions
| where timestamp > ago(1h)
| summarize count() by type, outerMessage
| order by count_ desc

// Trace specific execution log
traces
| where customDimensions.execution_log_id == "your-execution-log-id"
| order by timestamp asc
```

#### Health Check Endpoints
```bash
# Basic health check
curl https://atomsec-func-sfdc-prod.azurewebsites.net/api/health

# Detailed health check
curl https://atomsec-func-sfdc-prod.azurewebsites.net/api/health/detailed
```

## 📚 Learning Resources

### Internal Documentation
- [API Documentation](../openapi/README.md)
- [Architecture Overview](../architecture/system-architecture.md)
- [Deployment Guide](../operations/deployment-runbook.md)
- [Troubleshooting Guide](../operations/troubleshooting-guide.md)

### External Resources
- [Azure Functions Python Developer Guide](https://docs.microsoft.com/en-us/azure/azure-functions/functions-reference-python)
- [Salesforce REST API Documentation](https://developer.salesforce.com/docs/atlas.en-us.api_rest.meta/api_rest/)
- [Python Testing with pytest](https://docs.pytest.org/en/stable/)
- [Azure DevOps Documentation](https://docs.microsoft.com/en-us/azure/devops/)

### Training Recommendations
1. **Azure Functions Fundamentals** (Microsoft Learn)
2. **Python Best Practices** (Real Python)
3. **Salesforce API Integration** (Trailhead)
4. **Security Best Practices** (OWASP)
5. **Testing Strategies** (Test-Driven Development)

## 🤝 Getting Help

### Team Contacts
- **Tech Lead**: <EMAIL>
- **DevOps Engineer**: <EMAIL>
- **Security Team**: <EMAIL>

### Communication Channels
- **Slack**: #atomsec-sfdc-dev
- **Teams**: AtomSec Development Team
- **Email**: <EMAIL>

### Support Process
1. **Check Documentation**: Start with this guide and related docs
2. **Search Issues**: Check GitHub/Azure DevOps for similar issues
3. **Ask Team**: Post in Slack or Teams channel
4. **Create Issue**: If it's a bug or feature request
5. **Escalate**: Contact tech lead for urgent issues

## ✅ Onboarding Checklist

### Week 1: Environment Setup
- [ ] Development environment configured
- [ ] Local services running (database, Azurite)
- [ ] First successful local deployment
- [ ] Basic tests passing
- [ ] Access to all required systems (Azure, GitHub, etc.)

### Week 2: Code Familiarization
- [ ] Reviewed codebase structure
- [ ] Understood sequential task processing flow
- [ ] Completed first code review
- [ ] Fixed first bug or implemented small feature
- [ ] Familiar with testing framework

### Week 3: Feature Development
- [ ] Implemented first significant feature
- [ ] Written comprehensive tests
- [ ] Deployed to development environment
- [ ] Participated in architecture discussions
- [ ] Contributed to documentation

### Month 1: Full Integration
- [ ] Comfortable with entire development workflow
- [ ] Can debug production issues
- [ ] Mentoring newer team members
- [ ] Contributing to architectural decisions
- [ ] Leading feature development

---

Welcome to the team! We're excited to have you contribute to the AtomSec SFDC Service. If you have any questions or need help getting started, don't hesitate to reach out to the team.

**Document Version**: 1.0.0  
**Last Updated**: January 24, 2025  
**Next Review**: February 24, 2025