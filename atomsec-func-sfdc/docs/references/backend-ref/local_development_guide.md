# Local Development Guide

This guide provides detailed instructions for setting up and running the AtomSec Azure Functions application in a local development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **[Python 3.9+](https://www.python.org/downloads/)**: Required to run the Azure Functions
- **[Node.js](https://nodejs.org/)**: Required for Azurite (Azure Storage Emulator)
- **[Azure Functions Core Tools](https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local)**: Command-line tools for local development
- **[Azure CLI](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli)**: Required for authentication with Azure services
- **[Visual Studio Code](https://code.visualstudio.com/)** (recommended): With the Azure Functions extension

## Initial Setup

### 1. Clone the Repository

```bash
git clone https://<EMAIL>/AtomSec/atomsec_app/_git/atomsec-func-sfdc
cd atomsec-func-sfdc
```

### 2. Create and Activate a Python Virtual Environment

#### Windows:
```bash
python -m venv .venv
.venv\Scripts\activate
```

#### macOS/Linux:
```bash
python -m venv .venv
source .venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Install Azurite

Azurite is a lightweight server that emulates Azure Blob, Queue, and Table storage for local development.

```bash
npm install -g azurite
```

### 5. Run the Setup Script

The repository includes a setup script that configures your local environment:

```bash
python scripts/setup_local_dev.py
```

This script:
- Checks for prerequisites (Node.js, npm, Azure Functions Core Tools)
- Installs Azurite if needed
- Creates/updates the `local.settings.json` file with development settings
- Sets up VS Code tasks for running Azurite and the Functions host
- Creates a `config.py.template` file for Salesforce credentials
- Starts Azurite in the background

If the script encounters any issues, it will provide detailed error messages and instructions for resolving them. Common issues include:

- Missing prerequisites (Node.js, npm, Azure Functions Core Tools)
- Permission issues when installing Azurite globally
- Ports already in use (10100, 10101, 10102)

## Configuration Files

### local.settings.json

This file contains configuration settings for your local development environment. The setup script creates it with these default values:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "AzureWebJobsFeatureFlags": "EnableWorkerIndexing",
    "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
    "USE_LOCAL_STORAGE": "true"
  }
}
```

> **Important**: Do not add `AZURE_FUNCTIONS_ENVIRONMENT` to your local.settings.json file as it can cause conflicts with the Azure Functions Core Tools.

> **Note**: This file is excluded from source control in `.gitignore` because it may contain sensitive information.

### Salesforce Credentials for Local Development

For functions that interact with Salesforce, you'll need to create a `config.py` file in the root directory with your Salesforce credentials:

```python
# config.py - Add this file to .gitignore
client_id = "your_salesforce_client_id"
client_secret = "your_salesforce_client_secret"
username = "your_salesforce_username"
password = "your_salesforce_password"
auth_url = "https://login.salesforce.com/services/oauth2/token"
```

> **Important**: Never commit this file to source control. It's already included in `.gitignore`.

## Running the Application Locally

### Option 1: Using Visual Studio Code

1. Open the project in VS Code
2. Press `F5` or use the Run menu
3. Alternatively, use the "Start Function App with Azurite" task from the Command Palette (Ctrl+Shift+P)

### Option 2: Using Command Line

1. Start Azurite in one terminal:

```bash
azurite --location .azurite --silent --blobPort 10100 --queuePort 10101 --tablePort 10102
```

2. Start the Functions host in another terminal:

```bash
func start
```

## Testing the Functions

Once the Functions are running locally, you can test them using:

### Browser Testing

Open your browser to `http://localhost:7071/api/home` to see all available endpoints:

- `/api/api/health-score`: View Salesforce security health score data
- `/api/api/health-risks`: View Salesforce security health risks data
- `/api/api/integration/{tenant_url}/profiles`: View Salesforce profiles for a specific integration
- `/api/api/integration/{tenant_url}/permission-sets`: View Salesforce permission sets for a specific integration
- `/api/health`: Health check endpoint
- `/api/info`: System information endpoint

### API Testing Tools

Use tools like [Postman](https://www.postman.com/) or [curl](https://curl.se/) to make HTTP requests to the endpoints.

Example curl command:
```bash
curl http://localhost:7071/api/health
```

### Automated Tests

Run the automated tests with pytest:

```bash
# Run all tests
pytest tests/

# Run specific test files
pytest tests/test_general.py

# Run with verbose output
pytest tests/test_my_work.py -v
```

## Local Storage

When running locally, the application uses Azurite to emulate Azure Storage services:

- **Blob Storage**: Used for storing JSON data (port 10100)
- **Queue Storage**: Used for message queuing (port 10101)
- **Table Storage**: Used for structured data storage (port 10102)

The data is stored in the `.azurite` directory in your project folder.

## Debugging

### Logging

The application uses Python's logging module. When running locally, logs are displayed in the terminal where you started the Functions host.

You can adjust the log level in `host.json`:

```json
"logging": {
  "logLevel": {
    "default": "Information",
    "Worker.Rpc": "Warning",
    "Function": "Information"
  }
}
```

### VS Code Debugging

When running in VS Code, you can:
- Set breakpoints in your code
- Inspect variables
- Step through code execution
- View the call stack

### Common Issues and Solutions

#### Authentication Issues

If you encounter authentication issues with Azure services:

1. Run `az login` to authenticate with Azure CLI
2. Check if your account has access to the required Azure resources
3. For local development, the application uses `AzureCliCredential` which relies on your Azure CLI login

#### Azurite Connection Issues

If the Functions can't connect to Azurite:

1. Check if Azurite is running (`ps aux | grep azurite` or Task Manager)
2. Verify the ports (10100, 10101, 10102) are not in use by other applications
3. Check the Azurite logs in the terminal where you started it

#### Import Errors

If you encounter import errors:

1. Make sure you're running from the project root directory
2. Verify that your virtual environment is activated
3. Check that all dependencies are installed (`pip install -r requirements.txt`)

## Working with Blueprints

The application uses Azure Functions Blueprints to organize functions into logical groups:

- `blueprints/security_analysis.py`: Functions for security health score and risks analysis
- `blueprints/integration_tabs.py`: Functions for integration-specific data like profiles and permission sets
- `blueprints/general.py`: General utility functions

When adding a new function, add it to the appropriate blueprint or create a new one if needed.

Example of adding a new function to an existing blueprint:

```python
@bp.route(route="my_new_function")
def my_new_function(req: func.HttpRequest) -> func.HttpResponse:
    """
    My new function

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    # Function implementation
    return func.HttpResponse("Hello from my new function!")
```

## Environment Detection

The application automatically detects if it's running locally or in Azure:

```python
from shared.azure_services import is_local_dev

if is_local_dev():
    # Local development behavior
else:
    # Production behavior
```

This allows you to use different configurations and services based on the environment.

## Conclusion

This local development guide should help you get started with the AtomSec Azure Functions application. If you encounter any issues not covered here, please refer to the [Azure Functions documentation](https://learn.microsoft.com/en-us/azure/azure-functions/) or reach out to the development team.

## Additional Resources

- [Azure Functions Python Developer Guide](https://learn.microsoft.com/en-us/azure/azure-functions/functions-reference-python)
- [Azurite Documentation](https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite)
- [Azure Functions Core Tools Reference](https://learn.microsoft.com/en-us/azure/azure-functions/functions-core-tools-reference)
