# Salesforce Client Credentials Flow Setup Guide

This guide explains how to set up a Salesforce Connected App to use the OAuth 2.0 Client Credentials flow for server-to-server integration.

## Overview

The OAuth 2.0 Client Credentials flow is designed for server-to-server integrations where a client application needs to access Salesforce APIs without user interaction. This flow is ideal for background processes, batch jobs, and API integrations.

## Prerequisites

1. Salesforce account with administrative access
2. Permission to create and configure Connected Apps

## Step 1: Create a Connected App in Salesforce

1. Log in to Salesforce as an administrator
2. Navigate to **Setup** > **App Manager** > **New Connected App**
3. Fill in the required fields:
   - **Connected App Name**: AtomSec Integration
   - **API Name**: AtomSec_Integration
   - **Contact Email**: your email address
4. Enable OAuth Settings:
   - Check **Enable OAuth Settings**
   - **Callback URL**: https://your-app-url/callback (can be a placeholder)
   - Selected OAuth Scopes:
     - Access and manage your data (api)
     - Perform requests on your behalf at any time (refresh_token, offline_access)
5. **Important**: Check **Enable Client Credentials Flow**
6. Click **Save**

## Step 2: Configure Connected App Policies

After saving the Connected App, you'll be redirected to the Connected App detail page. Click **Edit Policies** to configure the following:

1. **OAuth Policies**:
   - **Permitted Users**: Admin approved users are pre-authorized
   - **IP Relaxation**: Relax IP restrictions
2. **Profile Access**: Add the profiles that should have access to this Connected App
3. Click **Save**

## Step 3: Get Client Credentials

1. Navigate back to the Connected App detail page
2. Note the **Consumer Key** (Client ID) and **Consumer Secret** (Client Secret)
3. These credentials will be used to authenticate with Salesforce

## Step 4: Test the Connection

Use the following curl command to test the connection:

```bash
curl -X POST https://login.salesforce.com/services/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET"
```

For a sandbox environment, use `https://test.salesforce.com` instead.

## Step 5: Configure in AtomSec

1. In the AtomSec application, navigate to the Integration setup
2. Select Salesforce as the integration type
3. Enter the following details:
   - **Org Name**: Your Salesforce organization name
   - **Tenant URL**: Your Salesforce instance URL (e.g., https://mycompany.my.salesforce.com)
   - **Client ID**: The Consumer Key from your Connected App
   - **Client Secret**: The Consumer Secret from your Connected App
   - **Environment**: Production or Sandbox
4. Click **Test Connection** to verify the credentials
5. If successful, click **Save** to store the integration

## Troubleshooting

### Common Errors

1. **unsupported_grant_type**: Make sure you've checked "Enable Client Credentials Flow" in your Connected App settings.
2. **invalid_client**: The Client ID or Client Secret is incorrect.
3. **invalid_client_id**: The Client ID is invalid or the Connected App has been deleted.
4. **invalid_grant**: The grant type is not supported for this client.

### Additional Tips

1. Make sure the Connected App is properly configured with the correct OAuth scopes
2. Verify that the user who created the Connected App has the necessary permissions
3. Check that the Connected App is active and not deleted
4. For sandbox environments, make sure you're using the correct login URL (test.salesforce.com)

## References

- [Salesforce OAuth 2.0 Client Credentials Flow Documentation](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_client_credentials_flow.htm)
- [Configure a Connected App for the OAuth 2.0 Client Credentials Flow](https://help.salesforce.com/s/articleView?id=sf.connected_app_client_credentials_setup.htm)
- [Salesforce Connected App Documentation](https://help.salesforce.com/s/articleView?id=sf.connected_app_overview.htm)
