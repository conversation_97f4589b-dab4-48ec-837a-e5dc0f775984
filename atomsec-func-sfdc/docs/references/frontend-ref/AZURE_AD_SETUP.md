# Azure AD Application Configuration

This application is configured to work with Azure AD authentication using the Web application type (not SPA).

## Steps to Configure Your Azure AD Application

1. **Sign in to the Azure Portal**:
   - Go to [https://portal.azure.com](https://portal.azure.com)
   - Sign in with your administrator account

2. **Navigate to Azure Active Directory**:
   - In the left sidebar, click on "Azure Active Directory"

3. **Go to App Registrations**:
   - In the Azure AD blade, click on "App registrations"
   - Find and select your application (ID: 2d313c1a-d62d-492c-869e-cf8cb9258204)

4. **Configure Authentication Settings**:
   - In the application's overview page, click on "Authentication" in the left menu
   - Under "Platform configurations", click "Add a platform" if Web is not already configured
   - Select "Web" from the options
   - Make sure the Redirect URI is set to "http://localhost:7071/api/auth/azure/callback"
   - Under "Implicit grant and hybrid flows", do NOT check any boxes
   - Save your changes

5. **Configure API Permissions**:
   - In the left menu, click on "API permissions"
   - Make sure you have the following permissions:
     - Microsoft Graph: User.Read (delegated)
   - Click "Grant admin consent" if needed

6. **Update Token Configuration**:
   - In the left menu, click on "Token configuration"
   - Add the claims you need (typically "email", "profile")

7. **Update Manifest (if needed)**:
   - In the left menu, click on "Manifest"
   - Ensure the "signInAudience" is set to "AzureADMyOrg" if you only want users from your organization to sign in
   - Save your changes

## Client Secret

1. **Create a Client Secret**:
   - In the left menu, click on "Certificates & secrets"
   - Under "Client secrets", click "New client secret"
   - Add a description and select an expiration period
   - Click "Add"
   - **IMPORTANT**: Copy the secret value immediately and store it securely. You won't be able to see it again.

2. **Configure the Client Secret in Your Backend**:
   - Add the client secret to your backend's environment variables or Key Vault
   - For local development, set the `AZURE_AD_CLIENT_SECRET` environment variable

## Important Note

This application is configured to use the Authorization Code flow for authentication, which is the recommended approach for Web applications with a backend. The backend handles the token exchange using the client secret.

The Web application configuration in Azure AD is designed for applications that can securely store a client secret, which is not possible in pure frontend applications.

After making these changes, restart both your frontend and backend applications and try the authentication flow again.
