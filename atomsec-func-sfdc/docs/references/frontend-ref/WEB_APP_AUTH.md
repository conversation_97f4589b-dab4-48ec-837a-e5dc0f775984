# Web App Authentication with Azure AD

This document provides instructions for setting up authentication between your React frontend and your Azure Function backend using Azure AD Web app type registration.

## Overview

The authentication flow works as follows:

1. User clicks "Sign in with Microsoft" in the React app
2. The React app redirects to the backend's `/api/auth/azure/login` endpoint
3. The backend redirects to Azure AD login page
4. User logs in with their Microsoft credentials
5. Azure AD redirects back to the backend's callback endpoint with an authorization code
6. The backend exchanges the code for tokens using the client secret
7. The backend creates application-specific tokens and redirects back to the frontend
8. The frontend stores these tokens and uses them for subsequent API calls

## Frontend Setup

The frontend is already set up to:

1. Redirect users to the backend's login endpoint
2. Handle the tokens received after successful authentication
3. Include the token in API requests to the backend

## Backend Setup

The backend is already set up to:

1. Redirect users to Azure AD for authentication
2. Exchange the authorization code for tokens using the client secret
3. Create application-specific tokens for the user
4. Validate these tokens for protected API calls

## Configuration

### Azure AD Configuration

1. Register a Web application (not SPA) in Azure AD
2. Set the redirect URI to your backend callback URL (e.g., `http://localhost:7071/api/auth/azure/callback`)
3. Create a client secret and note its value
4. Grant the necessary permissions (e.g., Microsoft Graph User.Read)

### Backend Configuration

Set the following environment variables in your Azure Function app:

```
AZURE_AD_CLIENT_ID=your-client-id
AZURE_AD_CLIENT_SECRET=your-client-secret
AZURE_AD_TENANT_ID=your-tenant-id
AZURE_AD_REDIRECT_URI=http://localhost:7071/api/auth/azure/callback
```

### Frontend Configuration

Set the following environment variables in your React app:

```
REACT_APP_AZURE_CLIENT_ID=your-client-id
REACT_APP_AZURE_TENANT_ID=your-tenant-id
REACT_APP_REDIRECT_URI=http://localhost:3000
```

## Testing the Authentication

1. Start your Azure Function backend
2. Start your React frontend
3. Click "Sign in with Microsoft" in the frontend
4. Complete the authentication flow
5. You should be redirected back to the frontend with valid tokens

## Troubleshooting

### Authentication Issues

If you're having issues with authentication:

1. Check that the client ID and tenant ID match between your Azure AD registration and your application configuration
2. Verify that the client secret is correct and not expired
3. Ensure the redirect URI is correctly configured in Azure AD
4. Check the backend logs for any token exchange errors

### CORS Issues

If you're seeing CORS errors:

1. Verify that your backend's CORS configuration includes your frontend's origin
2. Check that the allowed headers include 'Authorization'
3. Ensure the frontend is sending the correct headers with API requests

## Resources

- [Microsoft identity platform and OAuth 2.0 authorization code flow](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow)
- [Register an application with the Microsoft identity platform](https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-register-app)
