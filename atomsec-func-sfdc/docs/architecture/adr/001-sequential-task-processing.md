# ADR-001: Sequential Task Processing Architecture

## Status
Accepted

## Date
2025-01-24

## Context

The AtomSec SFDC Service needs to process multiple types of tasks for Salesforce organizations, including authentication, health checks, metadata extraction, and PMD scanning. These tasks have dependencies and must be executed in a specific order to ensure data consistency and proper error handling.

### Problem Statement
- Tasks have interdependencies (e.g., authentication must succeed before metadata extraction)
- Need to track task execution across the entire workflow
- Must handle partial failures gracefully
- Require visibility into the complete task sequence
- Need to coordinate between different task types

### Requirements
1. **Ordered Execution**: Tasks must execute in a predefined sequence
2. **Failure Handling**: System must handle failures at any step
3. **Progress Tracking**: Ability to track progress across the entire sequence
4. **Coordination**: Link related tasks for monitoring and debugging
5. **Scalability**: Support multiple concurrent task sequences

## Decision

We will implement a **Sequential Task Processing Architecture** with the following components:

### 1. Task Sequence Definition
```
1. sfdc_authenticate - Authenticate with Salesforce API
2. health_check - Perform comprehensive health checks
3. metadata_extraction - Extract Salesforce metadata
4. pmd_apex_security - Run PMD scanning (final step)
```

### 2. Execution Log Coordination
- All tasks in a sequence share the same `execution_log_id`
- The `execution_log_id` serves as a correlation identifier
- Tasks are linked through this identifier for tracking and monitoring

### 3. Task Processing Flow
```mermaid
graph LR
    A[Request] --> B[Generate execution_log_id]
    B --> C[Create sfdc_authenticate task]
    C --> D[Process authentication]
    D --> E[Create health_check task]
    E --> F[Process health check]
    F --> G[Create metadata_extraction task]
    G --> H[Process metadata extraction]
    H --> I[Create pmd_apex_security task]
    I --> J[Process PMD scanning]
    J --> K[Complete sequence]
```

### 4. Implementation Components

#### Task Coordination Service
```python
class TaskSequenceCoordinator:
    """Coordinates sequential task execution."""
    
    def create_task_sequence(self, org_id: str, user_id: str, params: Dict) -> str:
        """Create a new task sequence with execution_log_id."""
        
    def get_next_task(self, execution_log_id: str) -> Optional[TaskType]:
        """Determine the next task in the sequence."""
        
    def handle_task_failure(self, execution_log_id: str, failed_task: str) -> None:
        """Handle failure in task sequence."""
```

#### Execution Log Service
```python
class ExecutionLogService:
    """Manages execution log coordination."""
    
    def create_execution_log(self, org_id: str, user_id: str) -> str:
        """Create new execution log and return ID."""
        
    def update_execution_status(self, execution_log_id: str, status: str) -> None:
        """Update overall execution status."""
        
    def get_execution_progress(self, execution_log_id: str) -> ExecutionProgress:
        """Get progress of entire task sequence."""
```

### 5. Task State Management
- Each task maintains its own state (pending, running, completed, failed)
- Sequence state is derived from individual task states
- Failed tasks can be retried without affecting completed tasks
- Partial completion is supported (e.g., if PMD fails but metadata extraction succeeds)

## Consequences

### Positive Consequences
1. **Clear Dependencies**: Task dependencies are explicit and enforced
2. **Better Monitoring**: Complete visibility into task sequence execution
3. **Failure Isolation**: Failures in one task don't affect others unnecessarily
4. **Debugging**: Easy to trace issues across the entire workflow
5. **Scalability**: Multiple sequences can run concurrently
6. **Flexibility**: Individual tasks can be retried or skipped if needed

### Negative Consequences
1. **Complexity**: More complex than independent task processing
2. **Coordination Overhead**: Additional coordination logic required
3. **Potential Bottlenecks**: Sequential nature may limit parallelization
4. **State Management**: More complex state tracking requirements
5. **Testing Complexity**: More complex integration testing scenarios

### Risks and Mitigations

#### Risk: Sequence Coordination Failures
- **Mitigation**: Implement robust error handling and recovery mechanisms
- **Monitoring**: Alert on coordination service failures

#### Risk: Long-Running Sequences
- **Mitigation**: Implement timeouts and progress monitoring
- **Monitoring**: Track sequence duration and alert on anomalies

#### Risk: Partial Failure Handling
- **Mitigation**: Clear failure handling policies and retry mechanisms
- **Documentation**: Document expected behavior for each failure scenario

## Alternatives Considered

### Alternative 1: Independent Task Processing
**Description**: Process each task independently without coordination.

**Pros**:
- Simpler implementation
- Better parallelization
- No coordination overhead

**Cons**:
- No dependency enforcement
- Difficult to track related tasks
- Complex failure handling
- Poor visibility into workflows

**Decision**: Rejected due to dependency requirements and monitoring needs.

### Alternative 2: Workflow Engine (Azure Logic Apps)
**Description**: Use Azure Logic Apps or similar workflow engine.

**Pros**:
- Built-in workflow management
- Visual workflow design
- Robust error handling
- Monitoring and alerting

**Cons**:
- Additional service dependency
- Less control over execution
- Potential vendor lock-in
- Learning curve for team

**Decision**: Rejected to maintain control and reduce dependencies.

### Alternative 3: Event-Driven Architecture
**Description**: Use events to trigger subsequent tasks.

**Pros**:
- Loose coupling
- Good scalability
- Event-driven benefits

**Cons**:
- Complex event ordering
- Difficult to track sequences
- Potential race conditions
- More complex debugging

**Decision**: Rejected due to complexity and tracking requirements.

## Implementation Plan

### Phase 1: Core Infrastructure
1. Implement ExecutionLogService
2. Create TaskSequenceCoordinator
3. Update task creation to use execution_log_id
4. Basic sequence tracking

### Phase 2: Enhanced Coordination
1. Implement failure handling
2. Add retry mechanisms
3. Progress tracking and monitoring
4. Comprehensive testing

### Phase 3: Optimization
1. Performance optimization
2. Advanced monitoring and alerting
3. Operational tooling
4. Documentation and training

## Monitoring and Metrics

### Key Metrics
- **Sequence Completion Rate**: Percentage of sequences that complete successfully
- **Task Success Rate**: Success rate for each task type
- **Sequence Duration**: Time to complete entire sequence
- **Failure Points**: Where in the sequence failures occur most often

### Monitoring Queries
```kusto
// Sequence completion rate
customEvents
| where name == "SequenceCompleted" or name == "SequenceFailed"
| summarize 
    completed = countif(name == "SequenceCompleted"),
    failed = countif(name == "SequenceFailed"),
    completion_rate = (countif(name == "SequenceCompleted") * 100.0) / count()
by bin(timestamp, 1h)

// Task failure analysis
customEvents
| where name == "TaskFailed"
| extend task_type = tostring(customDimensions.task_type)
| summarize count() by task_type
| order by count_ desc
```

## References

- [Task Management API Documentation](../../openapi/examples/task-management.md)
- [Execution Log Coordination Service](../../../shared/execution_log_coordination_service.py)
- [Task Sequence Configuration](../../../shared/task_sequence_configuration.py)
- [Sequential Task Processing Tests](../../../tests/integration/test_sequential_task_processing.py)

## Related ADRs

- [ADR-004: Execution Log Coordination Strategy](004-execution-log-coordination.md)
- [ADR-002: Security Middleware Framework](002-security-middleware-framework.md)

---

**Author**: AtomSec Development Team  
**Reviewers**: Tech Lead, Architecture Team  
**Approved By**: CTO  
**Implementation Status**: Completed