# ADR-002: Security Middleware Framework

## Status
Accepted

## Date
2025-01-24

## Context

The AtomSec SFDC Service handles sensitive Salesforce data and credentials, requiring comprehensive security measures. The service needs consistent security validation across all endpoints while maintaining performance and usability.

### Problem Statement
- Multiple endpoints require similar security validations
- Salesforce access tokens need secure handling and validation
- Parameter validation must be comprehensive and consistent
- Security checks should not impact performance significantly
- Need centralized security policy enforcement

### Security Requirements
1. **Authentication**: Validate JWT tokens and API keys
2. **Authorization**: Enforce role-based access control
3. **Input Validation**: Comprehensive parameter validation
4. **Token Security**: Secure handling of Salesforce access tokens
5. **Audit Logging**: Log all security-relevant events
6. **Rate Limiting**: Prevent abuse and DoS attacks

## Decision

We will implement a **Security Middleware Framework** that provides centralized security validation for all API endpoints.

### 1. Middleware Architecture

```mermaid
graph TB
    A[HTTP Request] --> B[CORS Handler]
    B --> C[Authentication Middleware]
    C --> D[Authorization Middleware]
    D --> E[Parameter Validation Middleware]
    E --> F[Rate Limiting Middleware]
    F --> G[Audit Logging Middleware]
    G --> H[Endpoint Handler]
    H --> I[Response]
    I --> J[Security Headers Middleware]
    J --> K[HTTP Response]
```

### 2. Core Components

#### Security Middleware Decorator
```python
@secure_endpoint(
    required_fields=['task_type', 'org_id'],
    validate_auth=True,
    validate_execution_log=True,
    rate_limit='10/minute'
)
def create_task(req: func.HttpRequest) -> func.HttpResponse:
    """Create task with comprehensive security validation."""
    pass
```

#### Enhanced Parameter Validator
```python
class EnhancedParameterValidator:
    """Comprehensive parameter validation with security focus."""
    
    def validate_sfdc_request(self, request_data: Dict) -> ValidationResult:
        """Validate SFDC-specific request parameters."""
        
    def validate_sequential_task(self, task_data: Dict) -> ValidationResult:
        """Validate sequential task parameters."""
        
    def validate_access_token(self, token: str) -> TokenValidationResult:
        """Validate and sanitize Salesforce access tokens."""
```

#### Enhanced Authentication Service
```python
class EnhancedAuthService:
    """Enhanced authentication with Salesforce integration."""
    
    def authenticate_jwt_token(self, token: str) -> AuthResult:
        """Authenticate JWT token with enhanced validation."""
        
    def authenticate_salesforce_api(self, client_id: str, client_secret: str) -> SFAuthResult:
        """Authenticate with Salesforce API securely."""
        
    def validate_user_permissions(self, user_id: str, resource: str) -> bool:
        """Validate user permissions for resource access."""
```

### 3. Security Validation Layers

#### Layer 1: Request Authentication
- JWT token validation
- API key authentication
- Token expiration checks
- Signature verification

#### Layer 2: Authorization
- Role-based access control (RBAC)
- Resource-level permissions
- Organization-level access control
- User context validation

#### Layer 3: Parameter Validation
- Required field validation
- Data type validation
- Format validation (UUIDs, URLs, etc.)
- Security-specific validation (token format, etc.)

#### Layer 4: Business Logic Validation
- Execution log ID validation
- Task sequence validation
- Integration access validation
- Rate limiting enforcement

### 4. Implementation Details

#### Security Middleware Implementation
```python
def secure_endpoint(
    required_fields: List[str] = None,
    validate_auth: bool = True,
    validate_execution_log: bool = False,
    rate_limit: str = None
):
    """Decorator for comprehensive endpoint security."""
    
    def decorator(func):
        @wraps(func)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            try:
                # 1. Authentication
                if validate_auth:
                    auth_result = authenticate_request(req)
                    if not auth_result.success:
                        return create_error_response(401, "Authentication required")
                
                # 2. Rate limiting
                if rate_limit:
                    if not check_rate_limit(req, rate_limit):
                        return create_error_response(429, "Rate limit exceeded")
                
                # 3. Parameter validation
                if required_fields:
                    validation_result = validate_request_parameters(req, required_fields)
                    if not validation_result.success:
                        return create_error_response(400, validation_result.error)
                
                # 4. Execution log validation
                if validate_execution_log:
                    if not validate_execution_log_id(req):
                        return create_error_response(400, "Invalid execution log ID")
                
                # 5. Audit logging
                log_security_event(req, "endpoint_access")
                
                # 6. Execute endpoint
                response = func(req)
                
                # 7. Add security headers
                add_security_headers(response)
                
                return response
                
            except SecurityValidationError as e:
                log_security_event(req, "security_violation", str(e))
                return create_error_response(403, "Access denied")
            except Exception as e:
                log_security_event(req, "security_error", str(e))
                return create_error_response(500, "Internal security error")
        
        return wrapper
    return decorator
```

#### Token Security Implementation
```python
class SecureTokenHandler:
    """Secure handling of Salesforce access tokens."""
    
    def validate_token_format(self, token: str) -> bool:
        """Validate token format without exposing content."""
        
    def sanitize_token_for_logging(self, token: str) -> str:
        """Sanitize token for safe logging."""
        return f"{token[:8]}...{token[-4:]}" if len(token) > 12 else "[REDACTED]"
    
    def encrypt_token_for_storage(self, token: str) -> str:
        """Encrypt token for secure storage."""
        
    def decrypt_token_from_storage(self, encrypted_token: str) -> str:
        """Decrypt token from secure storage."""
```

## Consequences

### Positive Consequences
1. **Centralized Security**: Consistent security across all endpoints
2. **Reduced Code Duplication**: Security logic centralized in middleware
3. **Enhanced Security**: Comprehensive validation and protection
4. **Audit Trail**: Complete security event logging
5. **Performance**: Optimized security checks
6. **Maintainability**: Easier to update security policies

### Negative Consequences
1. **Complexity**: Additional abstraction layer
2. **Performance Overhead**: Security checks add latency
3. **Debugging Complexity**: More layers to debug
4. **Learning Curve**: Team needs to understand middleware patterns
5. **Potential Single Point of Failure**: Middleware bugs affect all endpoints

### Risks and Mitigations

#### Risk: Middleware Performance Impact
- **Mitigation**: Optimize security checks and use caching
- **Monitoring**: Track middleware execution time

#### Risk: Security Bypass
- **Mitigation**: Comprehensive testing and code reviews
- **Monitoring**: Security event monitoring and alerting

#### Risk: Token Exposure
- **Mitigation**: Secure token handling and sanitization
- **Monitoring**: Token access logging and anomaly detection

## Alternatives Considered

### Alternative 1: Endpoint-Level Security
**Description**: Implement security checks in each endpoint individually.

**Pros**:
- Simple implementation
- No middleware complexity
- Endpoint-specific customization

**Cons**:
- Code duplication
- Inconsistent security
- Maintenance overhead
- Higher risk of security gaps

**Decision**: Rejected due to consistency and maintenance concerns.

### Alternative 2: API Gateway Security
**Description**: Use Azure API Management for security enforcement.

**Pros**:
- Centralized security
- Built-in features
- Scalable solution
- External security layer

**Cons**:
- Additional service dependency
- Less control over security logic
- Cost implications
- Learning curve

**Decision**: Rejected to maintain control and reduce dependencies.

### Alternative 3: Third-Party Security Framework
**Description**: Use existing security framework like Flask-Security.

**Pros**:
- Proven solution
- Rich feature set
- Community support
- Faster implementation

**Cons**:
- External dependency
- May not fit Azure Functions model
- Less customization
- Potential security vulnerabilities

**Decision**: Rejected due to Azure Functions compatibility and customization needs.

## Implementation Plan

### Phase 1: Core Middleware
1. Implement basic security middleware decorator
2. Create enhanced parameter validator
3. Implement JWT authentication
4. Basic audit logging

### Phase 2: Advanced Features
1. Rate limiting implementation
2. Enhanced authorization (RBAC)
3. Salesforce token security
4. Comprehensive audit logging

### Phase 3: Optimization and Monitoring
1. Performance optimization
2. Security monitoring and alerting
3. Advanced threat detection
4. Security metrics and reporting

## Security Considerations

### Token Security
- Never log complete access tokens
- Encrypt tokens in transit and at rest
- Implement token rotation policies
- Monitor for token abuse

### Audit Logging
- Log all authentication attempts
- Log authorization failures
- Log parameter validation failures
- Log rate limiting violations

### Error Handling
- Don't expose internal security details
- Use generic error messages for security failures
- Log detailed errors internally
- Implement proper error correlation

## Testing Strategy

### Unit Tests
```python
class TestSecurityMiddleware:
    def test_authentication_success(self):
        """Test successful authentication."""
        
    def test_authentication_failure(self):
        """Test authentication failure handling."""
        
    def test_parameter_validation(self):
        """Test parameter validation logic."""
        
    def test_rate_limiting(self):
        """Test rate limiting enforcement."""
```

### Security Tests
```python
class TestSecurityValidation:
    def test_injection_attacks(self):
        """Test protection against injection attacks."""
        
    def test_token_manipulation(self):
        """Test token manipulation attempts."""
        
    def test_authorization_bypass(self):
        """Test authorization bypass attempts."""
```

## Monitoring and Metrics

### Security Metrics
- Authentication success/failure rates
- Authorization violations
- Parameter validation failures
- Rate limiting violations
- Token usage patterns

### Monitoring Queries
```kusto
// Authentication failures
customEvents
| where name == "AuthenticationFailure"
| summarize count() by bin(timestamp, 5m), tostring(customDimensions.reason)
| render timechart

// Security violations
customEvents
| where name == "SecurityViolation"
| summarize count() by tostring(customDimensions.violation_type)
| order by count_ desc
```

## References

- [Enhanced Parameter Validator](../../../shared/enhanced_parameter_validator.py)
- [Enhanced Auth Service](../../../shared/enhanced_auth_service.py)
- [Security Middleware](../../../shared/security_middleware.py)
- [Security Tests](../../../tests/security/)

## Related ADRs

- [ADR-001: Sequential Task Processing Architecture](001-sequential-task-processing.md)
- [ADR-004: Execution Log Coordination Strategy](004-execution-log-coordination.md)

---

**Author**: AtomSec Security Team  
**Reviewers**: Tech Lead, Security Architect  
**Approved By**: CISO, CTO  
**Implementation Status**: Completed