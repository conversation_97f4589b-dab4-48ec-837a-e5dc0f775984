# Service Communication Flows

## Overview

This document provides detailed service communication flows and diagrams for the AtomSec microservices architecture. It illustrates how different services interact, data flows, and communication patterns.

## High-Level Architecture Flow

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile App]
        API_CLIENT[API Client]
    end
    
    subgraph "Gateway Layer"
        APIM[Azure API Management]
        LB[Load Balancer]
    end
    
    subgraph "Service Layer"
        subgraph "DB Service Cluster"
            DB1[DB Service Instance 1]
            DB2[DB Service Instance 2]
            DB3[DB Service Instance 3]
        end
        
        subgraph "SFDC Service Cluster"
            SFDC1[SFDC Service Instance 1]
            SFDC2[SFDC Service Instance 2]
        end
        
        subgraph "Auth Service"
            AUTH[Auth Service]
        end
    end
    
    subgraph "External Services"
        SF[Salesforce API]
        GRAPH[Microsoft Graph]
        AZURE_AD[Azure AD]
    end
    
    subgraph "Azure Services"
        KV[Key Vault]
        SB[Service Bus]
        AI[App Insights]
        STORAGE[Blob Storage]
    end
    
    subgraph "Data Layer"
        SQL[SQL Database]
        REDIS[Redis Cache]
    end
    
    %% Client connections
    WEB --> APIM
    MOBILE --> APIM
    API_CLIENT --> APIM
    
    %% Gateway routing
    APIM --> LB
    LB --> DB1
    LB --> DB2
    LB --> DB3
    
    %% Service communication
    DB1 --> SFDC1
    DB2 --> SFDC2
    DB3 --> SFDC1
    
    %% Authentication flow
    DB1 --> AUTH
    DB2 --> AUTH
    DB3 --> AUTH
    
    %% External service calls
    SFDC1 --> SF
    SFDC2 --> SF
    AUTH --> AZURE_AD
    SFDC1 --> GRAPH
    
    %% Azure service dependencies
    DB1 --> SQL
    DB2 --> SQL
    DB3 --> SQL
    SFDC1 --> KV
    SFDC2 --> KV
    SFDC1 --> SB
    SFDC2 --> SB
    DB1 --> AI
    SFDC1 --> AI
    SFDC1 --> STORAGE
    SFDC2 --> STORAGE
    
    %% Caching
    DB1 --> REDIS
    DB2 --> REDIS
    DB3 --> REDIS
```

## Detailed Communication Patterns

### 1. User Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant DB as DB Service
    participant AUTH as Auth Service
    participant AD as Azure AD
    participant KV as Key Vault
    
    U->>FE: Login Request
    FE->>DB: POST /api/db/auth/login
    DB->>AUTH: Validate Credentials
    AUTH->>AD: Azure AD Authentication
    AD-->>AUTH: Authentication Result
    AUTH->>KV: Get User Permissions
    KV-->>AUTH: User Permissions
    AUTH-->>DB: Authentication Response
    DB-->>FE: JWT Token + User Info
    FE-->>U: Login Success
    
    Note over U,KV: Subsequent requests include JWT token
    
    U->>FE: API Request with JWT
    FE->>DB: Request with Authorization header
    DB->>AUTH: Validate JWT Token
    AUTH-->>DB: Token Validation Result
    DB->>DB: Process Request
    DB-->>FE: Response
    FE-->>U: Display Result
```

### 2. Integration Scan Flow

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant SF as Salesforce
    participant SB as Service Bus
    participant STORAGE as Blob Storage
    
    U->>FE: Trigger Integration Scan
    FE->>DB: POST /api/db/integration/scan/{id}
    DB->>DB: Validate Request & Auth
    DB->>SFDC: POST /api/integration/scan/{id}
    
    SFDC->>SFDC: Create Execution Log
    SFDC->>SB: Enqueue Scan Tasks
    SFDC-->>DB: Scan Initiated Response
    DB-->>FE: Task Created Response
    FE-->>U: Scan Started Notification
    
    %% Background Processing
    SB->>SFDC: Process sfdc_authenticate Task
    SFDC->>SF: Authenticate with Salesforce
    SF-->>SFDC: Authentication Token
    SFDC->>SB: Update Task Status
    
    SB->>SFDC: Process health_check Task
    SFDC->>SF: Health Check API Calls
    SF-->>SFDC: Health Data
    SFDC->>SB: Update Task Status
    
    SB->>SFDC: Process metadata_extraction Task
    SFDC->>SF: Extract Metadata
    SF-->>SFDC: Metadata Response
    SFDC->>STORAGE: Store Metadata
    SFDC->>SB: Update Task Status
    
    SB->>SFDC: Process pmd_apex_security Task
    SFDC->>SFDC: Run PMD Analysis
    SFDC->>STORAGE: Store PMD Results
    SFDC->>SB: Complete Task Sequence
    
    %% Status Updates
    SFDC->>DB: Update Integration Status
    DB->>FE: WebSocket/Polling Update
    FE->>U: Scan Complete Notification
```

### 3. Real-time Task Status Updates

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant SB as Service Bus
    participant WS as WebSocket Service
    
    %% Initial status request
    FE->>DB: GET /api/db/tasks/{id}/status
    DB->>SFDC: GET /api/tasks/{id}/status
    SFDC-->>DB: Current Task Status
    DB-->>FE: Task Status Response
    
    %% Real-time updates
    loop Task Processing
        SB->>SFDC: Process Task Step
        SFDC->>SFDC: Update Task Progress
        SFDC->>WS: Publish Status Update
        WS->>FE: Real-time Status Update
        FE->>FE: Update UI
    end
    
    %% Final completion
    SFDC->>DB: Task Completed
    DB->>WS: Final Status Update
    WS->>FE: Completion Notification
    FE->>FE: Show Results
```

### 4. Error Handling and Recovery Flow

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant SF as Salesforce
    participant CB as Circuit Breaker
    participant RETRY as Retry Service
    participant ALERT as Alert Service
    
    FE->>DB: API Request
    DB->>SFDC: Proxy Request
    SFDC->>CB: Check Circuit State
    
    alt Circuit Open
        CB-->>SFDC: Circuit Open - Fail Fast
        SFDC-->>DB: Service Unavailable
        DB-->>FE: Cached/Fallback Response
    else Circuit Closed
        CB->>SF: Forward Request
        SF-->>CB: Error Response (500)
        CB->>RETRY: Trigger Retry Logic
        
        loop Retry Attempts
            RETRY->>SF: Retry Request
            alt Success
                SF-->>RETRY: Success Response
                RETRY-->>CB: Success
                CB-->>SFDC: Success Response
            else Failure
                SF-->>RETRY: Error Response
                RETRY->>RETRY: Exponential Backoff
            end
        end
        
        alt Max Retries Exceeded
            RETRY->>CB: Open Circuit
            CB->>ALERT: Send Alert
            ALERT->>ALERT: Notify Operations Team
            CB-->>SFDC: Circuit Breaker Open
            SFDC-->>DB: Degraded Service Response
            DB-->>FE: Fallback Response
        end
    end
```

### 5. Configuration and Feature Flag Flow

```mermaid
sequenceDiagram
    participant APP as Application
    participant CONFIG as Config Manager
    participant KV as Key Vault
    participant FF as Feature Flag Service
    participant CACHE as Redis Cache
    
    APP->>CONFIG: Get Configuration
    CONFIG->>CACHE: Check Cache
    
    alt Cache Hit
        CACHE-->>CONFIG: Cached Config
    else Cache Miss
        CONFIG->>KV: Get Secrets
        KV-->>CONFIG: Secret Values
        CONFIG->>CACHE: Cache Config
    end
    
    CONFIG-->>APP: Configuration Object
    
    %% Feature flag evaluation
    APP->>FF: Check Feature Flag
    FF->>CACHE: Check Flag Cache
    
    alt Cache Hit
        CACHE-->>FF: Cached Flag Value
    else Cache Miss
        FF->>KV: Get Flag Configuration
        KV-->>FF: Flag Rules
        FF->>FF: Evaluate Flag
        FF->>CACHE: Cache Result
    end
    
    FF-->>APP: Feature Enabled/Disabled
    APP->>APP: Execute Feature Logic
```

## Service Communication Protocols

### HTTP/REST Communication

#### Request Headers
```http
# Standard Headers
Content-Type: application/json
Authorization: Bearer <jwt-token>
X-Request-ID: <unique-request-id>
X-Correlation-ID: <correlation-id>

# Internal Service Headers
X-Internal-Service: db-service
X-Service-Version: 1.0.0
X-Environment: production
```

#### Response Headers
```http
# Standard Headers
Content-Type: application/json
X-Request-ID: <unique-request-id>
X-Response-Time: 150ms

# CORS Headers
Access-Control-Allow-Origin: https://app.atomsec.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### Service Bus Messaging

#### Message Format
```json
{
  "messageId": "unique-message-id",
  "correlationId": "correlation-id",
  "messageType": "task.execution",
  "timestamp": "2025-01-24T10:00:00Z",
  "source": "sfdc-service",
  "data": {
    "taskId": "task-123",
    "taskType": "metadata_extraction",
    "executionLogId": "exec-456",
    "parameters": {
      "integrationId": "integration-789",
      "userId": "user-101"
    }
  },
  "metadata": {
    "retryCount": 0,
    "priority": "high",
    "ttl": 3600
  }
}
```

## Performance Optimization Patterns

### 1. Connection Pooling

```mermaid
graph TB
    subgraph "DB Service"
        DB_APP[Application]
        DB_POOL[HTTP Connection Pool]
    end
    
    subgraph "SFDC Service"
        SFDC_APP[Application]
        SFDC_POOL[HTTP Connection Pool]
        SF_POOL[Salesforce Connection Pool]
    end
    
    subgraph "External Services"
        SFDC_API[SFDC Service API]
        SF_API[Salesforce API]
    end
    
    DB_APP --> DB_POOL
    DB_POOL --> SFDC_API
    
    SFDC_APP --> SFDC_POOL
    SFDC_APP --> SF_POOL
    SF_POOL --> SF_API
    
    note1[Connection Reuse<br/>Reduces Latency]
    note2[Pool Size Management<br/>Prevents Resource Exhaustion]
```

### 2. Caching Strategy

```mermaid
graph TB
    subgraph "Request Flow"
        REQ[Incoming Request]
        CACHE_CHECK{Cache Check}
        CACHE_HIT[Cache Hit]
        CACHE_MISS[Cache Miss]
        SERVICE_CALL[Service Call]
        CACHE_UPDATE[Update Cache]
        RESPONSE[Response]
    end
    
    subgraph "Cache Layers"
        L1[L1: In-Memory Cache]
        L2[L2: Redis Cache]
        L3[L3: Database Cache]
    end
    
    REQ --> CACHE_CHECK
    CACHE_CHECK --> CACHE_HIT
    CACHE_CHECK --> CACHE_MISS
    CACHE_HIT --> RESPONSE
    CACHE_MISS --> SERVICE_CALL
    SERVICE_CALL --> CACHE_UPDATE
    CACHE_UPDATE --> RESPONSE
    
    CACHE_CHECK --> L1
    L1 --> L2
    L2 --> L3
```

## Monitoring and Observability

### 1. Distributed Tracing

```mermaid
graph LR
    subgraph "Request Trace"
        START[Request Start]
        DB_SPAN[DB Service Span]
        SFDC_SPAN[SFDC Service Span]
        SF_SPAN[Salesforce API Span]
        END[Request End]
    end
    
    subgraph "Trace Context"
        TRACE_ID[Trace ID: abc123]
        PARENT_SPAN[Parent Span ID]
        SPAN_ID[Span ID]
        BAGGAGE[Baggage Items]
    end
    
    START --> DB_SPAN
    DB_SPAN --> SFDC_SPAN
    SFDC_SPAN --> SF_SPAN
    SF_SPAN --> END
    
    DB_SPAN --> TRACE_ID
    SFDC_SPAN --> TRACE_ID
    SF_SPAN --> TRACE_ID
```

### 2. Metrics Collection

```mermaid
graph TB
    subgraph "Service Metrics"
        REQ_RATE[Request Rate]
        RESP_TIME[Response Time]
        ERROR_RATE[Error Rate]
        THROUGHPUT[Throughput]
    end
    
    subgraph "Business Metrics"
        SCAN_SUCCESS[Scan Success Rate]
        USER_ACTIVITY[User Activity]
        INTEGRATION_HEALTH[Integration Health]
    end
    
    subgraph "Infrastructure Metrics"
        CPU[CPU Usage]
        MEMORY[Memory Usage]
        DISK[Disk I/O]
        NETWORK[Network I/O]
    end
    
    subgraph "Collection & Storage"
        APP_INSIGHTS[Application Insights]
        PROMETHEUS[Prometheus]
        GRAFANA[Grafana Dashboards]
    end
    
    REQ_RATE --> APP_INSIGHTS
    RESP_TIME --> APP_INSIGHTS
    ERROR_RATE --> APP_INSIGHTS
    THROUGHPUT --> APP_INSIGHTS
    
    SCAN_SUCCESS --> PROMETHEUS
    USER_ACTIVITY --> PROMETHEUS
    INTEGRATION_HEALTH --> PROMETHEUS
    
    CPU --> APP_INSIGHTS
    MEMORY --> APP_INSIGHTS
    DISK --> APP_INSIGHTS
    NETWORK --> APP_INSIGHTS
    
    APP_INSIGHTS --> GRAFANA
    PROMETHEUS --> GRAFANA
```

## Security Communication Patterns

### 1. Service-to-Service Authentication

```mermaid
sequenceDiagram
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant KV as Key Vault
    participant AD as Azure AD
    
    DB->>KV: Get Service Principal Cert
    KV-->>DB: Certificate
    DB->>AD: Request Service Token
    AD-->>DB: Service Access Token
    
    DB->>SFDC: API Call with Service Token
    SFDC->>AD: Validate Service Token
    AD-->>SFDC: Token Valid
    SFDC->>SFDC: Process Request
    SFDC-->>DB: Response
```

### 2. End-to-End Encryption

```mermaid
graph TB
    subgraph "Client Side"
        CLIENT[Client Application]
        CLIENT_CERT[Client Certificate]
    end
    
    subgraph "Transport Layer"
        TLS[TLS 1.3 Encryption]
        CERT_VALIDATION[Certificate Validation]
    end
    
    subgraph "Service Layer"
        DB_SERVICE[DB Service]
        SFDC_SERVICE[SFDC Service]
        SERVICE_CERTS[Service Certificates]
    end
    
    subgraph "Data Layer"
        ENCRYPTED_DATA[Encrypted at Rest]
        KEY_ROTATION[Key Rotation]
    end
    
    CLIENT --> CLIENT_CERT
    CLIENT_CERT --> TLS
    TLS --> CERT_VALIDATION
    CERT_VALIDATION --> DB_SERVICE
    DB_SERVICE --> SFDC_SERVICE
    SFDC_SERVICE --> SERVICE_CERTS
    SERVICE_CERTS --> ENCRYPTED_DATA
    ENCRYPTED_DATA --> KEY_ROTATION
```

## Deployment Communication Flows

### 1. Blue-Green Deployment

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Azure Load Balancer]
    end
    
    subgraph "Blue Environment (Current)"
        BLUE_DB[DB Service Blue]
        BLUE_SFDC[SFDC Service Blue]
    end
    
    subgraph "Green Environment (New)"
        GREEN_DB[DB Service Green]
        GREEN_SFDC[SFDC Service Green]
    end
    
    subgraph "Shared Resources"
        DATABASE[Shared Database]
        KEY_VAULT[Shared Key Vault]
        SERVICE_BUS[Shared Service Bus]
    end
    
    LB --> BLUE_DB
    LB -.-> GREEN_DB
    
    BLUE_DB --> BLUE_SFDC
    GREEN_DB --> GREEN_SFDC
    
    BLUE_DB --> DATABASE
    GREEN_DB --> DATABASE
    BLUE_SFDC --> KEY_VAULT
    GREEN_SFDC --> KEY_VAULT
    BLUE_SFDC --> SERVICE_BUS
    GREEN_SFDC --> SERVICE_BUS
    
    note1[Traffic switches from<br/>Blue to Green after<br/>validation]
```

### 2. Canary Deployment

```mermaid
graph TB
    subgraph "Traffic Distribution"
        TRAFFIC[Incoming Traffic]
        SPLIT{Traffic Split}
    end
    
    subgraph "Stable Version (90%)"
        STABLE_DB[DB Service v1.0]
        STABLE_SFDC[SFDC Service v1.0]
    end
    
    subgraph "Canary Version (10%)"
        CANARY_DB[DB Service v1.1]
        CANARY_SFDC[SFDC Service v1.1]
    end
    
    subgraph "Monitoring"
        METRICS[Metrics Collection]
        ALERTS[Alert System]
        ROLLBACK[Auto Rollback]
    end
    
    TRAFFIC --> SPLIT
    SPLIT -->|90%| STABLE_DB
    SPLIT -->|10%| CANARY_DB
    
    STABLE_DB --> STABLE_SFDC
    CANARY_DB --> CANARY_SFDC
    
    STABLE_DB --> METRICS
    CANARY_DB --> METRICS
    METRICS --> ALERTS
    ALERTS --> ROLLBACK
```

---

This comprehensive service communication flow documentation provides detailed insights into how the AtomSec microservices architecture handles various communication patterns, ensuring robust, scalable, and secure inter-service communication.