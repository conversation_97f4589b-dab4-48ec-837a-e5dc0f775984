# AtomSec SFDC Service Deployment Runbook

This runbook provides step-by-step procedures for deploying the AtomSec SFDC Service across different environments.

## 📋 Pre-Deployment Checklist

### Prerequisites
- [ ] Azure CLI installed and configured
- [ ] Azure DevOps access with appropriate permissions
- [ ] Access to Azure Key Vault for secrets management
- [ ] Bicep CLI installed for Infrastructure as Code
- [ ] Python 3.9+ for local testing
- [ ] Valid Salesforce developer/admin credentials for testing

### Environment Verification
- [ ] Target Azure subscription is accessible
- [ ] Resource group exists or can be created
- [ ] Required Azure services are available in target region
- [ ] Network connectivity to dependencies (DB service, Service Bus)
- [ ] DNS configuration for custom domains (if applicable)

### Code Preparation
- [ ] Code is merged to appropriate branch (main for prod, develop for dev)
- [ ] All tests pass in CI/CD pipeline
- [ ] Security scans completed successfully
- [ ] Code review approved
- [ ] Version tag created (for production deployments)

## 🏗️ Infrastructure Deployment

### 1. Deploy Infrastructure Components

#### Using Bicep Templates
```bash
# Navigate to infrastructure directory
cd atomsec-func-sfdc/infrastructure

# Deploy to development environment
az deployment group create \
  --resource-group rg-atomsec-sfdc-dev \
  --template-file main.bicep \
  --parameters @parameters/dev.bicepparam \
  --confirm-with-what-if

# Deploy to staging environment
az deployment group create \
  --resource-group rg-atomsec-sfdc-staging \
  --template-file main.bicep \
  --parameters @parameters/staging.bicepparam \
  --confirm-with-what-if

# Deploy to production environment
az deployment group create \
  --resource-group rg-atomsec-sfdc-prod \
  --template-file main.bicep \
  --parameters @parameters/prod.bicepparam \
  --confirm-with-what-if
```

#### Using PowerShell (Windows)
```powershell
# Run the deployment script
.\infrastructure\deploy.ps1 -Environment "dev" -ResourceGroup "rg-atomsec-sfdc-dev"
```

#### Using Bash (Linux/macOS)
```bash
# Run the deployment script
./infrastructure/deploy.sh dev rg-atomsec-sfdc-dev
```

### 2. Verify Infrastructure Deployment

```bash
# Check resource group contents
az resource list --resource-group rg-atomsec-sfdc-dev --output table

# Verify Function App is created
az functionapp list --resource-group rg-atomsec-sfdc-dev --output table

# Check Application Insights
az monitor app-insights component show \
  --app atomsec-sfdc-dev-insights \
  --resource-group rg-atomsec-sfdc-dev

# Verify Key Vault
az keyvault list --resource-group rg-atomsec-sfdc-dev --output table
```

## 🚀 Application Deployment

### 1. Manual Deployment

#### Deploy Function App Code
```bash
# Build and package the application
cd atomsec-func-sfdc
pip install -r requirements.txt

# Deploy to Azure Function App
func azure functionapp publish atomsec-func-sfdc-dev --python

# Verify deployment
func azure functionapp list-functions atomsec-func-sfdc-dev
```

#### Configure Application Settings
```bash
# Set application settings
az functionapp config appsettings set \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev \
  --settings \
    "ENVIRONMENT=development" \
    "LOG_LEVEL=INFO" \
    "ENABLE_DEBUG=false"

# Configure connection strings
az functionapp config connection-string set \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev \
  --connection-string-type SQLAzure \
  --settings DefaultConnection="Server=tcp:atomsec-sql-dev.database.windows.net,1433;Database=atomsec-dev;Authentication=Active Directory Default;"
```

### 2. CI/CD Pipeline Deployment

#### Trigger Pipeline Deployment
```bash
# Trigger development pipeline
az pipelines run --name "atomsec-func-sfdc-dev" --branch develop

# Trigger staging pipeline
az pipelines run --name "atomsec-func-sfdc-staging" --branch main

# Trigger production pipeline (requires approval)
az pipelines run --name "atomsec-func-sfdc-prod" --branch main
```

#### Monitor Pipeline Progress
```bash
# Check pipeline status
az pipelines runs list --pipeline-name "atomsec-func-sfdc-dev" --status inProgress

# Get detailed run information
az pipelines runs show --id <run-id>
```

## 🔧 Configuration Management

### 1. Environment-Specific Configuration

#### Development Environment
```json
{
  "environment": "development",
  "log_level": "DEBUG",
  "enable_debug": true,
  "database_connection_timeout": 30,
  "salesforce_api_timeout": 60,
  "task_processing_timeout": 300,
  "enable_performance_monitoring": true,
  "enable_detailed_logging": true
}
```

#### Staging Environment
```json
{
  "environment": "staging",
  "log_level": "INFO",
  "enable_debug": false,
  "database_connection_timeout": 30,
  "salesforce_api_timeout": 45,
  "task_processing_timeout": 600,
  "enable_performance_monitoring": true,
  "enable_detailed_logging": false
}
```

#### Production Environment
```json
{
  "environment": "production",
  "log_level": "WARNING",
  "enable_debug": false,
  "database_connection_timeout": 20,
  "salesforce_api_timeout": 30,
  "task_processing_timeout": 900,
  "enable_performance_monitoring": true,
  "enable_detailed_logging": false
}
```

### 2. Secrets Management

#### Configure Key Vault Secrets
```bash
# Set database connection string
az keyvault secret set \
  --vault-name kv-atomsec-sfdc-dev \
  --name "DatabaseConnectionString" \
  --value "Server=tcp:atomsec-sql-dev.database.windows.net,1433;Database=atomsec-dev;Authentication=Active Directory Default;"

# Set Service Bus connection string
az keyvault secret set \
  --vault-name kv-atomsec-sfdc-dev \
  --name "ServiceBusConnectionString" \
  --value "Endpoint=sb://atomsec-sb-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=..."

# Set Application Insights instrumentation key
az keyvault secret set \
  --vault-name kv-atomsec-sfdc-dev \
  --name "ApplicationInsightsInstrumentationKey" \
  --value "12345678-1234-1234-1234-123456789012"
```

#### Grant Function App Access to Key Vault
```bash
# Enable system-assigned managed identity
az functionapp identity assign \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev

# Get the principal ID
PRINCIPAL_ID=$(az functionapp identity show \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev \
  --query principalId --output tsv)

# Grant access to Key Vault
az keyvault set-policy \
  --name kv-atomsec-sfdc-dev \
  --object-id $PRINCIPAL_ID \
  --secret-permissions get list
```

## 🧪 Post-Deployment Verification

### 1. Health Check Verification

```bash
# Test basic health endpoint
curl -s "https://atomsec-func-sfdc-dev.azurewebsites.net/api/health" | jq .

# Test detailed health endpoint
curl -s "https://atomsec-func-sfdc-dev.azurewebsites.net/api/health/detailed" | jq .

# Expected response structure
{
  "status": "healthy",
  "service": "atomsec-func-sfdc",
  "environment": "development",
  "version": "1.0.0",
  "checks": {
    "database": "connected",
    "service_bus": "connected",
    "key_vault": "connected"
  }
}
```

### 2. Functional Testing

#### Test Task Creation
```bash
# Create a test task
curl -X POST "https://atomsec-func-sfdc-dev.azurewebsites.net/api/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "task_type": "health_check",
    "org_id": "test-org-id",
    "user_id": "test-user",
    "params": {
      "integration_id": "test-integration-id"
    },
    "execution_log_id": "test-execution-log-id"
  }'
```

#### Test Integration Management
```bash
# List integrations
curl -s "https://atomsec-func-sfdc-dev.azurewebsites.net/api/integrations" \
  -H "Authorization: Bearer $AUTH_TOKEN" | jq .
```

### 3. Performance Testing

```bash
# Run basic load test
ab -n 100 -c 10 "https://atomsec-func-sfdc-dev.azurewebsites.net/api/health"

# Monitor response times
curl -w "@curl-format.txt" -s -o /dev/null "https://atomsec-func-sfdc-dev.azurewebsites.net/api/health"
```

### 4. Security Testing

```bash
# Test authentication
curl -s "https://atomsec-func-sfdc-dev.azurewebsites.net/api/tasks" \
  -H "Content-Type: application/json"
# Should return 401 Unauthorized

# Test CORS headers
curl -H "Origin: https://example.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: X-Requested-With" \
  -X OPTIONS \
  "https://atomsec-func-sfdc-dev.azurewebsites.net/api/health"
```

## 🔄 Blue-Green Deployment

### 1. Prepare Blue-Green Deployment

```bash
# Create deployment slot (green)
az functionapp deployment slot create \
  --name atomsec-func-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --slot green

# Deploy to green slot
func azure functionapp publish atomsec-func-sfdc-prod --slot green
```

### 2. Test Green Deployment

```bash
# Test green slot
curl -s "https://atomsec-func-sfdc-prod-green.azurewebsites.net/api/health" | jq .

# Run comprehensive tests against green slot
python scripts/blue_green_deployment.py --test-slot green
```

### 3. Swap Slots

```bash
# Swap green to production
az functionapp deployment slot swap \
  --name atomsec-func-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --slot green \
  --target-slot production

# Verify production is working
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health" | jq .
```

### 4. Rollback if Needed

```bash
# Rollback by swapping back
az functionapp deployment slot swap \
  --name atomsec-func-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --slot production \
  --target-slot green
```

## 📊 Monitoring Setup

### 1. Configure Application Insights

```bash
# Enable Application Insights
az functionapp config appsettings set \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev \
  --settings "APPINSIGHTS_INSTRUMENTATIONKEY=@Microsoft.KeyVault(VaultName=kv-atomsec-sfdc-dev;SecretName=ApplicationInsightsInstrumentationKey)"

# Configure sampling
az functionapp config appsettings set \
  --name atomsec-func-sfdc-dev \
  --resource-group rg-atomsec-sfdc-dev \
  --settings "APPINSIGHTS_SAMPLING_PERCENTAGE=100"
```

### 2. Set Up Alerts

```bash
# Create alert rule for high error rate
az monitor metrics alert create \
  --name "SFDC High Error Rate" \
  --resource-group rg-atomsec-sfdc-dev \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-dev/providers/Microsoft.Web/sites/atomsec-func-sfdc-dev" \
  --condition "avg exceptions/server > 10" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-dev/providers/microsoft.insights/actionGroups/sfdc-alerts"

# Create alert rule for high response time
az monitor metrics alert create \
  --name "SFDC High Response Time" \
  --resource-group rg-atomsec-sfdc-dev \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-dev/providers/Microsoft.Web/sites/atomsec-func-sfdc-dev" \
  --condition "avg requests/duration > 5000" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-dev/providers/microsoft.insights/actionGroups/sfdc-alerts"
```

## 🚨 Rollback Procedures

### 1. Immediate Rollback (Slot Swap)

```bash
# For production deployments using slots
az functionapp deployment slot swap \
  --name atomsec-func-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --slot production \
  --target-slot previous

# Verify rollback
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health" | jq .
```

### 2. Code Rollback

```bash
# Revert to previous version
git revert HEAD --no-edit
git push origin main

# Trigger deployment pipeline
az pipelines run --name "atomsec-func-sfdc-prod" --branch main
```

### 3. Infrastructure Rollback

```bash
# Rollback infrastructure changes
az deployment group create \
  --resource-group rg-atomsec-sfdc-prod \
  --template-file main.bicep \
  --parameters @parameters/prod-previous.bicepparam \
  --mode Complete
```

## 📝 Deployment Checklist

### Pre-Deployment
- [ ] Code review completed and approved
- [ ] All tests pass (unit, integration, security)
- [ ] Infrastructure templates validated
- [ ] Secrets updated in Key Vault
- [ ] Backup of current production state taken
- [ ] Deployment window scheduled and communicated
- [ ] Rollback plan prepared and tested

### During Deployment
- [ ] Infrastructure deployment completed successfully
- [ ] Application deployment completed successfully
- [ ] Configuration applied correctly
- [ ] Health checks pass
- [ ] Functional tests pass
- [ ] Performance tests pass
- [ ] Security tests pass
- [ ] Monitoring and alerting configured

### Post-Deployment
- [ ] Production health verified
- [ ] Key functionality tested
- [ ] Performance metrics within acceptable range
- [ ] Error rates within acceptable limits
- [ ] Monitoring dashboards updated
- [ ] Documentation updated
- [ ] Stakeholders notified of successful deployment
- [ ] Deployment artifacts archived

## 🔍 Troubleshooting Common Issues

### Deployment Failures

#### Issue: Function App deployment fails
```bash
# Check deployment logs
az functionapp log deployment list --name atomsec-func-sfdc-dev --resource-group rg-atomsec-sfdc-dev

# Check Function App logs
az functionapp log tail --name atomsec-func-sfdc-dev --resource-group rg-atomsec-sfdc-dev
```

#### Issue: Infrastructure deployment fails
```bash
# Check deployment operation details
az deployment group operation list --resource-group rg-atomsec-sfdc-dev --name main

# Get specific operation details
az deployment group operation show --resource-group rg-atomsec-sfdc-dev --name main --operation-id <operation-id>
```

### Configuration Issues

#### Issue: Key Vault access denied
```bash
# Check Function App managed identity
az functionapp identity show --name atomsec-func-sfdc-dev --resource-group rg-atomsec-sfdc-dev

# Check Key Vault access policies
az keyvault show --name kv-atomsec-sfdc-dev --query properties.accessPolicies
```

#### Issue: Database connection fails
```bash
# Test database connectivity
az sql db show-connection-string --server atomsec-sql-dev --name atomsec-dev --client ado.net

# Check firewall rules
az sql server firewall-rule list --server atomsec-sql-dev --resource-group rg-atomsec-sfdc-dev
```

### Performance Issues

#### Issue: High response times
```bash
# Check Application Insights metrics
az monitor metrics list --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-dev/providers/Microsoft.Web/sites/atomsec-func-sfdc-dev" --metric "requests/duration"

# Check Function App scaling
az functionapp show --name atomsec-func-sfdc-dev --resource-group rg-atomsec-sfdc-dev --query "siteConfig.functionAppScaleLimit"
```

## 📞 Emergency Contacts

### Development Team
- **Lead Developer**: <EMAIL>
- **DevOps Engineer**: <EMAIL>
- **Security Team**: <EMAIL>

### Azure Support
- **Azure Support Portal**: https://portal.azure.com/#blade/Microsoft_Azure_Support/HelpAndSupportBlade
- **Emergency Support**: +1-800-MICROSOFT

### Escalation Path
1. Development Team Lead
2. Engineering Manager
3. CTO
4. Azure Support (for infrastructure issues)

---

**Document Version**: 1.0.0  
**Last Updated**: January 24, 2025  
**Next Review**: February 24, 2025