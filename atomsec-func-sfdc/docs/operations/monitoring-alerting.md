# AtomSec SFDC Service Monitoring and Alerting Guide

This guide provides comprehensive information about monitoring, alerting, and observability for the AtomSec SFDC Service.

## 📊 Monitoring Overview

### Monitoring Architecture

```mermaid
graph TB
    subgraph "SFDC Service"
        A[Function App] --> B[Application Insights]
        A --> C[Azure Monitor]
        A --> D[Custom Metrics]
    end
    
    subgraph "Dependencies"
        E[SQL Database] --> F[Database Metrics]
        G[Service Bus] --> H[Queue Metrics]
        I[Key Vault] --> J[Access Metrics]
        K[Salesforce API] --> L[External Metrics]
    end
    
    subgraph "Monitoring Stack"
        B --> M[Log Analytics]
        C --> M
        F --> M
        H --> M
        J --> M
        L --> M
        
        M --> N[Dashboards]
        M --> O[Alerts]
        M --> P[Reports]
    end
    
    subgraph "Notification Channels"
        O --> Q[Email]
        O --> R[Teams]
        O --> S[SMS]
        O --> T[PagerDuty]
    end
```

### Key Monitoring Components

1. **Application Insights**: Application performance monitoring (APM)
2. **Azure Monitor**: Infrastructure and platform metrics
3. **Log Analytics**: Centralized logging and querying
4. **Custom Dashboards**: Business and operational metrics
5. **Alert Rules**: Proactive issue detection
6. **Action Groups**: Notification and response automation

## 🔍 Metrics and KPIs

### Application Performance Metrics

#### Response Time Metrics
```kusto
// Average response time by endpoint
requests
| where timestamp > ago(1h)
| summarize avg_duration = avg(duration) by name
| order by avg_duration desc

// 95th percentile response time
requests
| where timestamp > ago(1h)
| summarize p95_duration = percentile(duration, 95) by bin(timestamp, 5m)
| render timechart
```

#### Throughput Metrics
```kusto
// Requests per minute
requests
| where timestamp > ago(1h)
| summarize requests_per_minute = count() by bin(timestamp, 1m)
| render timechart

// Requests by endpoint
requests
| where timestamp > ago(1h)
| summarize count() by name
| order by count_ desc
```

#### Error Rate Metrics
```kusto
// Overall error rate
requests
| where timestamp > ago(1h)
| summarize 
    total_requests = count(),
    failed_requests = countif(success == false),
    error_rate = (countif(success == false) * 100.0) / count()
| project error_rate

// Error rate by endpoint
requests
| where timestamp > ago(1h)
| summarize 
    total_requests = count(),
    failed_requests = countif(success == false),
    error_rate = (countif(success == false) * 100.0) / count()
by name
| order by error_rate desc
```

### Business Metrics

#### Task Processing Metrics
```kusto
// Task completion rate
customEvents
| where name == "TaskCompleted" or name == "TaskFailed"
| where timestamp > ago(1h)
| summarize 
    completed = countif(name == "TaskCompleted"),
    failed = countif(name == "TaskFailed"),
    completion_rate = (countif(name == "TaskCompleted") * 100.0) / count()
by bin(timestamp, 5m)
| render timechart

// Task processing time by type
customEvents
| where name == "TaskCompleted"
| where timestamp > ago(1h)
| extend task_type = tostring(customDimensions.task_type)
| extend processing_time = todouble(customDimensions.processing_time)
| summarize avg_processing_time = avg(processing_time) by task_type
| order by avg_processing_time desc
```

#### Sequential Task Processing Metrics
```kusto
// Sequential task success rate
customEvents
| where name == "SequentialTaskCompleted"
| where timestamp > ago(1h)
| extend execution_log_id = tostring(customDimensions.execution_log_id)
| extend task_sequence = tostring(customDimensions.task_sequence)
| summarize 
    total_sequences = dcount(execution_log_id),
    completed_sequences = dcountif(execution_log_id, task_sequence == "completed"),
    success_rate = (dcountif(execution_log_id, task_sequence == "completed") * 100.0) / dcount(execution_log_id)
```

### Infrastructure Metrics

#### Function App Metrics
```kusto
// CPU and Memory utilization
performanceCounters
| where timestamp > ago(1h)
| where category == "Process" and counter == "% Processor Time"
| summarize avg_cpu = avg(value) by bin(timestamp, 5m)
| render timechart

// Memory usage
performanceCounters
| where timestamp > ago(1h)
| where category == "Memory" and counter == "Available Bytes"
| summarize avg_memory = avg(value) by bin(timestamp, 5m)
| render timechart
```

#### Database Metrics
```kusto
// Database connection metrics
dependencies
| where type == "SQL"
| where timestamp > ago(1h)
| summarize 
    avg_duration = avg(duration),
    success_rate = (countif(success == true) * 100.0) / count()
by bin(timestamp, 5m)
| render timechart

// Database query performance
dependencies
| where type == "SQL"
| where timestamp > ago(1h)
| summarize 
    avg_duration = avg(duration),
    p95_duration = percentile(duration, 95)
by data
| order by avg_duration desc
```

## 🚨 Alert Configuration

### Critical Alerts (P1)

#### Service Availability Alert
```bash
az monitor metrics alert create \
  --name "SFDC Service Down" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --condition "avg requests/count < 1" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --severity 0 \
  --description "SFDC service is not receiving requests" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/critical-alerts"
```

#### High Error Rate Alert
```bash
az monitor metrics alert create \
  --name "SFDC High Error Rate" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --condition "avg exceptions/server > 10" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --severity 1 \
  --description "SFDC service error rate is above threshold" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/critical-alerts"
```

### High Priority Alerts (P2)

#### Performance Degradation Alert
```bash
az monitor metrics alert create \
  --name "SFDC High Response Time" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --condition "avg requests/duration > 5000" \
  --window-size 10m \
  --evaluation-frequency 5m \
  --severity 2 \
  --description "SFDC service response time is above threshold" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/performance-alerts"
```

#### Database Connection Issues
```bash
az monitor metrics alert create \
  --name "SFDC Database Connection Failures" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Sql/servers/atomsec-sql-prod/databases/atomsec-prod" \
  --condition "avg connection_failed > 5" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --severity 2 \
  --description "Database connection failures detected" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/infrastructure-alerts"
```

### Medium Priority Alerts (P3)

#### Task Processing Delays
```bash
# Custom metric alert for task processing delays
az monitor scheduled-query-rule create \
  --name "SFDC Task Processing Delays" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/components/atomsec-sfdc-prod-insights" \
  --condition-query "customEvents | where name == 'TaskProcessingDelay' | where timestamp > ago(15m) | summarize count()" \
  --condition-threshold 5 \
  --condition-operator "GreaterThan" \
  --evaluation-frequency 5m \
  --window-size 15m \
  --severity 3 \
  --description "Task processing delays detected" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/operational-alerts"
```

#### Resource Utilization Alert
```bash
az monitor metrics alert create \
  --name "SFDC High CPU Usage" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --condition "avg CpuPercentage > 80" \
  --window-size 15m \
  --evaluation-frequency 5m \
  --severity 3 \
  --description "CPU usage is above 80%" \
  --action-group-ids "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/microsoft.insights/actionGroups/resource-alerts"
```

## 📧 Action Groups and Notifications

### Critical Alerts Action Group
```bash
az monitor action-group create \
  --name critical-alerts \
  --resource-group rg-atomsec-sfdc-prod \
  --short-name critical \
  --email-receivers \
    name=oncall-engineer \
    email=<EMAIL> \
    use-common-alert-schema=true \
  --sms-receivers \
    name=oncall-sms \
    country-code=1 \
    phone-number=********** \
  --webhook-receivers \
    name=pagerduty \
    service-uri=https://events.pagerduty.com/integration/your-integration-key/enqueue \
    use-common-alert-schema=true
```

### Performance Alerts Action Group
```bash
az monitor action-group create \
  --name performance-alerts \
  --resource-group rg-atomsec-sfdc-prod \
  --short-name perf \
  --email-receivers \
    name=dev-team \
    email=<EMAIL> \
    use-common-alert-schema=true \
  --azure-function-receivers \
    name=auto-scale \
    function-app-resource-id="/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-auto-scale" \
    function-name=AutoScaleFunction \
    http-trigger-url=https://atomsec-auto-scale.azurewebsites.net/api/AutoScale \
    use-common-alert-schema=true
```

## 📈 Dashboards

### Operational Dashboard

Create a comprehensive operational dashboard in Azure Portal or Grafana:

#### Key Widgets
1. **Service Health Overview**
   - Current status (healthy/degraded/down)
   - Uptime percentage
   - Last incident timestamp

2. **Performance Metrics**
   - Average response time (last 1h, 24h)
   - 95th percentile response time
   - Requests per minute
   - Error rate percentage

3. **Business Metrics**
   - Tasks processed (last 1h, 24h)
   - Task success rate
   - Sequential task completion rate
   - Average task processing time

4. **Infrastructure Metrics**
   - CPU utilization
   - Memory usage
   - Database connection pool status
   - Service Bus queue depth

### Executive Dashboard

Create a high-level dashboard for executives:

#### Key Widgets
1. **Service Availability**
   - Monthly uptime percentage
   - Mean time to recovery (MTTR)
   - Number of incidents

2. **Business Impact**
   - Total tasks processed
   - Customer satisfaction metrics
   - Revenue impact of downtime

3. **Performance Trends**
   - Response time trends (monthly)
   - Throughput trends (monthly)
   - Error rate trends (monthly)

### Custom Dashboard Configuration

```json
{
  "dashboard": {
    "id": "sfdc-operational-dashboard",
    "title": "AtomSec SFDC Service - Operational Dashboard",
    "tags": ["sfdc", "operational", "monitoring"],
    "timezone": "UTC",
    "panels": [
      {
        "id": 1,
        "title": "Service Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"atomsec-sfdc\"}",
            "legendFormat": "Service Status"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      }
    ]
  }
}
```

## 🔔 Alert Management

### Alert Severity Levels

| Severity | Response Time | Escalation | Examples |
|----------|---------------|------------|----------|
| P0 (Critical) | 5 minutes | Immediate | Service down, data loss |
| P1 (High) | 15 minutes | 30 minutes | High error rate, security breach |
| P2 (Medium) | 1 hour | 2 hours | Performance degradation, dependency issues |
| P3 (Low) | 4 hours | Next business day | Resource warnings, minor issues |

### Alert Lifecycle

1. **Alert Triggered**
   - Condition met based on metrics
   - Alert fired to action group
   - Incident created in tracking system

2. **Initial Response**
   - On-call engineer notified
   - Initial assessment performed
   - Severity confirmed or adjusted

3. **Investigation**
   - Root cause analysis
   - Impact assessment
   - Mitigation strategy determined

4. **Resolution**
   - Fix implemented
   - Service restored
   - Alert condition cleared

5. **Post-Incident**
   - Post-mortem conducted
   - Preventive measures identified
   - Documentation updated

### Alert Suppression

#### Maintenance Windows
```bash
# Create maintenance window
az monitor scheduled-query-rule update \
  --name "SFDC High Error Rate" \
  --resource-group rg-atomsec-sfdc-prod \
  --enabled false

# Re-enable after maintenance
az monitor scheduled-query-rule update \
  --name "SFDC High Error Rate" \
  --resource-group rg-atomsec-sfdc-prod \
  --enabled true
```

#### Dynamic Thresholds
```bash
# Use dynamic thresholds for adaptive alerting
az monitor metrics alert create \
  --name "SFDC Dynamic Response Time" \
  --resource-group rg-atomsec-sfdc-prod \
  --scopes "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --condition "avg requests/duration > dynamic High 2 of 4" \
  --window-size 5m \
  --evaluation-frequency 1m \
  --severity 2
```

## 📊 Reporting and Analytics

### Daily Reports

#### Service Health Report
```kusto
// Daily service health summary
let startTime = ago(1d);
let endTime = now();
requests
| where timestamp between (startTime .. endTime)
| summarize 
    total_requests = count(),
    successful_requests = countif(success == true),
    failed_requests = countif(success == false),
    avg_response_time = avg(duration),
    p95_response_time = percentile(duration, 95),
    availability = (countif(success == true) * 100.0) / count()
| extend report_date = format_datetime(endTime, 'yyyy-MM-dd')
```

#### Task Processing Report
```kusto
// Daily task processing summary
customEvents
| where name in ("TaskCompleted", "TaskFailed", "TaskStarted")
| where timestamp > ago(1d)
| extend task_type = tostring(customDimensions.task_type)
| summarize 
    tasks_started = countif(name == "TaskStarted"),
    tasks_completed = countif(name == "TaskCompleted"),
    tasks_failed = countif(name == "TaskFailed"),
    success_rate = (countif(name == "TaskCompleted") * 100.0) / countif(name == "TaskStarted")
by task_type
| order by tasks_started desc
```

### Weekly Reports

#### Performance Trend Report
```kusto
// Weekly performance trends
requests
| where timestamp > ago(7d)
| summarize 
    avg_response_time = avg(duration),
    p95_response_time = percentile(duration, 95),
    requests_per_hour = count() / 24.0,
    error_rate = (countif(success == false) * 100.0) / count()
by bin(timestamp, 1d)
| order by timestamp asc
```

### Monthly Reports

#### Business Impact Report
```kusto
// Monthly business metrics
let monthStart = startofmonth(ago(30d));
let monthEnd = endofmonth(ago(30d));
customEvents
| where timestamp between (monthStart .. monthEnd)
| where name == "TaskCompleted"
| extend org_id = tostring(customDimensions.org_id)
| summarize 
    total_tasks_processed = count(),
    unique_organizations = dcount(org_id),
    avg_tasks_per_org = count() / dcount(org_id)
| extend report_month = format_datetime(monthStart, 'yyyy-MM')
```

## 🔧 Monitoring Best Practices

### Metric Collection
1. **Use structured logging** with consistent format
2. **Implement correlation IDs** for request tracking
3. **Collect business metrics** alongside technical metrics
4. **Use appropriate sampling** to manage costs
5. **Tag resources consistently** for better organization

### Alert Design
1. **Set meaningful thresholds** based on baseline performance
2. **Avoid alert fatigue** with proper severity levels
3. **Use dynamic thresholds** for adaptive alerting
4. **Implement alert suppression** for maintenance windows
5. **Test alert rules** regularly to ensure they work

### Dashboard Design
1. **Focus on actionable metrics** that drive decisions
2. **Use appropriate visualizations** for different data types
3. **Organize by audience** (operational vs. executive)
4. **Keep dashboards simple** and easy to understand
5. **Update regularly** based on feedback and needs

### Performance Optimization
1. **Monitor query performance** and optimize slow queries
2. **Use efficient data retention** policies
3. **Implement proper indexing** for log analytics
4. **Cache frequently accessed** dashboard data
5. **Regular cleanup** of old metrics and logs

---

**Document Version**: 1.0.0  
**Last Updated**: January 24, 2025  
**Next Review**: February 24, 2025