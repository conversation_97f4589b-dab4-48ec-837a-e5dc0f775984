# AtomSec SFDC Service Troubleshooting Guide

This guide provides comprehensive troubleshooting procedures for common issues encountered with the AtomSec SFDC Service.

## 🚨 Emergency Response Procedures

### Immediate Actions for Critical Issues

1. **Service Down/Unavailable**
   - Check Azure Service Health: https://status.azure.com/
   - Verify Function App status in Azure Portal
   - Check Application Insights for error patterns
   - Initiate rollback if recent deployment caused issue

2. **High Error Rate (>5%)**
   - Check Application Insights error logs
   - Verify database connectivity
   - Check Service Bus queue health
   - Monitor resource utilization

3. **Performance Degradation**
   - Check Application Insights performance metrics
   - Verify database query performance
   - Check Function App scaling metrics
   - Monitor external service dependencies

## 🔍 Diagnostic Tools and Commands

### Azure CLI Diagnostics

```bash
# Check Function App status
az functionapp show --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod --query "state"

# Get Function App logs
az functionapp log tail --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod

# Check deployment status
az functionapp deployment list-publishing-profiles --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod

# Monitor metrics
az monitor metrics list \
  --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --metric "requests/count" \
  --start-time 2025-01-24T09:00:00Z \
  --end-time 2025-01-24T10:00:00Z
```

### Application Insights Queries

```kusto
// Error rate over time
requests
| where timestamp > ago(1h)
| summarize 
    total_requests = count(),
    failed_requests = countif(success == false),
    error_rate = (countif(success == false) * 100.0) / count()
by bin(timestamp, 5m)
| render timechart

// Top errors by count
exceptions
| where timestamp > ago(1h)
| summarize count() by type, outerMessage
| order by count_ desc
| take 10

// Performance metrics
requests
| where timestamp > ago(1h)
| summarize 
    avg_duration = avg(duration),
    p95_duration = percentile(duration, 95),
    p99_duration = percentile(duration, 99)
by bin(timestamp, 5m)
| render timechart

// Dependency failures
dependencies
| where timestamp > ago(1h)
| where success == false
| summarize count() by name, type, resultCode
| order by count_ desc
```

### Health Check Diagnostics

```bash
# Basic health check
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health" | jq .

# Detailed health check
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health/detailed" | jq .

# Check specific dependency
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health/detailed" | jq '.checks.database'
```

## 🐛 Common Issues and Solutions

### 1. Authentication and Authorization Issues

#### Issue: 401 Unauthorized Errors

**Symptoms:**
- API requests return 401 status code
- "Authentication required" error messages
- JWT token validation failures

**Diagnosis:**
```bash
# Check JWT token validity
curl -H "Authorization: Bearer $TOKEN" "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health"

# Verify token expiration
echo $TOKEN | cut -d. -f2 | base64 -d | jq .exp
```

**Solutions:**
1. **Expired Token**: Refresh the JWT token
2. **Invalid Token**: Verify token format and signing key
3. **Missing Authorization Header**: Ensure proper header format
4. **Key Vault Issues**: Check managed identity access to Key Vault

```bash
# Fix Key Vault access
az keyvault set-policy \
  --name kv-atomsec-sfdc-prod \
  --object-id $(az functionapp identity show --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod --query principalId -o tsv) \
  --secret-permissions get list
```

#### Issue: 403 Forbidden Errors

**Symptoms:**
- API requests return 403 status code
- "Access denied" error messages
- User has valid token but insufficient permissions

**Diagnosis:**
```bash
# Check user permissions in token
echo $TOKEN | cut -d. -f2 | base64 -d | jq .permissions

# Verify RBAC configuration
az role assignment list --assignee $USER_OBJECT_ID --scope $RESOURCE_SCOPE
```

**Solutions:**
1. **Insufficient Permissions**: Grant required permissions to user
2. **RBAC Misconfiguration**: Update role assignments
3. **Resource-Level Permissions**: Check organization-specific access

### 2. Database Connectivity Issues

#### Issue: Database Connection Failures

**Symptoms:**
- "Database service unavailable" in health checks
- SQL connection timeout errors
- Task creation/retrieval failures

**Diagnosis:**
```bash
# Test database connectivity
az sql db show-connection-string --server atomsec-sql-prod --name atomsec-prod --client ado.net

# Check firewall rules
az sql server firewall-rule list --server atomsec-sql-prod --resource-group rg-atomsec-sfdc-prod

# Monitor database metrics
az monitor metrics list \
  --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Sql/servers/atomsec-sql-prod/databases/atomsec-prod" \
  --metric "connection_failed"
```

**Solutions:**
1. **Firewall Issues**: Add Function App IP to SQL firewall rules
2. **Connection String**: Verify connection string in Key Vault
3. **Managed Identity**: Ensure Function App has SQL access
4. **Database Performance**: Check DTU/CPU usage and scale if needed

```bash
# Add firewall rule for Function App
az sql server firewall-rule create \
  --server atomsec-sql-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --name "FunctionAppAccess" \
  --start-ip-address $FUNCTION_APP_IP \
  --end-ip-address $FUNCTION_APP_IP

# Grant SQL access to managed identity
az sql server ad-admin set \
  --server atomsec-sql-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --display-name atomsec-func-sfdc-prod \
  --object-id $(az functionapp identity show --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod --query principalId -o tsv)
```

### 3. Service Bus Issues

#### Issue: Message Processing Failures

**Symptoms:**
- Tasks stuck in "pending" status
- Service Bus queue growing without processing
- "Service Bus unavailable" in health checks

**Diagnosis:**
```bash
# Check Service Bus namespace status
az servicebus namespace show --name atomsec-sb-prod --resource-group rg-atomsec-sfdc-prod --query "status"

# Check queue metrics
az servicebus queue show --namespace-name atomsec-sb-prod --name task-queue --resource-group rg-atomsec-sfdc-prod --query "messageCount"

# Check dead letter queue
az servicebus queue show --namespace-name atomsec-sb-prod --name task-queue --resource-group rg-atomsec-sfdc-prod --query "deadLetterMessageCount"
```

**Solutions:**
1. **Connection String Issues**: Verify Service Bus connection string
2. **Queue Permissions**: Check Function App access to Service Bus
3. **Message Format**: Verify message serialization/deserialization
4. **Dead Letter Messages**: Process or clear dead letter queue

```bash
# Grant Service Bus access
az role assignment create \
  --assignee $(az functionapp identity show --name atomsec-func-sfdc-prod --resource-group rg-atomsec-sfdc-prod --query principalId -o tsv) \
  --role "Azure Service Bus Data Receiver" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.ServiceBus/namespaces/atomsec-sb-prod"

# Clear dead letter queue (if needed)
python scripts/clear_poison_queue.py --queue-name task-queue --namespace atomsec-sb-prod
```

### 4. Salesforce Integration Issues

#### Issue: Salesforce API Authentication Failures

**Symptoms:**
- "Invalid Salesforce credentials" errors
- Tasks failing at authentication step
- Salesforce API rate limit errors

**Diagnosis:**
```bash
# Test Salesforce connection
curl -H "Authorization: Bearer $SF_ACCESS_TOKEN" \
  "https://your-org.my.salesforce.com/services/data/v59.0/sobjects/"

# Check token expiration
curl -H "Authorization: Bearer $SF_ACCESS_TOKEN" \
  "https://your-org.my.salesforce.com/services/oauth2/tokeninfo"
```

**Solutions:**
1. **Expired Token**: Refresh Salesforce access token
2. **Invalid Credentials**: Verify client ID and secret in Key Vault
3. **Rate Limiting**: Implement exponential backoff and retry logic
4. **API Version**: Ensure compatible Salesforce API version

```bash
# Update Salesforce credentials in Key Vault
az keyvault secret set \
  --vault-name kv-atomsec-sfdc-prod \
  --name "SalesforceClientId" \
  --value "your-client-id"

az keyvault secret set \
  --vault-name kv-atomsec-sfdc-prod \
  --name "SalesforceClientSecret" \
  --value "your-client-secret"
```

### 5. Performance Issues

#### Issue: High Response Times

**Symptoms:**
- API responses taking >5 seconds
- Timeout errors
- Poor user experience

**Diagnosis:**
```bash
# Check response time metrics
curl -w "@curl-format.txt" -s -o /dev/null "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health"

# Monitor Function App performance
az monitor metrics list \
  --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --metric "requests/duration"
```

**Solutions:**
1. **Database Query Optimization**: Optimize slow queries
2. **Connection Pooling**: Implement proper connection pooling
3. **Caching**: Add caching for frequently accessed data
4. **Function App Scaling**: Increase instance count or upgrade plan

```bash
# Scale Function App
az functionapp plan update \
  --name asp-atomsec-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --sku P1V2

# Update Function App settings for performance
az functionapp config appsettings set \
  --name atomsec-func-sfdc-prod \
  --resource-group rg-atomsec-sfdc-prod \
  --settings \
    "FUNCTIONS_WORKER_PROCESS_COUNT=4" \
    "PYTHON_THREADPOOL_THREAD_COUNT=8"
```

#### Issue: Memory Issues

**Symptoms:**
- Out of memory errors
- Function App restarts
- Performance degradation over time

**Diagnosis:**
```bash
# Check memory usage
az monitor metrics list \
  --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/rg-atomsec-sfdc-prod/providers/Microsoft.Web/sites/atomsec-func-sfdc-prod" \
  --metric "memoryWorkingSet"

# Check for memory leaks in Application Insights
```

**Solutions:**
1. **Memory Leaks**: Review code for unclosed connections/resources
2. **Large Objects**: Optimize data structures and processing
3. **Garbage Collection**: Tune Python garbage collection settings
4. **Instance Size**: Upgrade to higher memory Function App plan

### 6. Sequential Task Processing Issues

#### Issue: Tasks Not Processing in Sequence

**Symptoms:**
- Tasks executing out of order
- execution_log_id not properly tracked
- Incomplete task sequences

**Diagnosis:**
```bash
# Check task sequence for execution log
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/tasks?org_id=$ORG_ID&execution_log_id=$EXECUTION_LOG_ID" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {task_type, status, created_at}'

# Verify task coordination service
curl -s "https://atomsec-func-sfdc-prod.azurewebsites.net/api/health/detailed" | jq '.checks.task_coordination'
```

**Solutions:**
1. **Task Coordination**: Verify task coordination service is running
2. **Execution Log Validation**: Ensure proper execution_log_id validation
3. **Task Dependencies**: Check task dependency configuration
4. **Queue Processing**: Verify Service Bus message ordering

### 7. Monitoring and Alerting Issues

#### Issue: Missing or Incorrect Alerts

**Symptoms:**
- No alerts for known issues
- False positive alerts
- Alert fatigue

**Diagnosis:**
```bash
# Check alert rules
az monitor metrics alert list --resource-group rg-atomsec-sfdc-prod

# Check alert rule configuration
az monitor metrics alert show --name "SFDC High Error Rate" --resource-group rg-atomsec-sfdc-prod

# Check action group configuration
az monitor action-group show --name sfdc-alerts --resource-group rg-atomsec-sfdc-prod
```

**Solutions:**
1. **Alert Thresholds**: Adjust alert thresholds based on baseline metrics
2. **Alert Rules**: Create missing alert rules for critical metrics
3. **Action Groups**: Verify notification channels are working
4. **Alert Suppression**: Implement alert suppression for maintenance windows

## 🔧 Maintenance Procedures

### Regular Maintenance Tasks

#### Weekly Tasks
- [ ] Review Application Insights error logs
- [ ] Check database performance metrics
- [ ] Verify backup completion
- [ ] Review security alerts
- [ ] Update dependency versions (if needed)

#### Monthly Tasks
- [ ] Review and optimize database queries
- [ ] Analyze performance trends
- [ ] Update documentation
- [ ] Review and update alert thresholds
- [ ] Conduct disaster recovery test

#### Quarterly Tasks
- [ ] Security assessment and penetration testing
- [ ] Capacity planning review
- [ ] Architecture review
- [ ] Update runbooks and procedures
- [ ] Team training on new features/procedures

### Preventive Measures

#### Code Quality
- Implement comprehensive unit and integration tests
- Use static code analysis tools
- Regular code reviews
- Performance profiling

#### Infrastructure
- Regular security updates
- Automated backup verification
- Disaster recovery testing
- Capacity monitoring and planning

#### Monitoring
- Comprehensive logging and monitoring
- Proactive alerting
- Regular review of metrics and thresholds
- Performance baseline establishment

## 📞 Escalation Procedures

### Level 1: Development Team
- **Response Time**: 15 minutes during business hours
- **Scope**: Code issues, configuration problems, minor performance issues
- **Contact**: <EMAIL>

### Level 2: DevOps/Infrastructure Team
- **Response Time**: 30 minutes during business hours
- **Scope**: Infrastructure issues, deployment problems, major performance issues
- **Contact**: <EMAIL>

### Level 3: Engineering Management
- **Response Time**: 1 hour during business hours
- **Scope**: Critical system failures, security incidents, major outages
- **Contact**: <EMAIL>

### Level 4: Executive/External Support
- **Response Time**: 2 hours
- **Scope**: Business-critical outages, security breaches, regulatory issues
- **Contact**: <EMAIL>, Azure Support

## 📋 Incident Response Checklist

### Immediate Response (0-15 minutes)
- [ ] Acknowledge the incident
- [ ] Assess severity and impact
- [ ] Notify appropriate team members
- [ ] Begin initial investigation
- [ ] Document incident start time

### Investigation (15-60 minutes)
- [ ] Gather diagnostic information
- [ ] Identify root cause
- [ ] Determine fix strategy
- [ ] Estimate resolution time
- [ ] Update stakeholders

### Resolution (Variable)
- [ ] Implement fix
- [ ] Verify resolution
- [ ] Monitor for stability
- [ ] Update documentation
- [ ] Conduct post-incident review

### Post-Incident (24-48 hours)
- [ ] Complete incident report
- [ ] Identify preventive measures
- [ ] Update procedures and documentation
- [ ] Implement improvements
- [ ] Share lessons learned

## 📊 Key Performance Indicators (KPIs)

### Service Level Objectives (SLOs)
- **Availability**: 99.9% uptime
- **Response Time**: <2 seconds for 95% of requests
- **Error Rate**: <1% for all requests
- **Task Processing**: 95% of tasks complete within SLA

### Monitoring Metrics
- Request count and response times
- Error rates by endpoint
- Database query performance
- Service Bus queue depth
- Memory and CPU utilization
- Dependency health status

---

**Document Version**: 1.0.0  
**Last Updated**: January 24, 2025  
**Next Review**: February 24, 2025