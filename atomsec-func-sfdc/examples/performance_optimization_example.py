"""
Performance Optimization Framework Integration Example

This example demonstrates how to integrate and use the performance optimization
components including advanced caching, auto-scaling, and performance profiling.
"""

import time
import logging
from typing import Dict, Any, List
from src.shared.advanced_caching_service import get_cache_manager, cache_decorator
from src.shared.auto_scaling_service import get_auto_scaling_service, ScalingTrigger
from src.shared.performance_optimization_framework import get_performance_framework
from src.shared.cache_performance_analytics import get_cache_analytics, MetricType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceOptimizedService:
    """Example service with integrated performance optimization"""
    
    def __init__(self):
        """Initialize service with performance optimization components"""
        # Initialize cache manager with Redis support
        cache_config = {
            'l1_max_size': 1000,
            'l1_ttl': 300,
            'l2_ttl': 3600,
            'warming_enabled': True,
            'redis': {
                'enabled': False,  # Set to True if Redis is available
                'host': 'localhost',
                'port': 6379
            }
        }
        self.cache_manager = get_cache_manager(cache_config)
        
        # Initialize auto-scaling service
        scaling_config = {
            'initial_instances': 2,
            'evaluation_interval': 60,
            'cost_optimization': True,
            'load_balancing_strategy': 'round_robin'
        }
        self.auto_scaling = get_auto_scaling_service(scaling_config)
        
        # Initialize performance framework
        perf_config = {
            'profiler': {
                'enabled': True,
                'sampling_rate': 1.0,
                'memory_profiling': True
            }
        }
        self.profiler, self.query_optimizer, self.resource_optimizer = get_performance_framework(perf_config)
        
        # Initialize cache analytics
        analytics_config = {
            'max_metrics': 5000,
            'analysis_window': 1800,
            'anomaly_detection': True
        }
        self.cache_analytics = get_cache_analytics(analytics_config)
        
        logger.info("Performance optimized service initialized")
    
    @cache_decorator(cache_manager=None, ttl=600, priority=2)  # Will use global cache manager
    def expensive_computation(self, input_data: str) -> Dict[str, Any]:
        """Example expensive computation with caching"""
        # Simulate expensive computation
        time.sleep(0.1)
        
        result = {
            'processed_data': f"processed_{input_data}",
            'computation_time': 0.1,
            'timestamp': time.time()
        }
        
        return result
    
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process request with performance monitoring"""
        start_time = time.time()
        
        try:
            # Record scaling metrics
            self.auto_scaling.record_metric(ScalingTrigger.REQUEST_RATE, 1.0)
            
            # Process request (example)
            result = self.expensive_computation(request_data.get('input', 'default'))
            
            # Record performance metrics
            duration = time.time() - start_time
            self.auto_scaling.record_metric(ScalingTrigger.RESPONSE_TIME, duration)
            
            # Record cache analytics
            self.cache_analytics.record_metric(MetricType.RESPONSE_TIME, duration)
            
            return {
                'success': True,
                'result': result,
                'processing_time': duration
            }
            
        except Exception as e:
            # Record error metrics
            self.auto_scaling.record_metric(ScalingTrigger.ERROR_RATE, 1.0)
            logger.error(f"Request processing error: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def simulate_load(self, num_requests: int = 100):
        """Simulate load to demonstrate performance optimization"""
        logger.info(f"Simulating load with {num_requests} requests")
        
        results = []
        for i in range(num_requests):
            request_data = {'input': f'request_{i}'}
            result = self.process_request(request_data)
            results.append(result)
            
            # Simulate varying load
            if i % 10 == 0:
                time.sleep(0.01)  # Brief pause every 10 requests
        
        # Analyze results
        successful_requests = sum(1 for r in results if r['success'])
        avg_response_time = sum(r['processing_time'] for r in results) / len(results)
        
        logger.info(f"Load simulation completed:")
        logger.info(f"  - Successful requests: {successful_requests}/{num_requests}")
        logger.info(f"  - Average response time: {avg_response_time:.3f}s")
        
        return results
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        try:
            # Get cache performance
            cache_analytics = self.cache_manager.get_performance_analytics()
            cache_recommendations = self.cache_manager.get_optimization_recommendations()
            
            # Get scaling metrics
            scaling_metrics = self.auto_scaling.get_scaling_metrics()
            
            # Get profiler summary
            profiler_summary = self.profiler.get_performance_summary()
            profiler_recommendations = self.profiler.get_optimization_recommendations()
            
            # Get resource optimization report
            resource_report = self.resource_optimizer.get_resource_optimization_report()
            
            # Get cache analytics summary
            analytics_summary = self.cache_analytics.get_performance_summary()
            analytics_recommendations = self.cache_analytics.generate_optimization_recommendations()
            
            report = {
                'timestamp': time.time(),
                'cache_performance': {
                    'analytics': cache_analytics,
                    'recommendations': cache_recommendations
                },
                'scaling_performance': {
                    'metrics': scaling_metrics,
                    'current_instances': self.auto_scaling.current_instances
                },
                'profiler_performance': {
                    'summary': profiler_summary,
                    'recommendations': [
                        {
                            'category': r.category,
                            'priority': r.priority,
                            'title': r.title,
                            'description': r.description
                        } for r in profiler_recommendations
                    ]
                },
                'resource_optimization': resource_report,
                'cache_analytics': {
                    'summary': analytics_summary,
                    'recommendations': [
                        {
                            'priority': r.priority,
                            'category': r.category,
                            'title': r.title,
                            'description': r.description
                        } for r in analytics_recommendations
                    ]
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}
    
    def warm_cache_with_common_data(self):
        """Warm cache with commonly accessed data"""
        logger.info("Warming cache with common data")
        
        common_data = {
            'user_profile_1': {'name': 'John Doe', 'role': 'admin'},
            'user_profile_2': {'name': 'Jane Smith', 'role': 'user'},
            'config_settings': {'timeout': 30, 'retries': 3},
            'feature_flags': {'new_ui': True, 'beta_features': False}
        }
        
        self.cache_manager.warm_cache(common_data)
        logger.info(f"Cache warmed with {len(common_data)} items")
    
    def demonstrate_optimization_features(self):
        """Demonstrate various optimization features"""
        logger.info("=== Performance Optimization Demonstration ===")
        
        # 1. Warm cache
        self.warm_cache_with_common_data()
        
        # 2. Simulate load
        results = self.simulate_load(50)
        
        # 3. Wait for metrics collection
        time.sleep(2)
        
        # 4. Generate performance report
        report = self.get_performance_report()
        
        # 5. Display key metrics
        logger.info("\n=== Performance Report Summary ===")
        
        # Cache performance
        cache_perf = report.get('cache_performance', {})
        cache_analytics = cache_perf.get('analytics', {})
        if 'hit_rate' in cache_analytics:
            logger.info(f"Cache hit rate: {cache_analytics['hit_rate']:.2%}")
        
        # Scaling performance
        scaling_perf = report.get('scaling_performance', {})
        scaling_metrics = scaling_perf.get('metrics', {})
        if 'current_instances' in scaling_metrics:
            logger.info(f"Current instances: {scaling_metrics['current_instances']}")
        
        # Resource usage
        resource_opt = report.get('resource_optimization', {})
        current_status = resource_opt.get('current_status', {})
        if 'memory_percent' in current_status:
            logger.info(f"Memory usage: {current_status['memory_percent']:.1f}%")
        if 'cpu_percent' in current_status:
            logger.info(f"CPU usage: {current_status['cpu_percent']:.1f}%")
        
        # Recommendations
        logger.info("\n=== Optimization Recommendations ===")
        
        # Cache recommendations
        cache_recs = cache_perf.get('recommendations', [])
        for rec in cache_recs[:3]:  # Show top 3
            logger.info(f"Cache: {rec}")
        
        # Profiler recommendations
        profiler_recs = report.get('profiler_performance', {}).get('recommendations', [])
        for rec in profiler_recs[:3]:  # Show top 3
            logger.info(f"Performance: {rec['title']} - {rec['description']}")
        
        return report

def main():
    """Main demonstration function"""
    try:
        # Create performance optimized service
        service = PerformanceOptimizedService()
        
        # Run demonstration
        report = service.demonstrate_optimization_features()
        
        # Save report (optional)
        import json
        with open('performance_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info("Performance optimization demonstration completed")
        logger.info("Full report saved to performance_report.json")
        
    except Exception as e:
        logger.error(f"Demonstration error: {e}")

if __name__ == "__main__":
    main()