"""
Configuration Management Example

This example demonstrates how to use the enhanced configuration management system
with feature flags, task sequence configuration, and execution log coordination.
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the configuration management components
from src.shared.enhanced_configuration_manager import (
    get_configuration_manager, 
    get_config, 
    get_typed_config
)
from src.shared.feature_flag_service import (
    get_feature_flag_service, 
    is_feature_enabled, 
    UserContext
)
from src.shared.task_sequence_configuration import (
    get_task_sequence_configuration,
    TaskType
)
from src.shared.execution_log_coordination_service import (
    get_execution_log_coordination_service
)

def demonstrate_configuration_management():
    """Demonstrate enhanced configuration management"""
    print("\n=== Enhanced Configuration Management Demo ===")
    
    # Get configuration manager
    config_manager = get_configuration_manager()
    
    # Get configuration info
    config_info = config_manager.get_configuration_info()
    print(f"Configuration loaded from {config_info['sources']} with {config_info['total_keys']} keys")
    
    # Get typed configuration values
    jwt_expire_minutes = get_typed_config("security.jwt_access_token_expire_minutes", int, 30)
    enable_caching = get_typed_config("performance.enable_caching", bool, True)
    log_level = get_config("monitoring.log_level", "INFO")
    
    print(f"JWT expiration: {jwt_expire_minutes} minutes")
    print(f"Caching enabled: {enable_caching}")
    print(f"Log level: {log_level}")
    
    # Get structured configuration
    security_config = config_manager.get_security_config()
    performance_config = config_manager.get_performance_config()
    
    print(f"Security config - Rate limit: {security_config.rate_limit_requests_per_minute} RPM")
    print(f"Performance config - Connection pool size: {performance_config.connection_pool_size}")

def demonstrate_feature_flags():
    """Demonstrate feature flag service"""
    print("\n=== Feature Flag Service Demo ===")
    
    # Get feature flag service
    feature_service = get_feature_flag_service()
    
    # Create user context
    user_context = UserContext(
        user_id="test_user",
        organization_id="test_org",
        role="admin",
        attributes={"beta_tester": True}
    )
    
    # Check feature flags
    features_to_check = [
        "enhanced_security",
        "performance_monitoring",
        "advanced_caching",
        "new_ui_components",
        "beta_features"
    ]
    
    for feature in features_to_check:
        enabled = is_feature_enabled(feature, user_context)
        print(f"Feature '{feature}': {'ENABLED' if enabled else 'DISABLED'}")
    
    # Get feature configuration
    feature_config = feature_service.get_feature_configuration("advanced_caching")
    if feature_config:
        print(f"Advanced caching config: {feature_config['description']}")
    
    # Get usage metrics
    usage_metrics = feature_service.get_usage_metrics()
    print(f"Feature flag usage metrics for {len(usage_metrics)} features")

def demonstrate_task_sequence_configuration():
    """Demonstrate task sequence configuration"""
    print("\n=== Task Sequence Configuration Demo ===")
    
    # Get task sequence configuration
    task_config = get_task_sequence_configuration()
    
    # Get available task types and sequences
    task_types = task_config.get_all_task_types()
    sequence_names = task_config.get_all_sequence_names()
    
    print(f"Available task types: {[t.value for t in task_types]}")
    print(f"Available sequences: {sequence_names}")
    
    # Get task definition
    auth_task = task_config.get_task_definition(TaskType.SFDC_AUTHENTICATE)
    if auth_task:
        print(f"SFDC Auth task: {auth_task.name}")
        print(f"  Parameters: {[p.name for p in auth_task.parameters]}")
        print(f"  Timeout: {auth_task.timeout_seconds}s")
        print(f"  Retry attempts: {auth_task.retry_attempts}")
    
    # Get sequence definition
    standard_sequence = task_config.get_sequence_definition("standard_sfdc_scan")
    if standard_sequence:
        print(f"Standard SFDC scan sequence: {standard_sequence.name}")
        print(f"  Tasks: {[t.task_type.value for t in standard_sequence.tasks]}")
        print(f"  Timeout: {standard_sequence.execution_timeout_seconds}s")
    
    # Validate execution log ID
    test_execution_id = "12345678-1234-1234-1234-123456789abc"
    validation_result = task_config.validate_execution_log_id(test_execution_id)
    print(f"Execution log ID validation: {validation_result.message}")
    
    # Validate task parameters
    test_parameters = {
        "execution_log_id": test_execution_id,
        "client_id": "test_client_id",
        "client_secret": "test_client_secret_123",
        "username": "<EMAIL>"
    }
    
    param_results = task_config.validate_task_parameters(TaskType.SFDC_AUTHENTICATE, test_parameters)
    print(f"Parameter validation results: {len(param_results)} issues found")
    for result in param_results:
        if not result.is_valid:
            print(f"  ERROR: {result.message}")

def demonstrate_execution_log_coordination():
    """Demonstrate execution log coordination service"""
    print("\n=== Execution Log Coordination Demo ===")
    
    # Get coordination service
    coord_service = get_execution_log_coordination_service()
    
    # Create execution log
    execution_log_id = coord_service.create_execution_log(
        sequence_name="standard_sfdc_scan",
        user_id="test_user",
        organization_id="test_org",
        request_source="api",
        metadata={"test": True}
    )
    
    print(f"Created execution log: {execution_log_id}")
    
    # Start execution
    task_sequence = [
        TaskType.SFDC_AUTHENTICATE,
        TaskType.HEALTH_CHECK,
        TaskType.METADATA_EXTRACTION,
        TaskType.PMD_APEX_SECURITY
    ]
    
    success = coord_service.start_execution(execution_log_id, task_sequence)
    print(f"Started execution: {'SUCCESS' if success else 'FAILED'}")
    
    # Simulate task execution
    for task_type in task_sequence:
        # Start task
        coord_service.start_task(execution_log_id, task_type, {"started_by": "demo"})
        print(f"Started task: {task_type.value}")
        
        # Complete task (simulate success)
        coord_service.complete_task(
            execution_log_id, 
            task_type, 
            success=True,
            result_data={"demo_result": f"Task {task_type.value} completed"}
        )
        print(f"Completed task: {task_type.value}")
    
    # Get execution log
    log_entry = coord_service.get_execution_log(execution_log_id)
    if log_entry:
        print(f"Execution status: {log_entry.status.value}")
        print(f"Total tasks: {log_entry.total_tasks}")
        print(f"Success count: {log_entry.success_count}")
        print(f"Duration: {log_entry.total_duration_seconds:.2f}s")
    
    # Get execution summary
    summary = coord_service.get_execution_summary()
    print(f"Execution summary:")
    print(f"  Total executions: {summary.total_executions}")
    print(f"  Success rate: {summary.success_rate_percent:.1f}%")
    print(f"  Average duration: {summary.average_duration_seconds:.2f}s")

def demonstrate_integration():
    """Demonstrate integration between all components"""
    print("\n=== Integration Demo ===")
    
    # Check if task sequence optimization is enabled
    if is_feature_enabled("task_sequence_optimization"):
        print("Task sequence optimization is ENABLED")
        
        # Use optimized configuration
        config_manager = get_configuration_manager()
        performance_config = config_manager.get_performance_config()
        
        print(f"Using optimized settings:")
        print(f"  Connection pool size: {performance_config.connection_pool_size}")
        print(f"  Request timeout: {performance_config.request_timeout_seconds}s")
        print(f"  Caching enabled: {performance_config.enable_caching}")
    
    # Check if execution log coordination is enabled
    if is_feature_enabled("execution_log_coordination"):
        print("Execution log coordination is ENABLED")
        
        # Get coordination configuration
        task_config = get_task_sequence_configuration()
        log_validation = task_config.get_execution_log_validation()
        
        print(f"Execution log settings:")
        print(f"  Tracking enabled: {log_validation.enable_tracking}")
        print(f"  Correlation enabled: {log_validation.enable_correlation}")
        print(f"  Max execution time: {log_validation.max_execution_time_hours}h")

def main():
    """Main demonstration function"""
    print("Configuration Management System Demonstration")
    print("=" * 50)
    
    try:
        demonstrate_configuration_management()
        demonstrate_feature_flags()
        demonstrate_task_sequence_configuration()
        demonstrate_execution_log_coordination()
        demonstrate_integration()
        
        print("\n=== Demo completed successfully! ===")
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()