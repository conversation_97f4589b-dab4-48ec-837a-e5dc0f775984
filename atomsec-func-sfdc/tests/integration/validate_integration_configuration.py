#!/usr/bin/env python3
"""
Integration Configuration Validation

This script validates that the integration points are properly configured
by examining the code structure, endpoint definitions, and configuration files.
"""

import os
import sys
import json
import logging
import re
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegrationConfigurationValidator:
    """Validates integration configuration without running services"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.validation_results = {
            "proxy_endpoints": {"passed": [], "failed": []},
            "authentication_config": {"passed": [], "failed": []},
            "cors_configuration": {"passed": [], "failed": []},
            "service_communication": {"passed": [], "failed": []},
            "overall_status": "unknown"
        }
    
    def validate_proxy_endpoints_configuration(self) -> bool:
        """Validate proxy endpoints are properly configured"""
        logger.info("Validating proxy endpoints configuration...")
        
        # Check for proxy endpoint files
        proxy_files = [
            "api/sfdc_proxy_endpoints.py",
            "shared/service_communication.py",
            "shared/db_service_client.py",
            "shared/sfdc_service_client.py"
        ]
        
        for proxy_file in proxy_files:
            file_path = self.base_path / proxy_file
            if file_path.exists():
                self.validation_results["proxy_endpoints"]["passed"].append(
                    f"Proxy file exists: {proxy_file}"
                )
                
                try:
                    content = file_path.read_text()
                    
                    # Check for proper proxy patterns
                    if "async def" in content or "def " in content:
                        self.validation_results["proxy_endpoints"]["passed"].append(
                            f"{proxy_file}: Contains proxy functions"
                        )
                    
                    if "http" in content.lower() and ("request" in content.lower() or "call" in content.lower()):
                        self.validation_results["proxy_endpoints"]["passed"].append(
                            f"{proxy_file}: Contains HTTP request logic"
                        )
                        
                except Exception as e:
                    self.validation_results["proxy_endpoints"]["failed"].append(
                        f"{proxy_file}: Error reading file - {str(e)}"
                    )
            else:
                self.validation_results["proxy_endpoints"]["failed"].append(
                    f"Proxy file missing: {proxy_file}"
                )
        
        # Check API endpoints for proper structure
        api_dir = self.base_path / "api"
        if api_dir.exists():
            endpoint_files = list(api_dir.glob("*_endpoints.py"))
            
            for endpoint_file in endpoint_files:
                try:
                    content = endpoint_file.read_text()
                    
                    # Check for blueprint pattern
                    if "bp = " in content and "Blueprint" in content:
                        self.validation_results["proxy_endpoints"]["passed"].append(
                            f"{endpoint_file.name}: Uses blueprint pattern"
                        )
                    
                    # Check for route definitions
                    route_patterns = re.findall(r'@bp\.route\(["\']([^"\']+)["\']', content)
                    if route_patterns:
                        self.validation_results["proxy_endpoints"]["passed"].append(
                            f"{endpoint_file.name}: Defines {len(route_patterns)} routes"
                        )
                    
                except Exception as e:
                    self.validation_results["proxy_endpoints"]["failed"].append(
                        f"{endpoint_file.name}: Error analyzing - {str(e)}"
                    )
        
        return len(self.validation_results["proxy_endpoints"]["failed"]) == 0
    
    def validate_authentication_configuration(self) -> bool:
        """Validate authentication configuration across services"""
        logger.info("Validating authentication configuration...")
        
        # Check for authentication-related files
        auth_files = [
            "shared/auth_utils.py",
            "shared/enhanced_auth_service.py",
            "shared/advanced_auth_service.py",
            "api/auth_endpoints.py"
        ]
        
        for auth_file in auth_files:
            file_path = self.base_path / auth_file
            if file_path.exists():
                self.validation_results["authentication_config"]["passed"].append(
                    f"Auth file exists: {auth_file}"
                )
                
                try:
                    content = file_path.read_text()
                    
                    # Check for JWT handling
                    if "jwt" in content.lower() or "token" in content.lower():
                        self.validation_results["authentication_config"]["passed"].append(
                            f"{auth_file}: Contains JWT/token handling"
                        )
                    
                    # Check for internal service authentication
                    if "X-Internal-Service" in content or "internal" in content.lower():
                        self.validation_results["authentication_config"]["passed"].append(
                            f"{auth_file}: Supports internal service authentication"
                        )
                        
                except Exception as e:
                    self.validation_results["authentication_config"]["failed"].append(
                        f"{auth_file}: Error reading file - {str(e)}"
                    )
            else:
                self.validation_results["authentication_config"]["failed"].append(
                    f"Auth file missing: {auth_file}"
                )
        
        # Check function_app.py for auth middleware
        function_app_path = self.base_path / "function_app.py"
        if function_app_path.exists():
            try:
                content = function_app_path.read_text()
                
                if "auth" in content.lower():
                    self.validation_results["authentication_config"]["passed"].append(
                        "function_app.py: Contains auth configuration"
                    )
                
                if "register_functions" in content:
                    self.validation_results["authentication_config"]["passed"].append(
                        "function_app.py: Properly registers auth blueprints"
                    )
                    
            except Exception as e:
                self.validation_results["authentication_config"]["failed"].append(
                    f"function_app.py: Error analyzing - {str(e)}"
                )
        
        return len(self.validation_results["authentication_config"]["failed"]) == 0
    
    def validate_cors_configuration(self) -> bool:
        """Validate CORS configuration"""
        logger.info("Validating CORS configuration...")
        
        # Check for CORS-related files
        cors_files = [
            "shared/cors_middleware.py",
            "api/cors_handler.py"
        ]
        
        for cors_file in cors_files:
            file_path = self.base_path / cors_file
            if file_path.exists():
                self.validation_results["cors_configuration"]["passed"].append(
                    f"CORS file exists: {cors_file}"
                )
                
                try:
                    content = file_path.read_text()
                    
                    # Check for CORS headers
                    cors_headers = [
                        "Access-Control-Allow-Origin",
                        "Access-Control-Allow-Methods",
                        "Access-Control-Allow-Headers"
                    ]
                    
                    found_headers = [header for header in cors_headers if header in content]
                    if found_headers:
                        self.validation_results["cors_configuration"]["passed"].append(
                            f"{cors_file}: Contains CORS headers: {', '.join(found_headers)}"
                        )
                    
                    # Check for OPTIONS handling
                    if "OPTIONS" in content:
                        self.validation_results["cors_configuration"]["passed"].append(
                            f"{cors_file}: Handles OPTIONS requests"
                        )
                        
                except Exception as e:
                    self.validation_results["cors_configuration"]["failed"].append(
                        f"{cors_file}: Error reading file - {str(e)}"
                    )
            else:
                self.validation_results["cors_configuration"]["failed"].append(
                    f"CORS file missing: {cors_file}"
                )
        
        # Check if CORS is registered in function_app.py
        function_app_path = self.base_path / "function_app.py"
        if function_app_path.exists():
            try:
                content = function_app_path.read_text()
                
                if "cors" in content.lower():
                    self.validation_results["cors_configuration"]["passed"].append(
                        "function_app.py: CORS handler registered"
                    )
                    
            except Exception as e:
                self.validation_results["cors_configuration"]["failed"].append(
                    f"function_app.py: Error checking CORS registration - {str(e)}"
                )
        
        return len(self.validation_results["cors_configuration"]["failed"]) == 0
    
    def validate_service_communication_configuration(self) -> bool:
        """Validate service communication configuration"""
        logger.info("Validating service communication configuration...")
        
        # Check service communication files
        comm_files = [
            "shared/service_communication.py",
            "shared/db_service_client.py",
            "shared/sfdc_service_client.py"
        ]
        
        for comm_file in comm_files:
            file_path = self.base_path / comm_file
            if file_path.exists():
                self.validation_results["service_communication"]["passed"].append(
                    f"Service communication file exists: {comm_file}"
                )
                
                try:
                    content = file_path.read_text()
                    
                    # Check for HTTP client functionality
                    if "aiohttp" in content or "requests" in content or "http" in content.lower():
                        self.validation_results["service_communication"]["passed"].append(
                            f"{comm_file}: Contains HTTP client functionality"
                        )
                    
                    # Check for service endpoints configuration
                    if "endpoint" in content.lower() or "url" in content.lower():
                        self.validation_results["service_communication"]["passed"].append(
                            f"{comm_file}: Contains endpoint configuration"
                        )
                    
                    # Check for error handling
                    if "try:" in content and "except" in content:
                        self.validation_results["service_communication"]["passed"].append(
                            f"{comm_file}: Contains error handling"
                        )
                        
                except Exception as e:
                    self.validation_results["service_communication"]["failed"].append(
                        f"{comm_file}: Error reading file - {str(e)}"
                    )
            else:
                self.validation_results["service_communication"]["failed"].append(
                    f"Service communication file missing: {comm_file}"
                )
        
        # Check configuration files
        config_files = [
            "config/local.json",
            "config/production.json",
            "local.settings.json"
        ]
        
        for config_file in config_files:
            file_path = self.base_path / config_file
            if file_path.exists():
                self.validation_results["service_communication"]["passed"].append(
                    f"Configuration file exists: {config_file}"
                )
                
                try:
                    if config_file.endswith('.json'):
                        with open(file_path, 'r') as f:
                            config_data = json.load(f)
                        
                        # Check for service URLs or related configuration
                        config_str = json.dumps(config_data).lower()
                        if "url" in config_str or "endpoint" in config_str:
                            self.validation_results["service_communication"]["passed"].append(
                                f"{config_file}: Contains service configuration"
                            )
                            
                except Exception as e:
                    self.validation_results["service_communication"]["failed"].append(
                        f"{config_file}: Error reading configuration - {str(e)}"
                    )
        
        return len(self.validation_results["service_communication"]["failed"]) == 0
    
    def run_validation(self) -> Dict[str, Any]:
        """Run all validation checks"""
        logger.info("Starting integration configuration validation...")
        
        # Run all validation checks
        proxy_valid = self.validate_proxy_endpoints_configuration()
        auth_valid = self.validate_authentication_configuration()
        cors_valid = self.validate_cors_configuration()
        comm_valid = self.validate_service_communication_configuration()
        
        # Determine overall status
        if all([proxy_valid, auth_valid, cors_valid, comm_valid]):
            self.validation_results["overall_status"] = "passed"
        elif any([proxy_valid, auth_valid, cors_valid, comm_valid]):
            self.validation_results["overall_status"] = "partial"
        else:
            self.validation_results["overall_status"] = "failed"
        
        return self.validation_results
    
    def generate_report(self) -> str:
        """Generate a detailed validation report"""
        report = []
        report.append("=" * 80)
        report.append("INTEGRATION CONFIGURATION VALIDATION REPORT")
        report.append("=" * 80)
        report.append(f"Overall Status: {self.validation_results['overall_status'].upper()}")
        report.append("")
        
        for category, results in self.validation_results.items():
            if category == "overall_status":
                continue
                
            report.append(f"{category.replace('_', ' ').title()}:")
            report.append("-" * 40)
            
            if results["passed"]:
                report.append("✅ PASSED:")
                for item in results["passed"]:
                    report.append(f"  • {item}")
                report.append("")
            
            if results["failed"]:
                report.append("❌ FAILED:")
                for item in results["failed"]:
                    report.append(f"  • {item}")
                report.append("")
        
        report.append("=" * 80)
        return "\n".join(report)

def main():
    """Main function to run validation"""
    validator = IntegrationConfigurationValidator()
    results = validator.run_validation()
    report = validator.generate_report()
    
    print(report)
    
    # Save results to file
    results_path = Path("integration_configuration_results.json")
    with open(results_path, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: {results_path}")
    
    # Exit with appropriate code
    if results["overall_status"] == "passed":
        sys.exit(0)
    elif results["overall_status"] == "partial":
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()