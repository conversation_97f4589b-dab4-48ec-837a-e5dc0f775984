#!/usr/bin/env python3
"""
Enhanced Components Integration Tests

This module validates that all enhanced components work together correctly
with the existing SFDC service functionality.
"""

import pytest
import asyncio
import json
import uuid
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
import azure.functions as func
from typing import Dict, Any, List

# Import enhanced components
from src.shared.enhanced_auth_service import get_enhanced_auth_service
from src.shared.enhanced_configuration_manager import get_enhanced_configuration_manager
from src.shared.enhanced_parameter_validator import get_enhanced_parameter_validator
from src.shared.execution_log_coordination_service import get_execution_log_coordination_service
from src.shared.task_sequence_configuration import get_task_sequence_configuration
from src.shared.feature_flag_service import get_feature_flag_service
from src.shared.monitoring_dashboards import get_monitoring_dashboards_service
from src.shared.error_handler import get_enhanced_error_handler
from src.shared.task_sequence_failure_handler import get_task_sequence_failure_handler
from src.shared.security_middleware import get_security_middleware
from src.shared.performance_optimization_framework import get_performance_optimizer
from src.shared.advanced_caching_service import get_advanced_caching_service
from src.shared.auto_scaling_service import get_auto_scaling_service

# Import API endpoints
from api.enhanced_task_endpoints import enhanced_task_endpoints


@pytest.mark.integration
class TestEnhancedComponentsIntegration:
    """Integration tests for all enhanced components working together"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.execution_log_id = str(uuid.uuid4())
        
        # Initialize enhanced services
        self.auth_service = get_enhanced_auth_service()
        self.config_manager = get_enhanced_configuration_manager()
        self.validator = get_enhanced_parameter_validator()
        self.execution_service = get_execution_log_coordination_service()
        self.task_config = get_task_sequence_configuration()
        self.feature_flags = get_feature_flag_service()
        self.monitoring = get_monitoring_dashboards_service()
        self.error_handler = get_enhanced_error_handler()
        self.failure_handler = get_task_sequence_failure_handler()
        self.security_middleware = get_security_middleware()
        self.performance_optimizer = get_performance_optimizer()
        self.caching_service = get_advanced_caching_service()
        self.auto_scaling = get_auto_scaling_service()
        
        # Sample request data
        self.sample_request = {
            "execution_log_id": self.execution_log_id,
            "organization_id": "test_org_123",
            "user_id": "test_user_456",
            "task_type": "sfdc_authenticate",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            },
            "task_parameters": {
                "validate_connection": True,
                "store_credentials": True
            }
        }
    
    @pytest.mark.asyncio
    async def test_complete_enhanced_workflow_integration(self):
        """Test complete workflow with all enhanced components"""
        
        # Step 1: Configuration Management
        config = await self.config_manager.get_configuration("development")
        assert config is not None
        assert "security" in config
        assert "performance" in config
        assert "monitoring" in config
        
        # Step 2: Feature Flag Evaluation
        enhanced_auth_enabled = await self.feature_flags.is_feature_enabled(
            "enhanced_authentication",
            {"user_id": self.sample_request["user_id"]}
        )
        assert isinstance(enhanced_auth_enabled, bool)
        
        # Step 3: Security Middleware Processing
        security_context = await self.security_middleware.process_request(
            self.sample_request,
            {"Authorization": "Bearer test-token"}
        )
        assert security_context["authenticated"] is True
        assert "user_context" in security_context
        
        # Step 4: Enhanced Parameter Validation
        validated_params = self.validator.validate_sequential_task_parameters(
            self.sample_request,
            "sfdc_authenticate",
            self.execution_log_id
        )
        assert validated_params["execution_log_id"] == self.execution_log_id
        assert "salesforce_credentials" in validated_params
        
        # Step 5: Execution Log Coordination
        execution_context = await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request
        )
        assert execution_context["status"] == "initialized"
        
        # Step 6: Performance Optimization
        with self.performance_optimizer.optimize_execution("sfdc_authenticate"):
            # Step 7: Caching Service
            cache_key = f"auth_token_{self.sample_request['organization_id']}"
            cached_token = await self.caching_service.get_cached_data(cache_key)
            
            if not cached_token:
                # Simulate authentication
                auth_result = {
                    "success": True,
                    "token": "mock_auth_token_123",
                    "expires_at": (datetime.utcnow() + timedelta(hours=2)).isoformat()
                }
                
                # Cache the result
                await self.caching_service.set_cached_data(
                    cache_key,
                    auth_result,
                    ttl_seconds=7200
                )
            else:
                auth_result = cached_token
        
        # Step 8: Monitoring and Metrics
        await self.monitoring.track_task_execution(
            "sfdc_authenticate",
            self.execution_log_id,
            {"success": True, "duration_ms": 150}
        )
        
        # Step 9: Update Execution Log
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "completed",
            auth_result
        )
        
        # Step 10: Verify Integration
        final_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        
        assert "sfdc_authenticate" in final_context["completed_tasks"]
        assert final_context["task_results"]["sfdc_authenticate"]["success"] is True
        
        # Verify monitoring data was collected
        metrics = await self.monitoring.get_task_metrics("sfdc_authenticate")
        assert metrics["total_executions"] >= 1
    
    @pytest.mark.asyncio
    async def test_enhanced_error_handling_integration(self):
        """Test enhanced error handling across all components"""
        
        # Initialize execution log
        await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request
        )
        
        # Simulate error in authentication
        error = Exception("Salesforce authentication failed")
        
        # Test enhanced error handler
        error_context = await self.error_handler.handle_error(
            error,
            {
                "execution_log_id": self.execution_log_id,
                "task_type": "sfdc_authenticate",
                "user_id": self.sample_request["user_id"],
                "organization_id": self.sample_request["organization_id"]
            }
        )
        
        assert error_context["error_id"] is not None
        assert error_context["error_category"] == "authentication_error"
        assert error_context["retryable"] is True
        
        # Test task sequence failure handler
        failure_result = await self.failure_handler.handle_task_failure(
            self.execution_log_id,
            "sfdc_authenticate",
            error_context
        )
        
        assert failure_result["can_retry"] is True
        assert failure_result["retry_strategy"] is not None
        
        # Update execution log with failure
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "failed",
            error_context
        )
        
        # Verify monitoring captured the error
        error_metrics = await self.monitoring.get_error_metrics("sfdc_authenticate")
        assert error_metrics["total_errors"] >= 1
        assert "authentication_error" in error_metrics["error_categories"]
    
    @pytest.mark.asyncio
    async def test_performance_optimization_integration(self):
        """Test performance optimization across components"""
        
        # Test auto-scaling decision
        current_metrics = {
            "cpu_usage": 75.0,
            "memory_usage": 80.0,
            "request_rate": 150,
            "response_time_ms": 2500
        }
        
        scaling_decision = await self.auto_scaling.evaluate_scaling_decision(
            current_metrics
        )
        
        assert "scale_out" in scaling_decision
        assert "recommended_instances" in scaling_decision
        
        # Test performance optimization with caching
        cache_key = "performance_test_data"
        test_data = {"processed": True, "timestamp": datetime.utcnow().isoformat()}
        
        # Cache performance data
        await self.caching_service.set_cached_data(
            cache_key,
            test_data,
            ttl_seconds=300
        )
        
        # Retrieve and verify
        cached_data = await self.caching_service.get_cached_data(cache_key)
        assert cached_data["processed"] is True
        
        # Test cache performance analytics
        cache_stats = await self.caching_service.get_cache_performance_stats()
        assert "hit_rate" in cache_stats
        assert "total_requests" in cache_stats
    
    @pytest.mark.asyncio
    async def test_security_integration_across_components(self):
        """Test security integration across all components"""
        
        # Test enhanced authentication
        auth_token = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.test"
        
        auth_result = await self.auth_service.validate_jwt_token(auth_token)
        assert auth_result["valid"] is True
        assert "user_context" in auth_result
        
        # Test security middleware with authentication
        security_context = await self.security_middleware.process_request(
            self.sample_request,
            {"Authorization": auth_token}
        )
        
        assert security_context["authenticated"] is True
        assert security_context["security_level"] >= 1
        
        # Test parameter validation with security context
        validated_params = self.validator.validate_sequential_task_parameters(
            self.sample_request,
            "sfdc_authenticate",
            self.execution_log_id,
            security_context=security_context
        )
        
        assert validated_params["security_validated"] is True
        
        # Test audit logging
        await self.monitoring.track_security_event(
            "authentication_success",
            {
                "user_id": self.sample_request["user_id"],
                "execution_log_id": self.execution_log_id,
                "ip_address": "127.0.0.1"
            }
        )
        
        # Verify security metrics
        security_metrics = await self.monitoring.get_security_metrics()
        assert security_metrics["authentication_events"] >= 1
    
    @pytest.mark.asyncio
    async def test_configuration_driven_behavior(self):
        """Test that configuration drives component behavior"""
        
        # Test different configuration environments
        environments = ["development", "staging", "production"]
        
        for env in environments:
            config = await self.config_manager.get_configuration(env)
            
            # Verify environment-specific settings
            if env == "development":
                assert config["monitoring"]["log_level"] == "DEBUG"
                assert config["security"]["require_https"] is False
            elif env == "production":
                assert config["monitoring"]["log_level"] == "WARNING"
                assert config["security"]["require_https"] is True
            
            # Test feature flags per environment
            feature_config = await self.feature_flags.get_feature_configuration(
                "enhanced_monitoring"
            )
            
            assert "enabled" in feature_config
            assert "rollout_percentage" in feature_config
    
    @pytest.mark.asyncio
    async def test_monitoring_and_observability_integration(self):
        """Test comprehensive monitoring and observability"""
        
        # Initialize execution log
        await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request
        )
        
        # Track various metrics during task execution
        task_start_time = time.time()
        
        # Simulate task execution with monitoring
        await self.monitoring.track_task_start(
            "sfdc_authenticate",
            self.execution_log_id
        )
        
        # Simulate some processing time
        await asyncio.sleep(0.1)
        
        # Track custom metrics
        await self.monitoring.track_custom_metric(
            "salesforce_api_calls",
            1,
            {
                "organization_id": self.sample_request["organization_id"],
                "task_type": "sfdc_authenticate"
            }
        )
        
        # Track task completion
        task_duration = (time.time() - task_start_time) * 1000
        await self.monitoring.track_task_completion(
            "sfdc_authenticate",
            self.execution_log_id,
            task_duration,
            True
        )
        
        # Update execution log
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "completed",
            {"success": True, "duration_ms": task_duration}
        )
        
        # Verify comprehensive metrics collection
        task_metrics = await self.monitoring.get_task_metrics("sfdc_authenticate")
        assert task_metrics["total_executions"] >= 1
        assert task_metrics["average_duration_ms"] > 0
        
        execution_metrics = await self.execution_service.get_execution_metrics(
            self.execution_log_id
        )
        assert execution_metrics["task_count"] >= 1
        assert execution_metrics["total_execution_time_ms"] > 0
    
    @pytest.mark.asyncio
    async def test_failure_recovery_integration(self):
        """Test failure recovery across all components"""
        
        # Initialize execution log
        await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request
        )
        
        # Simulate task failure
        error = Exception("Network timeout during authentication")
        
        # Handle error through enhanced error handler
        error_context = await self.error_handler.handle_error(
            error,
            {
                "execution_log_id": self.execution_log_id,
                "task_type": "sfdc_authenticate",
                "retry_count": 0
            }
        )
        
        # Process failure through task sequence failure handler
        failure_result = await self.failure_handler.handle_task_failure(
            self.execution_log_id,
            "sfdc_authenticate",
            error_context
        )
        
        assert failure_result["can_retry"] is True
        assert failure_result["retry_delay_seconds"] > 0
        
        # Update execution log with failure
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "failed",
            error_context
        )
        
        # Attempt retry
        retry_result = await self.execution_service.retry_failed_task(
            self.execution_log_id,
            "sfdc_authenticate"
        )
        
        assert retry_result["can_retry"] is True
        assert retry_result["retry_count"] == 1
        
        # Simulate successful retry
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "completed",
            {"success": True, "retry_attempt": 1}
        )
        
        # Verify recovery metrics
        recovery_metrics = await self.monitoring.get_recovery_metrics()
        assert recovery_metrics["successful_retries"] >= 1
    
    @pytest.mark.asyncio
    async def test_api_endpoint_integration(self):
        """Test API endpoint integration with enhanced components"""
        
        # Create mock HTTP request
        mock_request = Mock(spec=func.HttpRequest)
        mock_request.method = "POST"
        mock_request.url = "https://test.com/api/tasks/execute"
        mock_request.headers = {
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        }
        mock_request.get_body.return_value = json.dumps(self.sample_request).encode()
        mock_request.get_json.return_value = self.sample_request
        
        # Test enhanced task endpoint
        with patch('shared.enhanced_auth_service.get_enhanced_auth_service') as mock_auth:
            mock_auth.return_value.validate_jwt_token.return_value = {
                "valid": True,
                "user_context": {"user_id": "test_user_456"}
            }
            
            # Execute endpoint
            response = await enhanced_task_endpoints.execute_task(mock_request)
            
            assert response.status_code in [200, 202]  # Success or accepted
            
            response_data = json.loads(response.get_body())
            assert "execution_log_id" in response_data
            assert response_data["status"] in ["accepted", "in_progress"]
    
    @pytest.mark.asyncio
    async def test_concurrent_execution_handling(self):
        """Test handling of concurrent executions with enhanced components"""
        
        # Create multiple execution logs
        execution_log_ids = [str(uuid.uuid4()) for _ in range(5)]
        
        # Initialize all execution logs concurrently
        tasks = []
        for log_id in execution_log_ids:
            request_data = self.sample_request.copy()
            request_data["execution_log_id"] = log_id
            
            task = self.execution_service.initialize_execution_log(
                log_id,
                "sfdc_scan",
                request_data
            )
            tasks.append(task)
        
        # Wait for all initializations
        results = await asyncio.gather(*tasks)
        
        # Verify all were initialized successfully
        for result in results:
            assert result["status"] == "initialized"
        
        # Test concurrent task execution
        auth_tasks = []
        for log_id in execution_log_ids:
            task = self.execution_service.update_task_status(
                log_id,
                "sfdc_authenticate",
                "completed",
                {"success": True}
            )
            auth_tasks.append(task)
        
        # Wait for all completions
        await asyncio.gather(*auth_tasks)
        
        # Verify all completed successfully
        for log_id in execution_log_ids:
            context = await self.execution_service.get_execution_context(log_id)
            assert "sfdc_authenticate" in context["completed_tasks"]
        
        # Verify monitoring captured all executions
        task_metrics = await self.monitoring.get_task_metrics("sfdc_authenticate")
        assert task_metrics["total_executions"] >= 5
    
    @pytest.mark.asyncio
    async def test_resource_management_integration(self):
        """Test resource management across components"""
        
        # Test connection pool management
        with self.performance_optimizer.get_connection_pool("database") as db_pool:
            assert db_pool is not None
            
            # Simulate database operations
            connection = db_pool.get_connection()
            assert connection is not None
            
            # Test connection reuse
            connection2 = db_pool.get_connection()
            assert connection2 is not None
        
        # Test memory management
        memory_stats = await self.performance_optimizer.get_memory_stats()
        assert "used_memory_mb" in memory_stats
        assert "available_memory_mb" in memory_stats
        
        # Test cache memory management
        cache_stats = await self.caching_service.get_memory_usage()
        assert "cache_size_mb" in cache_stats
        assert "max_cache_size_mb" in cache_stats
        
        # Test auto-scaling based on resource usage
        resource_metrics = {
            "memory_usage": memory_stats["used_memory_mb"] / memory_stats["available_memory_mb"] * 100,
            "cpu_usage": 65.0,
            "request_rate": 100
        }
        
        scaling_decision = await self.auto_scaling.evaluate_scaling_decision(
            resource_metrics
        )
        
        assert "recommended_action" in scaling_decision
    
    @pytest.mark.asyncio
    async def test_data_consistency_across_components(self):
        """Test data consistency across all enhanced components"""
        
        # Initialize execution log
        await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request
        )
        
        # Test that all components see consistent execution context
        execution_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        
        # Validate parameters using the same execution context
        validated_params = self.validator.validate_sequential_task_parameters(
            self.sample_request,
            "sfdc_authenticate",
            self.execution_log_id
        )
        
        assert validated_params["execution_log_id"] == execution_context["execution_log_id"]
        
        # Update task status and verify consistency
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "in_progress"
        )
        
        # Check that monitoring sees the same state
        await self.monitoring.track_task_start(
            "sfdc_authenticate",
            self.execution_log_id
        )
        
        # Verify failure handler sees consistent state
        updated_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        
        assert updated_context["current_task"] == "sfdc_authenticate"
        assert updated_context["status"] == "in_progress"
        
        # Complete task and verify all components see completion
        await self.execution_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            "completed",
            {"success": True}
        )
        
        final_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        
        assert "sfdc_authenticate" in final_context["completed_tasks"]
        assert final_context["current_task"] is None


@pytest.mark.integration
class TestEnhancedComponentsCompatibility:
    """Test compatibility of enhanced components with existing functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.execution_log_id = str(uuid.uuid4())
    
    @pytest.mark.asyncio
    async def test_backward_compatibility(self):
        """Test that enhanced components don't break existing functionality"""
        
        # Test that existing API endpoints still work
        from api.task_endpoints import task_endpoints
        
        # Create mock request in old format
        old_format_request = {
            "organization_id": "test_org_123",
            "task_type": "health_check",
            "parameters": {
                "check_api_limits": True
            }
        }
        
        mock_request = Mock(spec=func.HttpRequest)
        mock_request.method = "POST"
        mock_request.get_json.return_value = old_format_request
        
        # Should still work with enhanced components
        with patch('shared.salesforce_client.SalesforceClient'):
            response = await task_endpoints.health_check(mock_request)
            assert response.status_code in [200, 400, 401]  # Valid response codes
    
    @pytest.mark.asyncio
    async def test_gradual_enhancement_adoption(self):
        """Test gradual adoption of enhanced features"""
        
        # Test feature flag controlled enhancement
        feature_flags = get_feature_flag_service()
        
        # Test with enhanced features disabled
        await feature_flags.set_feature_flag("enhanced_validation", False)
        
        validator = get_enhanced_parameter_validator()
        
        # Should fall back to basic validation
        basic_request = {
            "execution_log_id": self.execution_log_id,
            "task_type": "sfdc_authenticate"
        }
        
        # Should not fail even with minimal data when enhanced features disabled
        try:
            result = validator.validate_sequential_task_parameters(
                basic_request,
                "sfdc_authenticate",
                self.execution_log_id
            )
            # Basic validation should pass
            assert result["task_type"] == "sfdc_authenticate"
        except Exception as e:
            # Should provide helpful error message
            assert "validation" in str(e).lower()
    
    @pytest.mark.asyncio
    async def test_performance_impact_assessment(self):
        """Test that enhanced components don't significantly impact performance"""
        
        # Measure baseline performance
        start_time = time.time()
        
        # Simulate basic operation
        execution_service = get_execution_log_coordination_service()
        await execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            {"organization_id": "test_org"}
        )
        
        baseline_duration = time.time() - start_time
        
        # Measure enhanced operation performance
        start_time = time.time()
        
        # Use all enhanced components
        auth_service = get_enhanced_auth_service()
        config_manager = get_enhanced_configuration_manager()
        validator = get_enhanced_parameter_validator()
        monitoring = get_monitoring_dashboards_service()
        
        # Perform enhanced operations
        config = await config_manager.get_configuration("development")
        
        enhanced_request = {
            "execution_log_id": str(uuid.uuid4()),
            "organization_id": "test_org",
            "task_type": "sfdc_authenticate",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password"
            }
        }
        
        validated_params = validator.validate_sequential_task_parameters(
            enhanced_request,
            "sfdc_authenticate",
            enhanced_request["execution_log_id"]
        )
        
        await monitoring.track_custom_metric("test_metric", 1, {})
        
        enhanced_duration = time.time() - start_time
        
        # Enhanced operations should not be more than 5x slower
        performance_ratio = enhanced_duration / baseline_duration if baseline_duration > 0 else 1
        assert performance_ratio < 5.0, f"Enhanced components too slow: {performance_ratio:.2f}x baseline"
    
    @pytest.mark.asyncio
    async def test_error_handling_compatibility(self):
        """Test that enhanced error handling is compatible with existing error flows"""
        
        # Test that existing error handling still works
        error_handler = get_enhanced_error_handler()
        
        # Simulate old-style error
        old_error = Exception("Traditional error message")
        
        # Enhanced error handler should handle old errors gracefully
        error_context = await error_handler.handle_error(
            old_error,
            {"task_type": "unknown", "execution_log_id": self.execution_log_id}
        )
        
        assert error_context["error_id"] is not None
        assert error_context["error_message"] == "Traditional error message"
        assert "error_category" in error_context
    
    @pytest.mark.asyncio
    async def test_configuration_migration(self):
        """Test migration from old configuration to enhanced configuration"""
        
        config_manager = get_enhanced_configuration_manager()
        
        # Test loading old-style configuration
        old_config = {
            "database_connection": "test_connection",
            "api_timeout": 30,
            "log_level": "INFO"
        }
        
        # Enhanced config manager should handle old format
        enhanced_config = await config_manager.migrate_legacy_configuration(old_config)
        
        assert "database" in enhanced_config
        assert enhanced_config["database"]["connection_string"] == "test_connection"
        assert enhanced_config["api"]["timeout_seconds"] == 30
        assert enhanced_config["monitoring"]["log_level"] == "INFO"


if __name__ == "__main__":
    # Allow running tests directly
    pytest.main([__file__, "-v", "--tb=short"])