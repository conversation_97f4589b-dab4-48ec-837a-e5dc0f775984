#!/usr/bin/env python3
"""
Service Integration Points Validation

This script validates all proxy endpoints between DB service and SFDC backend,
authentication flow across service boundaries, and CORS/security headers in proxy architecture.
"""

import os
import sys
import json
import asyncio
import logging
import aiohttp
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import time

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ServiceEndpoint:
    """Service endpoint configuration"""
    name: str
    url: str
    method: str
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict[str, Any]] = None
    expected_status: int = 200
    timeout: int = 30

@dataclass
class ValidationResult:
    """Validation result"""
    endpoint: str
    success: bool
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    error: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    response_data: Optional[Dict[str, Any]] = None

class ServiceIntegrationValidator:
    """Validates service integration points"""
    
    def __init__(self):
        self.db_service_url = "http://localhost:7072"
        self.sfdc_service_url = "http://localhost:7071"
        self.auth_service_url = "http://localhost:7073"
        self.session: Optional[aiohttp.ClientSession] = None
        self.validation_results: List[ValidationResult] = []
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def _close_session(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def _make_request(self, endpoint: ServiceEndpoint) -> ValidationResult:
        """Make HTTP request and return validation result"""
        session = await self._get_session()
        start_time = time.time()
        
        try:
            headers = endpoint.headers or {}
            headers.setdefault("Content-Type", "application/json")
            
            async with session.request(
                method=endpoint.method,
                url=endpoint.url,
                headers=headers,
                json=endpoint.data,
                timeout=aiohttp.ClientTimeout(total=endpoint.timeout)
            ) as response:
                
                response_time = time.time() - start_time
                response_headers = dict(response.headers)
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"message": await response.text()}
                
                success = response.status == endpoint.expected_status
                
                return ValidationResult(
                    endpoint=endpoint.name,
                    success=success,
                    status_code=response.status,
                    response_time=response_time,
                    headers=response_headers,
                    response_data=response_data
                )
                
        except asyncio.TimeoutError:
            return ValidationResult(
                endpoint=endpoint.name,
                success=False,
                error="Request timeout",
                response_time=time.time() - start_time
            )
        except Exception as e:
            return ValidationResult(
                endpoint=endpoint.name,
                success=False,
                error=str(e),
                response_time=time.time() - start_time
            )
    
    async def validate_health_endpoints(self) -> List[ValidationResult]:
        """Validate health endpoints for all services"""
        logger.info("Validating health endpoints...")
        
        health_endpoints = [
            ServiceEndpoint(
                name="DB Service Health",
                url=f"{self.db_service_url}/api/db/health",
                method="GET"
            ),
            ServiceEndpoint(
                name="SFDC Service Health",
                url=f"{self.sfdc_service_url}/api/health",
                method="GET"
            ),
            ServiceEndpoint(
                name="SFDC Service Info",
                url=f"{self.sfdc_service_url}/api/info",
                method="GET"
            )
        ]
        
        results = []
        for endpoint in health_endpoints:
            result = await self._make_request(endpoint)
            results.append(result)
            logger.info(f"Health check {endpoint.name}: {'✅ PASS' if result.success else '❌ FAIL'}")
        
        return results
    
    async def validate_proxy_endpoints(self) -> List[ValidationResult]:
        """Validate proxy endpoints between DB service and SFDC backend"""
        logger.info("Validating proxy endpoints...")
        
        # Test data for requests
        test_integration_id = "test-integration-123"
        test_task_data = {
            "task_type": "metadata_extraction",
            "org_id": "test-org-123",
            "user_id": "test-user-123",
            "params": {
                "access_token": "test-token",
                "instance_url": "https://test.salesforce.com",
                "integration_id": test_integration_id
            },
            "execution_log_id": "test-execution-123"
        }
        
        proxy_endpoints = [
            # Task management endpoints
            ServiceEndpoint(
                name="DB Service - Get Tasks",
                url=f"{self.db_service_url}/api/db/tasks",
                method="GET",
                headers={"X-Internal-Service": "test-client"},
                expected_status=200
            ),
            ServiceEndpoint(
                name="SFDC Service - Get Tasks",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200
            ),
            ServiceEndpoint(
                name="SFDC Service - Create Task",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="POST",
                headers={"X-Internal-Service": "db-service"},
                data=test_task_data,
                expected_status=201
            ),
            
            # Integration endpoints
            ServiceEndpoint(
                name="DB Service - Get Integrations",
                url=f"{self.db_service_url}/api/db/integrations",
                method="GET",
                headers={"X-Internal-Service": "test-client"},
                expected_status=200
            ),
            ServiceEndpoint(
                name="SFDC Service - Get Integrations",
                url=f"{self.sfdc_service_url}/api/integrations",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200
            ),
            
            # Security endpoints
            ServiceEndpoint(
                name="SFDC Service - Security Overview",
                url=f"{self.sfdc_service_url}/api/security/overview",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200
            ),
            
            # Policy endpoints
            ServiceEndpoint(
                name="DB Service - Get Policies",
                url=f"{self.db_service_url}/api/db/policies",
                method="GET",
                headers={"X-Internal-Service": "test-client"},
                expected_status=200
            ),
            ServiceEndpoint(
                name="SFDC Service - Get Policies",
                url=f"{self.sfdc_service_url}/api/policies",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200
            )
        ]
        
        results = []
        for endpoint in proxy_endpoints:
            result = await self._make_request(endpoint)
            results.append(result)
            
            status_icon = "✅ PASS" if result.success else "❌ FAIL"
            logger.info(f"Proxy endpoint {endpoint.name}: {status_icon} ({result.status_code})")
            
            if not result.success and result.error:
                logger.error(f"  Error: {result.error}")
        
        return results
    
    async def validate_authentication_flow(self) -> List[ValidationResult]:
        """Validate authentication flow across service boundaries"""
        logger.info("Validating authentication flow...")
        
        auth_endpoints = [
            # Test authentication endpoints
            ServiceEndpoint(
                name="DB Service - Auth Login (No Credentials)",
                url=f"{self.db_service_url}/api/db/auth/login",
                method="POST",
                data={"username": "", "password": ""},
                expected_status=400  # Should fail validation
            ),
            ServiceEndpoint(
                name="SFDC Service - Auth Login (No Credentials)",
                url=f"{self.sfdc_service_url}/api/auth/login",
                method="POST",
                data={"username": "", "password": ""},
                expected_status=400  # Should fail validation
            ),
            
            # Test internal service authentication
            ServiceEndpoint(
                name="SFDC Service - Internal Auth (No Header)",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="GET",
                expected_status=401  # Should require internal service header
            ),
            ServiceEndpoint(
                name="SFDC Service - Internal Auth (With Header)",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200  # Should work with internal header
            ),
            
            # Test user profile endpoints
            ServiceEndpoint(
                name="DB Service - User Profile (No Auth)",
                url=f"{self.db_service_url}/api/db/user/profile",
                method="GET",
                expected_status=401  # Should require authentication
            ),
            ServiceEndpoint(
                name="SFDC Service - User Profile (No Auth)",
                url=f"{self.sfdc_service_url}/api/user/profile",
                method="GET",
                expected_status=401  # Should require authentication
            )
        ]
        
        results = []
        for endpoint in auth_endpoints:
            result = await self._make_request(endpoint)
            results.append(result)
            
            status_icon = "✅ PASS" if result.success else "❌ FAIL"
            logger.info(f"Auth flow {endpoint.name}: {status_icon} ({result.status_code})")
        
        return results
    
    async def validate_cors_headers(self) -> List[ValidationResult]:
        """Validate CORS and security headers in proxy architecture"""
        logger.info("Validating CORS and security headers...")
        
        cors_endpoints = [
            # OPTIONS requests for CORS preflight
            ServiceEndpoint(
                name="DB Service - CORS Preflight",
                url=f"{self.db_service_url}/api/db/health",
                method="OPTIONS",
                headers={
                    "Origin": "http://localhost:3000",
                    "Access-Control-Request-Method": "GET",
                    "Access-Control-Request-Headers": "Content-Type, Authorization"
                },
                expected_status=204
            ),
            ServiceEndpoint(
                name="SFDC Service - CORS Preflight",
                url=f"{self.sfdc_service_url}/api/health",
                method="OPTIONS",
                headers={
                    "Origin": "http://localhost:3000",
                    "Access-Control-Request-Method": "GET",
                    "Access-Control-Request-Headers": "Content-Type, Authorization"
                },
                expected_status=204
            ),
            
            # GET requests to check CORS headers in response
            ServiceEndpoint(
                name="DB Service - CORS Headers Check",
                url=f"{self.db_service_url}/api/db/health",
                method="GET",
                headers={"Origin": "http://localhost:3000"},
                expected_status=200
            ),
            ServiceEndpoint(
                name="SFDC Service - CORS Headers Check",
                url=f"{self.sfdc_service_url}/api/health",
                method="GET",
                headers={"Origin": "http://localhost:3000"},
                expected_status=200
            )
        ]
        
        results = []
        for endpoint in cors_endpoints:
            result = await self._make_request(endpoint)
            results.append(result)
            
            # Check for CORS headers in response
            cors_headers_present = False
            if result.headers:
                cors_headers = [
                    "Access-Control-Allow-Origin",
                    "Access-Control-Allow-Methods",
                    "Access-Control-Allow-Headers"
                ]
                cors_headers_present = any(header in result.headers for header in cors_headers)
            
            status_icon = "✅ PASS" if result.success and cors_headers_present else "❌ FAIL"
            logger.info(f"CORS validation {endpoint.name}: {status_icon}")
            
            if result.success and not cors_headers_present:
                logger.warning(f"  Warning: CORS headers missing in response")
        
        return results
    
    async def validate_service_communication(self) -> List[ValidationResult]:
        """Validate service-to-service communication patterns"""
        logger.info("Validating service-to-service communication...")
        
        # Test internal service communication
        communication_tests = [
            # Test that SFDC service accepts internal requests
            ServiceEndpoint(
                name="Internal Communication - SFDC Tasks",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="GET",
                headers={
                    "X-Internal-Service": "db-service",
                    "X-Request-ID": "test-request-123",
                    "Content-Type": "application/json"
                },
                expected_status=200
            ),
            
            # Test that SFDC service rejects external requests without proper headers
            ServiceEndpoint(
                name="External Communication Block - SFDC Tasks",
                url=f"{self.sfdc_service_url}/api/tasks",
                method="GET",
                headers={"Content-Type": "application/json"},
                expected_status=401  # Should be blocked
            ),
            
            # Test service discovery and health checks
            ServiceEndpoint(
                name="Service Discovery - DB to SFDC Health",
                url=f"{self.sfdc_service_url}/api/health",
                method="GET",
                headers={"X-Internal-Service": "db-service"},
                expected_status=200
            )
        ]
        
        results = []
        for endpoint in communication_tests:
            result = await self._make_request(endpoint)
            results.append(result)
            
            status_icon = "✅ PASS" if result.success else "❌ FAIL"
            logger.info(f"Service communication {endpoint.name}: {status_icon}")
        
        return results
    
    async def run_validation(self) -> Dict[str, Any]:
        """Run all validation checks"""
        logger.info("Starting service integration points validation...")
        
        try:
            # Run all validation categories
            health_results = await self.validate_health_endpoints()
            proxy_results = await self.validate_proxy_endpoints()
            auth_results = await self.validate_authentication_flow()
            cors_results = await self.validate_cors_headers()
            communication_results = await self.validate_service_communication()
            
            # Combine all results
            all_results = {
                "health_endpoints": health_results,
                "proxy_endpoints": proxy_results,
                "authentication_flow": auth_results,
                "cors_headers": cors_results,
                "service_communication": communication_results
            }
            
            # Calculate summary statistics
            total_tests = sum(len(results) for results in all_results.values())
            passed_tests = sum(
                sum(1 for result in results if result.success)
                for results in all_results.values()
            )
            
            summary = {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "overall_status": "PASS" if passed_tests == total_tests else "FAIL"
            }
            
            return {
                "summary": summary,
                "results": all_results,
                "timestamp": time.time()
            }
            
        finally:
            await self._close_session()
    
    def generate_report(self, validation_data: Dict[str, Any]) -> str:
        """Generate a detailed validation report"""
        report = []
        report.append("=" * 80)
        report.append("SERVICE INTEGRATION POINTS VALIDATION REPORT")
        report.append("=" * 80)
        
        summary = validation_data["summary"]
        report.append(f"Overall Status: {summary['overall_status']}")
        report.append(f"Total Tests: {summary['total_tests']}")
        report.append(f"Passed: {summary['passed_tests']}")
        report.append(f"Failed: {summary['failed_tests']}")
        report.append(f"Success Rate: {summary['success_rate']:.1f}%")
        report.append("")
        
        # Detailed results by category
        for category, results in validation_data["results"].items():
            report.append(f"{category.replace('_', ' ').title()}:")
            report.append("-" * 40)
            
            for result in results:
                status_icon = "✅" if result.success else "❌"
                status_text = f"{result.status_code}" if result.status_code else "N/A"
                time_text = f"{result.response_time:.3f}s" if result.response_time else "N/A"
                
                report.append(f"{status_icon} {result.endpoint}")
                report.append(f"   Status: {status_text} | Time: {time_text}")
                
                if result.error:
                    report.append(f"   Error: {result.error}")
                
                # Show important headers for CORS validation
                if "cors" in category.lower() and result.headers:
                    cors_headers = {
                        k: v for k, v in result.headers.items()
                        if k.startswith("Access-Control-")
                    }
                    if cors_headers:
                        report.append(f"   CORS Headers: {cors_headers}")
                
                report.append("")
        
        report.append("=" * 80)
        return "\n".join(report)

async def main():
    """Main function to run validation"""
    validator = ServiceIntegrationValidator()
    
    try:
        validation_data = await validator.run_validation()
        report = validator.generate_report(validation_data)
        
        print(report)
        
        # Save results to file
        results_path = Path("service_integration_validation_results.json")
        with open(results_path, "w") as f:
            # Convert ValidationResult objects to dictionaries for JSON serialization
            json_data = {
                "summary": validation_data["summary"],
                "timestamp": validation_data["timestamp"],
                "results": {}
            }
            
            for category, results in validation_data["results"].items():
                json_data["results"][category] = [
                    {
                        "endpoint": r.endpoint,
                        "success": r.success,
                        "status_code": r.status_code,
                        "response_time": r.response_time,
                        "error": r.error,
                        "headers": dict(r.headers) if r.headers else None
                    }
                    for r in results
                ]
            
            json.dump(json_data, f, indent=2)
        
        print(f"\nDetailed results saved to: {results_path}")
        
        # Exit with appropriate code
        if validation_data["summary"]["overall_status"] == "PASS":
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Validation failed with error: {str(e)}")
        sys.exit(2)

if __name__ == "__main__":
    asyncio.run(main())