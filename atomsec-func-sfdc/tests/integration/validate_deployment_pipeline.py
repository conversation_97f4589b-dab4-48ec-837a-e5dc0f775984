#!/usr/bin/env python3
"""
Enhanced Deployment Pipeline Validation

This script validates the enhanced CI/CD pipeline, Infrastructure as Code,
and deployment strategies for the SFDC service.
"""

import sys
import os
import json
import yaml
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional

class DeploymentPipelineValidator:
    """Validates deployment pipeline components"""
    
    def __init__(self):
        self.results = []
        self.passed = 0
        self.total = 0
    
    def log_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        self.total += 1
        if passed:
            self.passed += 1
            print(f"✅ {test_name}: PASSED {message}")
        else:
            print(f"❌ {test_name}: FAILED {message}")
        
        self.results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
    
    def validate_pipeline_files(self) -> bool:
        """Validate that all pipeline files exist and are valid"""
        print("\n🔧 Validating Pipeline Files")
        print("-" * 50)
        
        pipeline_files = [
            ("Dev Pipeline", "pipeline-func-sfdc-dev.yml"),
            ("Staging Pipeline", "pipeline-func-sfdc-staging.yml"),
            ("Production Pipeline", "pipeline-func-sfdc-prod.yml"),
            ("Build Template", "pipeline-templates/build-template.yml"),
            ("Deployment Template", "pipeline-templates/deployment-template.yml")
        ]
        
        all_valid = True
        for name, file_path in pipeline_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = yaml.safe_load(f)
                    
                    # Basic YAML validation
                    if isinstance(content, dict):
                        self.log_result(f"Pipeline File: {name}", True, "Valid YAML structure")
                    else:
                        self.log_result(f"Pipeline File: {name}", False, "Invalid YAML structure")
                        all_valid = False
                        
                except yaml.YAMLError as e:
                    self.log_result(f"Pipeline File: {name}", False, f"YAML parsing error: {e}")
                    all_valid = False
                except Exception as e:
                    self.log_result(f"Pipeline File: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Pipeline File: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_infrastructure_as_code(self) -> bool:
        """Validate Infrastructure as Code templates"""
        print("\n🏗️  Validating Infrastructure as Code")
        print("-" * 50)
        
        # Bicep files validation
        bicep_files = [
            ("Main Infrastructure", "infrastructure/main.bicep"),
            ("Function App Module", "infrastructure/modules/functionapp.bicep"),
            ("Key Vault Module", "infrastructure/modules/keyvault.bicep"),
            ("App Insights Module", "infrastructure/modules/appinsights.bicep"),
            ("Storage Module", "infrastructure/modules/storage.bicep"),
            ("Service Bus Module", "infrastructure/modules/servicebus.bicep")
        ]
        
        all_valid = True
        for name, file_path in bicep_files:
            if Path(file_path).exists():
                try:
                    # Basic Bicep file validation (check for required keywords)
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for basic Bicep structure
                    if any(keyword in content for keyword in ['resource', 'param', 'var', 'output']):
                        self.log_result(f"Bicep File: {name}", True, "Contains Bicep keywords")
                    else:
                        self.log_result(f"Bicep File: {name}", False, "Missing Bicep keywords")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Bicep File: {name}", False, f"Error reading file: {e}")
                    all_valid = False
            else:
                self.log_result(f"Bicep File: {name}", False, "File not found")
                all_valid = False
        
        # Parameter files validation
        param_files = [
            ("Dev Parameters", "infrastructure/parameters/dev.bicepparam"),
            ("Staging Parameters", "infrastructure/parameters/staging.bicepparam")
        ]
        
        for name, file_path in param_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    if 'using' in content and 'param' in content:
                        self.log_result(f"Parameter File: {name}", True, "Valid parameter file structure")
                    else:
                        self.log_result(f"Parameter File: {name}", False, "Invalid parameter file structure")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Parameter File: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Parameter File: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_deployment_scripts(self) -> bool:
        """Validate deployment scripts"""
        print("\n📜 Validating Deployment Scripts")
        print("-" * 50)
        
        deployment_scripts = [
            ("Bash Deploy Script", "infrastructure/deploy.sh"),
            ("PowerShell Deploy Script", "infrastructure/deploy.ps1"),
            ("Blue-Green Deployment", "scripts/blue_green_deployment.py"),
            ("Canary Deployment", "scripts/canary_deployment.py"),
            ("Rollback Deployment", "scripts/rollback_deployment.py"),
            ("Deployment Monitoring", "scripts/deployment_monitoring.py")
        ]
        
        all_valid = True
        for name, file_path in deployment_scripts:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Basic validation - check if file has content
                    if len(content.strip()) > 0:
                        self.log_result(f"Deployment Script: {name}", True, f"File size: {len(content)} chars")
                    else:
                        self.log_result(f"Deployment Script: {name}", False, "Empty file")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Deployment Script: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Deployment Script: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_pipeline_quality_gates(self) -> bool:
        """Validate quality gates in pipeline"""
        print("\n🚪 Validating Pipeline Quality Gates")
        print("-" * 50)
        
        # Check main pipeline files for quality gates
        pipeline_files = [
            "pipeline-func-sfdc-dev.yml",
            "pipeline-func-sfdc-staging.yml", 
            "pipeline-func-sfdc-prod.yml"
        ]
        
        quality_gates = [
            "test",
            "security",
            "lint",
            "build",
            "deploy"
        ]
        
        all_valid = True
        for pipeline_file in pipeline_files:
            if Path(pipeline_file).exists():
                try:
                    with open(pipeline_file, 'r') as f:
                        content = f.read().lower()
                    
                    found_gates = []
                    for gate in quality_gates:
                        if gate in content:
                            found_gates.append(gate)
                    
                    if len(found_gates) >= 3:  # At least 3 quality gates
                        self.log_result(f"Quality Gates in {pipeline_file}", True, f"Found: {', '.join(found_gates)}")
                    else:
                        self.log_result(f"Quality Gates in {pipeline_file}", False, f"Only found: {', '.join(found_gates)}")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Quality Gates in {pipeline_file}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Quality Gates in {pipeline_file}", False, "Pipeline file not found")
                all_valid = False
        
        return all_valid
    
    def validate_environment_configuration(self) -> bool:
        """Validate environment-specific configuration"""
        print("\n🌍 Validating Environment Configuration")
        print("-" * 50)
        
        # Check for environment-specific configurations
        environments = ["dev", "staging", "production"]
        
        all_valid = True
        for env in environments:
            # Check for parameter files
            param_file = f"infrastructure/parameters/{env}.bicepparam"
            if Path(param_file).exists():
                self.log_result(f"Environment Config: {env}", True, "Parameter file exists")
            else:
                # Only dev and staging are required, production might be handled differently
                if env in ["dev", "staging"]:
                    self.log_result(f"Environment Config: {env}", False, "Parameter file missing")
                    all_valid = False
                else:
                    self.log_result(f"Environment Config: {env}", True, "Not required or handled differently")
        
        return all_valid
    
    def validate_security_scanning(self) -> bool:
        """Validate security scanning configuration"""
        print("\n🔒 Validating Security Scanning")
        print("-" * 50)
        
        # Check for security-related files and configurations
        security_files = [
            ("Security Tests", "tests/security/run_security_tests.py"),
            ("Security Config", "tests/security/security_config.yaml"),
            ("Authentication Tests", "tests/security/test_authentication_security.py"),
            ("Injection Attack Tests", "tests/security/test_injection_attacks.py")
        ]
        
        all_valid = True
        for name, file_path in security_files:
            if Path(file_path).exists():
                self.log_result(f"Security File: {name}", True)
            else:
                self.log_result(f"Security File: {name}", False, "File not found")
                all_valid = False
        
        # Check pipeline files for security scanning references
        pipeline_files = ["pipeline-func-sfdc-dev.yml", "pipeline-func-sfdc-staging.yml"]
        
        for pipeline_file in pipeline_files:
            if Path(pipeline_file).exists():
                try:
                    with open(pipeline_file, 'r') as f:
                        content = f.read().lower()
                    
                    security_keywords = ["security", "scan", "sast", "dast", "vulnerability"]
                    found_keywords = [kw for kw in security_keywords if kw in content]
                    
                    if found_keywords:
                        self.log_result(f"Security Scanning in {pipeline_file}", True, f"Found: {', '.join(found_keywords)}")
                    else:
                        self.log_result(f"Security Scanning in {pipeline_file}", False, "No security scanning keywords found")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Security Scanning in {pipeline_file}", False, f"Error: {e}")
                    all_valid = False
        
        return all_valid
    
    def validate_testing_integration(self) -> bool:
        """Validate testing integration in pipeline"""
        print("\n🧪 Validating Testing Integration")
        print("-" * 50)
        
        # Check for test files and configurations
        test_components = [
            ("Unit Tests", "tests/unit/"),
            ("Integration Tests", "tests/integration/"),
            ("Performance Tests", "tests/performance/"),
            ("Test Runner", "tests/run_unit_tests.py"),
            ("Load Testing", "tests/performance/test_load_testing.py"),
            ("Comprehensive Load Test", "tests/performance/comprehensive_load_test.py")
        ]
        
        all_valid = True
        for name, path in test_components:
            if Path(path).exists():
                if Path(path).is_dir():
                    # Count files in directory
                    test_files = list(Path(path).glob("test_*.py"))
                    if test_files:
                        self.log_result(f"Test Component: {name}", True, f"{len(test_files)} test files")
                    else:
                        self.log_result(f"Test Component: {name}", False, "No test files found")
                        all_valid = False
                else:
                    self.log_result(f"Test Component: {name}", True, "File exists")
            else:
                self.log_result(f"Test Component: {name}", False, "Not found")
                all_valid = False
        
        return all_valid
    
    def validate_monitoring_integration(self) -> bool:
        """Validate monitoring and alerting integration"""
        print("\n📊 Validating Monitoring Integration")
        print("-" * 50)
        
        # Check for monitoring-related files
        monitoring_files = [
            ("Monitoring Dashboards", "shared/monitoring_dashboards.py"),
            ("Deployment Monitoring", "scripts/deployment_monitoring.py"),
            ("Monitoring Documentation", "docs/operations/monitoring-alerting.md"),
            ("Application Insights Config", "infrastructure/modules/appinsights.bicep")
        ]
        
        all_valid = True
        for name, file_path in monitoring_files:
            if Path(file_path).exists():
                self.log_result(f"Monitoring File: {name}", True)
            else:
                self.log_result(f"Monitoring File: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_rollback_procedures(self) -> bool:
        """Validate rollback procedures"""
        print("\n🔄 Validating Rollback Procedures")
        print("-" * 50)
        
        rollback_files = [
            ("Rollback Script", "scripts/rollback_deployment.py"),
            ("Deployment Health Check", "scripts/deployment_health_check.py"),
            ("Smoke Tests", "scripts/smoke_tests.py"),
            ("Deployment Verification", "scripts/verify_deployment.py")
        ]
        
        all_valid = True
        for name, file_path in rollback_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for rollback-related keywords
                    rollback_keywords = ["rollback", "revert", "previous", "restore"]
                    if any(keyword in content.lower() for keyword in rollback_keywords):
                        self.log_result(f"Rollback Component: {name}", True, "Contains rollback logic")
                    else:
                        self.log_result(f"Rollback Component: {name}", True, "File exists (may contain related logic)")
                        
                except Exception as e:
                    self.log_result(f"Rollback Component: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Rollback Component: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_deployment_strategies(self) -> bool:
        """Validate advanced deployment strategies"""
        print("\n🚀 Validating Deployment Strategies")
        print("-" * 50)
        
        deployment_strategies = [
            ("Blue-Green Deployment", "scripts/blue_green_deployment.py"),
            ("Canary Deployment", "scripts/canary_deployment.py"),
            ("Advanced Deployment Strategies Doc", "docs/ADVANCED_DEPLOYMENT_STRATEGIES.md")
        ]
        
        all_valid = True
        for name, file_path in deployment_strategies:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for strategy-specific keywords
                    if "blue_green" in file_path.lower():
                        keywords = ["blue", "green", "swap", "slot"]
                    elif "canary" in file_path.lower():
                        keywords = ["canary", "traffic", "percentage", "gradual"]
                    else:
                        keywords = ["deployment", "strategy"]
                    
                    if any(keyword in content.lower() for keyword in keywords):
                        self.log_result(f"Deployment Strategy: {name}", True, "Contains strategy logic")
                    else:
                        self.log_result(f"Deployment Strategy: {name}", False, "Missing strategy keywords")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Deployment Strategy: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Deployment Strategy: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def run_validation(self) -> bool:
        """Run all deployment pipeline validation tests"""
        print("🚀 Enhanced Deployment Pipeline Validation")
        print("=" * 60)
        
        validation_tests = [
            ("Pipeline Files", self.validate_pipeline_files),
            ("Infrastructure as Code", self.validate_infrastructure_as_code),
            ("Deployment Scripts", self.validate_deployment_scripts),
            ("Pipeline Quality Gates", self.validate_pipeline_quality_gates),
            ("Environment Configuration", self.validate_environment_configuration),
            ("Security Scanning", self.validate_security_scanning),
            ("Testing Integration", self.validate_testing_integration),
            ("Monitoring Integration", self.validate_monitoring_integration),
            ("Rollback Procedures", self.validate_rollback_procedures),
            ("Deployment Strategies", self.validate_deployment_strategies)
        ]
        
        for test_name, test_func in validation_tests:
            print(f"\n🔍 Running: {test_name}")
            try:
                test_func()
            except Exception as e:
                self.log_result(f"{test_name} (Exception)", False, f"Unexpected error: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print(f"🏁 Deployment Pipeline Validation Complete: {self.passed}/{self.total} checks passed")
        
        success_rate = (self.passed / self.total) * 100 if self.total > 0 else 0
        
        if success_rate >= 90:
            print(f"🎉 Excellent! {success_rate:.1f}% of deployment checks passed")
            return True
        elif success_rate >= 80:
            print(f"✅ Good! {success_rate:.1f}% of deployment checks passed")
            return True
        elif success_rate >= 70:
            print(f"⚠️  Acceptable! {success_rate:.1f}% of deployment checks passed")
            return True
        else:
            print(f"❌ Poor! Only {success_rate:.1f}% of deployment checks passed")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate deployment validation report"""
        return {
            "timestamp": str(os.popen('date').read().strip()),
            "total_checks": self.total,
            "passed_checks": self.passed,
            "success_rate": (self.passed / self.total) * 100 if self.total > 0 else 0,
            "results": self.results,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        failed_tests = [r for r in self.results if not r["passed"]]
        
        if any("Pipeline File" in r["test"] for r in failed_tests):
            recommendations.append("Fix pipeline YAML syntax errors and ensure all pipeline files exist")
        
        if any("Bicep File" in r["test"] for r in failed_tests):
            recommendations.append("Complete Infrastructure as Code templates and validate Bicep syntax")
        
        if any("Security" in r["test"] for r in failed_tests):
            recommendations.append("Implement security scanning in CI/CD pipeline and add security tests")
        
        if any("Quality Gates" in r["test"] for r in failed_tests):
            recommendations.append("Add more quality gates to pipeline (testing, linting, security scanning)")
        
        if any("Rollback" in r["test"] for r in failed_tests):
            recommendations.append("Implement comprehensive rollback procedures and automated health checks")
        
        if any("Deployment Strategy" in r["test"] for r in failed_tests):
            recommendations.append("Implement advanced deployment strategies (blue-green, canary)")
        
        return recommendations


def main():
    """Main function"""
    validator = DeploymentPipelineValidator()
    
    # Run validation
    success = validator.run_validation()
    
    # Generate report
    report = validator.generate_report()
    
    # Save report
    with open("deployment_pipeline_validation_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Detailed report saved to: deployment_pipeline_validation_report.json")
    
    # Print recommendations
    if report["recommendations"]:
        print("\n💡 Recommendations:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    return success


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)