"""
Integration tests for Execution Log Coordination

Tests the execution log coordination service and its integration
with task sequence processing.
"""

import pytest
import asyncio
import uuid
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import json

from src.shared.execution_log_coordination_service import (
    ExecutionLogCoordinationService,
    get_execution_log_coordination_service,
    ExecutionLogError
)
from src.shared.task_sequence_configuration import (
    get_task_sequence_configuration
)


@pytest.mark.integration
class TestExecutionLogCoordination:
    """Integration tests for execution log coordination"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.coordination_service = get_execution_log_coordination_service()
        self.task_config = get_task_sequence_configuration()
        self.execution_log_id = str(uuid.uuid4())
        
        self.sample_request_data = {
            "organization_id": "test_org_123",
            "user_id": "test_user_456",
            "scan_type": "full_security_scan",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            }
        }
    
    @pytest.mark.asyncio
    async def test_execution_log_lifecycle(self):
        """Test complete execution log lifecycle"""
        
        # Step 1: Initialize execution log
        context = await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        assert context is not None
        assert context['execution_log_id'] == self.execution_log_id
        assert context['workflow_type'] == "sfdc_scan"
        assert context['status'] == 'initialized'
        assert context['created_at'] is not None
        assert len(context['completed_tasks']) == 0
        assert len(context['failed_tasks']) == 0
        assert context['current_task'] is None
        
        # Step 2: Start first task
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'in_progress'
        )
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['status'] == 'in_progress'
        assert context['current_task'] == 'sfdc_authenticate'
        
        # Step 3: Complete first task
        task_result = {
            'success': True,
            'authentication_token': 'mock_token_123',
            'server_url': 'https://test.salesforce.com'
        }
        
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed',
            task_result
        )
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert 'sfdc_authenticate' in context['completed_tasks']
        assert context['current_task'] is None
        assert context['task_results']['sfdc_authenticate'] == task_result
        
        # Step 4: Complete remaining tasks
        remaining_tasks = ['health_check', 'metadata_extraction', 'pmd_apex_security']
        
        for task_name in remaining_tasks:
            await self.coordination_service.update_task_status(
                self.execution_log_id,
                task_name,
                'in_progress'
            )
            
            await self.coordination_service.update_task_status(
                self.execution_log_id,
                task_name,
                'completed',
                {'success': True, 'task_type': task_name}
            )
        
        # Step 5: Verify final state
        final_context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert final_context['status'] == 'completed'
        assert len(final_context['completed_tasks']) == 4
        assert len(final_context['failed_tasks']) == 0
        assert final_context['completed_at'] is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_execution_prevention(self):
        """Test prevention of concurrent task execution"""
        
        # Initialize execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        # Start first task
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'in_progress'
        )
        
        # Try to start another task concurrently (should fail)
        with pytest.raises(ExecutionLogError, match="already in progress"):
            await self.coordination_service.update_task_status(
                self.execution_log_id,
                "health_check",
                'in_progress'
            )
        
        # Complete first task
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed'
        )
        
        # Now second task should be allowed
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "health_check",
            'in_progress'
        )
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['current_task'] == 'health_check'
    
    @pytest.mark.asyncio
    async def test_task_failure_handling(self):
        """Test handling of task failures"""
        
        # Initialize execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        # Complete authentication successfully
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed'
        )
        
        # Fail health check
        failure_details = {
            'error': 'API limits exceeded',
            'error_code': 'RATE_LIMIT_EXCEEDED',
            'retry_after': 3600,
            'retryable': True
        }
        
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "health_check",
            'failed',
            failure_details
        )
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['status'] == 'failed'
        assert 'health_check' in context['failed_tasks']
        assert context['task_results']['health_check'] == failure_details
        assert context['failed_at'] is not None
    
    @pytest.mark.asyncio
    async def test_task_retry_mechanism(self):
        """Test task retry mechanism"""
        
        # Initialize and fail a task
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'failed',
            {'error': 'Temporary network error', 'retryable': True}
        )
        
        # Test retry capability
        retry_info = await self.coordination_service.retry_failed_task(
            self.execution_log_id,
            "sfdc_authenticate"
        )
        
        assert retry_info['can_retry'] is True
        assert retry_info['retry_count'] == 1
        assert retry_info['max_retries'] > 0
        
        # Verify task is ready for retry
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['task_retry_counts']['sfdc_authenticate'] == 1
        assert context['status'] == 'retry_pending'
        
        # Complete retry successfully
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed',
            {'success': True, 'retry_attempt': 1}
        )
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert 'sfdc_authenticate' in context['completed_tasks']
        assert 'sfdc_authenticate' not in context['failed_tasks']
        assert context['status'] == 'in_progress'
    
    @pytest.mark.asyncio
    async def test_task_timeout_handling(self):
        """Test handling of task timeouts"""
        
        # Initialize execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        # Start a task
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'in_progress'
        )
        
        # Simulate timeout
        timeout_result = await self.coordination_service.handle_task_timeout(
            self.execution_log_id,
            "sfdc_authenticate",
            timeout_seconds=300
        )
        
        assert timeout_result['timed_out'] is True
        assert timeout_result['task_name'] == 'sfdc_authenticate'
        
        context = await self.coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['status'] == 'failed'
        assert 'sfdc_authenticate' in context['failed_tasks']
        assert 'timeout' in context['task_results']['sfdc_authenticate']['error'].lower()
    
    @pytest.mark.asyncio
    async def test_execution_log_persistence(self):
        """Test execution log persistence across service restarts"""
        
        # Create and populate execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        await self.coordination_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed',
            {'success': True, 'token': 'test_token'}
        )
        
        # Simulate service restart by creating new instance
        new_coordination_service = ExecutionLogCoordinationService()
        
        # Retrieve execution log with new instance
        context = await new_coordination_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context is not None
        assert context['execution_log_id'] == self.execution_log_id
        assert 'sfdc_authenticate' in context['completed_tasks']
        assert context['task_results']['sfdc_authenticate']['token'] == 'test_token'
    
    @pytest.mark.asyncio
    async def test_execution_log_cleanup(self):
        """Test cleanup of old execution logs"""
        
        # Create multiple execution logs
        execution_log_ids = []
        
        for i in range(5):
            log_id = str(uuid.uuid4())
            execution_log_ids.append(log_id)
            
            await self.coordination_service.initialize_execution_log(
                log_id,
                "sfdc_scan",
                self.sample_request_data
            )
            
            # Complete some logs
            if i < 3:
                await self.coordination_service.update_task_status(
                    log_id,
                    "sfdc_authenticate",
                    'completed'
                )
        
        # Test cleanup of completed logs older than threshold
        cleanup_result = await self.coordination_service.cleanup_old_execution_logs(
            max_age_hours=0,  # Clean up all
            keep_failed=True
        )
        
        assert cleanup_result['cleaned_count'] >= 3  # At least completed ones
        
        # Verify some logs still exist (failed/in-progress ones)
        remaining_logs = []
        for log_id in execution_log_ids:
            try:
                context = await self.coordination_service.get_execution_context(log_id)
                if context:
                    remaining_logs.append(log_id)
            except ExecutionLogError:
                pass  # Log was cleaned up
        
        assert len(remaining_logs) >= 2  # Incomplete logs should remain
    
    @pytest.mark.asyncio
    async def test_execution_log_metrics_collection(self):
        """Test collection of execution log metrics"""
        
        # Create and complete an execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        # Complete all tasks with timing information
        tasks = ['sfdc_authenticate', 'health_check', 'metadata_extraction', 'pmd_apex_security']
        
        for i, task_name in enumerate(tasks):
            await self.coordination_service.update_task_status(
                self.execution_log_id,
                task_name,
                'in_progress'
            )
            
            # Simulate task execution time
            import asyncio
            await asyncio.sleep(0.01)
            
            await self.coordination_service.update_task_status(
                self.execution_log_id,
                task_name,
                'completed',
                {
                    'success': True,
                    'execution_time_ms': (i + 1) * 100,
                    'items_processed': (i + 1) * 10
                }
            )
        
        # Get execution metrics
        metrics = await self.coordination_service.get_execution_metrics(
            self.execution_log_id
        )
        
        assert metrics is not None
        assert metrics['total_execution_time_ms'] > 0
        assert metrics['task_count'] == 4
        assert metrics['success_rate'] == 1.0
        assert len(metrics['task_metrics']) == 4
        
        # Verify individual task metrics
        for task_name in tasks:
            assert task_name in metrics['task_metrics']
            task_metric = metrics['task_metrics'][task_name]
            assert task_metric['status'] == 'completed'
            assert task_metric['execution_time_ms'] > 0
    
    @pytest.mark.asyncio
    async def test_execution_log_search_and_filtering(self):
        """Test searching and filtering execution logs"""
        
        # Create multiple execution logs with different characteristics
        test_logs = [
            {
                'id': str(uuid.uuid4()),
                'workflow_type': 'sfdc_scan',
                'org_id': 'org_123',
                'user_id': 'user_456'
            },
            {
                'id': str(uuid.uuid4()),
                'workflow_type': 'sfdc_scan',
                'org_id': 'org_456',
                'user_id': 'user_789'
            },
            {
                'id': str(uuid.uuid4()),
                'workflow_type': 'health_check',
                'org_id': 'org_123',
                'user_id': 'user_456'
            }
        ]
        
        # Initialize all logs
        for log_info in test_logs:
            request_data = self.sample_request_data.copy()
            request_data['organization_id'] = log_info['org_id']
            request_data['user_id'] = log_info['user_id']
            
            await self.coordination_service.initialize_execution_log(
                log_info['id'],
                log_info['workflow_type'],
                request_data
            )
        
        # Test filtering by organization
        org_123_logs = await self.coordination_service.find_execution_logs(
            filters={'organization_id': 'org_123'}
        )
        
        assert len(org_123_logs) == 2
        assert all(log['organization_id'] == 'org_123' for log in org_123_logs)
        
        # Test filtering by workflow type
        scan_logs = await self.coordination_service.find_execution_logs(
            filters={'workflow_type': 'sfdc_scan'}
        )
        
        assert len(scan_logs) == 2
        assert all(log['workflow_type'] == 'sfdc_scan' for log in scan_logs)
        
        # Test filtering by user
        user_456_logs = await self.coordination_service.find_execution_logs(
            filters={'user_id': 'user_456'}
        )
        
        assert len(user_456_logs) == 2
        assert all(log['user_id'] == 'user_456' for log in user_456_logs)
    
    @pytest.mark.asyncio
    async def test_execution_log_status_transitions(self):
        """Test valid execution log status transitions"""
        
        # Initialize execution log
        await self.coordination_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.sample_request_data
        )
        
        # Test valid transitions
        valid_transitions = [
            ('initialized', 'in_progress'),
            ('in_progress', 'completed'),
        ]
        
        for from_status, to_status in valid_transitions:
            # Reset to from_status
            if from_status == 'initialized':
                # Already initialized
                pass
            elif from_status == 'in_progress':
                await self.coordination_service.update_task_status(
                    self.execution_log_id,
                    "sfdc_authenticate",
                    'in_progress'
                )
            
            # Transition to to_status
            if to_status == 'in_progress':
                await self.coordination_service.update_task_status(
                    self.execution_log_id,
                    "sfdc_authenticate",
                    'in_progress'
                )
            elif to_status == 'completed':
                await self.coordination_service.update_task_status(
                    self.execution_log_id,
                    "sfdc_authenticate",
                    'completed'
                )
                # Complete remaining tasks
                for task in ['health_check', 'metadata_extraction', 'pmd_apex_security']:
                    await self.coordination_service.update_task_status(
                        self.execution_log_id,
                        task,
                        'completed'
                    )
            
            context = await self.coordination_service.get_execution_context(
                self.execution_log_id
            )
            
            assert context['status'] == to_status
    
    @pytest.mark.asyncio
    async def test_execution_log_data_validation(self):
        """Test validation of execution log data"""
        
        # Test invalid workflow type
        with pytest.raises(ExecutionLogError, match="Invalid workflow type"):
            await self.coordination_service.initialize_execution_log(
                self.execution_log_id,
                "invalid_workflow",
                self.sample_request_data
            )
        
        # Test missing required data
        invalid_request_data = {"organization_id": "test_org"}  # Missing required fields
        
        with pytest.raises(ExecutionLogError, match="Missing required"):
            await self.coordination_service.initialize_execution_log(
                self.execution_log_id,
                "sfdc_scan",
                invalid_request_data
            )
        
        # Test invalid execution log ID format
        with pytest.raises(ExecutionLogError, match="Invalid execution log ID"):
            await self.coordination_service.initialize_execution_log(
                "invalid-id-format",
                "sfdc_scan",
                self.sample_request_data
            )