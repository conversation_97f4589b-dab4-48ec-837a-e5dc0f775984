#!/usr/bin/env python3
"""
Enhanced Components Integration Validation

This script validates that all enhanced components are properly integrated
and can work together with the existing SFDC service.
"""

import sys
import os
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Tuple

# Add paths
sys.path.insert(0, '.')
sys.path.insert(0, 'shared')
sys.path.insert(0, 'api')

class IntegrationValidator:
    """Validates integration of enhanced components"""
    
    def __init__(self):
        self.results = []
        self.passed = 0
        self.total = 0
    
    def log_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        self.total += 1
        if passed:
            self.passed += 1
            print(f"✅ {test_name}: PASSED {message}")
        else:
            print(f"❌ {test_name}: FAILED {message}")
        
        self.results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
    
    def validate_file_structure(self) -> bool:
        """Validate that all enhanced component files exist"""
        print("\n📁 Validating Enhanced Components File Structure")
        print("-" * 50)
        
        required_files = [
            "shared/enhanced_auth_service.py",
            "shared/enhanced_configuration_manager.py", 
            "shared/enhanced_parameter_validator.py",
            "shared/execution_log_coordination_service.py",
            "shared/task_sequence_configuration.py",
            "shared/feature_flag_service.py",
            "shared/monitoring_dashboards.py",
            "shared/error_handler.py",
            "shared/task_sequence_failure_handler.py",
            "shared/security_middleware.py",
            "shared/performance_optimization_framework.py",
            "shared/advanced_caching_service.py",
            "shared/auto_scaling_service.py",
            "api/enhanced_task_endpoints.py"
        ]
        
        all_exist = True
        for file_path in required_files:
            if Path(file_path).exists():
                self.log_result(f"File exists: {file_path}", True)
            else:
                self.log_result(f"File exists: {file_path}", False, "File not found")
                all_exist = False
        
        return all_exist
    
    def validate_imports(self) -> bool:
        """Validate that enhanced components can be imported"""
        print("\n📦 Validating Enhanced Components Imports")
        print("-" * 50)
        
        import_tests = [
            ("EnhancedAuthService", "shared.enhanced_auth_service", "EnhancedAuthService"),
            ("EnhancedConfigurationManager", "shared.enhanced_configuration_manager", "EnhancedConfigurationManager"),
            ("EnhancedParameterValidator", "shared.enhanced_parameter_validator", "EnhancedParameterValidator"),
            ("ExecutionLogCoordinationService", "shared.execution_log_coordination_service", "ExecutionLogCoordinationService"),
            ("TaskSequenceConfiguration", "shared.task_sequence_configuration", "TaskSequenceConfiguration"),
            ("FeatureFlagService", "shared.feature_flag_service", "FeatureFlagService"),
            ("MonitoringDashboardsService", "shared.monitoring_dashboards", "MonitoringDashboardsService"),
            ("EnhancedErrorHandler", "shared.error_handler", "EnhancedErrorHandler"),
            ("TaskSequenceFailureHandler", "shared.task_sequence_failure_handler", "TaskSequenceFailureHandler"),
            ("SecurityMiddleware", "shared.security_middleware", "SecurityMiddleware"),
            ("PerformanceOptimizer", "shared.performance_optimization_framework", "PerformanceOptimizer"),
            ("AdvancedCachingService", "shared.advanced_caching_service", "AdvancedCachingService"),
            ("AutoScalingService", "shared.auto_scaling_service", "AutoScalingService")
        ]
        
        all_imported = True
        for test_name, module_name, class_name in import_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                self.log_result(f"Import: {test_name}", True)
            except ImportError as e:
                self.log_result(f"Import: {test_name}", False, f"ImportError: {e}")
                all_imported = False
            except AttributeError as e:
                self.log_result(f"Import: {test_name}", False, f"AttributeError: {e}")
                all_imported = False
            except Exception as e:
                self.log_result(f"Import: {test_name}", False, f"Error: {e}")
                all_imported = False
        
        return all_imported
    
    def validate_class_instantiation(self) -> bool:
        """Validate that enhanced components can be instantiated"""
        print("\n🏗️  Validating Enhanced Components Instantiation")
        print("-" * 50)
        
        # Test components that don't require configuration
        simple_components = [
            ("FeatureFlagService", "shared.feature_flag_service", "FeatureFlagService"),
            ("EnhancedParameterValidator", "shared.enhanced_parameter_validator", "EnhancedParameterValidator"),
            ("TaskSequenceConfiguration", "shared.task_sequence_configuration", "TaskSequenceConfiguration"),
            ("SecurityMiddleware", "shared.security_middleware", "SecurityMiddleware"),
            ("PerformanceOptimizer", "shared.performance_optimization_framework", "PerformanceOptimizer")
        ]
        
        all_instantiated = True
        for test_name, module_name, class_name in simple_components:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                instance = cls()
                self.log_result(f"Instantiate: {test_name}", True)
            except Exception as e:
                self.log_result(f"Instantiate: {test_name}", False, f"Error: {e}")
                all_instantiated = False
        
        return all_instantiated
    
    def validate_method_signatures(self) -> bool:
        """Validate that enhanced components have expected methods"""
        print("\n🔍 Validating Enhanced Components Method Signatures")
        print("-" * 50)
        
        method_tests = [
            ("FeatureFlagService.is_feature_enabled", "shared.feature_flag_service", "FeatureFlagService", "is_feature_enabled"),
            ("EnhancedParameterValidator.validate_sequential_task_parameters", "shared.enhanced_parameter_validator", "EnhancedParameterValidator", "validate_sequential_task_parameters"),
            ("TaskSequenceConfiguration.get_task_sequence", "shared.task_sequence_configuration", "TaskSequenceConfiguration", "get_task_sequence"),
            ("SecurityMiddleware.process_request", "shared.security_middleware", "SecurityMiddleware", "process_request"),
            ("PerformanceOptimizer.optimize_execution", "shared.performance_optimization_framework", "PerformanceOptimizer", "optimize_execution")
        ]
        
        all_methods_exist = True
        for test_name, module_name, class_name, method_name in method_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                instance = cls()
                method = getattr(instance, method_name)
                self.log_result(f"Method: {test_name}", True)
            except Exception as e:
                self.log_result(f"Method: {test_name}", False, f"Error: {e}")
                all_methods_exist = False
        
        return all_methods_exist
    
    def validate_configuration_files(self) -> bool:
        """Validate that configuration files exist and are valid"""
        print("\n⚙️  Validating Configuration Files")
        print("-" * 50)
        
        config_files = [
            "config/common.json",
            "config/local.json", 
            "config/production.json",
            "config/feature_flags.json"
        ]
        
        all_valid = True
        for config_file in config_files:
            if Path(config_file).exists():
                try:
                    with open(config_file, 'r') as f:
                        json.load(f)
                    self.log_result(f"Config file: {config_file}", True, "Valid JSON")
                except json.JSONDecodeError as e:
                    self.log_result(f"Config file: {config_file}", False, f"Invalid JSON: {e}")
                    all_valid = False
            else:
                self.log_result(f"Config file: {config_file}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_api_endpoints(self) -> bool:
        """Validate that enhanced API endpoints exist"""
        print("\n🌐 Validating Enhanced API Endpoints")
        print("-" * 50)
        
        try:
            from api.enhanced_task_endpoints import enhanced_task_endpoints
            
            # Check if it's a blueprint or has expected methods
            if hasattr(enhanced_task_endpoints, 'route') or hasattr(enhanced_task_endpoints, 'execute_task'):
                self.log_result("Enhanced Task Endpoints", True, "Blueprint/module loaded")
            else:
                self.log_result("Enhanced Task Endpoints", False, "No expected methods found")
                return False
                
        except ImportError as e:
            self.log_result("Enhanced Task Endpoints", False, f"Import failed: {e}")
            return False
        except Exception as e:
            self.log_result("Enhanced Task Endpoints", False, f"Error: {e}")
            return False
        
        return True
    
    def validate_test_files(self) -> bool:
        """Validate that test files exist"""
        print("\n🧪 Validating Test Files")
        print("-" * 50)
        
        test_files = [
            "tests/unit/test_enhanced_auth_service.py",
            "tests/unit/test_enhanced_configuration_manager.py",
            "tests/unit/test_enhanced_parameter_validator.py",
            "tests/unit/test_feature_flag_service.py",
            "tests/unit/test_monitoring_dashboards.py",
            "tests/unit/test_error_handler.py",
            "tests/unit/test_security_middleware.py",
            "tests/integration/test_sequential_task_processing.py",
            "tests/integration/test_execution_log_coordination.py",
            "tests/integration/test_deployment_integration.py"
        ]
        
        all_exist = True
        for test_file in test_files:
            if Path(test_file).exists():
                self.log_result(f"Test file: {test_file}", True)
            else:
                self.log_result(f"Test file: {test_file}", False, "File not found")
                all_exist = False
        
        return all_exist
    
    def validate_documentation(self) -> bool:
        """Validate that documentation files exist"""
        print("\n📚 Validating Documentation")
        print("-" * 50)
        
        doc_files = [
            "docs/operations/deployment-runbook.md",
            "docs/operations/troubleshooting-guide.md",
            "docs/operations/monitoring-alerting.md",
            "docs/openapi/README.md",
            "docs/openapi/sfdc-api-spec.yaml",
            "docs/architecture/adr/README.md"
        ]
        
        all_exist = True
        for doc_file in doc_files:
            if Path(doc_file).exists():
                self.log_result(f"Documentation: {doc_file}", True)
            else:
                self.log_result(f"Documentation: {doc_file}", False, "File not found")
                all_exist = False
        
        return all_exist
    
    def validate_infrastructure_files(self) -> bool:
        """Validate that infrastructure files exist"""
        print("\n🏗️  Validating Infrastructure Files")
        print("-" * 50)
        
        infra_files = [
            "infrastructure/main.bicep",
            "infrastructure/modules/functionapp.bicep",
            "infrastructure/modules/keyvault.bicep",
            "infrastructure/modules/appinsights.bicep",
            "infrastructure/deploy.sh",
            "pipeline-func-sfdc-dev.yml",
            "pipeline-func-sfdc-staging.yml",
            "pipeline-func-sfdc-prod.yml"
        ]
        
        all_exist = True
        for infra_file in infra_files:
            if Path(infra_file).exists():
                self.log_result(f"Infrastructure: {infra_file}", True)
            else:
                self.log_result(f"Infrastructure: {infra_file}", False, "File not found")
                all_exist = False
        
        return all_exist
    
    def run_validation(self) -> bool:
        """Run all validation tests"""
        print("🔍 Enhanced Components Integration Validation")
        print("=" * 60)
        
        validation_tests = [
            ("File Structure", self.validate_file_structure),
            ("Component Imports", self.validate_imports),
            ("Class Instantiation", self.validate_class_instantiation),
            ("Method Signatures", self.validate_method_signatures),
            ("Configuration Files", self.validate_configuration_files),
            ("API Endpoints", self.validate_api_endpoints),
            ("Test Files", self.validate_test_files),
            ("Documentation", self.validate_documentation),
            ("Infrastructure Files", self.validate_infrastructure_files)
        ]
        
        for test_name, test_func in validation_tests:
            print(f"\n🔍 Running: {test_name}")
            try:
                test_func()
            except Exception as e:
                self.log_result(f"{test_name} (Exception)", False, f"Unexpected error: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print(f"🏁 Validation Complete: {self.passed}/{self.total} checks passed")
        
        success_rate = (self.passed / self.total) * 100 if self.total > 0 else 0
        
        if success_rate >= 90:
            print(f"🎉 Excellent! {success_rate:.1f}% of checks passed")
            return True
        elif success_rate >= 75:
            print(f"✅ Good! {success_rate:.1f}% of checks passed")
            return True
        elif success_rate >= 60:
            print(f"⚠️  Acceptable! {success_rate:.1f}% of checks passed")
            return True
        else:
            print(f"❌ Poor! Only {success_rate:.1f}% of checks passed")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate validation report"""
        return {
            "timestamp": str(os.popen('date').read().strip()),
            "total_checks": self.total,
            "passed_checks": self.passed,
            "success_rate": (self.passed / self.total) * 100 if self.total > 0 else 0,
            "results": self.results
        }


def main():
    """Main function"""
    validator = IntegrationValidator()
    
    # Run validation
    success = validator.run_validation()
    
    # Generate report
    report = validator.generate_report()
    
    # Save report
    with open("integration_validation_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Detailed report saved to: integration_validation_report.json")
    
    return success


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)