#!/usr/bin/env python3
"""
Integration Tests for SFDC Function App Deployment

This module contains integration tests that validate the deployed SFDC service
works correctly with external dependencies and services.
"""

import pytest
import requests
import json
import time
import os
from typing import Dict, Any, Optional
from urllib.parse import urljoin


class TestDeploymentIntegration:
    """Integration tests for deployed SFDC service"""
    
    @pytest.fixture(scope="class")
    def base_url(self):
        """Get base URL from environment or use default"""
        return os.getenv('BASE_URL', 'https://func-atomsec-sfdc-dev.azurewebsites.net')
    
    @pytest.fixture(scope="class")
    def session(self):
        """Create HTTP session with default configuration"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'IntegrationTest/1.0',
            'Accept': 'application/json'
        })
        session.timeout = 30
        return session
    
    def test_service_health(self, base_url, session):
        """Test that the service is healthy and responding"""
        response = session.get(urljoin(base_url, '/api/health'))
        
        assert response.status_code == 200
        assert response.headers.get('content-type', '').startswith('application/json')
        
        health_data = response.json()
        assert 'status' in health_data
        assert health_data['status'] in ['healthy', 'ok', 'running']
        assert 'timestamp' in health_data
    
    def test_cors_configuration(self, base_url, session):
        """Test CORS configuration is working"""
        # Test preflight request
        headers = {
            'Origin': 'https://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = session.options(urljoin(base_url, '/api/health'), headers=headers)
        
        assert response.status_code in [200, 204]
        assert 'Access-Control-Allow-Origin' in response.headers
    
    def test_api_endpoints_structure(self, base_url, session):
        """Test that API endpoints have expected structure"""
        endpoints_to_test = [
            '/api/organizations',
            '/api/integrations',
            '/api/policies',
            '/api/security/health-score'
        ]
        
        for endpoint in endpoints_to_test:
            response = session.get(urljoin(base_url, endpoint))
            
            # We expect either success (200) or authentication required (401/403)
            # but not not-found (404) or server error (500)
            assert response.status_code in [200, 401, 403], f"Endpoint {endpoint} returned {response.status_code}"
            
            # If we get a JSON response, it should be valid
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    response.json()  # Should not raise exception
                except json.JSONDecodeError:
                    pytest.fail(f"Endpoint {endpoint} returned invalid JSON")
    
    def test_error_handling(self, base_url, session):
        """Test error handling for invalid requests"""
        # Test non-existent endpoint
        response = session.get(urljoin(base_url, '/api/nonexistent'))
        assert response.status_code == 404
        
        # Test invalid resource ID
        response = session.get(urljoin(base_url, '/api/organizations/invalid-id'))
        assert response.status_code in [400, 404]
        
        # Test malformed POST request
        response = session.post(
            urljoin(base_url, '/api/tasks'),
            json={'invalid': 'data'},
            headers={'Content-Type': 'application/json'}
        )
        assert response.status_code in [400, 401, 422]
    
    def test_response_times(self, base_url, session):
        """Test that response times are acceptable"""
        start_time = time.time()
        response = session.get(urljoin(base_url, '/api/health'))
        response_time = (time.time() - start_time) * 1000  # Convert to ms
        
        assert response.status_code == 200
        assert response_time < 5000, f"Response time {response_time:.1f}ms exceeds 5000ms threshold"
    
    def test_concurrent_requests(self, base_url, session):
        """Test handling of concurrent requests"""
        import concurrent.futures
        
        def make_health_request():
            return session.get(urljoin(base_url, '/api/health'))
        
        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_health_request) for _ in range(10)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # At least 80% should succeed
        successful_responses = sum(1 for r in responses if r.status_code == 200)
        success_rate = successful_responses / len(responses)
        
        assert success_rate >= 0.8, f"Only {success_rate:.1%} of concurrent requests succeeded"
    
    def test_security_headers(self, base_url, session):
        """Test that security headers are present"""
        response = session.get(urljoin(base_url, '/api/health'))
        
        assert response.status_code == 200
        
        # Check for important security headers
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection'
        ]
        
        present_headers = [h for h in security_headers if h in response.headers]
        
        # At least some security headers should be present
        assert len(present_headers) > 0, "No security headers found in response"
    
    def test_authentication_flow(self, base_url, session):
        """Test authentication-related endpoints"""
        # Test that protected endpoints require authentication
        protected_endpoints = [
            '/api/organizations',
            '/api/integrations',
            '/api/users/current'
        ]
        
        for endpoint in protected_endpoints:
            response = session.get(urljoin(base_url, endpoint))
            
            # Should either work (if auth is disabled) or require auth
            assert response.status_code in [200, 401, 403], f"Unexpected status for {endpoint}: {response.status_code}"
    
    def test_database_connectivity(self, base_url, session):
        """Test database connectivity (indirect test)"""
        # Test endpoints that would require database access
        response = session.get(urljoin(base_url, '/api/organizations'))
        
        # Should not return 500 (server error) which might indicate DB issues
        assert response.status_code != 500, "Database connectivity may be broken (500 error)"
        
        # Should return either data or auth required, not service unavailable
        assert response.status_code not in [503, 502], "Service may be unavailable"
    
    def test_external_service_integration(self, base_url, session):
        """Test integration with external services"""
        # Test endpoints that might call external services
        external_dependent_endpoints = [
            '/api/integrations',
            '/api/security/health-score'
        ]
        
        for endpoint in external_dependent_endpoints:
            response = session.get(urljoin(base_url, endpoint))
            
            # Should not timeout or return service unavailable
            assert response.status_code not in [502, 503, 504], f"External service integration issue for {endpoint}"
    
    def test_performance_under_load(self, base_url, session):
        """Test performance under moderate load"""
        import concurrent.futures
        import statistics
        
        def timed_request():
            start_time = time.time()
            response = session.get(urljoin(base_url, '/api/health'))
            duration = (time.time() - start_time) * 1000
            return response.status_code, duration
        
        # Make 20 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(timed_request) for _ in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        status_codes = [r[0] for r in results]
        durations = [r[1] for r in results]
        
        # Check success rate
        success_rate = sum(1 for code in status_codes if code == 200) / len(status_codes)
        assert success_rate >= 0.9, f"Success rate under load: {success_rate:.1%}"
        
        # Check performance metrics
        avg_duration = statistics.mean(durations)
        p95_duration = sorted(durations)[int(len(durations) * 0.95)]
        
        assert avg_duration < 3000, f"Average response time {avg_duration:.1f}ms exceeds 3000ms"
        assert p95_duration < 10000, f"95th percentile response time {p95_duration:.1f}ms exceeds 10000ms"
    
    def test_api_versioning(self, base_url, session):
        """Test API versioning if implemented"""
        # Test that API endpoints are accessible
        response = session.get(urljoin(base_url, '/api/health'))
        assert response.status_code == 200
        
        # Check if version information is available
        if 'api-version' in response.headers:
            version = response.headers['api-version']
            assert version is not None and version != ""
    
    def test_monitoring_endpoints(self, base_url, session):
        """Test monitoring and observability endpoints"""
        monitoring_endpoints = [
            '/api/health',
            # Add other monitoring endpoints as they become available
        ]
        
        for endpoint in monitoring_endpoints:
            response = session.get(urljoin(base_url, endpoint))
            assert response.status_code == 200, f"Monitoring endpoint {endpoint} not accessible"
    
    @pytest.mark.slow
    def test_extended_availability(self, base_url, session):
        """Extended availability test (runs for longer duration)"""
        # Test availability over a longer period
        test_duration = 60  # 1 minute
        check_interval = 5  # 5 seconds
        
        start_time = time.time()
        successful_checks = 0
        total_checks = 0
        
        while time.time() - start_time < test_duration:
            try:
                response = session.get(urljoin(base_url, '/api/health'))
                total_checks += 1
                
                if response.status_code == 200:
                    successful_checks += 1
                
                time.sleep(check_interval)
                
            except requests.RequestException:
                total_checks += 1
                # Continue testing even if individual requests fail
        
        availability = successful_checks / total_checks if total_checks > 0 else 0
        assert availability >= 0.95, f"Availability over {test_duration}s: {availability:.1%}"


class TestDeploymentEnvironment:
    """Tests specific to deployment environment configuration"""
    
    def test_environment_configuration(self, base_url, session):
        """Test environment-specific configuration"""
        response = session.get(urljoin(base_url, '/api/health'))
        assert response.status_code == 200
        
        # Environment should be detectable from URL or response
        if 'dev' in base_url:
            # Development environment tests
            pass
        elif 'staging' in base_url:
            # Staging environment tests
            pass
        elif 'prod' in base_url:
            # Production environment tests - stricter requirements
            health_data = response.json()
            assert 'environment' not in health_data or health_data.get('environment') != 'development'
    
    def test_ssl_configuration(self, base_url):
        """Test SSL/TLS configuration"""
        assert base_url.startswith('https://'), "Service should be accessible over HTTPS only"
        
        # Test that HTTP redirects to HTTPS (if applicable)
        if base_url.startswith('https://'):
            http_url = base_url.replace('https://', 'http://')
            try:
                response = requests.get(http_url, allow_redirects=False, timeout=10)
                # Should either redirect to HTTPS or be blocked
                assert response.status_code in [301, 302, 403, 404], "HTTP should redirect to HTTPS or be blocked"
            except requests.RequestException:
                # Connection refused is acceptable (HTTP blocked)
                pass


if __name__ == "__main__":
    # Allow running tests directly
    pytest.main([__file__, "-v"])