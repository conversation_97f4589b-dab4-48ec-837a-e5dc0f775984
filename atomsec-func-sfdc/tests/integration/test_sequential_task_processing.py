"""
Integration tests for Sequential Task Processing

Tests the complete workflow: authenticate → health → metadata → PMD
with proper execution_log_id coordination.
"""

import pytest
import asyncio
import json
import uuid
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import azure.functions as func

from src.shared.execution_log_coordination_service import (
    ExecutionLogCoordinationService,
    get_execution_log_coordination_service
)
from src.shared.task_sequence_configuration import (
    TaskSequenceConfiguration,
    get_task_sequence_configuration
)
from src.shared.enhanced_parameter_validator import (
    get_enhanced_parameter_validator,
    ParameterValidationError
)


@pytest.mark.integration
class TestSequentialTaskProcessing:
    """Integration tests for sequential task processing workflow"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.execution_log_service = get_execution_log_coordination_service()
        self.task_config = get_task_sequence_configuration()
        self.validator = get_enhanced_parameter_validator()
        
        # Test execution log ID
        self.execution_log_id = str(uuid.uuid4())
        
        # Sample request data
        self.base_request_data = {
            "execution_log_id": self.execution_log_id,
            "organization_id": "test_org_123",
            "user_id": "test_user_456",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            }
        }
    
    def create_mock_request(self, data: dict) -> Mock:
        """Create a mock HTTP request"""
        mock_request = Mock(spec=func.HttpRequest)
        mock_request.method = "POST"
        mock_request.url = "https://test.com/api/task"
        mock_request.headers = {
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        }
        mock_request.get_body.return_value = json.dumps(data).encode()
        mock_request.get_json.return_value = data
        return mock_request
    
    @pytest.mark.asyncio
    async def test_complete_sequential_task_workflow(self):
        """Test complete sequential task workflow from start to finish"""
        
        # Step 1: Initialize execution log
        execution_context = await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        assert execution_context is not None
        assert execution_context['execution_log_id'] == self.execution_log_id
        assert execution_context['status'] == 'initialized'
        
        # Step 2: Test sfdc_authenticate task
        auth_request_data = self.base_request_data.copy()
        auth_request_data.update({
            "task_type": "sfdc_authenticate",
            "task_parameters": {
                "validate_connection": True,
                "store_credentials": True
            }
        })
        
        auth_result = await self._simulate_task_execution(
            "sfdc_authenticate",
            auth_request_data
        )
        
        assert auth_result['success'] is True
        assert auth_result['execution_log_id'] == self.execution_log_id
        assert 'authentication_token' in auth_result
        
        # Step 3: Test health_check task
        health_request_data = self.base_request_data.copy()
        health_request_data.update({
            "task_type": "health_check",
            "task_parameters": {
                "check_api_limits": True,
                "check_permissions": True
            },
            "previous_task_result": auth_result
        })
        
        health_result = await self._simulate_task_execution(
            "health_check",
            health_request_data
        )
        
        assert health_result['success'] is True
        assert health_result['execution_log_id'] == self.execution_log_id
        assert 'health_status' in health_result
        
        # Step 4: Test metadata_extraction task
        metadata_request_data = self.base_request_data.copy()
        metadata_request_data.update({
            "task_type": "metadata_extraction",
            "task_parameters": {
                "extract_profiles": True,
                "extract_permission_sets": True,
                "extract_org_settings": True
            },
            "previous_task_result": health_result
        })
        
        metadata_result = await self._simulate_task_execution(
            "metadata_extraction",
            metadata_request_data
        )
        
        assert metadata_result['success'] is True
        assert metadata_result['execution_log_id'] == self.execution_log_id
        assert 'metadata' in metadata_result
        
        # Step 5: Test pmd_apex_security task (final step)
        pmd_request_data = self.base_request_data.copy()
        pmd_request_data.update({
            "task_type": "pmd_apex_security",
            "task_parameters": {
                "scan_apex_classes": True,
                "scan_triggers": True,
                "generate_report": True
            },
            "previous_task_result": metadata_result
        })
        
        pmd_result = await self._simulate_task_execution(
            "pmd_apex_security",
            pmd_request_data
        )
        
        assert pmd_result['success'] is True
        assert pmd_result['execution_log_id'] == self.execution_log_id
        assert 'pmd_results' in pmd_result
        
        # Step 6: Verify execution log completion
        final_context = await self.execution_log_service.get_execution_context(
            self.execution_log_id
        )
        
        assert final_context['status'] == 'completed'
        assert len(final_context['completed_tasks']) == 4
        assert all(task in final_context['completed_tasks'] for task in [
            'sfdc_authenticate', 'health_check', 'metadata_extraction', 'pmd_apex_security'
        ])
    
    async def _simulate_task_execution(self, task_type: str, request_data: dict) -> dict:
        """Simulate task execution with proper validation and coordination"""
        
        # Validate task parameters
        validated_params = self.validator.validate_sequential_task_parameters(
            request_data,
            task_type,
            self.execution_log_id
        )
        
        # Update execution log
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            task_type,
            'in_progress'
        )
        
        # Simulate task execution based on type
        if task_type == "sfdc_authenticate":
            result = await self._simulate_sfdc_authenticate(validated_params)
        elif task_type == "health_check":
            result = await self._simulate_health_check(validated_params)
        elif task_type == "metadata_extraction":
            result = await self._simulate_metadata_extraction(validated_params)
        elif task_type == "pmd_apex_security":
            result = await self._simulate_pmd_scan(validated_params)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
        
        # Update execution log with completion
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            task_type,
            'completed',
            result
        )
        
        return result
    
    async def _simulate_sfdc_authenticate(self, params: dict) -> dict:
        """Simulate Salesforce authentication task"""
        # Mock Salesforce authentication
        with patch('shared.salesforce_client.SalesforceClient') as mock_sf_client:
            mock_client = Mock()
            mock_client.authenticate.return_value = True
            mock_client.get_session_info.return_value = {
                'session_id': 'mock_session_123',
                'server_url': 'https://test.salesforce.com'
            }
            mock_sf_client.return_value = mock_client
            
            return {
                'success': True,
                'execution_log_id': params['execution_log_id'],
                'task_type': 'sfdc_authenticate',
                'authentication_token': 'mock_session_123',
                'server_url': 'https://test.salesforce.com',
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _simulate_health_check(self, params: dict) -> dict:
        """Simulate health check task"""
        # Mock health check operations
        return {
            'success': True,
            'execution_log_id': params['execution_log_id'],
            'task_type': 'health_check',
            'health_status': {
                'api_limits': {'used': 100, 'total': 5000},
                'permissions': {'valid': True, 'missing': []},
                'connectivity': {'status': 'healthy', 'response_time': 150}
            },
            'timestamp': datetime.utcnow().isoformat()
        }
    
    async def _simulate_metadata_extraction(self, params: dict) -> dict:
        """Simulate metadata extraction task"""
        # Mock metadata extraction
        return {
            'success': True,
            'execution_log_id': params['execution_log_id'],
            'task_type': 'metadata_extraction',
            'metadata': {
                'profiles': ['System Administrator', 'Standard User'],
                'permission_sets': ['Custom_Permissions', 'API_Access'],
                'org_settings': {
                    'password_policy': {'min_length': 8, 'complexity': True},
                    'session_settings': {'timeout': 120, 'lock_sessions': True}
                }
            },
            'extraction_stats': {
                'profiles_count': 2,
                'permission_sets_count': 2,
                'settings_extracted': 2
            },
            'timestamp': datetime.utcnow().isoformat()
        }
    
    async def _simulate_pmd_scan(self, params: dict) -> dict:
        """Simulate PMD security scan task"""
        # Mock PMD scan
        return {
            'success': True,
            'execution_log_id': params['execution_log_id'],
            'task_type': 'pmd_apex_security',
            'pmd_results': {
                'violations': [
                    {
                        'rule': 'AvoidHardcodingId',
                        'severity': 'High',
                        'file': 'TestClass.cls',
                        'line': 15,
                        'description': 'Hardcoded ID found'
                    }
                ],
                'summary': {
                    'total_violations': 1,
                    'high_severity': 1,
                    'medium_severity': 0,
                    'low_severity': 0
                }
            },
            'scan_stats': {
                'files_scanned': 25,
                'classes_analyzed': 20,
                'triggers_analyzed': 5
            },
            'timestamp': datetime.utcnow().isoformat()
        }
    
    @pytest.mark.asyncio
    async def test_execution_log_id_tracking_across_tasks(self):
        """Test that execution_log_id is properly tracked across all tasks"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Track execution_log_id through multiple tasks
        task_types = ["sfdc_authenticate", "health_check", "metadata_extraction"]
        
        for task_type in task_types:
            request_data = self.base_request_data.copy()
            request_data["task_type"] = task_type
            
            # Validate that execution_log_id is properly validated
            validated_params = self.validator.validate_sequential_task_parameters(
                request_data,
                task_type,
                self.execution_log_id
            )
            
            assert validated_params['execution_log_id'] == self.execution_log_id
            
            # Update task status
            await self.execution_log_service.update_task_status(
                self.execution_log_id,
                task_type,
                'completed'
            )
        
        # Verify execution context has all tasks
        context = await self.execution_log_service.get_execution_context(
            self.execution_log_id
        )
        
        assert len(context['completed_tasks']) == 3
        assert all(task in context['completed_tasks'] for task in task_types)
    
    @pytest.mark.asyncio
    async def test_task_failure_handling_in_sequence(self):
        """Test handling of task failures in the sequence"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Simulate successful authentication
        auth_data = self.base_request_data.copy()
        auth_data["task_type"] = "sfdc_authenticate"
        
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed'
        )
        
        # Simulate health check failure
        health_data = self.base_request_data.copy()
        health_data["task_type"] = "health_check"
        
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "health_check",
            'failed',
            {'error': 'API limits exceeded', 'retry_after': 3600}
        )
        
        # Verify execution context reflects the failure
        context = await self.execution_log_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['status'] == 'failed'
        assert 'sfdc_authenticate' in context['completed_tasks']
        assert 'health_check' in context['failed_tasks']
        assert context['current_task'] == 'health_check'
    
    @pytest.mark.asyncio
    async def test_task_sequence_validation(self):
        """Test validation of task sequence order"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Try to run health_check before authentication (should fail)
        health_data = self.base_request_data.copy()
        health_data["task_type"] = "health_check"
        
        with pytest.raises(ParameterValidationError, match="prerequisite"):
            self.validator.validate_sequential_task_parameters(
                health_data,
                "health_check",
                self.execution_log_id
            )
        
        # Complete authentication first
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed'
        )
        
        # Now health_check should be valid
        validated_params = self.validator.validate_sequential_task_parameters(
            health_data,
            "health_check",
            self.execution_log_id
        )
        
        assert validated_params['task_type'] == "health_check"
    
    @pytest.mark.asyncio
    async def test_concurrent_task_execution_prevention(self):
        """Test prevention of concurrent task execution for same execution_log_id"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Start first task
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'in_progress'
        )
        
        # Try to start another task concurrently (should fail)
        health_data = self.base_request_data.copy()
        health_data["task_type"] = "health_check"
        
        with pytest.raises(ParameterValidationError, match="already in progress"):
            self.validator.validate_sequential_task_parameters(
                health_data,
                "health_check",
                self.execution_log_id
            )
    
    @pytest.mark.asyncio
    async def test_task_timeout_handling(self):
        """Test handling of task timeouts"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Start a task and simulate timeout
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'in_progress'
        )
        
        # Simulate timeout by updating task status
        await self.execution_log_service.handle_task_timeout(
            self.execution_log_id,
            "sfdc_authenticate",
            timeout_seconds=300
        )
        
        # Verify execution context reflects timeout
        context = await self.execution_log_service.get_execution_context(
            self.execution_log_id
        )
        
        assert context['status'] == 'failed'
        assert 'sfdc_authenticate' in context['failed_tasks']
    
    @pytest.mark.asyncio
    async def test_task_retry_mechanism(self):
        """Test task retry mechanism"""
        
        # Initialize execution log
        await self.execution_log_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            self.base_request_data
        )
        
        # Simulate task failure with retry
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'failed',
            {'error': 'Temporary network error', 'retryable': True}
        )
        
        # Retry the task
        retry_result = await self.execution_log_service.retry_failed_task(
            self.execution_log_id,
            "sfdc_authenticate"
        )
        
        assert retry_result['can_retry'] is True
        assert retry_result['retry_count'] == 1
        
        # Complete the retry
        await self.execution_log_service.update_task_status(
            self.execution_log_id,
            "sfdc_authenticate",
            'completed'
        )
        
        # Verify execution context
        context = await self.execution_log_service.get_execution_context(
            self.execution_log_id
        )
        
        assert 'sfdc_authenticate' in context['completed_tasks']
        assert context['task_retry_counts']['sfdc_authenticate'] == 1


@pytest.mark.integration
class TestTaskSequenceConfiguration:
    """Integration tests for task sequence configuration"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.task_config = get_task_sequence_configuration()
    
    def test_get_task_sequence_definition(self):
        """Test getting task sequence definition"""
        sequence = self.task_config.get_task_sequence("sfdc_scan")
        
        assert sequence is not None
        assert len(sequence['tasks']) == 4
        
        expected_tasks = [
            'sfdc_authenticate',
            'health_check', 
            'metadata_extraction',
            'pmd_apex_security'
        ]
        
        actual_tasks = [task['name'] for task in sequence['tasks']]
        assert actual_tasks == expected_tasks
    
    def test_task_dependencies_validation(self):
        """Test task dependencies validation"""
        sequence = self.task_config.get_task_sequence("sfdc_scan")
        
        # Check that each task has correct dependencies
        tasks_by_name = {task['name']: task for task in sequence['tasks']}
        
        # sfdc_authenticate should have no dependencies
        assert tasks_by_name['sfdc_authenticate']['dependencies'] == []
        
        # health_check should depend on sfdc_authenticate
        assert tasks_by_name['health_check']['dependencies'] == ['sfdc_authenticate']
        
        # metadata_extraction should depend on health_check
        assert tasks_by_name['metadata_extraction']['dependencies'] == ['health_check']
        
        # pmd_apex_security should depend on metadata_extraction
        assert tasks_by_name['pmd_apex_security']['dependencies'] == ['metadata_extraction']
    
    def test_task_configuration_parameters(self):
        """Test task configuration parameters"""
        sequence = self.task_config.get_task_sequence("sfdc_scan")
        tasks_by_name = {task['name']: task for task in sequence['tasks']}
        
        # Check sfdc_authenticate configuration
        auth_config = tasks_by_name['sfdc_authenticate']['config']
        assert 'timeout_seconds' in auth_config
        assert 'retry_attempts' in auth_config
        assert 'required_parameters' in auth_config
        
        # Check required parameters for authentication
        required_params = auth_config['required_parameters']
        assert 'salesforce_credentials' in required_params
        assert 'organization_id' in required_params
    
    def test_validate_task_can_execute(self):
        """Test validation that a task can execute"""
        execution_context = {
            'execution_log_id': 'test-123',
            'completed_tasks': ['sfdc_authenticate'],
            'failed_tasks': [],
            'current_task': None,
            'status': 'in_progress'
        }
        
        # health_check should be able to execute after sfdc_authenticate
        can_execute = self.task_config.validate_task_can_execute(
            "health_check",
            execution_context
        )
        assert can_execute is True
        
        # metadata_extraction should NOT be able to execute yet
        can_execute = self.task_config.validate_task_can_execute(
            "metadata_extraction",
            execution_context
        )
        assert can_execute is False


@pytest.mark.integration
class TestEndToEndWorkflow:
    """End-to-end integration tests for complete workflow"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.execution_log_id = str(uuid.uuid4())
        self.execution_service = get_execution_log_coordination_service()
        self.task_config = get_task_sequence_configuration()
        self.validator = get_enhanced_parameter_validator()
    
    @pytest.mark.asyncio
    async def test_complete_sfdc_scan_workflow(self):
        """Test complete SFDC scan workflow from API request to completion"""
        
        # Simulate initial API request
        initial_request = {
            "scan_type": "full_security_scan",
            "organization_id": "test_org_123",
            "user_id": "test_user_456",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            },
            "scan_options": {
                "include_profiles": True,
                "include_permission_sets": True,
                "include_pmd_scan": True,
                "generate_report": True
            }
        }
        
        # Step 1: Initialize workflow
        execution_context = await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            initial_request
        )
        
        assert execution_context['status'] == 'initialized'
        
        # Step 2: Execute task sequence
        sequence = self.task_config.get_task_sequence("sfdc_scan")
        
        for task_def in sequence['tasks']:
            task_name = task_def['name']
            
            # Prepare task request
            task_request = initial_request.copy()
            task_request.update({
                "execution_log_id": self.execution_log_id,
                "task_type": task_name,
                "task_parameters": task_def.get('default_parameters', {})
            })
            
            # Validate task can execute
            current_context = await self.execution_service.get_execution_context(
                self.execution_log_id
            )
            
            can_execute = self.task_config.validate_task_can_execute(
                task_name,
                current_context
            )
            
            assert can_execute, f"Task {task_name} should be able to execute"
            
            # Validate parameters
            validated_params = self.validator.validate_sequential_task_parameters(
                task_request,
                task_name,
                self.execution_log_id
            )
            
            # Execute task (simulated)
            await self.execution_service.update_task_status(
                self.execution_log_id,
                task_name,
                'in_progress'
            )
            
            # Simulate task completion
            task_result = {
                'success': True,
                'task_type': task_name,
                'execution_log_id': self.execution_log_id,
                'timestamp': datetime.utcnow().isoformat(),
                'data': f"Mock result for {task_name}"
            }
            
            await self.execution_service.update_task_status(
                self.execution_log_id,
                task_name,
                'completed',
                task_result
            )
        
        # Step 3: Verify workflow completion
        final_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        
        assert final_context['status'] == 'completed'
        assert len(final_context['completed_tasks']) == 4
        assert len(final_context['failed_tasks']) == 0
        
        # Verify all expected tasks completed
        expected_tasks = [
            'sfdc_authenticate',
            'health_check',
            'metadata_extraction', 
            'pmd_apex_security'
        ]
        
        for task in expected_tasks:
            assert task in final_context['completed_tasks']
    
    @pytest.mark.asyncio
    async def test_workflow_with_partial_failure_and_recovery(self):
        """Test workflow with partial failure and recovery"""
        
        # Initialize workflow
        initial_request = {
            "scan_type": "security_scan",
            "organization_id": "test_org_123",
            "salesforce_credentials": {
                "username": "<EMAIL>",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            }
        }
        
        await self.execution_service.initialize_execution_log(
            self.execution_log_id,
            "sfdc_scan",
            initial_request
        )
        
        # Complete first two tasks successfully
        for task_name in ['sfdc_authenticate', 'health_check']:
            await self.execution_service.update_task_status(
                self.execution_log_id,
                task_name,
                'completed'
            )
        
        # Fail metadata_extraction task
        await self.execution_service.update_task_status(
            self.execution_log_id,
            'metadata_extraction',
            'failed',
            {'error': 'API timeout', 'retryable': True}
        )
        
        # Verify workflow is in failed state
        context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        assert context['status'] == 'failed'
        assert 'metadata_extraction' in context['failed_tasks']
        
        # Retry failed task
        retry_result = await self.execution_service.retry_failed_task(
            self.execution_log_id,
            'metadata_extraction'
        )
        assert retry_result['can_retry'] is True
        
        # Complete retry successfully
        await self.execution_service.update_task_status(
            self.execution_log_id,
            'metadata_extraction',
            'completed'
        )
        
        # Complete final task
        await self.execution_service.update_task_status(
            self.execution_log_id,
            'pmd_apex_security',
            'completed'
        )
        
        # Verify workflow completed successfully
        final_context = await self.execution_service.get_execution_context(
            self.execution_log_id
        )
        assert final_context['status'] == 'completed'
        assert len(final_context['completed_tasks']) == 4
        assert final_context['task_retry_counts']['metadata_extraction'] == 1