#!/usr/bin/env python3
"""
Enhanced Monitoring and Alerting Validation

This script validates the enhanced monitoring dashboards, metrics collection,
alerting rules, and notification systems for the SFDC service.
"""

import sys
import os
import json
import asyncio
import time
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional

# Add paths
sys.path.insert(0, '.')
sys.path.insert(0, 'shared')
sys.path.insert(0, 'api')

class MonitoringAlertingValidator:
    """Validates monitoring and alerting components"""
    
    def __init__(self):
        self.results = []
        self.passed = 0
        self.total = 0
    
    def log_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        self.total += 1
        if passed:
            self.passed += 1
            print(f"✅ {test_name}: PASSED {message}")
        else:
            print(f"❌ {test_name}: FAILED {message}")
        
        self.results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
    
    def validate_monitoring_components(self) -> bool:
        """Validate monitoring component files exist"""
        print("\n📊 Validating Monitoring Components")
        print("-" * 50)
        
        monitoring_files = [
            ("Monitoring Dashboards Service", "shared/monitoring_dashboards.py"),
            ("Enhanced Monitoring", "shared/monitoring.py"),
            ("Performance Monitoring", "shared/performance_optimizer.py"),
            ("Cache Performance Analytics", "shared/cache_performance_analytics.py"),
            ("Security Monitoring", "shared/security_monitoring_service.py"),
            ("User Activity Monitor", "shared/user_activity_monitor.py"),
            ("Task Performance Monitor", "shared/task_performance_monitor.py"),
            ("Intrusion Detection System", "shared/intrusion_detection_system.py")
        ]
        
        all_exist = True
        for name, file_path in monitoring_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for monitoring-related keywords
                    monitoring_keywords = ["metric", "monitor", "track", "log", "alert", "dashboard"]
                    found_keywords = [kw for kw in monitoring_keywords if kw.lower() in content.lower()]
                    
                    if found_keywords:
                        self.log_result(f"Monitoring Component: {name}", True, f"Contains: {', '.join(found_keywords[:3])}")
                    else:
                        self.log_result(f"Monitoring Component: {name}", False, "No monitoring keywords found")
                        all_exist = False
                        
                except Exception as e:
                    self.log_result(f"Monitoring Component: {name}", False, f"Error reading file: {e}")
                    all_exist = False
            else:
                self.log_result(f"Monitoring Component: {name}", False, "File not found")
                all_exist = False
        
        return all_exist
    
    def validate_monitoring_instantiation(self) -> bool:
        """Validate that monitoring components can be instantiated"""
        print("\n🏗️  Validating Monitoring Component Instantiation")
        print("-" * 50)
        
        monitoring_components = [
            ("MonitoringDashboardsService", "shared.monitoring_dashboards", "MonitoringDashboardsService"),
            ("SecurityMonitoringService", "shared.security_monitoring_service", "SecurityMonitoringService"),
            ("UserActivityMonitor", "shared.user_activity_monitor", "UserActivityMonitor"),
            ("TaskPerformanceMonitor", "shared.task_performance_monitor", "TaskPerformanceMonitor"),
            ("CachePerformanceAnalytics", "shared.cache_performance_analytics", "CachePerformanceAnalytics")
        ]
        
        all_instantiated = True
        for name, module_name, class_name in monitoring_components:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                instance = cls()
                self.log_result(f"Instantiate: {name}", True)
            except ImportError as e:
                self.log_result(f"Instantiate: {name}", False, f"Import error: {e}")
                all_instantiated = False
            except AttributeError as e:
                self.log_result(f"Instantiate: {name}", False, f"Class not found: {e}")
                all_instantiated = False
            except Exception as e:
                self.log_result(f"Instantiate: {name}", False, f"Instantiation error: {e}")
                all_instantiated = False
        
        return all_instantiated
    
    async def validate_monitoring_functionality(self) -> bool:
        """Validate monitoring functionality"""
        print("\n⚡ Validating Monitoring Functionality")
        print("-" * 50)
        
        try:
            # Test monitoring dashboards service
            from src.shared.monitoring_dashboards import MonitoringDashboardsService
            
            monitoring_service = MonitoringDashboardsService()
            
            # Test basic functionality
            test_cases = [
                ("Track Custom Metric", self._test_track_custom_metric, monitoring_service),
                ("Track Task Execution", self._test_track_task_execution, monitoring_service),
                ("Track Security Event", self._test_track_security_event, monitoring_service),
                ("Get Task Metrics", self._test_get_task_metrics, monitoring_service),
                ("Get Error Metrics", self._test_get_error_metrics, monitoring_service)
            ]
            
            all_functional = True
            for test_name, test_func, service in test_cases:
                try:
                    if asyncio.iscoroutinefunction(test_func):
                        result = await test_func(service)
                    else:
                        result = test_func(service)
                    
                    if result:
                        self.log_result(f"Monitoring Function: {test_name}", True)
                    else:
                        self.log_result(f"Monitoring Function: {test_name}", False, "Function returned False")
                        all_functional = False
                        
                except Exception as e:
                    self.log_result(f"Monitoring Function: {test_name}", False, f"Error: {e}")
                    all_functional = False
            
            return all_functional
            
        except Exception as e:
            self.log_result("Monitoring Functionality", False, f"Failed to initialize monitoring service: {e}")
            return False
    
    async def _test_track_custom_metric(self, service) -> bool:
        """Test tracking custom metrics"""
        try:
            if hasattr(service, 'track_custom_metric'):
                await service.track_custom_metric("test_metric", 1.0, {"test": "value"})
                return True
            else:
                # Try alternative method names
                if hasattr(service, 'track_metric'):
                    await service.track_metric("test_metric", 1.0)
                    return True
                return False
        except Exception:
            return False
    
    async def _test_track_task_execution(self, service) -> bool:
        """Test tracking task execution"""
        try:
            if hasattr(service, 'track_task_execution'):
                await service.track_task_execution("test_task", "test_log_123", {"success": True})
                return True
            elif hasattr(service, 'track_task_start'):
                await service.track_task_start("test_task", "test_log_123")
                return True
            return False
        except Exception:
            return False
    
    async def _test_track_security_event(self, service) -> bool:
        """Test tracking security events"""
        try:
            if hasattr(service, 'track_security_event'):
                await service.track_security_event("test_event", {"user_id": "test"})
                return True
            return False
        except Exception:
            return False
    
    async def _test_get_task_metrics(self, service) -> bool:
        """Test getting task metrics"""
        try:
            if hasattr(service, 'get_task_metrics'):
                metrics = await service.get_task_metrics("test_task")
                return metrics is not None
            return False
        except Exception:
            return False
    
    async def _test_get_error_metrics(self, service) -> bool:
        """Test getting error metrics"""
        try:
            if hasattr(service, 'get_error_metrics'):
                metrics = await service.get_error_metrics("test_task")
                return metrics is not None
            return False
        except Exception:
            return False
    
    def validate_dashboard_configurations(self) -> bool:
        """Validate dashboard configuration files"""
        print("\n📈 Validating Dashboard Configurations")
        print("-" * 50)
        
        # Check for dashboard-related files
        dashboard_files = [
            ("Application Insights Config", "infrastructure/modules/appinsights.bicep"),
            ("Monitoring Documentation", "docs/operations/monitoring-alerting.md"),
            ("Dashboard Scripts", "scripts/deploy_monitoring_dashboards.py")
        ]
        
        all_valid = True
        for name, file_path in dashboard_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for dashboard-related keywords
                    if "appinsights" in file_path.lower():
                        keywords = ["Application", "Insights", "workspace", "dashboard"]
                    elif "monitoring" in file_path.lower():
                        keywords = ["dashboard", "metric", "alert", "monitor"]
                    else:
                        keywords = ["dashboard", "chart", "metric"]
                    
                    found_keywords = [kw for kw in keywords if kw.lower() in content.lower()]
                    
                    if found_keywords:
                        self.log_result(f"Dashboard Config: {name}", True, f"Contains: {', '.join(found_keywords[:2])}")
                    else:
                        self.log_result(f"Dashboard Config: {name}", False, "No dashboard keywords found")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Dashboard Config: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Dashboard Config: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_alerting_configuration(self) -> bool:
        """Validate alerting configuration"""
        print("\n🚨 Validating Alerting Configuration")
        print("-" * 50)
        
        # Check for alerting-related configurations
        alerting_checks = [
            ("Monitoring Documentation", "docs/operations/monitoring-alerting.md", ["alert", "notification", "threshold"]),
            ("Application Insights Module", "infrastructure/modules/appinsights.bicep", ["alert", "rule", "action"]),
            ("Security Monitoring", "shared/security_monitoring_service.py", ["alert", "notify", "threshold"]),
            ("Intrusion Detection", "shared/intrusion_detection_system.py", ["alert", "detect", "suspicious"])
        ]
        
        all_valid = True
        for name, file_path, keywords in alerting_checks:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read().lower()
                    
                    found_keywords = [kw for kw in keywords if kw in content]
                    
                    if found_keywords:
                        self.log_result(f"Alerting Config: {name}", True, f"Contains: {', '.join(found_keywords)}")
                    else:
                        self.log_result(f"Alerting Config: {name}", False, f"Missing keywords: {', '.join(keywords)}")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Alerting Config: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Alerting Config: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_performance_monitoring(self) -> bool:
        """Validate performance monitoring components"""
        print("\n⚡ Validating Performance Monitoring")
        print("-" * 50)
        
        performance_files = [
            ("Performance Optimizer", "shared/performance_optimizer.py"),
            ("Performance Framework", "shared/performance_optimization_framework.py"),
            ("Cache Performance Analytics", "shared/cache_performance_analytics.py"),
            ("Task Performance Monitor", "shared/task_performance_monitor.py"),
            ("Auto Scaling Service", "shared/auto_scaling_service.py")
        ]
        
        all_valid = True
        for name, file_path in performance_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for performance monitoring keywords
                    perf_keywords = ["performance", "metric", "monitor", "optimize", "measure", "track"]
                    found_keywords = [kw for kw in perf_keywords if kw.lower() in content.lower()]
                    
                    if found_keywords:
                        self.log_result(f"Performance Monitor: {name}", True, f"Contains: {', '.join(found_keywords[:3])}")
                    else:
                        self.log_result(f"Performance Monitor: {name}", False, "No performance keywords found")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Performance Monitor: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Performance Monitor: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_security_monitoring(self) -> bool:
        """Validate security monitoring components"""
        print("\n🔒 Validating Security Monitoring")
        print("-" * 50)
        
        security_monitoring_files = [
            ("Security Monitoring Service", "shared/security_monitoring_service.py"),
            ("User Activity Monitor", "shared/user_activity_monitor.py"),
            ("Intrusion Detection System", "shared/intrusion_detection_system.py"),
            ("Security Middleware", "shared/security_middleware.py"),
            ("Enhanced Auth Service", "shared/enhanced_auth_service.py")
        ]
        
        all_valid = True
        for name, file_path in security_monitoring_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for security monitoring keywords
                    security_keywords = ["security", "monitor", "audit", "log", "track", "detect", "alert"]
                    found_keywords = [kw for kw in security_keywords if kw.lower() in content.lower()]
                    
                    if found_keywords:
                        self.log_result(f"Security Monitor: {name}", True, f"Contains: {', '.join(found_keywords[:3])}")
                    else:
                        self.log_result(f"Security Monitor: {name}", False, "No security monitoring keywords found")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Security Monitor: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Security Monitor: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_health_checks(self) -> bool:
        """Validate health check monitoring"""
        print("\n💚 Validating Health Check Monitoring")
        print("-" * 50)
        
        # Check for health check implementations
        health_check_locations = [
            ("Function App Health", "function_app.py", ["health", "status", "check"]),
            ("Deployment Health Check", "scripts/deployment_health_check.py", ["health", "verify", "status"]),
            ("Smoke Tests", "scripts/smoke_tests.py", ["test", "health", "verify"]),
            ("Integration Tests", "tests/integration/test_deployment_integration.py", ["health", "test", "service"])
        ]
        
        all_valid = True
        for name, file_path, keywords in health_check_locations:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read().lower()
                    
                    found_keywords = [kw for kw in keywords if kw in content]
                    
                    if found_keywords:
                        self.log_result(f"Health Check: {name}", True, f"Contains: {', '.join(found_keywords)}")
                    else:
                        self.log_result(f"Health Check: {name}", False, f"Missing keywords: {', '.join(keywords)}")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Health Check: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Health Check: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_logging_configuration(self) -> bool:
        """Validate logging configuration"""
        print("\n📝 Validating Logging Configuration")
        print("-" * 50)
        
        # Check for logging configurations
        logging_files = [
            ("Enhanced Monitoring", "shared/monitoring.py"),
            ("Function App", "function_app.py"),
            ("Common Utilities", "shared/common.py"),
            ("Error Handler", "shared/error_handler.py")
        ]
        
        all_valid = True
        for name, file_path in logging_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for logging keywords
                    logging_keywords = ["logging", "logger", "log", "debug", "info", "warning", "error"]
                    found_keywords = [kw for kw in logging_keywords if kw.lower() in content.lower()]
                    
                    if found_keywords:
                        self.log_result(f"Logging Config: {name}", True, f"Contains: {', '.join(found_keywords[:3])}")
                    else:
                        self.log_result(f"Logging Config: {name}", False, "No logging keywords found")
                        all_valid = False
                        
                except Exception as e:
                    self.log_result(f"Logging Config: {name}", False, f"Error: {e}")
                    all_valid = False
            else:
                self.log_result(f"Logging Config: {name}", False, "File not found")
                all_valid = False
        
        return all_valid
    
    def validate_metrics_collection(self) -> bool:
        """Validate metrics collection implementation"""
        print("\n📊 Validating Metrics Collection")
        print("-" * 50)
        
        # Test metrics collection components
        try:
            # Test if we can import and use metrics collection
            from src.shared.monitoring_dashboards import MonitoringDashboardsService
            
            monitoring_service = MonitoringDashboardsService()
            
            # Check for expected methods
            expected_methods = [
                "track_custom_metric",
                "track_task_execution", 
                "track_security_event",
                "get_task_metrics",
                "get_error_metrics",
                "get_security_metrics"
            ]
            
            found_methods = []
            for method_name in expected_methods:
                if hasattr(monitoring_service, method_name):
                    found_methods.append(method_name)
            
            if len(found_methods) >= 3:  # At least half the methods should exist
                self.log_result("Metrics Collection Methods", True, f"Found: {len(found_methods)}/{len(expected_methods)} methods")
            else:
                self.log_result("Metrics Collection Methods", False, f"Only found: {len(found_methods)}/{len(expected_methods)} methods")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("Metrics Collection", False, f"Failed to test metrics collection: {e}")
            return False
    
    async def run_validation(self) -> bool:
        """Run all monitoring and alerting validation tests"""
        print("📊 Enhanced Monitoring and Alerting Validation")
        print("=" * 60)
        
        validation_tests = [
            ("Monitoring Components", self.validate_monitoring_components),
            ("Component Instantiation", self.validate_monitoring_instantiation),
            ("Monitoring Functionality", self.validate_monitoring_functionality),
            ("Dashboard Configurations", self.validate_dashboard_configurations),
            ("Alerting Configuration", self.validate_alerting_configuration),
            ("Performance Monitoring", self.validate_performance_monitoring),
            ("Security Monitoring", self.validate_security_monitoring),
            ("Health Checks", self.validate_health_checks),
            ("Logging Configuration", self.validate_logging_configuration),
            ("Metrics Collection", self.validate_metrics_collection)
        ]
        
        for test_name, test_func in validation_tests:
            print(f"\n🔍 Running: {test_name}")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    await test_func()
                else:
                    test_func()
            except Exception as e:
                self.log_result(f"{test_name} (Exception)", False, f"Unexpected error: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print(f"🏁 Monitoring and Alerting Validation Complete: {self.passed}/{self.total} checks passed")
        
        success_rate = (self.passed / self.total) * 100 if self.total > 0 else 0
        
        if success_rate >= 90:
            print(f"🎉 Excellent! {success_rate:.1f}% of monitoring checks passed")
            return True
        elif success_rate >= 80:
            print(f"✅ Good! {success_rate:.1f}% of monitoring checks passed")
            return True
        elif success_rate >= 70:
            print(f"⚠️  Acceptable! {success_rate:.1f}% of monitoring checks passed")
            return True
        else:
            print(f"❌ Poor! Only {success_rate:.1f}% of monitoring checks passed")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate monitoring validation report"""
        return {
            "timestamp": str(os.popen('date').read().strip()),
            "total_checks": self.total,
            "passed_checks": self.passed,
            "success_rate": (self.passed / self.total) * 100 if self.total > 0 else 0,
            "results": self.results,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        failed_tests = [r for r in self.results if not r["passed"]]
        
        if any("Monitoring Component" in r["test"] for r in failed_tests):
            recommendations.append("Ensure all monitoring components are properly implemented and contain monitoring keywords")
        
        if any("Dashboard" in r["test"] for r in failed_tests):
            recommendations.append("Complete dashboard configurations and ensure Application Insights integration")
        
        if any("Alerting" in r["test"] for r in failed_tests):
            recommendations.append("Implement comprehensive alerting rules and notification systems")
        
        if any("Performance" in r["test"] for r in failed_tests):
            recommendations.append("Enhance performance monitoring components and metrics collection")
        
        if any("Security" in r["test"] for r in failed_tests):
            recommendations.append("Strengthen security monitoring and intrusion detection capabilities")
        
        if any("Health Check" in r["test"] for r in failed_tests):
            recommendations.append("Implement comprehensive health checks and monitoring endpoints")
        
        if any("Logging" in r["test"] for r in failed_tests):
            recommendations.append("Improve logging configuration and structured logging implementation")
        
        if any("Metrics" in r["test"] for r in failed_tests):
            recommendations.append("Complete metrics collection implementation and ensure all required methods exist")
        
        return recommendations


async def main():
    """Main function"""
    validator = MonitoringAlertingValidator()
    
    # Run validation
    success = await validator.run_validation()
    
    # Generate report
    report = validator.generate_report()
    
    # Save report
    with open("monitoring_alerting_validation_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Detailed report saved to: monitoring_alerting_validation_report.json")
    
    # Print recommendations
    if report["recommendations"]:
        print("\n💡 Recommendations:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)