#!/usr/bin/env python3
"""
Microservices Architecture Compliance Validation

This script validates that all enhancements maintain microservices architecture patterns
including blueprint pattern compliance, service separation, and proxy architecture.
"""

import os
import sys
import json
import logging
import importlib.util
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MicroservicesComplianceValidator:
    """Validates microservices architecture compliance"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.validation_results = {
            "blueprint_pattern": {"passed": [], "failed": []},
            "service_separation": {"passed": [], "failed": []},
            "proxy_architecture": {"passed": [], "failed": []},
            "api_consistency": {"passed": [], "failed": []},
            "overall_status": "unknown"
        }
    
    def validate_blueprint_pattern(self) -> bool:
        """Validate that all new components follow blueprint pattern"""
        logger.info("Validating blueprint pattern compliance...")
        
        api_dir = self.base_path / "api"
        if not api_dir.exists():
            self.validation_results["blueprint_pattern"]["failed"].append(
                "API directory not found"
            )
            return False
        
        # Check that all API files follow blueprint pattern
        api_files = list(api_dir.glob("*.py"))
        blueprint_compliant = []
        blueprint_violations = []
        
        for api_file in api_files:
            if api_file.name.startswith("__"):
                continue
                
            try:
                # Read file content to check for blueprint pattern
                content = api_file.read_text()
                
                # Check for required blueprint elements
                has_blueprint_import = "from azure.functions import Blueprint" in content or "Blueprint" in content
                has_bp_definition = "bp = Blueprint" in content or "bp=" in content
                has_route_decorator = "@bp.route" in content or "@bp.function_name" in content
                
                if has_blueprint_import and has_bp_definition and has_route_decorator:
                    blueprint_compliant.append(api_file.name)
                    self.validation_results["blueprint_pattern"]["passed"].append(
                        f"{api_file.name}: Follows blueprint pattern"
                    )
                else:
                    blueprint_violations.append(api_file.name)
                    self.validation_results["blueprint_pattern"]["failed"].append(
                        f"{api_file.name}: Missing blueprint pattern elements"
                    )
                    
            except Exception as e:
                blueprint_violations.append(api_file.name)
                self.validation_results["blueprint_pattern"]["failed"].append(
                    f"{api_file.name}: Error reading file - {str(e)}"
                )
        
        # Check function_app.py for proper blueprint registration
        function_app_path = self.base_path / "function_app.py"
        if function_app_path.exists():
            try:
                content = function_app_path.read_text()
                
                # Check for blueprint imports and registrations
                for api_file in blueprint_compliant:
                    module_name = api_file.replace(".py", "")
                    import_pattern = f"from api.{module_name} import bp"
                    register_pattern = f"app.register_functions({module_name.replace('_endpoints', '')}_bp)"
                    
                    if import_pattern in content or f"api.{module_name}" in content:
                        self.validation_results["blueprint_pattern"]["passed"].append(
                            f"{api_file}: Properly imported in function_app.py"
                        )
                    else:
                        self.validation_results["blueprint_pattern"]["failed"].append(
                            f"{api_file}: Not properly imported in function_app.py"
                        )
                        
            except Exception as e:
                self.validation_results["blueprint_pattern"]["failed"].append(
                    f"function_app.py: Error validating blueprint registration - {str(e)}"
                )
        
        return len(blueprint_violations) == 0
    
    def validate_service_separation(self) -> bool:
        """Validate service separation and communication patterns"""
        logger.info("Validating service separation patterns...")
        
        # Check that SFDC service doesn't contain DB-specific logic
        sfdc_specific_patterns = [
            "salesforce", "sfdc", "metadata_extraction", "pmd", "security_analysis"
        ]
        
        db_specific_patterns = [
            "database", "repository", "crud", "sql", "db_service"
        ]
        
        violations = []
        compliant_files = []
        
        # Check shared modules for proper separation
        shared_dir = self.base_path / "shared"
        if shared_dir.exists():
            for shared_file in shared_dir.glob("*.py"):
                if shared_file.name.startswith("__"):
                    continue
                    
                try:
                    content = shared_file.read_text().lower()
                    
                    # Check if file mixes SFDC and DB concerns
                    has_sfdc_logic = any(pattern in content for pattern in sfdc_specific_patterns)
                    has_db_logic = any(pattern in content for pattern in db_specific_patterns)
                    
                    if has_sfdc_logic and has_db_logic:
                        # Some mixing is acceptable in certain files
                        acceptable_mixed_files = [
                            "common.py", "config.py", "auth_utils.py", 
                            "monitoring.py", "error_handler.py"
                        ]
                        
                        if shared_file.name not in acceptable_mixed_files:
                            violations.append(shared_file.name)
                            self.validation_results["service_separation"]["failed"].append(
                                f"{shared_file.name}: Mixes SFDC and DB concerns"
                            )
                        else:
                            compliant_files.append(shared_file.name)
                            self.validation_results["service_separation"]["passed"].append(
                                f"{shared_file.name}: Acceptable mixed concerns"
                            )
                    else:
                        compliant_files.append(shared_file.name)
                        self.validation_results["service_separation"]["passed"].append(
                            f"{shared_file.name}: Proper separation of concerns"
                        )
                        
                except Exception as e:
                    violations.append(shared_file.name)
                    self.validation_results["service_separation"]["failed"].append(
                        f"{shared_file.name}: Error analyzing file - {str(e)}"
                    )
        
        # Check API endpoints for proper service boundaries
        api_dir = self.base_path / "api"
        if api_dir.exists():
            for api_file in api_dir.glob("*.py"):
                if api_file.name.startswith("__"):
                    continue
                    
                try:
                    content = api_file.read_text()
                    
                    # Check for direct database operations in SFDC service
                    direct_db_patterns = [
                        "CREATE TABLE", "INSERT INTO", "UPDATE SET", "DELETE FROM",
                        "SELECT * FROM", "cursor.execute", "connection.execute"
                    ]
                    
                    has_direct_db = any(pattern in content for pattern in direct_db_patterns)
                    
                    if has_direct_db:
                        violations.append(api_file.name)
                        self.validation_results["service_separation"]["failed"].append(
                            f"{api_file.name}: Contains direct database operations"
                        )
                    else:
                        compliant_files.append(api_file.name)
                        self.validation_results["service_separation"]["passed"].append(
                            f"{api_file.name}: Uses proper service boundaries"
                        )
                        
                except Exception as e:
                    violations.append(api_file.name)
                    self.validation_results["service_separation"]["failed"].append(
                        f"{api_file.name}: Error analyzing file - {str(e)}"
                    )
        
        return len(violations) == 0
    
    def validate_proxy_architecture(self) -> bool:
        """Validate proxy architecture functionality"""
        logger.info("Validating proxy architecture patterns...")
        
        # Check that SFDC service has proper internal endpoints
        internal_endpoints = []
        external_endpoints = []
        
        api_dir = self.base_path / "api"
        if api_dir.exists():
            for api_file in api_dir.glob("*.py"):
                if api_file.name.startswith("__"):
                    continue
                    
                try:
                    content = api_file.read_text()
                    
                    # Look for route definitions
                    import re
                    route_patterns = re.findall(r'@bp\.route\(["\']([^"\']+)["\']', content)
                    
                    for route in route_patterns:
                        # Classify endpoints as internal or external
                        if any(pattern in route for pattern in ["/internal/", "/proxy/", "/sfdc-proxy/"]):
                            internal_endpoints.append(f"{api_file.name}:{route}")
                        else:
                            external_endpoints.append(f"{api_file.name}:{route}")
                            
                except Exception as e:
                    self.validation_results["proxy_architecture"]["failed"].append(
                        f"{api_file.name}: Error analyzing routes - {str(e)}"
                    )
        
        # Validate that SFDC service has appropriate endpoint types
        if internal_endpoints:
            self.validation_results["proxy_architecture"]["passed"].append(
                f"Found {len(internal_endpoints)} internal endpoints for proxy communication"
            )
        
        if external_endpoints:
            self.validation_results["proxy_architecture"]["passed"].append(
                f"Found {len(external_endpoints)} external endpoints for direct access"
            )
        
        # Check for proper CORS handling
        cors_handler_path = self.base_path / "api" / "cors_handler.py"
        cors_middleware_path = self.base_path / "shared" / "cors_middleware.py"
        
        cors_configured = False
        
        if cors_handler_path.exists():
            try:
                content = cors_handler_path.read_text()
                
                if "handle_cors_preflight" in content or "OPTIONS" in content:
                    cors_configured = True
                    self.validation_results["proxy_architecture"]["passed"].append(
                        "CORS handler properly configured"
                    )
                    
            except Exception as e:
                self.validation_results["proxy_architecture"]["failed"].append(
                    f"CORS handler: Error analyzing - {str(e)}"
                )
        
        if cors_middleware_path.exists():
            try:
                content = cors_middleware_path.read_text()
                
                if "Access-Control-Allow-Origin" in content:
                    cors_configured = True
                    self.validation_results["proxy_architecture"]["passed"].append(
                        "CORS middleware properly configured with headers"
                    )
                    
            except Exception as e:
                self.validation_results["proxy_architecture"]["failed"].append(
                    f"CORS middleware: Error analyzing - {str(e)}"
                )
        
        if not cors_configured:
            self.validation_results["proxy_architecture"]["failed"].append(
                "CORS not properly configured"
            )
        
        # Check for service communication utilities
        service_comm_files = [
            "shared/db_service_client.py",
            "shared/sfdc_service_client.py",
            "shared/service_communication.py"
        ]
        
        for comm_file in service_comm_files:
            comm_path = self.base_path / comm_file
            if comm_path.exists():
                self.validation_results["proxy_architecture"]["passed"].append(
                    f"Service communication utility found: {comm_file}"
                )
            else:
                self.validation_results["proxy_architecture"]["failed"].append(
                    f"Service communication utility missing: {comm_file}"
                )
        
        return len(self.validation_results["proxy_architecture"]["failed"]) == 0
    
    def validate_api_consistency(self) -> bool:
        """Validate API consistency with specifications"""
        logger.info("Validating API consistency...")
        
        # Check if API specifications exist and are up to date
        api_spec_path = self.base_path / "docs" / "API_SPECIFICATIONS_EXPORT.md"
        if api_spec_path.exists():
            self.validation_results["api_consistency"]["passed"].append(
                "API specifications document exists"
            )
            
            try:
                spec_content = api_spec_path.read_text()
                
                # Check for required API sections
                required_sections = [
                    "Task Management API",
                    "Health Check API", 
                    "Policy Management API",
                    "Integration Management API",
                    "Error Responses"
                ]
                
                for section in required_sections:
                    if section in spec_content:
                        self.validation_results["api_consistency"]["passed"].append(
                            f"API spec contains {section}"
                        )
                    else:
                        self.validation_results["api_consistency"]["failed"].append(
                            f"API spec missing {section}"
                        )
                        
            except Exception as e:
                self.validation_results["api_consistency"]["failed"].append(
                    f"Error reading API specifications - {str(e)}"
                )
        else:
            self.validation_results["api_consistency"]["failed"].append(
                "API specifications document not found"
            )
        
        # Check for OpenAPI/Swagger documentation
        openapi_paths = [
            "docs/openapi/sfdc-api-spec.yaml",
            "docs/openapi/swagger-ui.html"
        ]
        
        for openapi_path in openapi_paths:
            path = self.base_path / openapi_path
            if path.exists():
                self.validation_results["api_consistency"]["passed"].append(
                    f"OpenAPI documentation found: {openapi_path}"
                )
            else:
                self.validation_results["api_consistency"]["failed"].append(
                    f"OpenAPI documentation missing: {openapi_path}"
                )
        
        return len(self.validation_results["api_consistency"]["failed"]) == 0
    
    def run_validation(self) -> Dict[str, Any]:
        """Run all validation checks"""
        logger.info("Starting microservices architecture compliance validation...")
        
        # Run all validation checks
        blueprint_valid = self.validate_blueprint_pattern()
        separation_valid = self.validate_service_separation()
        proxy_valid = self.validate_proxy_architecture()
        api_valid = self.validate_api_consistency()
        
        # Determine overall status
        if all([blueprint_valid, separation_valid, proxy_valid, api_valid]):
            self.validation_results["overall_status"] = "passed"
        elif any([blueprint_valid, separation_valid, proxy_valid, api_valid]):
            self.validation_results["overall_status"] = "partial"
        else:
            self.validation_results["overall_status"] = "failed"
        
        return self.validation_results
    
    def generate_report(self) -> str:
        """Generate a detailed validation report"""
        report = []
        report.append("=" * 80)
        report.append("MICROSERVICES ARCHITECTURE COMPLIANCE VALIDATION REPORT")
        report.append("=" * 80)
        report.append(f"Overall Status: {self.validation_results['overall_status'].upper()}")
        report.append("")
        
        for category, results in self.validation_results.items():
            if category == "overall_status":
                continue
                
            report.append(f"{category.replace('_', ' ').title()}:")
            report.append("-" * 40)
            
            if results["passed"]:
                report.append("✅ PASSED:")
                for item in results["passed"]:
                    report.append(f"  • {item}")
                report.append("")
            
            if results["failed"]:
                report.append("❌ FAILED:")
                for item in results["failed"]:
                    report.append(f"  • {item}")
                report.append("")
        
        report.append("=" * 80)
        return "\n".join(report)

def main():
    """Main function to run validation"""
    validator = MicroservicesComplianceValidator()
    results = validator.run_validation()
    report = validator.generate_report()
    
    print(report)
    
    # Save results to file
    results_path = Path("architecture_compliance_results.json")
    with open(results_path, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: {results_path}")
    
    # Exit with appropriate code
    if results["overall_status"] == "passed":
        sys.exit(0)
    elif results["overall_status"] == "partial":
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()