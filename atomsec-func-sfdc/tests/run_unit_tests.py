#!/usr/bin/env python3
"""
Comprehensive Unit Test Runner

This script runs all unit tests with coverage reporting and quality gates.
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path


def run_command(command, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return e.returncode, e.stdout, e.stderr


def install_test_dependencies():
    """Install test dependencies"""
    print("Installing test dependencies...")
    
    dependencies = [
        "pytest>=7.4.4",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.12.0",
        "pytest-xdist>=3.5.0",
        "pytest-asyncio>=0.23.5",
        "hypothesis>=6.98.0",
        "coverage>=7.0.0"
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        returncode, stdout, stderr = run_command(f"pip install {dep}")
        if returncode != 0:
            print(f"Warning: Failed to install {dep}: {stderr}")


def run_unit_tests(test_path=None, coverage=True, parallel=False, verbose=False):
    """Run unit tests with specified options"""
    print("Running unit tests...")
    
    # Build pytest command
    cmd_parts = ["python", "-m", "pytest"]
    
    # Add test path
    if test_path:
        cmd_parts.append(test_path)
    else:
        cmd_parts.append("tests/unit")
    
    # Add markers for unit tests only
    cmd_parts.extend(["-m", "unit"])
    
    # Add coverage options
    if coverage:
        cmd_parts.extend([
            "--cov=shared",
            "--cov=api", 
            "--cov=blueprints",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=json:coverage.json",
            "--cov-fail-under=80"
        ])
    
    # Add parallel execution
    if parallel:
        cmd_parts.extend(["-n", "auto"])
    
    # Add verbosity
    if verbose:
        cmd_parts.append("-v")
    
    # Add other options
    cmd_parts.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])
    
    command = " ".join(cmd_parts)
    print(f"Running command: {command}")
    
    returncode, stdout, stderr = run_command(command)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return returncode == 0


def generate_test_report():
    """Generate comprehensive test report"""
    print("Generating test report...")
    
    report = {
        "timestamp": "",
        "test_results": {},
        "coverage": {},
        "quality_metrics": {}
    }
    
    # Read coverage report if available
    coverage_file = Path("coverage.json")
    if coverage_file.exists():
        try:
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
                report["coverage"] = {
                    "total_coverage": coverage_data.get("totals", {}).get("percent_covered", 0),
                    "files": coverage_data.get("files", {}),
                    "summary": coverage_data.get("totals", {})
                }
        except Exception as e:
            print(f"Warning: Could not read coverage report: {e}")
    
    # Save report
    with open("test_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print("Test report saved to test_report.json")


def run_security_tests():
    """Run security-focused tests"""
    print("Running security tests...")
    
    command = "python -m pytest tests/unit -m security -v"
    returncode, stdout, stderr = run_command(command)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return returncode == 0


def run_performance_tests():
    """Run performance-focused tests"""
    print("Running performance tests...")
    
    command = "python -m pytest tests/unit -m performance -v"
    returncode, stdout, stderr = run_command(command)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return returncode == 0


def check_test_quality():
    """Check test quality metrics"""
    print("Checking test quality...")
    
    quality_checks = []
    
    # Check test coverage
    coverage_file = Path("coverage.json")
    if coverage_file.exists():
        try:
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
                total_coverage = coverage_data.get("totals", {}).get("percent_covered", 0)
                
                if total_coverage >= 80:
                    quality_checks.append(("Coverage >= 80%", True, f"{total_coverage:.1f}%"))
                else:
                    quality_checks.append(("Coverage >= 80%", False, f"{total_coverage:.1f}%"))
        except Exception as e:
            quality_checks.append(("Coverage check", False, f"Error: {e}"))
    else:
        quality_checks.append(("Coverage report", False, "Not found"))
    
    # Check test file structure
    unit_test_dir = Path("tests/unit")
    if unit_test_dir.exists():
        test_files = list(unit_test_dir.glob("test_*.py"))
        mock_files = list(unit_test_dir.glob("mocks/mock_*.py"))
        
        quality_checks.append(("Unit test files", len(test_files) > 0, f"{len(test_files)} files"))
        quality_checks.append(("Mock files", len(mock_files) > 0, f"{len(mock_files)} files"))
    else:
        quality_checks.append(("Test directory", False, "Not found"))
    
    # Print quality report
    print("\nTest Quality Report:")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed, details in quality_checks:
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status} {check_name}: {details}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    print(f"Overall Quality: {'✓ PASS' if all_passed else '✗ FAIL'}")
    
    return all_passed


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run comprehensive unit tests")
    parser.add_argument("--path", help="Specific test path to run")
    parser.add_argument("--no-coverage", action="store_true", help="Skip coverage reporting")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--security-only", action="store_true", help="Run only security tests")
    parser.add_argument("--performance-only", action="store_true", help="Run only performance tests")
    parser.add_argument("--quality-check", action="store_true", help="Run quality checks only")
    
    args = parser.parse_args()
    
    # Change to project directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    success = True
    
    try:
        # Install dependencies if requested
        if args.install_deps:
            install_test_dependencies()
        
        # Run quality check only
        if args.quality_check:
            success = check_test_quality()
        # Run security tests only
        elif args.security_only:
            success = run_security_tests()
        # Run performance tests only
        elif args.performance_only:
            success = run_performance_tests()
        # Run all unit tests
        else:
            success = run_unit_tests(
                test_path=args.path,
                coverage=not args.no_coverage,
                parallel=args.parallel,
                verbose=args.verbose
            )
            
            if success and not args.no_coverage:
                generate_test_report()
                success = check_test_quality()
    
    except KeyboardInterrupt:
        print("\nTest execution interrupted by user")
        success = False
    except Exception as e:
        print(f"Error running tests: {e}")
        success = False
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()