"""
Unit tests for Enhanced Parameter Validator
"""

import pytest
from unittest.mock import Mock, patch
import json
from datetime import datetime

# Import the module under test
from src.shared.enhanced_parameter_validator import EnhancedParameterValidator, ValidationResult, ValidationError


class TestEnhancedParameterValidator:
    """Test suite for EnhancedParameterValidator"""

    def setup_method(self):
        """Set up test fixtures"""
        self.validator = EnhancedParameterValidator()

    def test_validate_request_data_success(self):
        """Test successful request data validation"""
        data = {
            "execution_log_id": "test-123",
            "secure_access_token": "valid-token",
            "user_id": "user123"
        }
        schema = {
            "execution_log_id": {"required": True, "type": "string"},
            "secure_access_token": {"required": True, "type": "string"},
            "user_id": {"required": True, "type": "string"}
        }

        result = self.validator.validate_request_data(data, schema)
        
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_request_data_missing_required_field(self):
        """Test validation failure for missing required field"""
        data = {
            "execution_log_id": "test-123"
            # Missing secure_access_token
        }
        schema = {
            "execution_log_id": {"required": True, "type": "string"},
            "secure_access_token": {"required": True, "type": "string"}
        }

        result = self.validator.validate_request_data(data, schema)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "secure_access_token" in result.errors[0].field

    def test_validate_request_data_invalid_type(self):
        """Test validation failure for invalid data type"""
        data = {
            "execution_log_id": 123,  # Should be string
            "secure_access_token": "valid-token"
        }
        schema = {
            "execution_log_id": {"required": True, "type": "string"},
            "secure_access_token": {"required": True, "type": "string"}
        }

        result = self.validator.validate_request_data(data, schema)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "execution_log_id" in result.errors[0].field

    def test_sanitize_input_string(self):
        """Test input sanitization for strings"""
        malicious_input = "<script>alert('xss')</script>"
        sanitized = self.validator.sanitize_input(malicious_input)
        
        assert "<script>" not in sanitized
        assert "alert" not in sanitized

    def test_sanitize_input_dict(self):
        """Test input sanitization for dictionaries"""
        malicious_dict = {
            "name": "<script>alert('xss')</script>",
            "description": "Normal text",
            "nested": {
                "value": "<img src=x onerror=alert(1)>"
            }
        }
        
        sanitized = self.validator.sanitize_input(malicious_dict)
        
        assert "<script>" not in sanitized["name"]
        assert sanitized["description"] == "Normal text"
        assert "<img" not in sanitized["nested"]["value"]

    def test_validate_execution_log_id_valid(self):
        """Test valid execution log ID validation"""
        valid_id = "exec-log-12345-abcdef"
        
        result = self.validator.validate_execution_log_id(valid_id)
        
        assert result is True

    def test_validate_execution_log_id_invalid_format(self):
        """Test invalid execution log ID format"""
        invalid_id = "invalid-format"
        
        result = self.validator.validate_execution_log_id(invalid_id)
        
        assert result is False

    def test_validate_execution_log_id_empty(self):
        """Test empty execution log ID"""
        result = self.validator.validate_execution_log_id("")
        assert result is False
        
        result = self.validator.validate_execution_log_id(None)
        assert result is False

    def test_validate_secure_token_valid(self):
        """Test valid secure token validation"""
        valid_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        
        with patch.object(self.validator, '_verify_jwt_token', return_value=True):
            result = self.validator.validate_secure_token(valid_token)
            assert result is True

    def test_validate_secure_token_invalid(self):
        """Test invalid secure token validation"""
        invalid_token = "invalid.token.format"
        
        with patch.object(self.validator, '_verify_jwt_token', return_value=False):
            result = self.validator.validate_secure_token(invalid_token)
            assert result is False

    def test_validate_task_parameters_sfdc_authenticate(self):
        """Test task parameter validation for sfdc_authenticate"""
        params = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token",
            "salesforce_instance_url": "https://test.salesforce.com",
            "client_id": "client123"
        }
        
        result = self.validator.validate_task_parameters("sfdc_authenticate", params)
        
        assert result.is_valid is True

    def test_validate_task_parameters_health_check(self):
        """Test task parameter validation for health_check"""
        params = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token",
            "check_types": ["database", "salesforce", "storage"]
        }
        
        result = self.validator.validate_task_parameters("health_check", params)
        
        assert result.is_valid is True

    def test_validate_task_parameters_metadata_extraction(self):
        """Test task parameter validation for metadata_extraction"""
        params = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token",
            "metadata_types": ["Profile", "PermissionSet"],
            "batch_size": 100
        }
        
        result = self.validator.validate_task_parameters("metadata_extraction", params)
        
        assert result.is_valid is True

    def test_validate_task_parameters_pmd_apex_security(self):
        """Test task parameter validation for pmd_apex_security"""
        params = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token",
            "scan_rules": ["security", "performance"],
            "output_format": "json"
        }
        
        result = self.validator.validate_task_parameters("pmd_apex_security", params)
        
        assert result.is_valid is True

    def test_validate_task_parameters_unknown_task(self):
        """Test task parameter validation for unknown task type"""
        params = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token"
        }
        
        result = self.validator.validate_task_parameters("unknown_task", params)
        
        assert result.is_valid is False
        assert any("Unknown task type" in error.message for error in result.errors)

    def test_validate_file_upload_valid_type(self):
        """Test valid file upload validation"""
        file_data = b"valid file content"
        allowed_types = ["text/plain", "application/json"]
        
        with patch('magic.from_buffer', return_value="text/plain"):
            result = self.validator.validate_file_upload(file_data, allowed_types)
            assert result is True

    def test_validate_file_upload_invalid_type(self):
        """Test invalid file upload validation"""
        file_data = b"executable content"
        allowed_types = ["text/plain", "application/json"]
        
        with patch('magic.from_buffer', return_value="application/x-executable"):
            result = self.validator.validate_file_upload(file_data, allowed_types)
            assert result is False

    def test_validate_file_upload_empty_file(self):
        """Test empty file upload validation"""
        file_data = b""
        allowed_types = ["text/plain"]
        
        result = self.validator.validate_file_upload(file_data, allowed_types)
        assert result is False

    def test_validate_file_upload_large_file(self):
        """Test large file upload validation"""
        # Create a file larger than the default limit (10MB)
        large_file_data = b"x" * (11 * 1024 * 1024)
        allowed_types = ["text/plain"]
        
        with patch('magic.from_buffer', return_value="text/plain"):
            result = self.validator.validate_file_upload(large_file_data, allowed_types)
            assert result is False

    def test_validation_result_creation(self):
        """Test ValidationResult creation and properties"""
        errors = [
            ValidationError("field1", "error message 1"),
            ValidationError("field2", "error message 2")
        ]
        
        result = ValidationResult(is_valid=False, errors=errors)
        
        assert result.is_valid is False
        assert len(result.errors) == 2
        assert result.errors[0].field == "field1"
        assert result.errors[1].field == "field2"

    def test_validation_error_creation(self):
        """Test ValidationError creation and properties"""
        error = ValidationError("test_field", "test error message")
        
        assert error.field == "test_field"
        assert error.message == "test error message"

    @patch('shared.enhanced_parameter_validator.logging')
    def test_logging_validation_errors(self, mock_logging):
        """Test that validation errors are properly logged"""
        data = {
            "execution_log_id": ""  # Invalid empty string
        }
        schema = {
            "execution_log_id": {"required": True, "type": "string", "min_length": 1}
        }

        result = self.validator.validate_request_data(data, schema)
        
        assert result.is_valid is False
        # Verify logging was called (implementation dependent)

    def test_complex_nested_validation(self):
        """Test validation of complex nested data structures"""
        data = {
            "execution_log_id": "exec-123",
            "secure_access_token": "valid-token",
            "task_config": {
                "batch_size": 100,
                "timeout": 300,
                "retry_count": 3,
                "options": {
                    "enable_logging": True,
                    "log_level": "INFO"
                }
            }
        }
        
        schema = {
            "execution_log_id": {"required": True, "type": "string"},
            "secure_access_token": {"required": True, "type": "string"},
            "task_config": {
                "required": True,
                "type": "dict",
                "schema": {
                    "batch_size": {"required": True, "type": "int", "min": 1, "max": 1000},
                    "timeout": {"required": True, "type": "int", "min": 1},
                    "retry_count": {"required": True, "type": "int", "min": 0, "max": 10},
                    "options": {
                        "required": False,
                        "type": "dict",
                        "schema": {
                            "enable_logging": {"required": False, "type": "bool"},
                            "log_level": {"required": False, "type": "string"}
                        }
                    }
                }
            }
        }

        result = self.validator.validate_request_data(data, schema)
        
        assert result.is_valid is True
        assert len(result.errors) == 0