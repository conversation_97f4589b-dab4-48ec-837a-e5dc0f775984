"""
Unit tests for Enhanced Authentication Service
"""

import pytest
import jwt
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json

from src.shared.enhanced_auth_service import (
    EnhancedAuthService,
    get_enhanced_auth_service,
    AuthenticationError,
    RateLimitExceededError,
    SalesforceAuthenticationError,
    TokenValidationResult
)


@pytest.mark.unit
class TestEnhancedAuthService:
    """Test cases for EnhancedAuthService"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.auth_service = EnhancedAuthService()
        self.valid_payload = {
            'sub': 'user123',
            'email': '<EMAIL>',
            'scopes': ['read', 'write'],
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
    
    def test_init(self):
        """Test EnhancedAuthService initialization"""
        assert self.auth_service is not None
        assert hasattr(self.auth_service, 'jwt_secret')
        assert hasattr(self.auth_service, 'rate_limiter')
    
    @patch('shared.enhanced_auth_service.get_enhanced_configuration_manager')
    def test_validate_jwt_token_enhanced_success(self, mock_config_manager):
        """Test successful JWT token validation"""
        # Mock configuration
        mock_config = Mock()
        mock_config.get_security_config.return_value = {
            'jwt_secret_key': 'test-secret',
            'jwt_algorithm': 'HS256'
        }
        mock_config_manager.return_value = mock_config
        
        # Create valid token
        token = jwt.encode(self.valid_payload, 'test-secret', algorithm='HS256')
        
        # Test validation
        result = self.auth_service.validate_jwt_token_enhanced(token)
        
        assert isinstance(result, dict)
        assert result['sub'] == 'user123'
        assert result['email'] == '<EMAIL>'
        assert result['scopes'] == ['read', 'write']
    
    @patch('shared.enhanced_auth_service.get_enhanced_configuration_manager')
    def test_validate_jwt_token_enhanced_expired(self, mock_config_manager):
        """Test JWT token validation with expired token"""
        # Mock configuration
        mock_config = Mock()
        mock_config.get_security_config.return_value = {
            'jwt_secret_key': 'test-secret',
            'jwt_algorithm': 'HS256'
        }
        mock_config_manager.return_value = mock_config
        
        # Create expired token
        expired_payload = {
            'sub': 'user123',
            'exp': datetime.utcnow() - timedelta(hours=1)
        }
        token = jwt.encode(expired_payload, 'test-secret', algorithm='HS256')
        
        # Test validation
        with pytest.raises(AuthenticationError, match="Token has expired"):
            self.auth_service.validate_jwt_token_enhanced(token)
    
    @patch('shared.enhanced_auth_service.get_enhanced_configuration_manager')
    def test_validate_jwt_token_enhanced_invalid_signature(self, mock_config_manager):
        """Test JWT token validation with invalid signature"""
        # Mock configuration
        mock_config = Mock()
        mock_config.get_security_config.return_value = {
            'jwt_secret_key': 'test-secret',
            'jwt_algorithm': 'HS256'
        }
        mock_config_manager.return_value = mock_config
        
        # Create token with wrong secret
        token = jwt.encode(self.valid_payload, 'wrong-secret', algorithm='HS256')
        
        # Test validation
        with pytest.raises(AuthenticationError, match="Invalid token signature"):
            self.auth_service.validate_jwt_token_enhanced(token)
    
    def test_validate_jwt_token_enhanced_malformed(self):
        """Test JWT token validation with malformed token"""
        malformed_token = "not.a.valid.jwt"
        
        with pytest.raises(AuthenticationError, match="Invalid token format"):
            self.auth_service.validate_jwt_token_enhanced(malformed_token)
    
    @patch('shared.enhanced_auth_service.time')
    def test_check_rate_limit_within_limit(self, mock_time):
        """Test rate limiting when within allowed limits"""
        mock_time.time.return_value = 1000.0
        
        user_id = "user123"
        endpoint = "/api/test"
        
        # Should not raise exception when within limit
        try:
            for i in range(50):  # Well within typical limits
                self.auth_service.check_rate_limit_enhanced(user_id, endpoint)
        except RateLimitExceededError:
            pytest.fail("Rate limit should not be exceeded")
    
    @patch('shared.enhanced_auth_service.time')
    def test_check_rate_limit_exceeded(self, mock_time):
        """Test rate limiting when limit is exceeded"""
        mock_time.time.return_value = 1000.0
        
        user_id = "user123"
        endpoint = "/api/test"
        
        # Mock rate limiter to simulate exceeded limit
        with patch.object(self.auth_service.rate_limiter, 'is_allowed', return_value=False):
            with pytest.raises(RateLimitExceededError):
                self.auth_service.check_rate_limit_enhanced(user_id, endpoint)
    
    @patch('shared.enhanced_auth_service.get_salesforce_client')
    def test_validate_salesforce_credentials_success(self, mock_sf_client):
        """Test successful Salesforce credential validation"""
        # Mock Salesforce client
        mock_client = Mock()
        mock_client.describe.return_value = {'sobjects': []}
        mock_sf_client.return_value = mock_client
        
        credentials = {
            'username': '<EMAIL>',
            'password': 'password',
            'security_token': 'token',
            'domain': 'test'
        }
        
        result = self.auth_service.validate_salesforce_credentials(credentials)
        
        assert result is True
        mock_sf_client.assert_called_once()
    
    @patch('shared.enhanced_auth_service.get_salesforce_client')
    def test_validate_salesforce_credentials_failure(self, mock_sf_client):
        """Test Salesforce credential validation failure"""
        # Mock Salesforce client to raise exception
        mock_sf_client.side_effect = Exception("Authentication failed")
        
        credentials = {
            'username': '<EMAIL>',
            'password': 'wrong_password',
            'security_token': 'token',
            'domain': 'test'
        }
        
        with pytest.raises(SalesforceAuthenticationError):
            self.auth_service.validate_salesforce_credentials(credentials)
    
    def test_generate_secure_token(self):
        """Test secure token generation"""
        token = self.auth_service.generate_secure_token()
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Generate another token to ensure uniqueness
        another_token = self.auth_service.generate_secure_token()
        assert token != another_token
    
    @patch('shared.enhanced_auth_service.get_enhanced_configuration_manager')
    def test_create_jwt_token(self, mock_config_manager):
        """Test JWT token creation"""
        # Mock configuration
        mock_config = Mock()
        mock_config.get_security_config.return_value = {
            'jwt_secret_key': 'test-secret',
            'jwt_algorithm': 'HS256',
            'token_expiration_hours': 24
        }
        mock_config_manager.return_value = mock_config
        
        payload = {
            'user_id': 'user123',
            'email': '<EMAIL>',
            'scopes': ['read']
        }
        
        token = self.auth_service.create_jwt_token(payload)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token.split('.')) == 3  # JWT has 3 parts
    
    def test_validate_user_permissions_admin(self):
        """Test user permission validation for admin user"""
        user_context = {
            'user_id': 'admin123',
            'roles': ['admin'],
            'scopes': ['read', 'write', 'admin']
        }
        
        result = self.auth_service.validate_user_permissions(
            user_context, 'test_resource', 'admin'
        )
        
        assert result is True
    
    def test_validate_user_permissions_insufficient(self):
        """Test user permission validation for insufficient permissions"""
        user_context = {
            'user_id': 'user123',
            'roles': ['user'],
            'scopes': ['read']
        }
        
        result = self.auth_service.validate_user_permissions(
            user_context, 'test_resource', 'write'
        )
        
        assert result is False
    
    def test_validate_user_permissions_no_context(self):
        """Test user permission validation with no user context"""
        result = self.auth_service.validate_user_permissions(
            None, 'test_resource', 'read'
        )
        
        assert result is False
    
    @patch('shared.enhanced_auth_service.logger')
    def test_audit_authentication_event(self, mock_logger):
        """Test authentication event auditing"""
        event_data = {
            'user_id': 'user123',
            'event_type': 'login_success',
            'ip_address': '***********',
            'user_agent': 'Test Client'
        }
        
        self.auth_service.audit_authentication_event(event_data)
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert 'authentication_event' in args[0]
    
    def test_extract_token_from_header_valid(self):
        """Test token extraction from valid Authorization header"""
        auth_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        
        token = self.auth_service.extract_token_from_header(auth_header)
        
        assert token == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    
    def test_extract_token_from_header_invalid(self):
        """Test token extraction from invalid Authorization header"""
        invalid_headers = [
            "Basic dXNlcjpwYXNz",  # Basic auth
            "Bearer",  # Missing token
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  # Missing Bearer prefix
            "",  # Empty header
            None  # None header
        ]
        
        for header in invalid_headers:
            token = self.auth_service.extract_token_from_header(header)
            assert token is None


@pytest.mark.unit
class TestEnhancedAuthServiceGlobal:
    """Test cases for global enhanced auth service functions"""
    
    def test_get_enhanced_auth_service_singleton(self):
        """Test that get_enhanced_auth_service returns singleton instance"""
        service1 = get_enhanced_auth_service()
        service2 = get_enhanced_auth_service()
        
        assert service1 is service2
        assert isinstance(service1, EnhancedAuthService)


@pytest.mark.unit
class TestAuthenticationExceptions:
    """Test cases for authentication exceptions"""
    
    def test_authentication_error(self):
        """Test AuthenticationError exception"""
        with pytest.raises(AuthenticationError) as exc_info:
            raise AuthenticationError("Invalid credentials")
        
        assert str(exc_info.value) == "Invalid credentials"
    
    def test_rate_limit_exceeded_error(self):
        """Test RateLimitExceededError exception"""
        with pytest.raises(RateLimitExceededError) as exc_info:
            raise RateLimitExceededError("Rate limit exceeded")
        
        assert str(exc_info.value) == "Rate limit exceeded"
    
    def test_salesforce_authentication_error(self):
        """Test SalesforceAuthenticationError exception"""
        with pytest.raises(SalesforceAuthenticationError) as exc_info:
            raise SalesforceAuthenticationError("Salesforce auth failed")
        
        assert str(exc_info.value) == "Salesforce auth failed"


@pytest.mark.unit
class TestTokenValidationResult:
    """Test cases for TokenValidationResult"""
    
    def test_token_validation_result_success(self):
        """Test successful token validation result"""
        payload = {'user_id': 'user123', 'email': '<EMAIL>'}
        result = TokenValidationResult(is_valid=True, payload=payload)
        
        assert result.is_valid is True
        assert result.payload == payload
        assert result.error_message is None
    
    def test_token_validation_result_failure(self):
        """Test failed token validation result"""
        error_msg = "Token expired"
        result = TokenValidationResult(is_valid=False, error_message=error_msg)
        
        assert result.is_valid is False
        assert result.payload is None
        assert result.error_message == error_msg