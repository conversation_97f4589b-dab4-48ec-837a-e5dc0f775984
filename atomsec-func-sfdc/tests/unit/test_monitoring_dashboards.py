"""
Unit tests for Monitoring Dashboards
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime, timedelta

# Import the module under test
from src.shared.monitoring_dashboards import (
    MonitoringDashboardService, 
    DashboardConfig, 
    MetricDefinition,
    AlertRule,
    DashboardCreationError
)


class TestMonitoringDashboardService:
    """Test suite for MonitoringDashboardService"""

    def setup_method(self):
        """Set up test fixtures"""
        self.config = DashboardConfig(
            application_insights_workspace_id="test-workspace-id",
            resource_group="test-rg",
            subscription_id="test-subscription",
            dashboard_name_prefix="atomsec-sfdc"
        )
        
        with patch('shared.monitoring_dashboards.ApplicationInsightsManagementClient'):
            self.service = MonitoringDashboardService(self.config)

    def test_dashboard_config_creation(self):
        """Test DashboardConfig creation and properties"""
        assert self.config.application_insights_workspace_id == "test-workspace-id"
        assert self.config.resource_group == "test-rg"
        assert self.config.subscription_id == "test-subscription"
        assert self.config.dashboard_name_prefix == "atomsec-sfdc"

    def test_metric_definition_creation(self):
        """Test MetricDefinition creation and properties"""
        metric = MetricDefinition(
            name="response_time",
            display_name="Response Time",
            query="requests | summarize avg(duration) by bin(timestamp, 5m)",
            chart_type="line",
            aggregation="average"
        )
        
        assert metric.name == "response_time"
        assert metric.display_name == "Response Time"
        assert metric.chart_type == "line"
        assert metric.aggregation == "average"
        assert "avg(duration)" in metric.query

    def test_alert_rule_creation(self):
        """Test AlertRule creation and properties"""
        alert = AlertRule(
            name="high_error_rate",
            display_name="High Error Rate Alert",
            query="requests | where success == false | summarize count() by bin(timestamp, 5m)",
            threshold=10,
            operator="GreaterThan",
            frequency_minutes=5,
            severity="High"
        )
        
        assert alert.name == "high_error_rate"
        assert alert.threshold == 10
        assert alert.operator == "GreaterThan"
        assert alert.frequency_minutes == 5
        assert alert.severity == "High"

    @patch('shared.monitoring_dashboards.ApplicationInsightsManagementClient')
    def test_create_performance_dashboard_success(self, mock_client):
        """Test successful performance dashboard creation"""
        mock_dashboard_client = Mock()
        mock_client.return_value.dashboards = mock_dashboard_client
        mock_dashboard_client.create_or_update.return_value = Mock(id="dashboard-123")
        
        service = MonitoringDashboardService(self.config)
        result = service.create_performance_dashboard()
        
        assert result is not None
        assert "dashboard-123" in str(result.id)
        mock_dashboard_client.create_or_update.assert_called_once()

    @patch('shared.monitoring_dashboards.ApplicationInsightsManagementClient')
    def test_create_performance_dashboard_failure(self, mock_client):
        """Test performance dashboard creation failure"""
        mock_dashboard_client = Mock()
        mock_client.return_value.dashboards = mock_dashboard_client
        mock_dashboard_client.create_or_update.side_effect = Exception("Creation failed")
        
        service = MonitoringDashboardService(self.config)
        
        with pytest.raises(DashboardCreationError):
            service.create_performance_dashboard()

    def test_generate_performance_metrics_definitions(self):
        """Test performance metrics definitions generation"""
        metrics = self.service.generate_performance_metrics_definitions()
        
        assert len(metrics) > 0
        
        # Check for expected performance metrics
        metric_names = [metric.name for metric in metrics]
        expected_metrics = [
            "response_time",
            "request_rate", 
            "error_rate",
            "cpu_usage",
            "memory_usage"
        ]
        
        for expected in expected_metrics:
            assert expected in metric_names

    def test_generate_business_metrics_definitions(self):
        """Test business metrics definitions generation"""
        metrics = self.service.generate_business_metrics_definitions()
        
        assert len(metrics) > 0
        
        # Check for expected business metrics
        metric_names = [metric.name for metric in metrics]
        expected_metrics = [
            "task_completion_rate",
            "sfdc_authentication_success",
            "metadata_extraction_volume",
            "pmd_scan_results"
        ]
        
        for expected in expected_metrics:
            assert expected in metric_names

    def test_generate_security_metrics_definitions(self):
        """Test security metrics definitions generation"""
        metrics = self.service.generate_security_metrics_definitions()
        
        assert len(metrics) > 0
        
        # Check for expected security metrics
        metric_names = [metric.name for metric in metrics]
        expected_metrics = [
            "authentication_failures",
            "rate_limit_violations",
            "suspicious_activities",
            "token_validation_errors"
        ]
        
        for expected in expected_metrics:
            assert expected in metric_names

    def test_create_alert_rules_success(self):
        """Test successful alert rules creation"""
        alert_rules = [
            AlertRule(
                name="test_alert",
                display_name="Test Alert",
                query="requests | where success == false",
                threshold=5,
                operator="GreaterThan",
                frequency_minutes=5,
                severity="Medium"
            )
        ]
        
        with patch.object(self.service, '_create_single_alert_rule', return_value=Mock(id="alert-123")):
            results = self.service.create_alert_rules(alert_rules)
            
            assert len(results) == 1
            assert "alert-123" in str(results[0].id)

    def test_create_alert_rules_partial_failure(self):
        """Test alert rules creation with partial failures"""
        alert_rules = [
            AlertRule(
                name="success_alert",
                display_name="Success Alert",
                query="requests | where success == true",
                threshold=1,
                operator="LessThan",
                frequency_minutes=5,
                severity="Low"
            ),
            AlertRule(
                name="failure_alert",
                display_name="Failure Alert", 
                query="invalid query",
                threshold=5,
                operator="GreaterThan",
                frequency_minutes=5,
                severity="High"
            )
        ]
        
        def mock_create_alert(alert_rule):
            if "invalid" in alert_rule.query:
                raise Exception("Invalid query")
            return Mock(id=f"alert-{alert_rule.name}")
        
        with patch.object(self.service, '_create_single_alert_rule', side_effect=mock_create_alert):
            results = self.service.create_alert_rules(alert_rules)
            
            # Should have one successful result
            assert len(results) == 1
            assert "success_alert" in str(results[0].id)

    def test_generate_dashboard_json_structure(self):
        """Test dashboard JSON structure generation"""
        metrics = [
            MetricDefinition(
                name="test_metric",
                display_name="Test Metric",
                query="requests | count",
                chart_type="number",
                aggregation="count"
            )
        ]
        
        dashboard_json = self.service.generate_dashboard_json("Test Dashboard", metrics)
        
        assert "properties" in dashboard_json
        assert "lenses" in dashboard_json["properties"]
        assert len(dashboard_json["properties"]["lenses"]) > 0
        
        # Check for required dashboard properties
        properties = dashboard_json["properties"]
        assert "metadata" in properties
        assert properties["metadata"]["model"]["displayName"] == "Test Dashboard"

    def test_validate_metric_query_valid(self):
        """Test metric query validation with valid query"""
        valid_query = "requests | where timestamp > ago(1h) | summarize count() by bin(timestamp, 5m)"
        
        result = self.service.validate_metric_query(valid_query)
        
        assert result is True

    def test_validate_metric_query_invalid(self):
        """Test metric query validation with invalid query"""
        invalid_queries = [
            "",  # Empty query
            "invalid syntax here",  # Invalid KQL
            "DROP TABLE users;",  # SQL injection attempt
            "requests |",  # Incomplete query
        ]
        
        for query in invalid_queries:
            result = self.service.validate_metric_query(query)
            assert result is False

    def test_get_dashboard_url_generation(self):
        """Test dashboard URL generation"""
        dashboard_id = "dashboard-123"
        
        url = self.service.get_dashboard_url(dashboard_id)
        
        assert url is not None
        assert dashboard_id in url
        assert "portal.azure.com" in url
        assert self.config.subscription_id in url

    def test_delete_dashboard_success(self):
        """Test successful dashboard deletion"""
        dashboard_id = "dashboard-123"
        
        with patch.object(self.service, '_delete_dashboard_by_id', return_value=True):
            result = self.service.delete_dashboard(dashboard_id)
            
            assert result is True

    def test_delete_dashboard_failure(self):
        """Test dashboard deletion failure"""
        dashboard_id = "nonexistent-dashboard"
        
        with patch.object(self.service, '_delete_dashboard_by_id', side_effect=Exception("Not found")):
            result = self.service.delete_dashboard(dashboard_id)
            
            assert result is False

    def test_list_existing_dashboards(self):
        """Test listing existing dashboards"""
        mock_dashboards = [
            Mock(id="dashboard-1", name="Performance Dashboard"),
            Mock(id="dashboard-2", name="Business Dashboard"),
            Mock(id="dashboard-3", name="Security Dashboard")
        ]
        
        with patch.object(self.service, '_list_dashboards', return_value=mock_dashboards):
            dashboards = self.service.list_existing_dashboards()
            
            assert len(dashboards) == 3
            assert dashboards[0].name == "Performance Dashboard"

    def test_update_dashboard_success(self):
        """Test successful dashboard update"""
        dashboard_id = "dashboard-123"
        new_metrics = [
            MetricDefinition(
                name="updated_metric",
                display_name="Updated Metric",
                query="requests | summarize count()",
                chart_type="bar",
                aggregation="count"
            )
        ]
        
        with patch.object(self.service, '_update_dashboard_by_id', return_value=Mock(id=dashboard_id)):
            result = self.service.update_dashboard(dashboard_id, "Updated Dashboard", new_metrics)
            
            assert result is not None
            assert dashboard_id in str(result.id)

    def test_export_dashboard_configuration(self):
        """Test dashboard configuration export"""
        dashboard_id = "dashboard-123"
        
        mock_config = {
            "name": "Test Dashboard",
            "metrics": [
                {
                    "name": "test_metric",
                    "query": "requests | count",
                    "chart_type": "number"
                }
            ],
            "alerts": []
        }
        
        with patch.object(self.service, '_get_dashboard_config', return_value=mock_config):
            config = self.service.export_dashboard_configuration(dashboard_id)
            
            assert config["name"] == "Test Dashboard"
            assert len(config["metrics"]) == 1
            assert config["metrics"][0]["name"] == "test_metric"

    def test_import_dashboard_configuration(self):
        """Test dashboard configuration import"""
        config = {
            "name": "Imported Dashboard",
            "metrics": [
                {
                    "name": "imported_metric",
                    "display_name": "Imported Metric",
                    "query": "requests | summarize avg(duration)",
                    "chart_type": "line",
                    "aggregation": "average"
                }
            ],
            "alerts": []
        }
        
        with patch.object(self.service, 'create_performance_dashboard', return_value=Mock(id="imported-dashboard")):
            result = self.service.import_dashboard_configuration(config)
            
            assert result is not None
            assert "imported-dashboard" in str(result.id)

    def test_dashboard_creation_error_exception(self):
        """Test DashboardCreationError exception"""
        with pytest.raises(DashboardCreationError) as exc_info:
            raise DashboardCreationError("Failed to create dashboard")
        
        assert str(exc_info.value) == "Failed to create dashboard"

    def test_generate_workbook_json(self):
        """Test Application Insights workbook JSON generation"""
        metrics = [
            MetricDefinition(
                name="workbook_metric",
                display_name="Workbook Metric",
                query="requests | summarize count() by bin(timestamp, 1h)",
                chart_type="timechart",
                aggregation="count"
            )
        ]
        
        workbook_json = self.service.generate_workbook_json("Test Workbook", metrics)
        
        assert "properties" in workbook_json
        assert "serializedData" in workbook_json["properties"]
        
        # Parse the serialized data to verify structure
        serialized_data = json.loads(workbook_json["properties"]["serializedData"])
        assert "items" in serialized_data
        assert len(serialized_data["items"]) > 0

    @patch('shared.monitoring_dashboards.logging')
    def test_logging_dashboard_operations(self, mock_logging):
        """Test that dashboard operations are properly logged"""
        with patch.object(self.service, '_create_single_alert_rule', side_effect=Exception("Test error")):
            alert_rules = [
                AlertRule(
                    name="test_alert",
                    display_name="Test Alert",
                    query="requests | count",
                    threshold=1,
                    operator="GreaterThan",
                    frequency_minutes=5,
                    severity="Low"
                )
            ]
            
            self.service.create_alert_rules(alert_rules)
            
            # Verify error logging was called
            mock_logging.error.assert_called()

    def test_metric_aggregation_types(self):
        """Test different metric aggregation types"""
        aggregation_types = ["count", "sum", "average", "minimum", "maximum", "percentile"]
        
        for agg_type in aggregation_types:
            metric = MetricDefinition(
                name=f"test_{agg_type}",
                display_name=f"Test {agg_type.title()}",
                query=f"requests | summarize {agg_type}(duration)",
                chart_type="number",
                aggregation=agg_type
            )
            
            assert metric.aggregation == agg_type
            assert agg_type in metric.query

    def test_chart_type_validation(self):
        """Test chart type validation"""
        valid_chart_types = ["line", "bar", "pie", "number", "table", "timechart"]
        
        for chart_type in valid_chart_types:
            metric = MetricDefinition(
                name=f"test_{chart_type}",
                display_name=f"Test {chart_type.title()}",
                query="requests | count",
                chart_type=chart_type,
                aggregation="count"
            )
            
            assert metric.chart_type == chart_type

    def test_dashboard_permissions_configuration(self):
        """Test dashboard permissions configuration"""
        permissions = {
            "readers": ["<EMAIL>", "<EMAIL>"],
            "contributors": ["<EMAIL>"],
            "owners": ["<EMAIL>"]
        }
        
        dashboard_json = self.service.generate_dashboard_json("Test Dashboard", [])
        enhanced_json = self.service.configure_dashboard_permissions(dashboard_json, permissions)
        
        assert "properties" in enhanced_json
        # Permissions would be configured in the actual implementation