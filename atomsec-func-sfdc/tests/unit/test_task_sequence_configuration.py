"""
Unit tests for Task Sequence Configuration
"""
import pytest
from unittest.mock import patch, Mock
from src.shared.task_sequence_configuration import TaskSequenceConfiguration, TaskSequenceValidator
from tests.unit.mocks.mock_database import MockDatabaseConnection

class TestTaskSequenceConfiguration:
    """Test cases for Task Sequence Configuration"""
    
    @pytest.fixture
    def task_config(self):
        """Task sequence configuration instance"""
        return TaskSequenceConfiguration()
    
    @pytest.fixture
    def mock_db_connection(self):
        """Mock database connection"""
        return MockDatabaseConnection()
    
    def test_initialization(self, task_config):
        """Test task sequence configuration initialization"""
        assert task_config is not None
        assert hasattr(task_config, 'task_sequences')
        assert hasattr(task_config, 'task_dependencies')
    
    def test_get_default_task_sequence(self, task_config):
        """Test getting default task sequence"""
        sequence = task_config.get_task_sequence('default')
        
        expected_tasks = [
            'sfdc_authenticate',
            'health_check',
            'metadata_extraction',
            'pmd_apex_security'
        ]
        
        assert sequence == expected_tasks
    
    def test_get_security_scan_sequence(self, task_config):
        """Test getting security scan task sequence"""
        sequence = task_config.get_task_sequence('security_scan')
        
        # Should include security-specific tasks
        assert 'sfdc_authenticate' in sequence
        assert 'health_check' in sequence
        assert 'security_analysis' in sequence
        assert 'pmd_apex_security' in sequence
    
    def test_get_custom_task_sequence(self, task_config):
        """Test getting custom task sequence"""
        custom_sequence = ['task1', 'task2', 'task3']
        task_config.add_task_sequence('custom', custom_sequence)
        
        sequence = task_config.get_task_sequence('custom')
        assert sequence == custom_sequence
    
    def test_get_non_existent_sequence(self, task_config):
        """Test getting non-existent task sequence"""
        sequence = task_config.get_task_sequence('non_existent')
        # Should return default sequence
        assert sequence == task_config.get_task_sequence('default')
    
    def test_validate_task_sequence(self, task_config):
        """Test task sequence validation"""
        valid_sequence = ['sfdc_authenticate', 'health_check', 'metadata_extraction']
        
        # Should not raise exception
        task_config.validate_task_sequence(valid_sequence)
    
    def test_validate_invalid_task_sequence(self, task_config):
        """Test validation of invalid task sequence"""
        invalid_sequence = ['invalid_task', 'another_invalid_task']
        
        with pytest.raises(ValueError, match="Invalid task in sequence"):
            task_config.validate_task_sequence(invalid_sequence)
    
    def test_get_task_dependencies(self, task_config):
        """Test getting task dependencies"""
        dependencies = task_config.get_task_dependencies('metadata_extraction')
        
        assert 'sfdc_authenticate' in dependencies
        assert 'health_check' in dependencies
    
    def test_get_task_timeout(self, task_config):
        """Test getting task timeout configuration"""
        timeout = task_config.get_task_timeout('pmd_apex_security')
        assert timeout > 0  # PMD tasks should have longer timeout
        
        timeout = task_config.get_task_timeout('health_check')
        assert timeout > 0  # Should have reasonable timeout
    
    def test_get_task_retry_policy(self, task_config):
        """Test getting task retry policy"""
        retry_policy = task_config.get_task_retry_policy('sfdc_authenticate')
        
        assert 'max_attempts' in retry_policy
        assert 'backoff_factor' in retry_policy
        assert retry_policy['max_attempts'] > 1
    
    def test_is_task_optional(self, task_config):
        """Test checking if task is optional"""
        # Authentication should not be optional
        assert task_config.is_task_optional('sfdc_authenticate') is False
        
        # Some tasks might be optional
        assert task_config.is_task_optional('optional_task') is True
    
    def test_get_parallel_tasks(self, task_config):
        """Test getting tasks that can run in parallel"""
        parallel_tasks = task_config.get_parallel_tasks()
        
        # Should return groups of tasks that can run in parallel
        assert isinstance(parallel_tasks, list)
    
    def test_add_task_sequence(self, task_config):
        """Test adding new task sequence"""
        new_sequence = ['task_a', 'task_b', 'task_c']
        task_config.add_task_sequence('new_sequence', new_sequence)
        
        retrieved_sequence = task_config.get_task_sequence('new_sequence')
        assert retrieved_sequence == new_sequence
    
    def test_remove_task_sequence(self, task_config):
        """Test removing task sequence"""
        # Add a sequence first
        task_config.add_task_sequence('temp_sequence', ['task1'])
        
        # Remove it
        task_config.remove_task_sequence('temp_sequence')
        
        # Should return default sequence now
        sequence = task_config.get_task_sequence('temp_sequence')
        assert sequence == task_config.get_task_sequence('default')
    
    def test_update_task_configuration(self, task_config):
        """Test updating task configuration"""
        new_config = {
            'timeout': 300,
            'retry_attempts': 5,
            'optional': False
        }
        
        task_config.update_task_configuration('test_task', new_config)
        
        # Verify updates
        assert task_config.get_task_timeout('test_task') == 300
        retry_policy = task_config.get_task_retry_policy('test_task')
        assert retry_policy['max_attempts'] == 5
        assert task_config.is_task_optional('test_task') is False

class TestTaskSequenceValidator:
    """Test cases for Task Sequence Validator"""
    
    @pytest.fixture
    def validator(self):
        """Task sequence validator instance"""
        return TaskSequenceValidator()
    
    @pytest.fixture
    def mock_db_connection(self):
        """Mock database connection"""
        return MockDatabaseConnection()
    
    def test_validate_execution_log_id(self, validator):
        """Test execution log ID validation"""
        # Valid execution log ID
        assert validator.validate_execution_log_id('exec_log_123') is True
        
        # Invalid execution log ID
        assert validator.validate_execution_log_id('') is False
        assert validator.validate_execution_log_id(None) is False
    
    def test_validate_task_parameters(self, validator):
        """Test task parameter validation"""
        valid_params = {
            'execution_log_id': 'exec_log_123',
            'task_type': 'sfdc_authenticate',
            'salesforce_credentials': {
                'client_id': 'test_client',
                'client_secret': 'test_secret'
            }
        }
        
        # Should not raise exception
        validator.validate_task_parameters(valid_params)
    
    def test_validate_invalid_task_parameters(self, validator):
        """Test validation of invalid task parameters"""
        invalid_params = {
            'task_type': 'sfdc_authenticate'
            # Missing execution_log_id
        }
        
        with pytest.raises(ValueError, match="Missing required parameter"):
            validator.validate_task_parameters(invalid_params)
    
    def test_validate_task_sequence_order(self, validator):
        """Test task sequence order validation"""
        valid_sequence = [
            'sfdc_authenticate',
            'health_check',
            'metadata_extraction',
            'pmd_apex_security'
        ]
        
        # Should not raise exception
        validator.validate_task_sequence_order(valid_sequence)
    
    def test_validate_invalid_task_sequence_order(self, validator):
        """Test validation of invalid task sequence order"""
        invalid_sequence = [
            'metadata_extraction',  # Should come after authentication
            'sfdc_authenticate'
        ]
        
        with pytest.raises(ValueError, match="Invalid task sequence order"):
            validator.validate_task_sequence_order(invalid_sequence)
    
    def test_validate_task_dependencies(self, validator):
        """Test task dependency validation"""
        # Test that metadata_extraction depends on authentication
        dependencies = validator.get_task_dependencies('metadata_extraction')
        assert 'sfdc_authenticate' in dependencies
    
    def test_validate_execution_context(self, validator, mock_db_connection):
        """Test execution context validation"""
        context = {
            'execution_log_id': 'exec_log_123',
            'user_id': 'user123',
            'organization_id': 'org456',
            'task_sequence': ['sfdc_authenticate', 'health_check']
        }
        
        with patch.object(validator, 'db_connection', mock_db_connection):
            # Should not raise exception
            validator.validate_execution_context(context)
    
    def test_validate_task_prerequisites(self, validator):
        """Test task prerequisite validation"""
        completed_tasks = ['sfdc_authenticate', 'health_check']
        next_task = 'metadata_extraction'
        
        # Should pass validation
        assert validator.validate_task_prerequisites(next_task, completed_tasks) is True
        
        # Should fail if prerequisites not met
        incomplete_tasks = ['sfdc_authenticate']  # Missing health_check
        assert validator.validate_task_prerequisites('metadata_extraction', incomplete_tasks) is False
    
    def test_validate_concurrent_execution(self, validator, mock_db_connection):
        """Test concurrent execution validation"""
        execution_log_id = 'exec_log_123'
        
        with patch.object(validator, 'db_connection', mock_db_connection):
            # Should allow first execution
            assert validator.validate_concurrent_execution(execution_log_id) is True
            
            # Mock active execution
            mock_db_connection._data['execution_logs'].append({
                'id': execution_log_id,
                'status': 'running'
            })
            
            # Should prevent concurrent execution
            assert validator.validate_concurrent_execution(execution_log_id) is False
    
    @pytest.mark.parametrize("task_type,expected_timeout", [
        ('sfdc_authenticate', 60),
        ('health_check', 30),
        ('metadata_extraction', 300),
        ('pmd_apex_security', 600)
    ])
    def test_get_task_timeout_values(self, validator, task_type, expected_timeout):
        """Test task timeout value retrieval"""
        timeout = validator.get_task_timeout(task_type)
        assert timeout == expected_timeout
    
    def test_validate_task_result(self, validator):
        """Test task result validation"""
        valid_result = {
            'task_type': 'sfdc_authenticate',
            'status': 'completed',
            'execution_log_id': 'exec_log_123',
            'result_data': {'authenticated': True},
            'execution_time': 45.5
        }
        
        # Should not raise exception
        validator.validate_task_result(valid_result)
    
    def test_validate_invalid_task_result(self, validator):
        """Test validation of invalid task result"""
        invalid_result = {
            'task_type': 'sfdc_authenticate',
            'status': 'invalid_status'  # Invalid status
        }
        
        with pytest.raises(ValueError, match="Invalid task result"):
            validator.validate_task_result(invalid_result)