"""
Unit tests for Feature Flag Service
"""
import pytest
import json
from unittest.mock import patch, Mock, mock_open
from src.shared.feature_flag_service import FeatureFlagService
from tests.unit.mocks.mock_key_vault import MockKeyVaultService

class TestFeatureFlagService:
    """Test cases for Feature Flag Service"""
    
    @pytest.fixture
    def mock_feature_flags(self):
        """Mock feature flags configuration"""
        return {
            "enhanced_logging": {
                "enabled": True,
                "description": "Enhanced logging with correlation IDs",
                "rollout_percentage": 100,
                "user_groups": ["admin", "developer"]
            },
            "performance_monitoring": {
                "enabled": False,
                "description": "Advanced performance monitoring",
                "rollout_percentage": 0,
                "user_groups": []
            },
            "new_authentication": {
                "enabled": True,
                "description": "New authentication system",
                "rollout_percentage": 50,
                "user_groups": ["beta_users"]
            }
        }
    
    @pytest.fixture
    def feature_flag_service(self, mock_feature_flags):
        """Feature flag service with mocked configuration"""
        with patch('builtins.open', mock_open(read_data=json.dumps(mock_feature_flags))):
            service = FeatureFlagService()
            return service
    
    def test_initialization(self, feature_flag_service):
        """Test feature flag service initialization"""
        assert feature_flag_service is not None
        assert hasattr(feature_flag_service, 'flags')
        assert len(feature_flag_service.flags) > 0
    
    def test_is_feature_enabled_simple(self, feature_flag_service):
        """Test simple feature flag evaluation"""
        # Test enabled feature
        assert feature_flag_service.is_feature_enabled('enhanced_logging') is True
        
        # Test disabled feature
        assert feature_flag_service.is_feature_enabled('performance_monitoring') is False
        
        # Test non-existent feature
        assert feature_flag_service.is_feature_enabled('non_existent_feature') is False
    
    def test_is_feature_enabled_with_user_context(self, feature_flag_service):
        """Test feature flag evaluation with user context"""
        # Test user in allowed group
        user_context = {"user_id": "user123", "groups": ["admin"]}
        assert feature_flag_service.is_feature_enabled('enhanced_logging', user_context) is True
        
        # Test user not in allowed group
        user_context = {"user_id": "user456", "groups": ["regular_user"]}
        assert feature_flag_service.is_feature_enabled('enhanced_logging', user_context) is True  # Still enabled due to 100% rollout
    
    def test_rollout_percentage_evaluation(self, feature_flag_service):
        """Test rollout percentage evaluation"""
        # Mock random to test percentage rollout
        with patch('random.randint') as mock_random:
            # Test user within rollout percentage
            mock_random.return_value = 25  # 25% < 50%
            user_context = {"user_id": "user123"}
            assert feature_flag_service.is_feature_enabled('new_authentication', user_context) is True
            
            # Test user outside rollout percentage
            mock_random.return_value = 75  # 75% > 50%
            user_context = {"user_id": "user456"}
            assert feature_flag_service.is_feature_enabled('new_authentication', user_context) is False
    
    def test_get_feature_configuration(self, feature_flag_service):
        """Test getting feature configuration"""
        config = feature_flag_service.get_feature_configuration('enhanced_logging')
        
        assert config['enabled'] is True
        assert config['description'] == "Enhanced logging with correlation IDs"
        assert config['rollout_percentage'] == 100
        assert 'admin' in config['user_groups']
    
    def test_get_feature_configuration_non_existent(self, feature_flag_service):
        """Test getting configuration for non-existent feature"""
        config = feature_flag_service.get_feature_configuration('non_existent_feature')
        assert config == {}
    
    def test_track_feature_usage(self, feature_flag_service):
        """Test feature usage tracking"""
        # Track feature usage
        feature_flag_service.track_feature_usage('enhanced_logging', 'user123')
        
        # Verify usage was tracked
        usage_stats = feature_flag_service.get_usage_stats('enhanced_logging')
        assert usage_stats['total_uses'] == 1
        assert 'user123' in usage_stats['unique_users']
    
    def test_get_usage_stats(self, feature_flag_service):
        """Test getting usage statistics"""
        # Track multiple uses
        feature_flag_service.track_feature_usage('enhanced_logging', 'user123')
        feature_flag_service.track_feature_usage('enhanced_logging', 'user456')
        feature_flag_service.track_feature_usage('enhanced_logging', 'user123')  # Duplicate user
        
        stats = feature_flag_service.get_usage_stats('enhanced_logging')
        
        assert stats['total_uses'] == 3
        assert len(stats['unique_users']) == 2
        assert 'user123' in stats['unique_users']
        assert 'user456' in stats['unique_users']
    
    def test_get_all_features(self, feature_flag_service):
        """Test getting all feature flags"""
        all_features = feature_flag_service.get_all_features()
        
        assert 'enhanced_logging' in all_features
        assert 'performance_monitoring' in all_features
        assert 'new_authentication' in all_features
        assert len(all_features) == 3
    
    def test_update_feature_flag(self, feature_flag_service):
        """Test updating feature flag configuration"""
        # Update feature flag
        new_config = {
            "enabled": False,
            "description": "Updated description",
            "rollout_percentage": 25,
            "user_groups": ["admin"]
        }
        
        feature_flag_service.update_feature_flag('enhanced_logging', new_config)
        
        # Verify update
        updated_config = feature_flag_service.get_feature_configuration('enhanced_logging')
        assert updated_config['enabled'] is False
        assert updated_config['description'] == "Updated description"
        assert updated_config['rollout_percentage'] == 25
    
    def test_reload_configuration(self, feature_flag_service, mock_feature_flags):
        """Test reloading feature flag configuration"""
        # Modify mock data
        updated_flags = mock_feature_flags.copy()
        updated_flags['new_feature'] = {
            "enabled": True,
            "description": "New feature",
            "rollout_percentage": 100,
            "user_groups": []
        }
        
        with patch('builtins.open', mock_open(read_data=json.dumps(updated_flags))):
            feature_flag_service.reload_configuration()
            
            # Verify new feature is loaded
            assert feature_flag_service.is_feature_enabled('new_feature') is True
    
    def test_feature_flag_with_environment_override(self, feature_flag_service):
        """Test feature flag with environment variable override"""
        with patch.dict('os.environ', {'FEATURE_FLAG_ENHANCED_LOGGING': 'false'}):
            # Environment override should disable the feature
            assert feature_flag_service.is_feature_enabled('enhanced_logging') is False
    
    def test_user_group_evaluation(self, feature_flag_service):
        """Test user group-based feature evaluation"""
        # Test user in beta group for new_authentication
        user_context = {"user_id": "user123", "groups": ["beta_users"]}
        
        # Mock random to simulate user outside rollout percentage
        with patch('random.randint', return_value=75):  # 75% > 50%
            # Should still be enabled due to user group membership
            assert feature_flag_service.is_feature_enabled('new_authentication', user_context) is True
    
    def test_feature_flag_evaluation_priority(self, feature_flag_service):
        """Test feature flag evaluation priority (user groups > rollout > enabled)"""
        # Create a feature that's disabled but has user groups
        feature_flag_service.update_feature_flag('test_priority', {
            "enabled": False,
            "rollout_percentage": 0,
            "user_groups": ["admin"]
        })
        
        # User in admin group should get access despite feature being disabled
        user_context = {"user_id": "admin_user", "groups": ["admin"]}
        assert feature_flag_service.is_feature_enabled('test_priority', user_context) is True
        
        # Regular user should not get access
        user_context = {"user_id": "regular_user", "groups": ["user"]}
        assert feature_flag_service.is_feature_enabled('test_priority', user_context) is False
    
    def test_error_handling_invalid_configuration(self):
        """Test error handling with invalid configuration"""
        with patch('builtins.open', mock_open(read_data="invalid json")):
            with pytest.raises(json.JSONDecodeError):
                FeatureFlagService()
    
    def test_error_handling_missing_configuration_file(self):
        """Test error handling when configuration file is missing"""
        with patch('builtins.open', side_effect=FileNotFoundError):
            service = FeatureFlagService()
            # Should initialize with empty flags
            assert service.flags == {}
            assert service.is_feature_enabled('any_feature') is False
    
    @pytest.mark.parametrize("rollout_percentage,random_value,expected", [
        (0, 50, False),
        (50, 25, True),
        (50, 75, False),
        (100, 99, True),
        (100, 0, True)
    ])
    def test_rollout_percentage_scenarios(self, feature_flag_service, rollout_percentage, random_value, expected):
        """Test various rollout percentage scenarios"""
        # Create test feature with specific rollout percentage
        feature_flag_service.update_feature_flag('test_rollout', {
            "enabled": True,
            "rollout_percentage": rollout_percentage,
            "user_groups": []
        })
        
        with patch('random.randint', return_value=random_value):
            user_context = {"user_id": "test_user"}
            result = feature_flag_service.is_feature_enabled('test_rollout', user_context)
            assert result == expected