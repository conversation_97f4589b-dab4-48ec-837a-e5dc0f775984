"""
Unit tests for Performance Optimizer
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta

from src.shared.performance_optimizer import (
    PerformanceOptimizer,
    ConnectionPoolManager,
    CacheManager,
    get_performance_optimizer,
    PerformanceMonitor,
    ResourceManager
)


@pytest.mark.unit
class TestConnectionPoolManager:
    """Test cases for ConnectionPoolManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.pool_manager = ConnectionPoolManager()
    
    def test_init(self):
        """Test ConnectionPoolManager initialization"""
        assert self.pool_manager is not None
        assert hasattr(self.pool_manager, 'pools')
        assert hasattr(self.pool_manager, 'pool_configs')
    
    @patch('shared.performance_optimizer.create_connection_pool')
    def test_get_database_connection_pool(self, mock_create_pool):
        """Test getting database connection pool"""
        mock_pool = Mock()
        mock_create_pool.return_value = mock_pool
        
        pool = self.pool_manager.get_database_connection_pool('test_db')
        
        assert pool == mock_pool
        mock_create_pool.assert_called_once()
    
    @patch('shared.performance_optimizer.httpx.AsyncClient')
    def test_get_http_client_pool(self, mock_http_client):
        """Test getting HTTP client pool"""
        mock_client = Mock()
        mock_http_client.return_value = mock_client
        
        client = self.pool_manager.get_http_client_pool('test_service')
        
        assert client == mock_client
        mock_http_client.assert_called_once()
    
    def test_get_connection_pool_stats(self):
        """Test getting connection pool statistics"""
        stats = self.pool_manager.get_connection_pool_stats()
        
        assert isinstance(stats, dict)
        assert 'total_pools' in stats
        assert 'active_connections' in stats
    
    def test_cleanup_idle_connections(self):
        """Test cleaning up idle connections"""
        # Mock some idle connections
        mock_pool = Mock()
        mock_pool.cleanup_idle.return_value = 5
        self.pool_manager.pools['test_pool'] = mock_pool
        
        cleaned = self.pool_manager.cleanup_idle_connections()
        
        assert cleaned >= 0
        mock_pool.cleanup_idle.assert_called_once()
    
    def test_close_all_pools(self):
        """Test closing all connection pools"""
        # Mock some pools
        mock_pool1 = Mock()
        mock_pool2 = Mock()
        self.pool_manager.pools['pool1'] = mock_pool1
        self.pool_manager.pools['pool2'] = mock_pool2
        
        self.pool_manager.close_all_pools()
        
        mock_pool1.close.assert_called_once()
        mock_pool2.close.assert_called_once()
        assert len(self.pool_manager.pools) == 0


@pytest.mark.unit
class TestCacheManager:
    """Test cases for CacheManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.cache_manager = CacheManager()
    
    def test_init(self):
        """Test CacheManager initialization"""
        assert self.cache_manager is not None
        assert hasattr(self.cache_manager, 'local_cache')
        assert hasattr(self.cache_manager, 'cache_stats')
    
    def test_get_set_cache_local(self):
        """Test local cache get and set operations"""
        key = "test_key"
        value = {"data": "test_value"}
        ttl = 300
        
        # Set cache value
        self.cache_manager.set_cache(key, value, ttl)
        
        # Get cache value
        cached_value = self.cache_manager.get_cache(key)
        
        assert cached_value == value
    
    def test_get_cache_miss(self):
        """Test cache miss scenario"""
        key = "nonexistent_key"
        
        cached_value = self.cache_manager.get_cache(key)
        
        assert cached_value is None
    
    def test_delete_cache(self):
        """Test cache deletion"""
        key = "test_key"
        value = {"data": "test_value"}
        
        # Set and then delete
        self.cache_manager.set_cache(key, value, 300)
        self.cache_manager.delete_cache(key)
        
        # Should be None after deletion
        cached_value = self.cache_manager.get_cache(key)
        assert cached_value is None
    
    def test_clear_cache(self):
        """Test clearing all cache"""
        # Set multiple cache entries
        self.cache_manager.set_cache("key1", "value1", 300)
        self.cache_manager.set_cache("key2", "value2", 300)
        
        # Clear cache
        self.cache_manager.clear_cache()
        
        # All entries should be gone
        assert self.cache_manager.get_cache("key1") is None
        assert self.cache_manager.get_cache("key2") is None
    
    def test_cache_with_pattern_invalidation(self):
        """Test cache invalidation with pattern matching"""
        # Set cache entries with pattern
        self.cache_manager.set_cache("user:123:profile", {"name": "John"}, 300)
        self.cache_manager.set_cache("user:123:settings", {"theme": "dark"}, 300)
        self.cache_manager.set_cache("user:456:profile", {"name": "Jane"}, 300)
        
        # Invalidate pattern
        self.cache_manager.invalidate_pattern("user:123:*")
        
        # Check results
        assert self.cache_manager.get_cache("user:123:profile") is None
        assert self.cache_manager.get_cache("user:123:settings") is None
        assert self.cache_manager.get_cache("user:456:profile") is not None
    
    def test_get_cache_stats(self):
        """Test getting cache statistics"""
        # Perform some cache operations
        self.cache_manager.set_cache("key1", "value1", 300)
        self.cache_manager.get_cache("key1")  # Hit
        self.cache_manager.get_cache("key2")  # Miss
        
        stats = self.cache_manager.get_cache_stats()
        
        assert isinstance(stats, dict)
        assert 'hits' in stats
        assert 'misses' in stats
        assert 'hit_rate' in stats
    
    @patch('shared.performance_optimizer.redis.Redis')
    def test_distributed_cache_operations(self, mock_redis):
        """Test distributed cache operations with Redis"""
        # Mock Redis client
        mock_redis_client = Mock()
        mock_redis.return_value = mock_redis_client
        
        # Enable distributed cache
        cache_manager = CacheManager(use_distributed=True)
        
        key = "test_key"
        value = {"data": "test_value"}
        
        # Test set operation
        cache_manager.set_distributed_cache(key, value, 300)
        mock_redis_client.setex.assert_called_once()
        
        # Test get operation
        mock_redis_client.get.return_value = '{"data": "test_value"}'
        cached_value = cache_manager.get_distributed_cache(key)
        
        mock_redis_client.get.assert_called_once_with(key)
        assert cached_value == value


@pytest.mark.unit
class TestPerformanceMonitor:
    """Test cases for PerformanceMonitor"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.monitor = PerformanceMonitor()
    
    def test_init(self):
        """Test PerformanceMonitor initialization"""
        assert self.monitor is not None
        assert hasattr(self.monitor, 'metrics')
        assert hasattr(self.monitor, 'start_times')
    
    def test_start_stop_timer(self):
        """Test timer start and stop functionality"""
        operation_id = "test_operation"
        
        # Start timer
        self.monitor.start_timer(operation_id)
        assert operation_id in self.monitor.start_times
        
        # Simulate some work
        time.sleep(0.01)
        
        # Stop timer
        duration = self.monitor.stop_timer(operation_id)
        
        assert duration > 0
        assert operation_id not in self.monitor.start_times
    
    def test_record_metric(self):
        """Test recording custom metrics"""
        metric_name = "test_metric"
        value = 42.5
        tags = {"environment": "test"}
        
        self.monitor.record_metric(metric_name, value, tags)
        
        # Check if metric was recorded
        assert metric_name in self.monitor.metrics
        recorded_metric = self.monitor.metrics[metric_name][-1]
        assert recorded_metric['value'] == value
        assert recorded_metric['tags'] == tags
    
    def test_get_performance_stats(self):
        """Test getting performance statistics"""
        # Record some metrics
        self.monitor.record_metric("response_time", 100)
        self.monitor.record_metric("response_time", 150)
        self.monitor.record_metric("response_time", 120)
        
        stats = self.monitor.get_performance_stats()
        
        assert isinstance(stats, dict)
        assert 'response_time' in stats
        assert 'avg' in stats['response_time']
        assert 'min' in stats['response_time']
        assert 'max' in stats['response_time']
    
    def test_track_resource_usage(self):
        """Test resource usage tracking"""
        usage = self.monitor.track_resource_usage()
        
        assert isinstance(usage, dict)
        assert 'cpu_percent' in usage
        assert 'memory_percent' in usage
        assert 'memory_used_mb' in usage
    
    @patch('shared.performance_optimizer.psutil')
    def test_get_system_metrics(self, mock_psutil):
        """Test getting system metrics"""
        # Mock psutil responses
        mock_psutil.cpu_percent.return_value = 45.2
        mock_psutil.virtual_memory.return_value = Mock(percent=60.5, used=1024*1024*1024)
        mock_psutil.disk_usage.return_value = Mock(percent=75.0)
        
        metrics = self.monitor.get_system_metrics()
        
        assert metrics['cpu_percent'] == 45.2
        assert metrics['memory_percent'] == 60.5
        assert metrics['disk_percent'] == 75.0
    
    def test_performance_context_manager(self):
        """Test performance monitoring context manager"""
        operation_name = "test_operation"
        
        with self.monitor.measure_performance(operation_name) as measurement:
            time.sleep(0.01)  # Simulate work
            measurement.add_metadata({"status": "success"})
        
        # Check if measurement was recorded
        assert operation_name in self.monitor.metrics
        recorded = self.monitor.metrics[operation_name][-1]
        assert recorded['duration'] > 0
        assert recorded['metadata']['status'] == 'success'


@pytest.mark.unit
class TestResourceManager:
    """Test cases for ResourceManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.resource_manager = ResourceManager()
    
    def test_init(self):
        """Test ResourceManager initialization"""
        assert self.resource_manager is not None
        assert hasattr(self.resource_manager, 'resource_limits')
        assert hasattr(self.resource_manager, 'active_resources')
    
    def test_acquire_release_resource(self):
        """Test resource acquisition and release"""
        resource_type = "database_connection"
        resource_id = "conn_123"
        
        # Acquire resource
        success = self.resource_manager.acquire_resource(resource_type, resource_id)
        assert success is True
        assert resource_id in self.resource_manager.active_resources[resource_type]
        
        # Release resource
        self.resource_manager.release_resource(resource_type, resource_id)
        assert resource_id not in self.resource_manager.active_resources[resource_type]
    
    def test_resource_limit_enforcement(self):
        """Test resource limit enforcement"""
        resource_type = "http_connection"
        limit = 2
        
        # Set resource limit
        self.resource_manager.set_resource_limit(resource_type, limit)
        
        # Acquire resources up to limit
        assert self.resource_manager.acquire_resource(resource_type, "conn1") is True
        assert self.resource_manager.acquire_resource(resource_type, "conn2") is True
        
        # Should fail to acquire beyond limit
        assert self.resource_manager.acquire_resource(resource_type, "conn3") is False
    
    def test_cleanup_expired_resources(self):
        """Test cleanup of expired resources"""
        resource_type = "temporary_resource"
        resource_id = "temp_123"
        
        # Acquire resource with short TTL
        self.resource_manager.acquire_resource(resource_type, resource_id, ttl=0.01)
        
        # Wait for expiration
        time.sleep(0.02)
        
        # Cleanup expired resources
        cleaned = self.resource_manager.cleanup_expired_resources()
        
        assert cleaned > 0
        assert resource_id not in self.resource_manager.active_resources[resource_type]
    
    def test_get_resource_stats(self):
        """Test getting resource statistics"""
        # Acquire some resources
        self.resource_manager.acquire_resource("type1", "res1")
        self.resource_manager.acquire_resource("type1", "res2")
        self.resource_manager.acquire_resource("type2", "res3")
        
        stats = self.resource_manager.get_resource_stats()
        
        assert isinstance(stats, dict)
        assert 'type1' in stats
        assert 'type2' in stats
        assert stats['type1']['active'] == 2
        assert stats['type2']['active'] == 1


@pytest.mark.unit
class TestPerformanceOptimizer:
    """Test cases for PerformanceOptimizer main class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.optimizer = PerformanceOptimizer()
    
    def test_init(self):
        """Test PerformanceOptimizer initialization"""
        assert self.optimizer is not None
        assert hasattr(self.optimizer, 'connection_manager')
        assert hasattr(self.optimizer, 'cache_manager')
        assert hasattr(self.optimizer, 'performance_monitor')
        assert hasattr(self.optimizer, 'resource_manager')
    
    def test_optimize_function_execution(self):
        """Test function execution optimization"""
        @self.optimizer.optimize_execution()
        def test_function(x, y):
            time.sleep(0.01)  # Simulate work
            return x + y
        
        result = test_function(2, 3)
        
        assert result == 5
        # Check if performance was monitored
        assert 'test_function' in self.optimizer.performance_monitor.metrics
    
    def test_cache_function_result(self):
        """Test function result caching"""
        call_count = 0
        
        @self.optimizer.cache_result(ttl=300)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # First call
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1
        
        # Second call should use cache
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 1  # Should not increment
    
    def test_async_function_optimization(self):
        """Test async function optimization"""
        @self.optimizer.optimize_async_execution()
        async def async_test_function(x):
            await asyncio.sleep(0.01)
            return x * 2
        
        # Run async function
        result = asyncio.run(async_test_function(5))
        
        assert result == 10
        # Check if performance was monitored
        assert 'async_test_function' in self.optimizer.performance_monitor.metrics
    
    def test_get_optimization_report(self):
        """Test getting optimization report"""
        # Perform some operations to generate data
        self.optimizer.performance_monitor.record_metric("test_metric", 100)
        self.optimizer.resource_manager.acquire_resource("test_type", "test_id")
        
        report = self.optimizer.get_optimization_report()
        
        assert isinstance(report, dict)
        assert 'performance_stats' in report
        assert 'resource_stats' in report
        assert 'cache_stats' in report
        assert 'connection_stats' in report
    
    def test_cleanup_resources(self):
        """Test resource cleanup"""
        # Acquire some resources
        self.optimizer.resource_manager.acquire_resource("test_type", "test_id")
        
        # Cleanup
        self.optimizer.cleanup_resources()
        
        # Resources should be cleaned up
        stats = self.optimizer.resource_manager.get_resource_stats()
        assert stats.get('test_type', {}).get('active', 0) == 0


@pytest.mark.unit
class TestPerformanceOptimizerGlobal:
    """Test cases for global performance optimizer functions"""
    
    def test_get_performance_optimizer_singleton(self):
        """Test that get_performance_optimizer returns singleton instance"""
        optimizer1 = get_performance_optimizer()
        optimizer2 = get_performance_optimizer()
        
        assert optimizer1 is optimizer2
        assert isinstance(optimizer1, PerformanceOptimizer)


@pytest.mark.unit
class TestPerformanceDecorators:
    """Test cases for performance decorators"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.optimizer = PerformanceOptimizer()
    
    def test_measure_execution_time_decorator(self):
        """Test execution time measurement decorator"""
        @self.optimizer.measure_execution_time
        def timed_function():
            time.sleep(0.01)
            return "result"
        
        result = timed_function()
        
        assert result == "result"
        # Check if timing was recorded
        assert 'timed_function' in self.optimizer.performance_monitor.metrics
    
    def test_retry_on_failure_decorator(self):
        """Test retry on failure decorator"""
        attempt_count = 0
        
        @self.optimizer.retry_on_failure(max_retries=3, delay=0.01)
        def flaky_function():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = flaky_function()
        
        assert result == "success"
        assert attempt_count == 3
    
    def test_circuit_breaker_decorator(self):
        """Test circuit breaker decorator"""
        @self.optimizer.circuit_breaker(failure_threshold=2, timeout=0.1)
        def unreliable_function():
            raise Exception("Service unavailable")
        
        # First few calls should raise the original exception
        with pytest.raises(Exception, match="Service unavailable"):
            unreliable_function()
        
        with pytest.raises(Exception, match="Service unavailable"):
            unreliable_function()
        
        # After threshold, should raise circuit breaker exception
        with pytest.raises(Exception):  # Circuit breaker exception
            unreliable_function()