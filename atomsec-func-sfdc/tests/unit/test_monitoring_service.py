"""
Unit tests for Monitoring Service
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import logging

from src.shared.monitoring import (
    StructuredLogger,
    MetricsCollector,
    PerformanceMonitor,
    HealthCheckService,
    get_monitoring_service,
    MonitoringService
)


@pytest.mark.unit
class TestStructuredLogger:
    """Test cases for StructuredLogger"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.logger = StructuredLogger("test_logger")
    
    def test_init(self):
        """Test StructuredLogger initialization"""
        assert self.logger is not None
        assert self.logger.logger_name == "test_logger"
        assert hasattr(self.logger, 'correlation_id')
    
    @patch('shared.monitoring.logging.getLogger')
    def test_log_info_with_context(self, mock_get_logger):
        """Test logging info with context"""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        context = {"user_id": "user123", "operation": "test_op"}
        self.logger.info("Test message", context)
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert "Test message" in args[0]
        assert "user_id" in kwargs.get("extra", {})
    
    @patch('shared.monitoring.logging.getLogger')
    def test_log_error_with_exception(self, mock_get_logger):
        """Test logging error with exception"""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        try:
            raise ValueError("Test error")
        except Exception as e:
            self.logger.error("Error occurred", {"operation": "test"}, e)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert "Error occurred" in args[0]
        assert "exception_type" in kwargs.get("extra", {})
    
    @patch('shared.monitoring.logging.getLogger')
    def test_log_warning(self, mock_get_logger):
        """Test logging warning"""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        self.logger.warning("Warning message", {"severity": "medium"})
        
        mock_logger.warning.assert_called_once()
    
    def test_set_correlation_id(self):
        """Test setting correlation ID"""
        correlation_id = "test-correlation-123"
        self.logger.set_correlation_id(correlation_id)
        
        assert self.logger.correlation_id == correlation_id
    
    def test_generate_correlation_id(self):
        """Test generating correlation ID"""
        correlation_id = self.logger.generate_correlation_id()
        
        assert correlation_id is not None
        assert isinstance(correlation_id, str)
        assert len(correlation_id) > 0
    
    def test_create_log_context(self):
        """Test creating log context"""
        user_context = {"user_id": "user123", "role": "admin"}
        operation_context = {"operation": "test_op", "duration": 100}
        
        context = self.logger.create_log_context(user_context, operation_context)
        
        assert "user_id" in context
        assert "operation" in context
        assert "timestamp" in context
        assert "correlation_id" in context


@pytest.mark.unit
class TestMetricsCollector:
    """Test cases for MetricsCollector"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.collector = MetricsCollector()
    
    def test_init(self):
        """Test MetricsCollector initialization"""
        assert self.collector is not None
        assert hasattr(self.collector, 'metrics_buffer')
        assert hasattr(self.collector, 'telemetry_client')
    
    def test_record_counter_metric(self):
        """Test recording counter metric"""
        metric_name = "request_count"
        value = 1
        tags = {"endpoint": "/api/test", "method": "POST"}
        
        self.collector.record_counter(metric_name, value, tags)
        
        # Check if metric was buffered
        assert len(self.collector.metrics_buffer) > 0
        recorded_metric = self.collector.metrics_buffer[-1]
        assert recorded_metric['name'] == metric_name
        assert recorded_metric['value'] == value
        assert recorded_metric['tags'] == tags
        assert recorded_metric['type'] == 'counter'
    
    def test_record_gauge_metric(self):
        """Test recording gauge metric"""
        metric_name = "memory_usage"
        value = 85.5
        tags = {"instance": "web-01"}
        
        self.collector.record_gauge(metric_name, value, tags)
        
        # Check if metric was buffered
        recorded_metric = self.collector.metrics_buffer[-1]
        assert recorded_metric['name'] == metric_name
        assert recorded_metric['value'] == value
        assert recorded_metric['type'] == 'gauge'
    
    def test_record_histogram_metric(self):
        """Test recording histogram metric"""
        metric_name = "response_time"
        value = 250.0
        tags = {"endpoint": "/api/users"}
        
        self.collector.record_histogram(metric_name, value, tags)
        
        # Check if metric was buffered
        recorded_metric = self.collector.metrics_buffer[-1]
        assert recorded_metric['name'] == metric_name
        assert recorded_metric['value'] == value
        assert recorded_metric['type'] == 'histogram'
    
    def test_record_custom_event(self):
        """Test recording custom event"""
        event_name = "user_login"
        properties = {"user_id": "user123", "login_method": "oauth"}
        
        self.collector.record_custom_event(event_name, properties)
        
        # Check if event was buffered
        recorded_event = self.collector.metrics_buffer[-1]
        assert recorded_event['name'] == event_name
        assert recorded_event['properties'] == properties
        assert recorded_event['type'] == 'event'
    
    @patch('shared.monitoring.TelemetryClient')
    def test_flush_metrics_to_application_insights(self, mock_telemetry_client):
        """Test flushing metrics to Application Insights"""
        # Mock telemetry client
        mock_client = Mock()
        mock_telemetry_client.return_value = mock_client
        
        # Add some metrics to buffer
        self.collector.record_counter("test_counter", 1, {})
        self.collector.record_gauge("test_gauge", 50.0, {})
        
        # Flush metrics
        self.collector.flush_metrics()
        
        # Verify telemetry client was called
        assert mock_client.track_metric.call_count >= 2
        assert len(self.collector.metrics_buffer) == 0  # Buffer should be cleared
    
    def test_get_metrics_summary(self):
        """Test getting metrics summary"""
        # Record various metrics
        self.collector.record_counter("requests", 100, {})
        self.collector.record_gauge("cpu_usage", 75.0, {})
        self.collector.record_histogram("latency", 200.0, {})
        
        summary = self.collector.get_metrics_summary()
        
        assert isinstance(summary, dict)
        assert 'total_metrics' in summary
        assert 'metric_types' in summary
        assert summary['total_metrics'] == 3
    
    def test_create_custom_dimension(self):
        """Test creating custom dimensions"""
        dimensions = {
            "environment": "production",
            "service": "sfdc-service",
            "version": "1.0.0"
        }
        
        custom_dims = self.collector.create_custom_dimensions(dimensions)
        
        assert isinstance(custom_dims, dict)
        assert custom_dims["environment"] == "production"
        assert custom_dims["service"] == "sfdc-service"


@pytest.mark.unit
class TestPerformanceMonitor:
    """Test cases for PerformanceMonitor"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.monitor = PerformanceMonitor()
    
    def test_init(self):
        """Test PerformanceMonitor initialization"""
        assert self.monitor is not None
        assert hasattr(self.monitor, 'active_operations')
        assert hasattr(self.monitor, 'performance_data')
    
    def test_start_operation_tracking(self):
        """Test starting operation tracking"""
        operation_id = "test_operation_123"
        operation_name = "database_query"
        
        self.monitor.start_operation(operation_id, operation_name)
        
        assert operation_id in self.monitor.active_operations
        operation = self.monitor.active_operations[operation_id]
        assert operation['name'] == operation_name
        assert 'start_time' in operation
    
    def test_end_operation_tracking(self):
        """Test ending operation tracking"""
        operation_id = "test_operation_123"
        operation_name = "database_query"
        
        # Start and end operation
        self.monitor.start_operation(operation_id, operation_name)
        result = self.monitor.end_operation(operation_id, {"status": "success"})
        
        assert result is not None
        assert result['duration'] > 0
        assert result['metadata']['status'] == 'success'
        assert operation_id not in self.monitor.active_operations
    
    def test_track_dependency_call(self):
        """Test tracking dependency calls"""
        dependency_name = "salesforce_api"
        dependency_type = "http"
        duration = 150.0
        success = True
        
        self.monitor.track_dependency(
            dependency_name, dependency_type, duration, success
        )
        
        # Check if dependency was recorded
        assert len(self.monitor.performance_data) > 0
        recorded = self.monitor.performance_data[-1]
        assert recorded['type'] == 'dependency'
        assert recorded['name'] == dependency_name
        assert recorded['duration'] == duration
        assert recorded['success'] == success
    
    def test_track_request_performance(self):
        """Test tracking request performance"""
        request_name = "POST /api/users"
        duration = 200.0
        status_code = 200
        
        self.monitor.track_request(request_name, duration, status_code)
        
        # Check if request was recorded
        recorded = self.monitor.performance_data[-1]
        assert recorded['type'] == 'request'
        assert recorded['name'] == request_name
        assert recorded['duration'] == duration
        assert recorded['status_code'] == status_code
    
    def test_get_performance_statistics(self):
        """Test getting performance statistics"""
        # Track some operations
        self.monitor.track_request("GET /api/users", 100.0, 200)
        self.monitor.track_request("POST /api/users", 150.0, 201)
        self.monitor.track_dependency("database", "sql", 50.0, True)
        
        stats = self.monitor.get_performance_statistics()
        
        assert isinstance(stats, dict)
        assert 'requests' in stats
        assert 'dependencies' in stats
        assert 'summary' in stats
    
    def test_performance_context_manager(self):
        """Test performance monitoring context manager"""
        operation_name = "complex_operation"
        
        with self.monitor.track_operation(operation_name) as tracker:
            # Simulate some work
            import time
            time.sleep(0.01)
            tracker.add_metadata({"items_processed": 100})
        
        # Check if operation was tracked
        assert len(self.monitor.performance_data) > 0
        recorded = self.monitor.performance_data[-1]
        assert recorded['name'] == operation_name
        assert recorded['duration'] > 0
        assert recorded['metadata']['items_processed'] == 100
    
    def test_get_slow_operations(self):
        """Test getting slow operations"""
        # Track operations with different durations
        self.monitor.track_request("fast_request", 50.0, 200)
        self.monitor.track_request("slow_request", 2000.0, 200)
        self.monitor.track_dependency("fast_db", "sql", 30.0, True)
        self.monitor.track_dependency("slow_db", "sql", 1500.0, True)
        
        slow_ops = self.monitor.get_slow_operations(threshold_ms=1000.0)
        
        assert len(slow_ops) == 2
        assert any(op['name'] == 'slow_request' for op in slow_ops)
        assert any(op['name'] == 'slow_db' for op in slow_ops)


@pytest.mark.unit
class TestHealthCheckService:
    """Test cases for HealthCheckService"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.health_service = HealthCheckService()
    
    def test_init(self):
        """Test HealthCheckService initialization"""
        assert self.health_service is not None
        assert hasattr(self.health_service, 'health_checks')
        assert hasattr(self.health_service, 'last_check_results')
    
    def test_register_health_check(self):
        """Test registering a health check"""
        def dummy_check():
            return {"status": "healthy", "details": "All good"}
        
        self.health_service.register_health_check("dummy_service", dummy_check)
        
        assert "dummy_service" in self.health_service.health_checks
    
    @patch('shared.monitoring.requests.get')
    def test_check_database_health(self, mock_requests):
        """Test database health check"""
        # Mock successful database connection
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "ok"}
        mock_requests.return_value = mock_response
        
        result = self.health_service.check_database_health()
        
        assert result['status'] == 'healthy'
        assert 'response_time' in result
    
    @patch('shared.monitoring.requests.get')
    def test_check_external_service_health(self, mock_requests):
        """Test external service health check"""
        service_url = "https://api.salesforce.com/health"
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_requests.return_value = mock_response
        
        result = self.health_service.check_external_service_health(service_url)
        
        assert result['status'] == 'healthy'
        assert result['response_time'] == 500  # milliseconds
    
    @patch('shared.monitoring.requests.get')
    def test_check_external_service_health_failure(self, mock_requests):
        """Test external service health check failure"""
        service_url = "https://api.salesforce.com/health"
        
        # Mock failed response
        mock_requests.side_effect = Exception("Connection timeout")
        
        result = self.health_service.check_external_service_health(service_url)
        
        assert result['status'] == 'unhealthy'
        assert 'Connection timeout' in result['error']
    
    @patch('shared.monitoring.psutil')
    def test_check_system_health(self, mock_psutil):
        """Test system health check"""
        # Mock system metrics
        mock_psutil.cpu_percent.return_value = 45.0
        mock_psutil.virtual_memory.return_value = Mock(percent=60.0)
        mock_psutil.disk_usage.return_value = Mock(percent=70.0)
        
        result = self.health_service.check_system_health()
        
        assert result['status'] == 'healthy'
        assert result['cpu_percent'] == 45.0
        assert result['memory_percent'] == 60.0
        assert result['disk_percent'] == 70.0
    
    @patch('shared.monitoring.psutil')
    def test_check_system_health_high_usage(self, mock_psutil):
        """Test system health check with high resource usage"""
        # Mock high resource usage
        mock_psutil.cpu_percent.return_value = 95.0
        mock_psutil.virtual_memory.return_value = Mock(percent=90.0)
        mock_psutil.disk_usage.return_value = Mock(percent=85.0)
        
        result = self.health_service.check_system_health()
        
        assert result['status'] == 'degraded'
        assert 'High resource usage detected' in result['message']
    
    def test_run_all_health_checks(self):
        """Test running all registered health checks"""
        # Register some dummy health checks
        def healthy_check():
            return {"status": "healthy", "service": "service1"}
        
        def unhealthy_check():
            return {"status": "unhealthy", "service": "service2", "error": "Service down"}
        
        self.health_service.register_health_check("service1", healthy_check)
        self.health_service.register_health_check("service2", unhealthy_check)
        
        results = self.health_service.run_all_health_checks()
        
        assert isinstance(results, dict)
        assert "service1" in results
        assert "service2" in results
        assert results["service1"]["status"] == "healthy"
        assert results["service2"]["status"] == "unhealthy"
    
    def test_get_overall_health_status(self):
        """Test getting overall health status"""
        # Mock some health check results
        self.health_service.last_check_results = {
            "database": {"status": "healthy"},
            "salesforce": {"status": "healthy"},
            "system": {"status": "degraded"}
        }
        
        overall_status = self.health_service.get_overall_health_status()
        
        assert overall_status['status'] == 'degraded'  # Worst status
        assert overall_status['total_checks'] == 3
        assert overall_status['healthy_checks'] == 2
        assert overall_status['unhealthy_checks'] == 0
        assert overall_status['degraded_checks'] == 1


@pytest.mark.unit
class TestMonitoringService:
    """Test cases for MonitoringService main class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.monitoring_service = MonitoringService()
    
    def test_init(self):
        """Test MonitoringService initialization"""
        assert self.monitoring_service is not None
        assert hasattr(self.monitoring_service, 'logger')
        assert hasattr(self.monitoring_service, 'metrics_collector')
        assert hasattr(self.monitoring_service, 'performance_monitor')
        assert hasattr(self.monitoring_service, 'health_service')
    
    def test_log_and_track_operation(self):
        """Test logging and tracking operation"""
        operation_name = "user_authentication"
        context = {"user_id": "user123", "method": "oauth"}
        
        with self.monitoring_service.track_operation(operation_name, context) as tracker:
            # Simulate some work
            import time
            time.sleep(0.01)
            tracker.record_metric("auth_attempts", 1)
        
        # Check if operation was logged and tracked
        assert len(self.monitoring_service.performance_monitor.performance_data) > 0
        assert len(self.monitoring_service.metrics_collector.metrics_buffer) > 0
    
    def test_create_monitoring_dashboard_data(self):
        """Test creating monitoring dashboard data"""
        # Generate some sample data
        self.monitoring_service.metrics_collector.record_counter("requests", 100, {})
        self.monitoring_service.performance_monitor.track_request("GET /api/users", 150.0, 200)
        
        dashboard_data = self.monitoring_service.create_dashboard_data()
        
        assert isinstance(dashboard_data, dict)
        assert 'metrics_summary' in dashboard_data
        assert 'performance_stats' in dashboard_data
        assert 'health_status' in dashboard_data
    
    def test_export_monitoring_data(self):
        """Test exporting monitoring data"""
        # Generate some sample data
        self.monitoring_service.metrics_collector.record_gauge("cpu_usage", 75.0, {})
        self.monitoring_service.performance_monitor.track_dependency("database", "sql", 50.0, True)
        
        exported_data = self.monitoring_service.export_monitoring_data()
        
        assert isinstance(exported_data, dict)
        assert 'timestamp' in exported_data
        assert 'metrics' in exported_data
        assert 'performance' in exported_data
        assert 'health' in exported_data


@pytest.mark.unit
class TestMonitoringServiceGlobal:
    """Test cases for global monitoring service functions"""
    
    def test_get_monitoring_service_singleton(self):
        """Test that get_monitoring_service returns singleton instance"""
        service1 = get_monitoring_service()
        service2 = get_monitoring_service()
        
        assert service1 is service2
        assert isinstance(service1, MonitoringService)


@pytest.mark.unit
class TestMonitoringDecorators:
    """Test cases for monitoring decorators"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.monitoring_service = MonitoringService()
    
    def test_monitor_performance_decorator(self):
        """Test performance monitoring decorator"""
        @self.monitoring_service.monitor_performance
        def test_function(x, y):
            import time
            time.sleep(0.01)
            return x + y
        
        result = test_function(2, 3)
        
        assert result == 5
        # Check if performance was monitored
        assert len(self.monitoring_service.performance_monitor.performance_data) > 0
    
    def test_log_execution_decorator(self):
        """Test execution logging decorator"""
        @self.monitoring_service.log_execution
        def test_function(name):
            return f"Hello, {name}!"
        
        result = test_function("World")
        
        assert result == "Hello, World!"
        # Logging would be verified through mock assertions in real implementation
    
    def test_track_metrics_decorator(self):
        """Test metrics tracking decorator"""
        @self.monitoring_service.track_metrics("function_calls")
        def test_function():
            return "executed"
        
        result = test_function()
        
        assert result == "executed"
        # Check if metric was recorded
        assert len(self.monitoring_service.metrics_collector.metrics_buffer) > 0