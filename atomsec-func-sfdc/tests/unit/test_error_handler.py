"""
Unit tests for Enhanced Error Handler
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime, timedelta
from enum import Enum

# Import the module under test
from src.shared.error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    CircuitBreaker, 
    RetryConfig, 
    ErrorCategory,
    CircuitBreakerState,
    ErrorResponse,
    CircuitBreakerOpenError
)


class TestErrorHandler:
    """Test suite for ErrorHandler"""

    def setup_method(self):
        """Set up test fixtures"""
        self.error_handler = ErrorHandler()

    def test_error_category_enum(self):
        """Test ErrorCategory enum values"""
        assert ErrorCategory.TRANSIENT.value == "transient"
        assert ErrorCategory.PERMANENT.value == "permanent"
        assert ErrorCategory.BUSINESS_LOGIC.value == "business_logic"
        assert ErrorCategory.SYSTEM.value == "system"

    def test_circuit_breaker_state_enum(self):
        """Test CircuitBreakerState enum values"""
        assert CircuitBreakerState.CLOSED.value == "closed"
        assert CircuitBreakerState.OPEN.value == "open"
        assert CircuitBreakerState.HALF_OPEN.value == "half_open"

    def test_retry_config_creation(self):
        """Test RetryConfig creation and properties"""
        config = RetryConfig(
            max_attempts=3,
            base_delay_seconds=1.0,
            max_delay_seconds=60.0,
            exponential_base=2.0,
            jitter=True
        )
        
        assert config.max_attempts == 3
        assert config.base_delay_seconds == 1.0
        assert config.max_delay_seconds == 60.0
        assert config.exponential_base == 2.0
        assert config.jitter is True

    def test_error_response_creation(self):
        """Test ErrorResponse creation and properties"""
        response = ErrorResponse(
            error_code="VALIDATION_ERROR",
            message="Invalid input data",
            details={"field": "email", "reason": "invalid format"},
            correlation_id="corr-123",
            timestamp=datetime.utcnow()
        )
        
        assert response.error_code == "VALIDATION_ERROR"
        assert response.message == "Invalid input data"
        assert response.details["field"] == "email"
        assert response.correlation_id == "corr-123"
        assert isinstance(response.timestamp, datetime)

    def test_categorize_error_transient(self):
        """Test error categorization for transient errors"""
        transient_errors = [
            ConnectionError("Connection timeout"),
            TimeoutError("Request timeout"),
            Exception("Service temporarily unavailable")
        ]
        
        for error in transient_errors:
            category = self.error_handler.categorize_error(error)
            assert category == ErrorCategory.TRANSIENT

    def test_categorize_error_permanent(self):
        """Test error categorization for permanent errors"""
        permanent_errors = [
            ValueError("Invalid parameter"),
            KeyError("Missing required field"),
            TypeError("Invalid data type")
        ]
        
        for error in permanent_errors:
            category = self.error_handler.categorize_error(error)
            assert category == ErrorCategory.PERMANENT

    def test_categorize_error_business_logic(self):
        """Test error categorization for business logic errors"""
        # Mock business logic exceptions
        class ValidationError(Exception):
            pass
        
        class BusinessRuleViolation(Exception):
            pass
        
        business_errors = [
            ValidationError("Validation failed"),
            BusinessRuleViolation("Business rule violated")
        ]
        
        with patch.object(self.error_handler, '_is_business_logic_error', return_value=True):
            for error in business_errors:
                category = self.error_handler.categorize_error(error)
                assert category == ErrorCategory.BUSINESS_LOGIC

    def test_categorize_error_system(self):
        """Test error categorization for system errors"""
        system_errors = [
            MemoryError("Out of memory"),
            OSError("Disk full"),
            SystemError("System error occurred")
        ]
        
        for error in system_errors:
            category = self.error_handler.categorize_error(error)
            assert category == ErrorCategory.SYSTEM

    def test_build_error_response_with_context(self):
        """Test error response building with context"""
        error = ValueError("Invalid email format")
        context = {
            "user_id": "user123",
            "operation": "user_registration",
            "request_data": {"email": "invalid-email"}
        }
        correlation_id = "corr-456"
        
        response = self.error_handler.build_error_response(error, context, correlation_id)
        
        assert response.error_code is not None
        assert "Invalid email format" in response.message
        assert response.correlation_id == correlation_id
        assert response.details is not None
        assert isinstance(response.timestamp, datetime)

    def test_build_error_response_without_context(self):
        """Test error response building without context"""
        error = ConnectionError("Database connection failed")
        
        response = self.error_handler.build_error_response(error)
        
        assert response.error_code is not None
        assert "Database connection failed" in response.message
        assert response.correlation_id is not None  # Should generate one
        assert isinstance(response.timestamp, datetime)

    def test_should_retry_transient_error(self):
        """Test retry decision for transient errors"""
        transient_error = ConnectionError("Connection timeout")
        
        should_retry = self.error_handler.should_retry(transient_error, attempt=1, max_attempts=3)
        
        assert should_retry is True

    def test_should_retry_permanent_error(self):
        """Test retry decision for permanent errors"""
        permanent_error = ValueError("Invalid parameter")
        
        should_retry = self.error_handler.should_retry(permanent_error, attempt=1, max_attempts=3)
        
        assert should_retry is False

    def test_should_retry_max_attempts_reached(self):
        """Test retry decision when max attempts reached"""
        transient_error = ConnectionError("Connection timeout")
        
        should_retry = self.error_handler.should_retry(transient_error, attempt=3, max_attempts=3)
        
        assert should_retry is False

    def test_calculate_retry_delay_exponential(self):
        """Test exponential backoff delay calculation"""
        config = RetryConfig(
            max_attempts=5,
            base_delay_seconds=1.0,
            max_delay_seconds=60.0,
            exponential_base=2.0,
            jitter=False  # Disable jitter for predictable testing
        )
        
        # Test exponential progression
        delay1 = self.error_handler.calculate_retry_delay(1, config)
        delay2 = self.error_handler.calculate_retry_delay(2, config)
        delay3 = self.error_handler.calculate_retry_delay(3, config)
        
        assert delay1 == 1.0  # base_delay * (exponential_base ^ 0)
        assert delay2 == 2.0  # base_delay * (exponential_base ^ 1)
        assert delay3 == 4.0  # base_delay * (exponential_base ^ 2)

    def test_calculate_retry_delay_max_limit(self):
        """Test retry delay respects maximum limit"""
        config = RetryConfig(
            max_attempts=10,
            base_delay_seconds=1.0,
            max_delay_seconds=5.0,  # Low max to test capping
            exponential_base=2.0,
            jitter=False
        )
        
        # High attempt number should be capped at max_delay_seconds
        delay = self.error_handler.calculate_retry_delay(10, config)
        
        assert delay == 5.0

    def test_calculate_retry_delay_with_jitter(self):
        """Test retry delay with jitter enabled"""
        config = RetryConfig(
            max_attempts=5,
            base_delay_seconds=2.0,
            max_delay_seconds=60.0,
            exponential_base=2.0,
            jitter=True
        )
        
        # With jitter, delay should vary but be within expected range
        delays = [self.error_handler.calculate_retry_delay(2, config) for _ in range(10)]
        
        # All delays should be different (with high probability)
        assert len(set(delays)) > 1
        
        # All delays should be within reasonable bounds (base delay ± some variance)
        base_expected = 4.0  # base_delay * (exponential_base ^ 1)
        for delay in delays:
            assert 0.5 * base_expected <= delay <= 1.5 * base_expected

    def test_log_error_with_context(self):
        """Test error logging with context"""
        error = ValueError("Test error")
        context = {
            "user_id": "user123",
            "operation": "test_operation"
        }
        
        with patch('shared.error_handler.logging') as mock_logging:
            self.error_handler.log_error(error, context)
            
            # Verify logging was called
            mock_logging.error.assert_called_once()

    def test_log_error_without_context(self):
        """Test error logging without context"""
        error = ConnectionError("Database error")
        
        with patch('shared.error_handler.logging') as mock_logging:
            self.error_handler.log_error(error)
            
            # Verify logging was called
            mock_logging.error.assert_called_once()

    def test_get_error_code_mapping(self):
        """Test error code mapping for different exception types"""
        error_mappings = [
            (ValueError("test"), "VALIDATION_ERROR"),
            (ConnectionError("test"), "CONNECTION_ERROR"),
            (TimeoutError("test"), "TIMEOUT_ERROR"),
            (KeyError("test"), "MISSING_FIELD_ERROR"),
            (Exception("test"), "INTERNAL_ERROR")
        ]
        
        for error, expected_code in error_mappings:
            code = self.error_handler.get_error_code(error)
            assert code == expected_code

    def test_sanitize_error_message_sensitive_data(self):
        """Test error message sanitization removes sensitive data"""
        sensitive_message = "Authentication failed for user password123 with token abc123def456"
        
        sanitized = self.error_handler.sanitize_error_message(sensitive_message)
        
        assert "password123" not in sanitized
        assert "abc123def456" not in sanitized
        assert "Authentication failed" in sanitized

    def test_sanitize_error_message_safe_data(self):
        """Test error message sanitization preserves safe data"""
        safe_message = "Invalid email format for field 'email'"
        
        sanitized = self.error_handler.sanitize_error_message(safe_message)
        
        assert sanitized == safe_message

    def test_create_fallback_response(self):
        """Test fallback response creation"""
        operation = "user_authentication"
        
        response = self.error_handler.create_fallback_response(operation)
        
        assert response is not None
        assert "fallback" in response.get("status", "").lower()
        assert operation in str(response)

    def test_track_error_metrics(self):
        """Test error metrics tracking"""
        error = ValueError("Test error")
        context = {"operation": "test_op"}
        
        with patch.object(self.error_handler, '_increment_error_counter') as mock_counter:
            self.error_handler.track_error_metrics(error, context)
            
            mock_counter.assert_called_once()

    def test_get_retry_config_for_operation(self):
        """Test getting retry configuration for specific operations"""
        operation_configs = [
            ("database_query", 5),
            ("external_api_call", 3),
            ("file_upload", 2),
            ("default_operation", 3)  # Default
        ]
        
        for operation, expected_max_attempts in operation_configs:
            config = self.error_handler.get_retry_config_for_operation(operation)
            
            assert config.max_attempts == expected_max_attempts
            assert isinstance(config, RetryConfig)


class TestCircuitBreaker:
    """Test suite for CircuitBreaker"""

    def setup_method(self):
        """Set up test fixtures"""
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout_seconds=60,
            success_threshold=2
        )

    def test_circuit_breaker_initial_state(self):
        """Test circuit breaker initial state"""
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert self.circuit_breaker.failure_count == 0
        assert self.circuit_breaker.success_count == 0

    def test_circuit_breaker_record_success_closed(self):
        """Test recording success when circuit is closed"""
        self.circuit_breaker.record_success()
        
        assert self.circuit_breaker.failure_count == 0
        assert self.circuit_breaker.success_count == 1
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED

    def test_circuit_breaker_record_failure_closed(self):
        """Test recording failure when circuit is closed"""
        # Record failures up to threshold
        for i in range(2):
            self.circuit_breaker.record_failure()
            assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        
        # This failure should open the circuit
        self.circuit_breaker.record_failure()
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN

    def test_circuit_breaker_open_state_blocks_calls(self):
        """Test that open circuit breaker blocks calls"""
        # Force circuit to open
        for i in range(3):
            self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN
        
        # Should raise exception when trying to execute
        with pytest.raises(CircuitBreakerOpenError):
            self.circuit_breaker.call(lambda: "test")

    def test_circuit_breaker_half_open_transition(self):
        """Test transition from open to half-open state"""
        # Force circuit to open
        for i in range(3):
            self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN
        
        # Mock time passage to trigger half-open state
        with patch('time.time') as mock_time:
            mock_time.return_value = self.circuit_breaker.last_failure_time + 61  # Past recovery timeout
            
            # Next call should transition to half-open
            try:
                self.circuit_breaker.call(lambda: "success")
                assert self.circuit_breaker.state == CircuitBreakerState.HALF_OPEN
            except CircuitBreakerOpenError:
                # This is expected behavior during transition
                pass

    def test_circuit_breaker_half_open_success_recovery(self):
        """Test recovery from half-open to closed on success"""
        # Set circuit to half-open state
        self.circuit_breaker.state = CircuitBreakerState.HALF_OPEN
        self.circuit_breaker.success_count = 0
        
        # Record enough successes to close circuit
        for i in range(2):  # success_threshold = 2
            self.circuit_breaker.record_success()
        
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert self.circuit_breaker.failure_count == 0

    def test_circuit_breaker_half_open_failure_reopens(self):
        """Test that failure in half-open state reopens circuit"""
        # Set circuit to half-open state
        self.circuit_breaker.state = CircuitBreakerState.HALF_OPEN
        
        # Record failure should reopen circuit
        self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN

    def test_circuit_breaker_call_success(self):
        """Test successful function call through circuit breaker"""
        def successful_operation():
            return "success"
        
        result = self.circuit_breaker.call(successful_operation)
        
        assert result == "success"
        assert self.circuit_breaker.success_count == 1
        assert self.circuit_breaker.failure_count == 0

    def test_circuit_breaker_call_failure(self):
        """Test failed function call through circuit breaker"""
        def failing_operation():
            raise ValueError("Operation failed")
        
        with pytest.raises(ValueError):
            self.circuit_breaker.call(failing_operation)
        
        assert self.circuit_breaker.failure_count == 1
        assert self.circuit_breaker.success_count == 0

    def test_circuit_breaker_reset(self):
        """Test circuit breaker reset functionality"""
        # Record some failures
        for i in range(2):
            self.circuit_breaker.record_failure()
        
        # Reset circuit breaker
        self.circuit_breaker.reset()
        
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert self.circuit_breaker.failure_count == 0
        assert self.circuit_breaker.success_count == 0

    def test_circuit_breaker_get_state_info(self):
        """Test getting circuit breaker state information"""
        # Record some activity
        self.circuit_breaker.record_failure()
        self.circuit_breaker.record_success()
        
        state_info = self.circuit_breaker.get_state_info()
        
        assert state_info["state"] == CircuitBreakerState.CLOSED.value
        assert state_info["failure_count"] == 1
        assert state_info["success_count"] == 1
        assert "last_failure_time" in state_info

    def test_circuit_breaker_open_error_exception(self):
        """Test CircuitBreakerOpenError exception"""
        with pytest.raises(CircuitBreakerOpenError) as exc_info:
            raise CircuitBreakerOpenError("Circuit breaker is open")
        
        assert str(exc_info.value) == "Circuit breaker is open"

    def test_circuit_breaker_with_different_thresholds(self):
        """Test circuit breaker with different threshold configurations"""
        # High failure threshold
        high_threshold_cb = CircuitBreaker(
            failure_threshold=10,
            recovery_timeout_seconds=30,
            success_threshold=5
        )
        
        # Should not open after few failures
        for i in range(5):
            high_threshold_cb.record_failure()
        
        assert high_threshold_cb.state == CircuitBreakerState.CLOSED
        
        # Should open after reaching threshold
        for i in range(5):
            high_threshold_cb.record_failure()
        
        assert high_threshold_cb.state == CircuitBreakerState.OPEN