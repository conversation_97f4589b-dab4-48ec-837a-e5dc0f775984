"""
Mock Key Vault service for testing
"""
import logging
from typing import Dict, Optional, Any
from unittest.mock import Mock

logger = logging.getLogger(__name__)

class MockKeyVaultService:
    """Mock Azure Key Vault service for testing"""
    
    def __init__(self):
        self._secrets: Dict[str, str] = {
            "salesforce-client-id": "test_client_id",
            "salesforce-client-secret": "test_client_secret",
            "salesforce-private-key": "test_private_key",
            "jwt-secret": "test_jwt_secret",
            "database-connection-string": "test_db_connection",
            "application-insights-key": "test_app_insights_key"
        }
        self._access_log: list = []
    
    def get_secret(self, secret_name: str) -> str:
        """Get a secret from the mock vault"""
        self._access_log.append(f"GET:{secret_name}")
        if secret_name in self._secrets:
            logger.info(f"Retrieved secret: {secret_name}")
            return self._secrets[secret_name]
        else:
            logger.error(f"Secret not found: {secret_name}")
            raise KeyError(f"Secret '{secret_name}' not found in Key Vault")
    
    def set_secret(self, secret_name: str, value: str) -> None:
        """Set a secret in the mock vault"""
        self._access_log.append(f"SET:{secret_name}")
        self._secrets[secret_name] = value
        logger.info(f"Set secret: {secret_name}")
    
    def delete_secret(self, secret_name: str) -> None:
        """Delete a secret from the mock vault"""
        self._access_log.append(f"DELETE:{secret_name}")
        if secret_name in self._secrets:
            del self._secrets[secret_name]
            logger.info(f"Deleted secret: {secret_name}")
        else:
            raise KeyError(f"Secret '{secret_name}' not found")
    
    def list_secrets(self) -> list:
        """List all secret names"""
        self._access_log.append("LIST")
        return list(self._secrets.keys())
    
    def get_access_log(self) -> list:
        """Get access log for testing"""
        return self._access_log.copy()
    
    def clear_access_log(self) -> None:
        """Clear access log"""
        self._access_log.clear()
    
    def reset(self) -> None:
        """Reset the mock vault to initial state"""
        self.__init__()

# Global mock instance
mock_key_vault = MockKeyVaultService()

class MockAzureKeyVaultClient:
    """Mock Azure Key Vault client"""
    
    def __init__(self, vault_url: str, credential: Any = None):
        self.vault_url = vault_url
        self.credential = credential
    
    def get_secret(self, name: str) -> Mock:
        """Mock get_secret method"""
        secret_mock = Mock()
        secret_mock.value = mock_key_vault.get_secret(name)
        secret_mock.name = name
        return secret_mock
    
    def set_secret(self, name: str, value: str) -> Mock:
        """Mock set_secret method"""
        mock_key_vault.set_secret(name, value)
        secret_mock = Mock()
        secret_mock.value = value
        secret_mock.name = name
        return secret_mock