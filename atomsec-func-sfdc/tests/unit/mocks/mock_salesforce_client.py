"""
Mock Salesforce client for unit testing
"""

from unittest.mock import Mock, <PERSON>Mock
from typing import Dict, Any, Optional, List
import json
from datetime import datetime, timedelta


class MockSalesforceClient:
    """Mock Salesforce client for testing"""
    
    def __init__(self):
        self.is_authenticated = False
        self.access_token = None
        self.instance_url = "https://test.salesforce.com"
        self.metadata_cache = {}
        self.query_results = {}
        self.api_calls = []
        
        # Mock data
        self.mock_profiles = [
            {
                "Id": "00e000000000001",
                "Name": "System Administrator",
                "UserLicense": {"Name": "Salesforce"},
                "PermissionsModifyAllData": True,
                "PermissionsViewAllData": True
            },
            {
                "Id": "00e000000000002", 
                "Name": "Standard User",
                "UserLicense": {"Name": "Salesforce"},
                "PermissionsModifyAllData": False,
                "PermissionsViewAllData": False
            }
        ]
        
        self.mock_permission_sets = [
            {
                "Id": "0PS000000000001",
                "Name": "API_Only_User",
                "Label": "API Only User",
                "IsOwnedByProfile": False,
                "PermissionsApiEnabled": True
            },
            {
                "Id": "0PS000000000002",
                "Name": "Marketing_User", 
                "Label": "Marketing User",
                "IsOwnedByProfile": False,
                "PermissionsRunReports": True
            }
        ]
        
        self.mock_users = [
            {
                "Id": "005000000000001",
                "Username": "<EMAIL>",
                "Email": "<EMAIL>",
                "ProfileId": "00e000000000001",
                "IsActive": True,
                "LastLoginDate": datetime.utcnow().isoformat()
            },
            {
                "Id": "005000000000002",
                "Username": "<EMAIL>", 
                "Email": "<EMAIL>",
                "ProfileId": "00e000000000002",
                "IsActive": True,
                "LastLoginDate": (datetime.utcnow() - timedelta(days=1)).isoformat()
            }
        ]
    
    def authenticate(self, client_id: str, client_secret: str, username: str = None, password: str = None) -> bool:
        """Mock authentication method"""
        self.api_calls.append({
            "method": "authenticate",
            "params": {"client_id": client_id, "username": username},
            "timestamp": datetime.utcnow()
        })
        
        if client_id == "invalid_client":
            self.is_authenticated = False
            return False
        
        self.is_authenticated = True
        self.access_token = "mock_access_token_12345"
        return True
    
    def is_connected(self) -> bool:
        """Mock connection check"""
        return self.is_authenticated
    
    def query(self, soql_query: str) -> Dict[str, Any]:
        """Mock SOQL query method"""
        self.api_calls.append({
            "method": "query",
            "params": {"query": soql_query},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        # Simple query routing based on query content
        if "Profile" in soql_query:
            return {
                "totalSize": len(self.mock_profiles),
                "done": True,
                "records": self.mock_profiles
            }
        elif "PermissionSet" in soql_query:
            return {
                "totalSize": len(self.mock_permission_sets),
                "done": True,
                "records": self.mock_permission_sets
            }
        elif "User" in soql_query:
            return {
                "totalSize": len(self.mock_users),
                "done": True,
                "records": self.mock_users
            }
        else:
            return {
                "totalSize": 0,
                "done": True,
                "records": []
            }
    
    def describe_metadata(self, metadata_type: str) -> Dict[str, Any]:
        """Mock metadata describe method"""
        self.api_calls.append({
            "method": "describe_metadata",
            "params": {"metadata_type": metadata_type},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        mock_metadata = {
            "Profile": {
                "xmlName": "Profile",
                "directoryName": "profiles",
                "suffix": "profile",
                "childXmlNames": ["ProfileApplicationVisibility", "ProfileObjectPermissions"]
            },
            "PermissionSet": {
                "xmlName": "PermissionSet",
                "directoryName": "permissionsets", 
                "suffix": "permissionset",
                "childXmlNames": ["PermissionSetApplicationVisibility", "PermissionSetObjectPermissions"]
            },
            "CustomObject": {
                "xmlName": "CustomObject",
                "directoryName": "objects",
                "suffix": "object",
                "childXmlNames": ["CustomField", "ValidationRule", "WorkflowRule"]
            }
        }
        
        return mock_metadata.get(metadata_type, {
            "xmlName": metadata_type,
            "directoryName": metadata_type.lower(),
            "suffix": metadata_type.lower()
        })
    
    def retrieve_metadata(self, metadata_type: str, names: List[str]) -> Dict[str, Any]:
        """Mock metadata retrieve method"""
        self.api_calls.append({
            "method": "retrieve_metadata",
            "params": {"metadata_type": metadata_type, "names": names},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        # Mock metadata content
        mock_metadata_content = {}
        for name in names:
            if metadata_type == "Profile":
                mock_metadata_content[name] = {
                    "fullName": name,
                    "userPermissions": [
                        {"enabled": True, "name": "ApiEnabled"},
                        {"enabled": False, "name": "ModifyAllData"}
                    ],
                    "objectPermissions": [
                        {
                            "object": "Account",
                            "allowCreate": True,
                            "allowDelete": False,
                            "allowEdit": True,
                            "allowRead": True
                        }
                    ]
                }
            elif metadata_type == "PermissionSet":
                mock_metadata_content[name] = {
                    "fullName": name,
                    "label": name.replace("_", " "),
                    "userPermissions": [
                        {"enabled": True, "name": "ApiEnabled"}
                    ],
                    "objectPermissions": [
                        {
                            "object": "Contact",
                            "allowCreate": False,
                            "allowDelete": False,
                            "allowEdit": True,
                            "allowRead": True
                        }
                    ]
                }
        
        return {
            "success": True,
            "metadata": mock_metadata_content
        }
    
    def get_organization_info(self) -> Dict[str, Any]:
        """Mock organization info method"""
        self.api_calls.append({
            "method": "get_organization_info",
            "params": {},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        return {
            "Id": "00D000000000001",
            "Name": "Test Organization",
            "OrganizationType": "Developer Edition",
            "InstanceName": "CS1",
            "IsSandbox": True,
            "LanguageLocaleKey": "en_US",
            "TimeZoneSidKey": "America/Los_Angeles"
        }
    
    def run_apex_code(self, apex_code: str) -> Dict[str, Any]:
        """Mock Apex code execution"""
        self.api_calls.append({
            "method": "run_apex_code",
            "params": {"apex_code": apex_code[:100]},  # Truncate for logging
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        # Mock successful execution
        return {
            "success": True,
            "compiled": True,
            "compileProblem": None,
            "exceptionMessage": None,
            "exceptionStackTrace": None,
            "line": -1,
            "column": -1
        }
    
    def get_security_health_check(self) -> Dict[str, Any]:
        """Mock security health check"""
        self.api_calls.append({
            "method": "get_security_health_check",
            "params": {},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        return {
            "overallScore": 85,
            "risks": [
                {
                    "riskType": "PasswordPolicy",
                    "severity": "Medium",
                    "description": "Password policy could be stronger",
                    "recommendation": "Enable password complexity requirements"
                },
                {
                    "riskType": "SessionTimeout",
                    "severity": "Low", 
                    "description": "Session timeout is longer than recommended",
                    "recommendation": "Reduce session timeout to 2 hours"
                }
            ],
            "compliantSettings": [
                {
                    "settingType": "TwoFactorAuthentication",
                    "status": "Enabled",
                    "description": "Two-factor authentication is properly configured"
                }
            ]
        }
    
    def get_apex_classes(self) -> List[Dict[str, Any]]:
        """Mock Apex classes retrieval"""
        self.api_calls.append({
            "method": "get_apex_classes",
            "params": {},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        return [
            {
                "Id": "01p000000000001",
                "Name": "AccountController",
                "Body": "public class AccountController { /* mock code */ }",
                "LengthWithoutComments": 150,
                "CreatedDate": datetime.utcnow().isoformat(),
                "LastModifiedDate": datetime.utcnow().isoformat()
            },
            {
                "Id": "01p000000000002",
                "Name": "ContactService",
                "Body": "public class ContactService { /* mock code */ }",
                "LengthWithoutComments": 200,
                "CreatedDate": datetime.utcnow().isoformat(),
                "LastModifiedDate": datetime.utcnow().isoformat()
            }
        ]
    
    def get_apex_triggers(self) -> List[Dict[str, Any]]:
        """Mock Apex triggers retrieval"""
        self.api_calls.append({
            "method": "get_apex_triggers",
            "params": {},
            "timestamp": datetime.utcnow()
        })
        
        if not self.is_authenticated:
            raise Exception("Not authenticated")
        
        return [
            {
                "Id": "01q000000000001",
                "Name": "AccountTrigger",
                "TableEnumOrId": "Account",
                "Body": "trigger AccountTrigger on Account (before insert) { /* mock code */ }",
                "LengthWithoutComments": 100,
                "CreatedDate": datetime.utcnow().isoformat(),
                "LastModifiedDate": datetime.utcnow().isoformat()
            }
        ]
    
    def logout(self):
        """Mock logout method"""
        self.api_calls.append({
            "method": "logout",
            "params": {},
            "timestamp": datetime.utcnow()
        })
        
        self.is_authenticated = False
        self.access_token = None
    
    def get_api_calls_history(self) -> List[Dict[str, Any]]:
        """Get history of API calls for testing"""
        return self.api_calls
    
    def clear_api_calls_history(self):
        """Clear API calls history for testing"""
        self.api_calls.clear()
    
    def simulate_connection_error(self):
        """Simulate connection error for testing"""
        self.is_authenticated = False
        raise ConnectionError("Simulated connection error")
    
    def simulate_timeout_error(self):
        """Simulate timeout error for testing"""
        raise TimeoutError("Simulated timeout error")
    
    def simulate_authentication_error(self):
        """Simulate authentication error for testing"""
        self.is_authenticated = False
        raise Exception("Invalid credentials")


class MockSalesforceMetadataAPI:
    """Mock Salesforce Metadata API for testing"""
    
    def __init__(self, client: MockSalesforceClient):
        self.client = client
        self.deployed_metadata = {}
    
    def deploy(self, metadata_zip: bytes, options: Dict[str, Any] = None) -> str:
        """Mock metadata deployment"""
        if not self.client.is_authenticated:
            raise Exception("Not authenticated")
        
        deployment_id = f"deploy_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        self.deployed_metadata[deployment_id] = {
            "status": "Succeeded",
            "metadata_size": len(metadata_zip),
            "options": options or {},
            "timestamp": datetime.utcnow()
        }
        
        return deployment_id
    
    def check_deploy_status(self, deployment_id: str) -> Dict[str, Any]:
        """Mock deployment status check"""
        if deployment_id not in self.deployed_metadata:
            return {
                "done": True,
                "success": False,
                "details": {"message": "Deployment not found"}
            }
        
        deployment = self.deployed_metadata[deployment_id]
        return {
            "done": True,
            "success": deployment["status"] == "Succeeded",
            "details": {
                "componentSuccesses": 5,
                "componentFailures": 0,
                "testsRun": 0,
                "testsCompleted": 0
            }
        }
    
    def retrieve(self, metadata_types: List[str]) -> bytes:
        """Mock metadata retrieval"""
        if not self.client.is_authenticated:
            raise Exception("Not authenticated")
        
        # Return mock ZIP content
        mock_zip_content = b"PK\x03\x04mock_metadata_zip_content"
        return mock_zip_content
    
    def list_metadata(self, metadata_type: str, folder: str = None) -> List[Dict[str, Any]]:
        """Mock metadata listing"""
        if not self.client.is_authenticated:
            raise Exception("Not authenticated")
        
        mock_metadata_list = {
            "Profile": [
                {"fullName": "Admin", "type": "Profile", "id": "00e000000000001"},
                {"fullName": "Standard User", "type": "Profile", "id": "00e000000000002"}
            ],
            "PermissionSet": [
                {"fullName": "API_Only_User", "type": "PermissionSet", "id": "0PS000000000001"},
                {"fullName": "Marketing_User", "type": "PermissionSet", "id": "0PS000000000002"}
            ],
            "ApexClass": [
                {"fullName": "AccountController", "type": "ApexClass", "id": "01p000000000001"},
                {"fullName": "ContactService", "type": "ApexClass", "id": "01p000000000002"}
            ]
        }
        
        return mock_metadata_list.get(metadata_type, [])


class MockSalesforceToolingAPI:
    """Mock Salesforce Tooling API for testing"""
    
    def __init__(self, client: MockSalesforceClient):
        self.client = client
    
    def query(self, soql_query: str) -> Dict[str, Any]:
        """Mock Tooling API query"""
        if not self.client.is_authenticated:
            raise Exception("Not authenticated")
        
        # Mock responses for common Tooling API queries
        if "ApexClass" in soql_query:
            return {
                "totalSize": 2,
                "done": True,
                "records": self.client.get_apex_classes()
            }
        elif "ApexTrigger" in soql_query:
            return {
                "totalSize": 1,
                "done": True,
                "records": self.client.get_apex_triggers()
            }
        else:
            return {
                "totalSize": 0,
                "done": True,
                "records": []
            }
    
    def execute_anonymous(self, apex_code: str) -> Dict[str, Any]:
        """Mock anonymous Apex execution"""
        return self.client.run_apex_code(apex_code)
    
    def run_tests_synchronous(self, class_names: List[str]) -> Dict[str, Any]:
        """Mock synchronous test execution"""
        if not self.client.is_authenticated:
            raise Exception("Not authenticated")
        
        return {
            "numTestsRun": len(class_names) * 2,  # Assume 2 tests per class
            "numFailures": 0,
            "numTestsCompleted": len(class_names) * 2,
            "successes": [
                {
                    "name": f"test{i}",
                    "methodName": f"testMethod{i}",
                    "time": 50.0
                } for i in range(len(class_names) * 2)
            ],
            "failures": [],
            "codeCoverage": [
                {
                    "name": class_name,
                    "numLocationsNotCovered": 0,
                    "numLocations": 10,
                    "type": "Class"
                } for class_name in class_names
            ]
        }