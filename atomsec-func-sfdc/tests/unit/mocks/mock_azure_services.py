"""
Mock Azure services for unit testing
"""

from unittest.mock import Mo<PERSON>, <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional, List
import json
from datetime import datetime, timedelta


class MockKeyVaultClient:
    """Mock Azure Key Vault client for testing"""
    
    def __init__(self):
        self.secrets = {
            "test-secret": "test-secret-value",
            "jwt-secret-key": "test-jwt-secret",
            "salesforce-client-id": "test-sf-client-id",
            "salesforce-client-secret": "test-sf-client-secret",
            "database-connection-string": "test-db-connection"
        }
    
    def get_secret(self, secret_name: str) -> Mock:
        """Mock get_secret method"""
        mock_secret = Mock()
        mock_secret.value = self.secrets.get(secret_name, f"mock-{secret_name}")
        mock_secret.name = secret_name
        return mock_secret
    
    def set_secret(self, secret_name: str, value: str) -> Mock:
        """Mock set_secret method"""
        self.secrets[secret_name] = value
        mock_secret = Mock()
        mock_secret.name = secret_name
        mock_secret.value = value
        return mock_secret
    
    def delete_secret(self, secret_name: str) -> <PERSON>ck:
        """Mock delete_secret method"""
        if secret_name in self.secrets:
            del self.secrets[secret_name]
        mock_result = Mock()
        mock_result.name = secret_name
        return mock_result
    
    def list_properties_of_secrets(self) -> List[Mock]:
        """Mock list_properties_of_secrets method"""
        secrets = []
        for name in self.secrets.keys():
            mock_secret = Mock()
            mock_secret.name = name
            mock_secret.enabled = True
            secrets.append(mock_secret)
        return secrets


class MockApplicationInsightsClient:
    """Mock Application Insights client for testing"""
    
    def __init__(self):
        self.telemetry_data = []
        self.custom_events = []
        self.metrics = []
        self.dependencies = []
        self.exceptions = []
    
    def track_event(self, name: str, properties: Dict[str, Any] = None, measurements: Dict[str, float] = None):
        """Mock track_event method"""
        event = {
            "name": name,
            "properties": properties or {},
            "measurements": measurements or {},
            "timestamp": datetime.utcnow()
        }
        self.custom_events.append(event)
    
    def track_metric(self, name: str, value: float, properties: Dict[str, Any] = None):
        """Mock track_metric method"""
        metric = {
            "name": name,
            "value": value,
            "properties": properties or {},
            "timestamp": datetime.utcnow()
        }
        self.metrics.append(metric)
    
    def track_dependency(self, name: str, data: str, type_name: str, target: str, 
                        duration: int, success: bool, properties: Dict[str, Any] = None):
        """Mock track_dependency method"""
        dependency = {
            "name": name,
            "data": data,
            "type": type_name,
            "target": target,
            "duration": duration,
            "success": success,
            "properties": properties or {},
            "timestamp": datetime.utcnow()
        }
        self.dependencies.append(dependency)
    
    def track_exception(self, exception: Exception, properties: Dict[str, Any] = None):
        """Mock track_exception method"""
        exception_data = {
            "exception": exception,
            "type": type(exception).__name__,
            "message": str(exception),
            "properties": properties or {},
            "timestamp": datetime.utcnow()
        }
        self.exceptions.append(exception_data)
    
    def flush(self):
        """Mock flush method"""
        pass
    
    def get_tracked_events(self) -> List[Dict[str, Any]]:
        """Get all tracked events for testing"""
        return self.custom_events
    
    def get_tracked_metrics(self) -> List[Dict[str, Any]]:
        """Get all tracked metrics for testing"""
        return self.metrics
    
    def get_tracked_dependencies(self) -> List[Dict[str, Any]]:
        """Get all tracked dependencies for testing"""
        return self.dependencies
    
    def get_tracked_exceptions(self) -> List[Dict[str, Any]]:
        """Get all tracked exceptions for testing"""
        return self.exceptions
    
    def clear_telemetry(self):
        """Clear all telemetry data for testing"""
        self.telemetry_data.clear()
        self.custom_events.clear()
        self.metrics.clear()
        self.dependencies.clear()
        self.exceptions.clear()


class MockServiceBusClient:
    """Mock Azure Service Bus client for testing"""
    
    def __init__(self):
        self.queues = {}
        self.sent_messages = []
        self.received_messages = []
    
    def get_queue_sender(self, queue_name: str) -> 'MockServiceBusSender':
        """Mock get_queue_sender method"""
        if queue_name not in self.queues:
            self.queues[queue_name] = []
        return MockServiceBusSender(queue_name, self)
    
    def get_queue_receiver(self, queue_name: str) -> 'MockServiceBusReceiver':
        """Mock get_queue_receiver method"""
        if queue_name not in self.queues:
            self.queues[queue_name] = []
        return MockServiceBusReceiver(queue_name, self)
    
    def create_queue(self, queue_name: str):
        """Mock create_queue method"""
        if queue_name not in self.queues:
            self.queues[queue_name] = []
    
    def delete_queue(self, queue_name: str):
        """Mock delete_queue method"""
        if queue_name in self.queues:
            del self.queues[queue_name]
    
    def list_queues(self) -> List[str]:
        """Mock list_queues method"""
        return list(self.queues.keys())


class MockServiceBusSender:
    """Mock Service Bus sender for testing"""
    
    def __init__(self, queue_name: str, client: MockServiceBusClient):
        self.queue_name = queue_name
        self.client = client
    
    def send_messages(self, messages):
        """Mock send_messages method"""
        if not isinstance(messages, list):
            messages = [messages]
        
        for message in messages:
            message_data = {
                "body": message.body if hasattr(message, 'body') else str(message),
                "properties": getattr(message, 'properties', {}),
                "timestamp": datetime.utcnow(),
                "queue_name": self.queue_name
            }
            self.client.queues[self.queue_name].append(message_data)
            self.client.sent_messages.append(message_data)
    
    def close(self):
        """Mock close method"""
        pass


class MockServiceBusReceiver:
    """Mock Service Bus receiver for testing"""
    
    def __init__(self, queue_name: str, client: MockServiceBusClient):
        self.queue_name = queue_name
        self.client = client
    
    def receive_messages(self, max_message_count: int = 1, max_wait_time: int = 5):
        """Mock receive_messages method"""
        messages = []
        queue_messages = self.client.queues.get(self.queue_name, [])
        
        for i in range(min(max_message_count, len(queue_messages))):
            message_data = queue_messages.pop(0)
            mock_message = Mock()
            mock_message.body = message_data["body"]
            mock_message.properties = message_data.get("properties", {})
            messages.append(mock_message)
            self.client.received_messages.append(message_data)
        
        return messages
    
    def complete_message(self, message):
        """Mock complete_message method"""
        pass
    
    def abandon_message(self, message):
        """Mock abandon_message method"""
        pass
    
    def dead_letter_message(self, message, reason: str = None):
        """Mock dead_letter_message method"""
        pass
    
    def close(self):
        """Mock close method"""
        pass


class MockBlobServiceClient:
    """Mock Azure Blob Storage client for testing"""
    
    def __init__(self):
        self.containers = {}
        self.blobs = {}
    
    def get_container_client(self, container_name: str) -> 'MockBlobContainerClient':
        """Mock get_container_client method"""
        if container_name not in self.containers:
            self.containers[container_name] = {}
        return MockBlobContainerClient(container_name, self)
    
    def create_container(self, container_name: str):
        """Mock create_container method"""
        if container_name not in self.containers:
            self.containers[container_name] = {}
    
    def delete_container(self, container_name: str):
        """Mock delete_container method"""
        if container_name in self.containers:
            del self.containers[container_name]
    
    def list_containers(self) -> List[str]:
        """Mock list_containers method"""
        return list(self.containers.keys())


class MockBlobContainerClient:
    """Mock Blob container client for testing"""
    
    def __init__(self, container_name: str, client: MockBlobServiceClient):
        self.container_name = container_name
        self.client = client
    
    def upload_blob(self, name: str, data: bytes, overwrite: bool = False):
        """Mock upload_blob method"""
        container_blobs = self.client.containers[self.container_name]
        if name in container_blobs and not overwrite:
            raise Exception(f"Blob {name} already exists")
        
        container_blobs[name] = {
            "data": data,
            "size": len(data),
            "last_modified": datetime.utcnow(),
            "content_type": "application/octet-stream"
        }
    
    def download_blob(self, name: str):
        """Mock download_blob method"""
        container_blobs = self.client.containers[self.container_name]
        if name not in container_blobs:
            raise Exception(f"Blob {name} not found")
        
        mock_blob = Mock()
        mock_blob.readall.return_value = container_blobs[name]["data"]
        return mock_blob
    
    def delete_blob(self, name: str):
        """Mock delete_blob method"""
        container_blobs = self.client.containers[self.container_name]
        if name in container_blobs:
            del container_blobs[name]
    
    def list_blobs(self):
        """Mock list_blobs method"""
        container_blobs = self.client.containers[self.container_name]
        blobs = []
        for name, data in container_blobs.items():
            mock_blob = Mock()
            mock_blob.name = name
            mock_blob.size = data["size"]
            mock_blob.last_modified = data["last_modified"]
            blobs.append(mock_blob)
        return blobs


class MockTableServiceClient:
    """Mock Azure Table Storage client for testing"""
    
    def __init__(self):
        self.tables = {}
    
    def get_table_client(self, table_name: str) -> 'MockTableClient':
        """Mock get_table_client method"""
        if table_name not in self.tables:
            self.tables[table_name] = {}
        return MockTableClient(table_name, self)
    
    def create_table(self, table_name: str):
        """Mock create_table method"""
        if table_name not in self.tables:
            self.tables[table_name] = {}
    
    def delete_table(self, table_name: str):
        """Mock delete_table method"""
        if table_name in self.tables:
            del self.tables[table_name]
    
    def list_tables(self) -> List[str]:
        """Mock list_tables method"""
        return list(self.tables.keys())


class MockTableClient:
    """Mock Table client for testing"""
    
    def __init__(self, table_name: str, client: MockTableServiceClient):
        self.table_name = table_name
        self.client = client
    
    def create_entity(self, entity: Dict[str, Any]):
        """Mock create_entity method"""
        table_entities = self.client.tables[self.table_name]
        partition_key = entity.get("PartitionKey")
        row_key = entity.get("RowKey")
        
        if not partition_key or not row_key:
            raise ValueError("PartitionKey and RowKey are required")
        
        entity_key = f"{partition_key}_{row_key}"
        if entity_key in table_entities:
            raise Exception(f"Entity {entity_key} already exists")
        
        entity["Timestamp"] = datetime.utcnow()
        table_entities[entity_key] = entity.copy()
    
    def get_entity(self, partition_key: str, row_key: str) -> Dict[str, Any]:
        """Mock get_entity method"""
        table_entities = self.client.tables[self.table_name]
        entity_key = f"{partition_key}_{row_key}"
        
        if entity_key not in table_entities:
            raise Exception(f"Entity {entity_key} not found")
        
        return table_entities[entity_key].copy()
    
    def update_entity(self, entity: Dict[str, Any], mode: str = "merge"):
        """Mock update_entity method"""
        table_entities = self.client.tables[self.table_name]
        partition_key = entity.get("PartitionKey")
        row_key = entity.get("RowKey")
        entity_key = f"{partition_key}_{row_key}"
        
        if entity_key not in table_entities:
            raise Exception(f"Entity {entity_key} not found")
        
        if mode == "replace":
            table_entities[entity_key] = entity.copy()
        else:  # merge
            table_entities[entity_key].update(entity)
        
        table_entities[entity_key]["Timestamp"] = datetime.utcnow()
    
    def delete_entity(self, partition_key: str, row_key: str):
        """Mock delete_entity method"""
        table_entities = self.client.tables[self.table_name]
        entity_key = f"{partition_key}_{row_key}"
        
        if entity_key in table_entities:
            del table_entities[entity_key]
    
    def query_entities(self, query_filter: str = None) -> List[Dict[str, Any]]:
        """Mock query_entities method"""
        table_entities = self.client.tables[self.table_name]
        entities = list(table_entities.values())
        
        # Simple query filtering (for testing purposes)
        if query_filter:
            # This is a very basic implementation for testing
            # In reality, you'd need proper OData query parsing
            filtered_entities = []
            for entity in entities:
                # Simple contains check for testing
                if any(str(query_filter).lower() in str(value).lower() for value in entity.values()):
                    filtered_entities.append(entity)
            return filtered_entities
        
        return entities