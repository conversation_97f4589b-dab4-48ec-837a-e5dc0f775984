"""
Mock Service Bus for testing
"""
import logging
from typing import Dict, List, Optional, Any
from unittest.mock import Mock
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class MockServiceBusMessage:
    """Mock Service Bus message"""
    
    def __init__(self, body: str, properties: Optional[Dict] = None):
        self.body = body
        self.properties = properties or {}
        self.message_id = f"msg_{datetime.now().timestamp()}"
        self.enqueued_time = datetime.now()
        self.delivery_count = 1
    
    def complete(self) -> None:
        """Mark message as completed"""
        logger.info(f"Message {self.message_id} completed")
    
    def abandon(self) -> None:
        """Abandon message"""
        logger.info(f"Message {self.message_id} abandoned")
    
    def dead_letter(self, reason: str = None) -> None:
        """Dead letter message"""
        logger.info(f"Message {self.message_id} dead lettered: {reason}")

class MockServiceBusQueue:
    """Mock Service Bus queue"""
    
    def __init__(self, queue_name: str):
        self.queue_name = queue_name
        self.messages: List[MockServiceBusMessage] = []
        self.dead_letter_messages: List[MockServiceBusMessage] = []
        self.message_log: List[str] = []
    
    def send_message(self, message_body: str, properties: Optional[Dict] = None) -> None:
        """Send message to queue"""
        message = MockServiceBusMessage(message_body, properties)
        self.messages.append(message)
        self.message_log.append(f"SEND:{message.message_id}")
        logger.info(f"Message sent to {self.queue_name}: {message.message_id}")
    
    def receive_message(self, timeout: int = 30) -> Optional[MockServiceBusMessage]:
        """Receive message from queue"""
        if self.messages:
            message = self.messages.pop(0)
            self.message_log.append(f"RECEIVE:{message.message_id}")
            logger.info(f"Message received from {self.queue_name}: {message.message_id}")
            return message
        return None
    
    def peek_message(self) -> Optional[MockServiceBusMessage]:
        """Peek at next message without removing it"""
        if self.messages:
            message = self.messages[0]
            self.message_log.append(f"PEEK:{message.message_id}")
            return message
        return None
    
    def get_message_count(self) -> int:
        """Get number of messages in queue"""
        return len(self.messages)
    
    def get_dead_letter_count(self) -> int:
        """Get number of dead letter messages"""
        return len(self.dead_letter_messages)
    
    def clear_messages(self) -> None:
        """Clear all messages"""
        self.messages.clear()
        self.dead_letter_messages.clear()
        self.message_log.clear()

class MockServiceBusClient:
    """Mock Service Bus client"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.queues: Dict[str, MockServiceBusQueue] = {}
        self.is_connected = True
    
    def get_queue_sender(self, queue_name: str) -> Mock:
        """Get queue sender"""
        if queue_name not in self.queues:
            self.queues[queue_name] = MockServiceBusQueue(queue_name)
        
        sender = Mock()
        sender.send_message = self.queues[queue_name].send_message
        return sender
    
    def get_queue_receiver(self, queue_name: str) -> Mock:
        """Get queue receiver"""
        if queue_name not in self.queues:
            self.queues[queue_name] = MockServiceBusQueue(queue_name)
        
        receiver = Mock()
        receiver.receive_message = self.queues[queue_name].receive_message
        receiver.peek_message = self.queues[queue_name].peek_message
        return receiver
    
    def get_queue(self, queue_name: str) -> MockServiceBusQueue:
        """Get queue for testing"""
        if queue_name not in self.queues:
            self.queues[queue_name] = MockServiceBusQueue(queue_name)
        return self.queues[queue_name]
    
    def close(self) -> None:
        """Close client"""
        self.is_connected = False
        logger.info("Service Bus client closed")

# Global mock instances
mock_service_bus_client = MockServiceBusClient("mock_connection_string")

class MockTaskMessage:
    """Mock task message for queue processing"""
    
    def __init__(self, task_type: str, execution_log_id: str, parameters: Dict[str, Any]):
        self.task_type = task_type
        self.execution_log_id = execution_log_id
        self.parameters = parameters
        self.message_id = f"task_{datetime.now().timestamp()}"
        self.created_at = datetime.now()
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps({
            "task_type": self.task_type,
            "execution_log_id": self.execution_log_id,
            "parameters": self.parameters,
            "message_id": self.message_id,
            "created_at": self.created_at.isoformat()
        })
    
    @classmethod
    def from_json(cls, json_str: str) -> 'MockTaskMessage':
        """Create from JSON string"""
        data = json.loads(json_str)
        message = cls(
            task_type=data["task_type"],
            execution_log_id=data["execution_log_id"],
            parameters=data["parameters"]
        )
        message.message_id = data["message_id"]
        message.created_at = datetime.fromisoformat(data["created_at"])
        return message