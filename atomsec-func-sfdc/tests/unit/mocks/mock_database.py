"""
Mock database services for testing
"""
import logging
from typing import Dict, List, Optional, Any
from unittest.mock import Mock
from datetime import datetime

logger = logging.getLogger(__name__)

class MockDatabaseConnection:
    """Mock database connection"""
    
    def __init__(self):
        self.is_connected = True
        self.query_log: List[str] = []
        self._data: Dict[str, List[Dict]] = {
            "users": [
                {"id": 1, "username": "testuser", "email": "<EMAIL>"},
                {"id": 2, "username": "admin", "email": "<EMAIL>"}
            ],
            "tasks": [
                {"id": 1, "name": "test_task", "status": "completed", "execution_log_id": "test_log_1"},
                {"id": 2, "name": "health_check", "status": "running", "execution_log_id": "test_log_2"}
            ],
            "execution_logs": [
                {"id": "test_log_1", "status": "completed", "created_at": datetime.now()},
                {"id": "test_log_2", "status": "running", "created_at": datetime.now()}
            ]
        }
    
    def execute(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """Execute a mock query"""
        self.query_log.append(query)
        logger.info(f"Executing query: {query}")
        
        # Simple query parsing for testing
        if "SELECT" in query.upper():
            if "users" in query.lower():
                return self._data["users"]
            elif "tasks" in query.lower():
                return self._data["tasks"]
            elif "execution_logs" in query.lower():
                return self._data["execution_logs"]
        
        return []
    
    def commit(self) -> None:
        """Mock commit"""
        logger.info("Transaction committed")
    
    def rollback(self) -> None:
        """Mock rollback"""
        logger.info("Transaction rolled back")
    
    def close(self) -> None:
        """Mock close connection"""
        self.is_connected = False
        logger.info("Connection closed")
    
    def get_query_log(self) -> List[str]:
        """Get query log for testing"""
        return self.query_log.copy()
    
    def clear_query_log(self) -> None:
        """Clear query log"""
        self.query_log.clear()

class MockConnectionPool:
    """Mock connection pool"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.active_connections = 0
        self.connection_log: List[str] = []
    
    def get_connection(self) -> MockDatabaseConnection:
        """Get a connection from the pool"""
        if self.active_connections >= self.max_connections:
            raise Exception("Connection pool exhausted")
        
        self.active_connections += 1
        self.connection_log.append(f"GET_CONNECTION:{self.active_connections}")
        logger.info(f"Connection acquired. Active: {self.active_connections}")
        return MockDatabaseConnection()
    
    def return_connection(self, connection: MockDatabaseConnection) -> None:
        """Return a connection to the pool"""
        if self.active_connections > 0:
            self.active_connections -= 1
            self.connection_log.append(f"RETURN_CONNECTION:{self.active_connections}")
            logger.info(f"Connection returned. Active: {self.active_connections}")
    
    def get_stats(self) -> Dict[str, int]:
        """Get pool statistics"""
        return {
            "max_connections": self.max_connections,
            "active_connections": self.active_connections,
            "available_connections": self.max_connections - self.active_connections
        }
    
    def get_connection_log(self) -> List[str]:
        """Get connection log for testing"""
        return self.connection_log.copy()

# Global mock instances
mock_connection_pool = MockConnectionPool()
mock_database_connection = MockDatabaseConnection()