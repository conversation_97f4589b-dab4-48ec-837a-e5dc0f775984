"""
Unit tests for Enhanced Configuration Manager
"""
import pytest
import os
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from src.shared.enhanced_configuration_manager import EnhancedConfigurationManager
from tests.unit.mocks.mock_key_vault import Mock<PERSON>eyVaultService, MockAzureKeyVaultClient

class TestEnhancedConfigurationManager:
    """Test cases for Enhanced Configuration Manager"""
    
    @pytest.fixture
    def mock_key_vault(self):
        """Mock Key Vault service"""
        return MockKeyVaultService()
    
    @pytest.fixture
    def config_manager(self, mock_key_vault):
        """Configuration manager with mocked dependencies"""
        with patch('shared.enhanced_configuration_manager.DefaultAzureCredential'):
            with patch('shared.enhanced_configuration_manager.SecretClient', return_value=MockAzureKeyVaultClient("https://test.vault.azure.net")):
                manager = EnhancedConfigurationManager()
                manager.key_vault_service = mock_key_vault
                return manager
    
    def test_initialization(self, config_manager):
        """Test configuration manager initialization"""
        assert config_manager is not None
        assert hasattr(config_manager, 'config')
        assert hasattr(config_manager, 'environment')
    
    def test_load_environment_config(self, config_manager):
        """Test loading environment-specific configuration"""
        # Test development environment
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Development'}):
            config_manager.load_configuration()
            assert config_manager.environment == 'Development'
        
        # Test production environment
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Production'}):
            config_manager.load_configuration()
            assert config_manager.environment == 'Production'
    
    def test_get_secret_from_key_vault(self, config_manager, mock_key_vault):
        """Test retrieving secrets from Key Vault"""
        # Test successful secret retrieval
        secret_value = config_manager.get_secret("salesforce-client-id")
        assert secret_value == "test_client_id"
        
        # Test non-existent secret
        with pytest.raises(KeyError):
            config_manager.get_secret("non-existent-secret")
    
    def test_get_configuration_value(self, config_manager):
        """Test getting configuration values with fallbacks"""
        # Test environment variable
        with patch.dict(os.environ, {'TEST_CONFIG': 'env_value'}):
            value = config_manager.get_config_value('TEST_CONFIG', 'default_value')
            assert value == 'env_value'
        
        # Test default value
        value = config_manager.get_config_value('NON_EXISTENT_CONFIG', 'default_value')
        assert value == 'default_value'
    
    def test_validate_configuration(self, config_manager):
        """Test configuration validation"""
        # Mock required configuration
        with patch.dict(os.environ, {
            'SALESFORCE_CLIENT_ID': 'test_id',
            'DATABASE_CONNECTION_STRING': 'test_connection'
        }):
            # Should not raise exception
            config_manager.validate_configuration()
    
    def test_configuration_validation_failure(self, config_manager):
        """Test configuration validation with missing required values"""
        # Clear environment variables
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Missing required configuration"):
                config_manager.validate_configuration()
    
    def test_reload_configuration(self, config_manager):
        """Test configuration reload functionality"""
        initial_config = config_manager.config.copy()
        
        # Modify environment and reload
        with patch.dict(os.environ, {'NEW_CONFIG': 'new_value'}):
            config_manager.reload_configuration()
            
            # Configuration should be updated
            assert config_manager.get_config_value('NEW_CONFIG') == 'new_value'
    
    def test_get_database_config(self, config_manager):
        """Test database configuration retrieval"""
        with patch.dict(os.environ, {
            'DATABASE_CONNECTION_STRING': 'test_connection',
            'DATABASE_POOL_SIZE': '20',
            'DATABASE_TIMEOUT': '30'
        }):
            db_config = config_manager.get_database_config()
            
            assert db_config['connection_string'] == 'test_connection'
            assert db_config['pool_size'] == 20
            assert db_config['timeout'] == 30
    
    def test_get_salesforce_config(self, config_manager, mock_key_vault):
        """Test Salesforce configuration retrieval"""
        sf_config = config_manager.get_salesforce_config()
        
        assert sf_config['client_id'] == 'test_client_id'
        assert sf_config['client_secret'] == 'test_client_secret'
        assert sf_config['private_key'] == 'test_private_key'
    
    def test_get_monitoring_config(self, config_manager):
        """Test monitoring configuration retrieval"""
        with patch.dict(os.environ, {
            'APPINSIGHTS_INSTRUMENTATIONKEY': 'test_key',
            'LOG_LEVEL': 'INFO',
            'ENABLE_TELEMETRY': 'true'
        }):
            monitoring_config = config_manager.get_monitoring_config()
            
            assert monitoring_config['instrumentation_key'] == 'test_key'
            assert monitoring_config['log_level'] == 'INFO'
            assert monitoring_config['enable_telemetry'] is True
    
    def test_is_development_environment(self, config_manager):
        """Test development environment detection"""
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Development'}):
            config_manager.load_configuration()
            assert config_manager.is_development() is True
        
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Production'}):
            config_manager.load_configuration()
            assert config_manager.is_development() is False
    
    def test_is_production_environment(self, config_manager):
        """Test production environment detection"""
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Production'}):
            config_manager.load_configuration()
            assert config_manager.is_production() is True
        
        with patch.dict(os.environ, {'AZURE_FUNCTIONS_ENVIRONMENT': 'Development'}):
            config_manager.load_configuration()
            assert config_manager.is_production() is False
    
    def test_configuration_caching(self, config_manager, mock_key_vault):
        """Test that configuration values are cached"""
        # First call should access Key Vault
        secret1 = config_manager.get_secret("salesforce-client-id")
        initial_log_length = len(mock_key_vault.get_access_log())
        
        # Second call should use cache
        secret2 = config_manager.get_secret("salesforce-client-id")
        final_log_length = len(mock_key_vault.get_access_log())
        
        assert secret1 == secret2
        # Should not have made additional Key Vault calls due to caching
        assert final_log_length == initial_log_length
    
    def test_error_handling_key_vault_unavailable(self, config_manager):
        """Test error handling when Key Vault is unavailable"""
        with patch.object(config_manager, 'key_vault_client') as mock_client:
            mock_client.get_secret.side_effect = Exception("Key Vault unavailable")
            
            with pytest.raises(Exception, match="Key Vault unavailable"):
                config_manager.get_secret("test-secret")
    
    def test_configuration_with_feature_flags(self, config_manager):
        """Test configuration with feature flags"""
        with patch.dict(os.environ, {
            'FEATURE_FLAG_ENHANCED_LOGGING': 'true',
            'FEATURE_FLAG_PERFORMANCE_MONITORING': 'false'
        }):
            config_manager.load_configuration()
            
            assert config_manager.get_feature_flag('ENHANCED_LOGGING') is True
            assert config_manager.get_feature_flag('PERFORMANCE_MONITORING') is False
            assert config_manager.get_feature_flag('NON_EXISTENT_FLAG') is False
    
    @pytest.mark.parametrize("env_value,expected", [
        ("true", True),
        ("True", True),
        ("TRUE", True),
        ("1", True),
        ("false", False),
        ("False", False),
        ("FALSE", False),
        ("0", False),
        ("", False),
        ("invalid", False)
    ])
    def test_boolean_configuration_parsing(self, config_manager, env_value, expected):
        """Test boolean configuration value parsing"""
        with patch.dict(os.environ, {'TEST_BOOL_CONFIG': env_value}):
            result = config_manager.get_boolean_config('TEST_BOOL_CONFIG')
            assert result == expected