"""
Unit tests for Security Middleware
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import json
from datetime import datetime, timedelta
import jwt

# Import the module under test
from src.shared.security_middleware import SecurityMiddleware, SecurityConfig, RateLimitExceeded, AuthenticationError


class TestSecurityMiddleware:
    """Test suite for SecurityMiddleware"""

    def setup_method(self):
        """Set up test fixtures"""
        self.config = SecurityConfig(
            jwt_secret_key="test-secret-key",
            token_expiration_minutes=60,
            rate_limit_requests_per_minute=100,
            allowed_origins=["https://localhost:3000", "https://test.example.com"],
            require_https=False,  # Disabled for testing
            enable_audit_logging=True
        )
        self.middleware = SecurityMiddleware(self.config)

    def test_security_config_creation(self):
        """Test SecurityConfig creation and properties"""
        assert self.config.jwt_secret_key == "test-secret-key"
        assert self.config.token_expiration_minutes == 60
        assert self.config.rate_limit_requests_per_minute == 100
        assert len(self.config.allowed_origins) == 2
        assert self.config.require_https is False
        assert self.config.enable_audit_logging is True

    def test_generate_jwt_token_valid_payload(self):
        """Test JWT token generation with valid payload"""
        payload = {
            "user_id": "user123",
            "email": "<EMAIL>",
            "roles": ["user", "admin"]
        }
        
        token = self.middleware.generate_jwt_token(payload)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token.split('.')) == 3  # JWT has 3 parts

    def test_validate_jwt_token_valid(self):
        """Test JWT token validation with valid token"""
        payload = {
            "user_id": "user123",
            "email": "<EMAIL>",
            "exp": datetime.utcnow() + timedelta(minutes=30)
        }
        
        token = self.middleware.generate_jwt_token(payload)
        result = self.middleware.validate_jwt_token(token)
        
        assert result.is_valid is True
        assert result.payload["user_id"] == "user123"
        assert result.payload["email"] == "<EMAIL>"

    def test_validate_jwt_token_expired(self):
        """Test JWT token validation with expired token"""
        # Create an expired token
        expired_payload = {
            "user_id": "user123",
            "exp": datetime.utcnow() - timedelta(minutes=30)  # Expired 30 minutes ago
        }
        
        expired_token = jwt.encode(expired_payload, self.config.jwt_secret_key, algorithm="HS256")
        result = self.middleware.validate_jwt_token(expired_token)
        
        assert result.is_valid is False
        assert "expired" in result.error_message.lower()

    def test_validate_jwt_token_invalid_signature(self):
        """Test JWT token validation with invalid signature"""
        # Create token with different secret
        payload = {"user_id": "user123"}
        invalid_token = jwt.encode(payload, "wrong-secret", algorithm="HS256")
        
        result = self.middleware.validate_jwt_token(invalid_token)
        
        assert result.is_valid is False
        assert "signature" in result.error_message.lower()

    def test_validate_jwt_token_malformed(self):
        """Test JWT token validation with malformed token"""
        malformed_token = "not.a.valid.jwt.token"
        
        result = self.middleware.validate_jwt_token(malformed_token)
        
        assert result.is_valid is False
        assert result.error_message is not None

    def test_check_rate_limit_within_limit(self):
        """Test rate limiting when within allowed limits"""
        user_id = "user123"
        endpoint = "/api/test"
        
        # Make requests within the limit
        for i in range(50):  # Well within 100 requests per minute
            result = self.middleware.check_rate_limit(user_id, endpoint)
            assert result is True

    def test_check_rate_limit_exceeded(self):
        """Test rate limiting when limit is exceeded"""
        user_id = "user123"
        endpoint = "/api/test"
        
        # Mock the rate limiter to simulate exceeded limit
        with patch.object(self.middleware, '_get_current_request_count', return_value=150):
            with pytest.raises(RateLimitExceeded):
                self.middleware.check_rate_limit(user_id, endpoint)

    def test_validate_cors_origin_allowed(self):
        """Test CORS validation with allowed origin"""
        allowed_origin = "https://localhost:3000"
        
        result = self.middleware.validate_cors_origin(allowed_origin)
        
        assert result is True

    def test_validate_cors_origin_not_allowed(self):
        """Test CORS validation with disallowed origin"""
        disallowed_origin = "https://malicious.example.com"
        
        result = self.middleware.validate_cors_origin(disallowed_origin)
        
        assert result is False

    def test_validate_cors_origin_wildcard(self):
        """Test CORS validation with wildcard origin"""
        # Update config to allow wildcard
        self.config.allowed_origins = ["*"]
        middleware = SecurityMiddleware(self.config)
        
        result = middleware.validate_cors_origin("https://any-domain.com")
        
        assert result is True

    def test_sanitize_headers_removes_sensitive(self):
        """Test header sanitization removes sensitive information"""
        headers = {
            "Authorization": "Bearer secret-token",
            "X-API-Key": "secret-api-key",
            "Content-Type": "application/json",
            "User-Agent": "Test Client"
        }
        
        sanitized = self.middleware.sanitize_headers(headers)
        
        assert "Authorization" not in sanitized
        assert "X-API-Key" not in sanitized
        assert sanitized["Content-Type"] == "application/json"
        assert sanitized["User-Agent"] == "Test Client"

    def test_audit_security_event_logging(self):
        """Test security event auditing and logging"""
        event = {
            "event_type": "authentication_failure",
            "user_id": "user123",
            "ip_address": "***********",
            "timestamp": datetime.utcnow(),
            "details": {"reason": "invalid_token"}
        }
        
        with patch('shared.security_middleware.logging') as mock_logging:
            self.middleware.audit_security_event(event)
            
            # Verify logging was called
            mock_logging.warning.assert_called_once()

    def test_extract_bearer_token_valid(self):
        """Test bearer token extraction from valid Authorization header"""
        auth_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        
        token = self.middleware.extract_bearer_token(auth_header)
        
        assert token == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    def test_extract_bearer_token_invalid_format(self):
        """Test bearer token extraction from invalid Authorization header"""
        invalid_headers = [
            "Basic dXNlcjpwYXNz",  # Basic auth
            "Bearer",  # Missing token
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  # Missing Bearer prefix
            ""  # Empty header
        ]
        
        for header in invalid_headers:
            token = self.middleware.extract_bearer_token(header)
            assert token is None

    def test_validate_request_https_required(self):
        """Test HTTPS validation when required"""
        # Enable HTTPS requirement
        self.config.require_https = True
        middleware = SecurityMiddleware(self.config)
        
        # Test HTTP request (should fail)
        request_info = {
            "url": "http://example.com/api/test",
            "headers": {"Host": "example.com"}
        }
        
        result = middleware.validate_request_security(request_info)
        
        assert result.is_valid is False
        assert "https" in result.error_message.lower()

    def test_validate_request_https_not_required(self):
        """Test HTTPS validation when not required (development)"""
        # HTTPS not required (default in our test setup)
        request_info = {
            "url": "http://localhost:7071/api/test",
            "headers": {"Host": "localhost:7071"}
        }
        
        result = self.middleware.validate_request_security(request_info)
        
        # Should pass other validations even without HTTPS
        assert result.is_valid is True or "https" not in result.error_message.lower()

    def test_validate_content_type_json(self):
        """Test content type validation for JSON requests"""
        headers = {"Content-Type": "application/json"}
        
        result = self.middleware.validate_content_type(headers, ["application/json"])
        
        assert result is True

    def test_validate_content_type_invalid(self):
        """Test content type validation for invalid content type"""
        headers = {"Content-Type": "text/html"}
        
        result = self.middleware.validate_content_type(headers, ["application/json"])
        
        assert result is False

    def test_validate_content_type_missing(self):
        """Test content type validation when header is missing"""
        headers = {}
        
        result = self.middleware.validate_content_type(headers, ["application/json"])
        
        assert result is False

    def test_generate_correlation_id(self):
        """Test correlation ID generation"""
        correlation_id = self.middleware.generate_correlation_id()
        
        assert correlation_id is not None
        assert isinstance(correlation_id, str)
        assert len(correlation_id) > 0
        
        # Generate another one to ensure uniqueness
        another_id = self.middleware.generate_correlation_id()
        assert correlation_id != another_id

    def test_validate_user_permissions_admin(self):
        """Test user permission validation for admin user"""
        user_context = {
            "user_id": "admin123",
            "roles": ["admin", "user"],
            "permissions": ["read", "write", "delete"]
        }
        
        result = self.middleware.validate_user_permissions(
            user_context, "test_resource", "delete"
        )
        
        assert result is True

    def test_validate_user_permissions_insufficient(self):
        """Test user permission validation for insufficient permissions"""
        user_context = {
            "user_id": "user123",
            "roles": ["user"],
            "permissions": ["read"]
        }
        
        result = self.middleware.validate_user_permissions(
            user_context, "test_resource", "delete"
        )
        
        assert result is False

    def test_validate_user_permissions_no_context(self):
        """Test user permission validation with no user context"""
        result = self.middleware.validate_user_permissions(
            None, "test_resource", "read"
        )
        
        assert result is False

    @patch('shared.security_middleware.datetime')
    def test_rate_limit_window_reset(self, mock_datetime):
        """Test rate limit window reset after time period"""
        user_id = "user123"
        endpoint = "/api/test"
        
        # Set initial time
        initial_time = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.utcnow.return_value = initial_time
        
        # Make requests up to limit
        for i in range(100):
            result = self.middleware.check_rate_limit(user_id, endpoint)
            assert result is True
        
        # Move time forward by more than a minute
        later_time = initial_time + timedelta(minutes=2)
        mock_datetime.utcnow.return_value = later_time
        
        # Should be able to make requests again
        result = self.middleware.check_rate_limit(user_id, endpoint)
        assert result is True

    def test_security_headers_injection(self):
        """Test security headers are properly injected"""
        response_headers = {}
        
        enhanced_headers = self.middleware.inject_security_headers(response_headers)
        
        expected_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ]
        
        for header in expected_headers:
            assert header in enhanced_headers

    def test_authentication_error_exception(self):
        """Test AuthenticationError exception"""
        with pytest.raises(AuthenticationError) as exc_info:
            raise AuthenticationError("Invalid token")
        
        assert str(exc_info.value) == "Invalid token"

    def test_rate_limit_exceeded_exception(self):
        """Test RateLimitExceeded exception"""
        with pytest.raises(RateLimitExceeded) as exc_info:
            raise RateLimitExceeded("Rate limit exceeded for user123")
        
        assert str(exc_info.value) == "Rate limit exceeded for user123"

    def test_middleware_integration_valid_request(self):
        """Test complete middleware integration with valid request"""
        request_data = {
            "headers": {
                "Authorization": "Bearer " + self.middleware.generate_jwt_token({"user_id": "user123"}),
                "Content-Type": "application/json",
                "Origin": "https://localhost:3000"
            },
            "url": "http://localhost:7071/api/test",
            "method": "POST",
            "body": {"test": "data"}
        }
        
        # This would be called by the actual middleware in a real scenario
        auth_result = self.middleware.validate_jwt_token(
            self.middleware.extract_bearer_token(request_data["headers"]["Authorization"])
        )
        cors_result = self.middleware.validate_cors_origin(request_data["headers"]["Origin"])
        rate_limit_result = self.middleware.check_rate_limit("user123", "/api/test")
        
        assert auth_result.is_valid is True
        assert cors_result is True
        assert rate_limit_result is True

    def test_middleware_integration_invalid_request(self):
        """Test complete middleware integration with invalid request"""
        request_data = {
            "headers": {
                "Authorization": "Bearer invalid.jwt.token",
                "Content-Type": "application/json",
                "Origin": "https://malicious.com"
            },
            "url": "http://localhost:7071/api/test",
            "method": "POST"
        }
        
        auth_result = self.middleware.validate_jwt_token(
            self.middleware.extract_bearer_token(request_data["headers"]["Authorization"])
        )
        cors_result = self.middleware.validate_cors_origin(request_data["headers"]["Origin"])
        
        assert auth_result.is_valid is False
        assert cors_result is False