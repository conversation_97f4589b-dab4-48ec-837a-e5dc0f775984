"""
Comprehensive load test for SFDC Function App staging validation
"""
from locust import HttpUser, task, between, events
import json
import random
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ComprehensiveLoadTestUser(HttpUser):
    """
    Comprehensive load testing user that simulates realistic usage patterns
    """
    wait_time = between(1, 5)
    
    def on_start(self):
        """Initialize user session"""
        logger.info(f"Starting user session: {self.client.base_url}")
        self.user_id = random.randint(1000, 9999)
        self.session_start = time.time()
        
        # Simulate user authentication flow
        self.authenticate()
    
    def authenticate(self):
        """Simulate authentication process"""
        # In a real scenario, this would perform actual authentication
        self.auth_headers = {
            'Authorization': f'Bearer test-token-{self.user_id}',
            'Content-Type': 'application/json',
            'User-Agent': 'LoadTest/1.0'
        }
    
    @task(15)
    def health_check_with_monitoring(self):
        """Health check with response time monitoring"""
        start_time = time.time()
        
        with self.client.get("/api/health", catch_response=True) as response:
            response_time = (time.time() - start_time) * 1000  # Convert to ms
            
            if response.status_code == 200:
                if response_time > 2000:  # 2 second threshold
                    logger.warning(f"Slow health check: {response_time:.2f}ms")
                response.success()
            else:
                logger.error(f"Health check failed: {response.status_code}")
                response.failure(f"Health check failed with status {response.status_code}")
    
    @task(10)
    def organization_operations(self):
        """Test organization-related operations"""
        # Get organizations list
        with self.client.get("/api/organizations", headers=self.auth_headers, catch_response=True) as response:
            if response.status_code in [200, 401]:
                response.success()
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, list) and len(data) > 0:
                            # Test getting specific organization
                            org_id = random.choice(data).get('id', 1)
                            self.get_organization_details(org_id)
                    except (json.JSONDecodeError, AttributeError):
                        pass
            else:
                response.failure(f"Organizations request failed with status {response.status_code}")
    
    def get_organization_details(self, org_id):
        """Get details for a specific organization"""
        with self.client.get(f"/api/organizations/{org_id}", headers=self.auth_headers, catch_response=True) as response:
            if response.status_code in [200, 401, 404]:
                response.success()
            else:
                response.failure(f"Organization details failed with status {response.status_code}")
    
    @task(8)
    def integration_workflows(self):
        """Test integration-related workflows"""
        # List integrations
        with self.client.get("/api/integrations", headers=self.auth_headers, catch_response=True) as response:
            if response.status_code in [200, 401]:
                response.success()
                
                if response.status_code == 200:
                    # Simulate integration operations
                    integration_id = random.randint(1, 10)
                    self.test_integration_operations(integration_id)
            else:
                response.failure(f"Integrations list failed with status {response.status_code}")
    
    def test_integration_operations(self, integration_id):
        """Test various integration operations"""
        operations = [
            ('GET', f'/api/integrations/{integration_id}'),
            ('GET', f'/api/integrations/{integration_id}/health-check'),
            ('GET', f'/api/integrations/{integration_id}/overview'),
        ]
        
        for method, endpoint in operations:
            with self.client.request(method, endpoint, headers=self.auth_headers, catch_response=True) as response:
                if response.status_code in [200, 401, 404]:
                    response.success()
                else:
                    response.failure(f"{method} {endpoint} failed with status {response.status_code}")
    
    @task(5)
    def security_operations(self):
        """Test security-related operations"""
        security_endpoints = [
            '/api/security/health-score',
            '/api/security/health-risks',
            '/api/policies',
        ]
        
        for endpoint in security_endpoints:
            with self.client.get(endpoint, headers=self.auth_headers, catch_response=True) as response:
                if response.status_code in [200, 401, 404]:
                    response.success()
                else:
                    response.failure(f"Security endpoint {endpoint} failed with status {response.status_code}")
    
    @task(3)
    def scan_operations(self):
        """Test scan-related operations"""
        integration_id = random.randint(1, 5)
        scan_data = {
            'scan_type': random.choice(['security', 'compliance', 'performance']),
            'options': {
                'deep_scan': random.choice([True, False]),
                'include_metadata': True
            }
        }
        
        with self.client.post(
            f"/api/integrations/{integration_id}/scan",
            headers=self.auth_headers,
            json=scan_data,
            catch_response=True
        ) as response:
            if response.status_code in [200, 202, 401, 404]:
                response.success()
            else:
                response.failure(f"Scan operation failed with status {response.status_code}")
    
    @task(2)
    def user_profile_operations(self):
        """Test user profile operations"""
        profile_endpoints = [
            '/api/user/profile',
            '/api/users/current',
        ]
        
        for endpoint in profile_endpoints:
            with self.client.get(endpoint, headers=self.auth_headers, catch_response=True) as response:
                if response.status_code in [200, 401]:
                    response.success()
                else:
                    response.failure(f"Profile endpoint {endpoint} failed with status {response.status_code}")
    
    @task(1)
    def task_operations(self):
        """Test task-related operations"""
        # Get tasks
        with self.client.get("/api/tasks", headers=self.auth_headers, catch_response=True) as response:
            if response.status_code in [200, 401]:
                response.success()
            else:
                response.failure(f"Tasks endpoint failed with status {response.status_code}")
        
        # Test task creation (POST)
        task_data = {
            'type': 'security_scan',
            'integration_id': random.randint(1, 5),
            'priority': random.choice(['low', 'medium', 'high'])
        }
        
        with self.client.post("/api/tasks", headers=self.auth_headers, json=task_data, catch_response=True) as response:
            if response.status_code in [200, 201, 202, 401]:
                response.success()
            else:
                response.failure(f"Task creation failed with status {response.status_code}")
    
    def on_stop(self):
        """Cleanup when user session ends"""
        session_duration = time.time() - self.session_start
        logger.info(f"User session ended. Duration: {session_duration:.2f}s")


class HighVolumeUser(HttpUser):
    """
    High-volume user for stress testing
    """
    wait_time = between(0.1, 1.0)
    
    @task(30)
    def rapid_health_checks(self):
        """Rapid-fire health checks"""
        with self.client.get("/api/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Rapid health check failed")
    
    @task(10)
    def concurrent_api_calls(self):
        """Concurrent API calls to test system limits"""
        endpoints = [
            "/api/organizations",
            "/api/integrations",
            "/api/policies",
            "/api/security/health-score"
        ]
        
        endpoint = random.choice(endpoints)
        with self.client.get(endpoint, catch_response=True) as response:
            if response.status_code in [200, 401, 404]:
                response.success()
            else:
                response.failure(f"Concurrent call to {endpoint} failed")


class ErrorScenarioUser(HttpUser):
    """
    User that tests error scenarios and edge cases
    """
    wait_time = between(2, 4)
    
    @task(5)
    def test_invalid_endpoints(self):
        """Test invalid endpoints to check error handling"""
        invalid_endpoints = [
            "/api/nonexistent",
            "/api/integrations/999999",
            "/api/organizations/invalid-id",
        ]
        
        endpoint = random.choice(invalid_endpoints)
        with self.client.get(endpoint, catch_response=True) as response:
            if response.status_code in [404, 400]:
                response.success()  # Expected error responses
            else:
                response.failure(f"Unexpected response for invalid endpoint {endpoint}: {response.status_code}")
    
    @task(3)
    def test_malformed_requests(self):
        """Test malformed requests"""
        malformed_data = [
            "invalid json",
            {"incomplete": "data"},
            {"very_long_field": "x" * 10000}
        ]
        
        data = random.choice(malformed_data)
        with self.client.post("/api/tasks", json=data, catch_response=True) as response:
            if response.status_code in [400, 401, 422]:
                response.success()  # Expected error responses
            else:
                response.failure(f"Unexpected response for malformed request: {response.status_code}")


# Event handlers for custom metrics
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Custom request handler for additional metrics"""
    if response_time > 5000:  # 5 second threshold
        logger.warning(f"Very slow request: {name} took {response_time:.2f}ms")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Test start handler"""
    logger.info("Comprehensive load test starting...")
    logger.info(f"Target host: {environment.host}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Test stop handler"""
    logger.info("Comprehensive load test completed")
    
    # Log summary statistics
    stats = environment.stats
    logger.info(f"Total requests: {stats.total.num_requests}")
    logger.info(f"Total failures: {stats.total.num_failures}")
    logger.info(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    logger.info(f"Max response time: {stats.total.max_response_time:.2f}ms")