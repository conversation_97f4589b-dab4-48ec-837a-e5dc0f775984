"""
Load Testing Suite for SFDC Service

This module contains load tests for critical endpoints and workflows.
"""

import pytest
import asyncio
import time
import statistics
import concurrent.futures
from unittest.mock import Mock, patch
import json
import uuid
from typing import List, Dict, Any
import azure.functions as func

from src.shared.performance_optimizer import get_performance_optimizer
from src.shared.monitoring import get_monitoring_service


@pytest.mark.performance
class TestEndpointLoadTesting:
    """Load tests for critical API endpoints"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.performance_optimizer = get_performance_optimizer()
        self.monitoring_service = get_monitoring_service()
        self.test_results = []
    
    def create_mock_request(self, endpoint: str, data: dict = None) -> Mock:
        """Create a mock HTTP request"""
        mock_request = Mock(spec=func.HttpRequest)
        mock_request.method = "POST"
        mock_request.url = f"https://test.com{endpoint}"
        mock_request.headers = {
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        }
        
        if data:
            mock_request.get_body.return_value = json.dumps(data).encode()
            mock_request.get_json.return_value = data
        else:
            mock_request.get_body.return_value = b'{}'
            mock_request.get_json.return_value = {}
        
        return mock_request
    
    def measure_endpoint_performance(self, endpoint_func, request_data: dict, 
                                   concurrent_users: int = 10, 
                                   requests_per_user: int = 10) -> Dict[str, Any]:
        """Measure endpoint performance under load"""
        
        def execute_request():
            """Execute a single request"""
            start_time = time.time()
            try:
                mock_request = self.create_mock_request("/api/test", request_data)
                response = endpoint_func(mock_request)
                end_time = time.time()
                
                return {
                    'success': True,
                    'response_time': (end_time - start_time) * 1000,  # milliseconds
                    'status_code': getattr(response, 'status_code', 200)
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': (end_time - start_time) * 1000,
                    'error': str(e)
                }
        
        # Execute load test
        results = []
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            # Submit all requests
            futures = []
            for user in range(concurrent_users):
                for request in range(requests_per_user):
                    future = executor.submit(execute_request)
                    futures.append(future)
            
            # Collect results
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                results.append(result)
        
        end_time = time.time()
        
        # Calculate statistics
        response_times = [r['response_time'] for r in results]
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        total_requests = len(results)
        success_rate = len(successful_requests) / total_requests if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': success_rate,
            'total_time': (end_time - start_time) * 1000,
            'requests_per_second': total_requests / (end_time - start_time),
            'response_times': {
                'min': min(response_times) if response_times else 0,
                'max': max(response_times) if response_times else 0,
                'avg': statistics.mean(response_times) if response_times else 0,
                'median': statistics.median(response_times) if response_times else 0,
                'p95': self._percentile(response_times, 95) if response_times else 0,
                'p99': self._percentile(response_times, 99) if response_times else 0
            },
            'errors': [r['error'] for r in failed_requests]
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    @patch('shared.security_middleware.get_security_middleware')
    def test_authentication_endpoint_load(self, mock_security_middleware):
        """Test authentication endpoint under load"""
        
        # Mock security middleware
        mock_middleware = Mock()
        mock_middleware.validate_request_security.return_value = {
            'params': {'username': '<EMAIL>'},
            'user': {'id': 'user123'},
            'validation_time': 0.01
        }
        mock_middleware.add_security_headers.return_value = Mock(spec=func.HttpResponse)
        mock_security_middleware.return_value = mock_middleware
        
        # Mock authentication endpoint
        def mock_auth_endpoint(req):
            # Simulate authentication logic
            time.sleep(0.05)  # Simulate processing time
            return func.HttpResponse(
                json.dumps({"success": True, "token": "mock_token"}),
                status_code=200,
                mimetype="application/json"
            )
        
        # Test data
        request_data = {
            "username": "<EMAIL>",
            "password": "test_password"
        }
        
        # Run load test
        results = self.measure_endpoint_performance(
            mock_auth_endpoint,
            request_data,
            concurrent_users=20,
            requests_per_user=5
        )
        
        # Assertions
        assert results['success_rate'] >= 0.95  # 95% success rate
        assert results['response_times']['avg'] < 200  # Average response time < 200ms
        assert results['response_times']['p95'] < 500  # 95th percentile < 500ms
        assert results['requests_per_second'] >= 50  # At least 50 RPS
        
        self.test_results.append({
            'test_name': 'authentication_endpoint_load',
            'results': results
        })
    
    @patch('shared.salesforce_client.SalesforceClient')
    def test_salesforce_integration_load(self, mock_sf_client):
        """Test Salesforce integration endpoints under load"""
        
        # Mock Salesforce client
        mock_client = Mock()
        mock_client.authenticate.return_value = True
        mock_client.query.return_value = {'records': [{'Id': '123', 'Name': 'Test'}]}
        mock_sf_client.return_value = mock_client
        
        # Mock Salesforce endpoint
        def mock_sf_endpoint(req):
            # Simulate Salesforce API call
            time.sleep(0.1)  # Simulate network latency
            return func.HttpResponse(
                json.dumps({"success": True, "data": {"records": []}}),
                status_code=200,
                mimetype="application/json"
            )
        
        # Test data
        request_data = {
            "organization_id": "test_org_123",
            "query": "SELECT Id, Name FROM Account LIMIT 10"
        }
        
        # Run load test
        results = self.measure_endpoint_performance(
            mock_sf_endpoint,
            request_data,
            concurrent_users=15,
            requests_per_user=8
        )
        
        # Assertions for external API integration
        assert results['success_rate'] >= 0.90  # 90% success rate (external dependency)
        assert results['response_times']['avg'] < 500  # Average response time < 500ms
        assert results['response_times']['p95'] < 1000  # 95th percentile < 1s
        assert results['requests_per_second'] >= 20  # At least 20 RPS
        
        self.test_results.append({
            'test_name': 'salesforce_integration_load',
            'results': results
        })
    
    def test_health_check_endpoint_load(self):
        """Test health check endpoint under high load"""
        
        # Mock health check endpoint
        def mock_health_endpoint(req):
            # Simulate health check logic
            time.sleep(0.01)  # Minimal processing time
            return func.HttpResponse(
                json.dumps({
                    "status": "healthy",
                    "timestamp": time.time(),
                    "checks": {
                        "database": "healthy",
                        "external_apis": "healthy"
                    }
                }),
                status_code=200,
                mimetype="application/json"
            )
        
        # Run high-load test
        results = self.measure_endpoint_performance(
            mock_health_endpoint,
            {},
            concurrent_users=50,
            requests_per_user=20
        )
        
        # Health checks should be very fast and reliable
        assert results['success_rate'] >= 0.99  # 99% success rate
        assert results['response_times']['avg'] < 50  # Average response time < 50ms
        assert results['response_times']['p95'] < 100  # 95th percentile < 100ms
        assert results['requests_per_second'] >= 200  # At least 200 RPS
        
        self.test_results.append({
            'test_name': 'health_check_endpoint_load',
            'results': results
        })
    
    def test_concurrent_task_processing_load(self):
        """Test concurrent task processing under load"""
        
        # Mock task processing endpoint
        def mock_task_endpoint(req):
            # Simulate task processing
            task_type = req.get_json().get('task_type', 'default')
            
            # Different processing times for different task types
            processing_times = {
                'sfdc_authenticate': 0.1,
                'health_check': 0.05,
                'metadata_extraction': 0.2,
                'pmd_apex_security': 0.3
            }
            
            time.sleep(processing_times.get(task_type, 0.1))
            
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "task_type": task_type,
                    "execution_log_id": str(uuid.uuid4())
                }),
                status_code=200,
                mimetype="application/json"
            )
        
        # Test different task types
        task_types = ['sfdc_authenticate', 'health_check', 'metadata_extraction', 'pmd_apex_security']
        
        for task_type in task_types:
            request_data = {
                "task_type": task_type,
                "execution_log_id": str(uuid.uuid4()),
                "organization_id": "test_org_123"
            }
            
            results = self.measure_endpoint_performance(
                mock_task_endpoint,
                request_data,
                concurrent_users=10,
                requests_per_user=5
            )
            
            # Task processing should be reliable
            assert results['success_rate'] >= 0.95
            assert results['response_times']['avg'] < 1000  # < 1 second average
            
            self.test_results.append({
                'test_name': f'task_processing_load_{task_type}',
                'results': results
            })
    
    def teardown_method(self):
        """Generate performance test report"""
        if self.test_results:
            self._generate_performance_report()
    
    def _generate_performance_report(self):
        """Generate comprehensive performance test report"""
        report = {
            'timestamp': time.time(),
            'test_summary': {
                'total_tests': len(self.test_results),
                'passed_tests': 0,
                'failed_tests': 0
            },
            'test_results': self.test_results,
            'performance_thresholds': {
                'success_rate_threshold': 0.95,
                'avg_response_time_threshold': 500,
                'p95_response_time_threshold': 1000,
                'min_requests_per_second': 20
            }
        }
        
        # Calculate summary
        for test_result in self.test_results:
            results = test_result['results']
            if (results['success_rate'] >= 0.95 and 
                results['response_times']['avg'] < 500 and
                results['response_times']['p95'] < 1000):
                report['test_summary']['passed_tests'] += 1
            else:
                report['test_summary']['failed_tests'] += 1
        
        # Save report
        with open('performance_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nPerformance Test Report Generated:")
        print(f"Total Tests: {report['test_summary']['total_tests']}")
        print(f"Passed: {report['test_summary']['passed_tests']}")
        print(f"Failed: {report['test_summary']['failed_tests']}")


@pytest.mark.performance
class TestMemoryAndResourceUsage:
    """Tests for memory and resource usage under load"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.performance_optimizer = get_performance_optimizer()
        self.initial_memory = self._get_memory_usage()
    
    def _get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': process.memory_percent()
        }
    
    def test_memory_usage_under_load(self):
        """Test memory usage doesn't grow excessively under load"""
        
        # Simulate memory-intensive operations
        def memory_intensive_operation():
            # Create some data structures
            data = []
            for i in range(1000):
                data.append({
                    'id': i,
                    'data': 'x' * 100,  # 100 bytes per item
                    'timestamp': time.time()
                })
            
            # Process data
            processed = [item for item in data if item['id'] % 2 == 0]
            return len(processed)
        
        # Run operations and monitor memory
        memory_samples = []
        
        for i in range(50):  # 50 iterations
            result = memory_intensive_operation()
            memory_usage = self._get_memory_usage()
            memory_samples.append(memory_usage)
            
            # Small delay to allow garbage collection
            time.sleep(0.01)
        
        # Analyze memory usage
        final_memory = memory_samples[-1]
        max_memory = max(sample['rss_mb'] for sample in memory_samples)
        avg_memory = statistics.mean(sample['rss_mb'] for sample in memory_samples)
        
        # Memory growth should be reasonable
        memory_growth = final_memory['rss_mb'] - self.initial_memory['rss_mb']
        
        assert memory_growth < 100, f"Memory growth too high: {memory_growth}MB"
        assert max_memory < 500, f"Peak memory usage too high: {max_memory}MB"
        assert final_memory['percent'] < 80, f"Memory percentage too high: {final_memory['percent']}%"
        
        print(f"Memory Usage - Initial: {self.initial_memory['rss_mb']:.1f}MB, "
              f"Final: {final_memory['rss_mb']:.1f}MB, "
              f"Growth: {memory_growth:.1f}MB")
    
    def test_connection_pool_performance(self):
        """Test connection pool performance under concurrent load"""
        
        connection_manager = self.performance_optimizer.connection_manager
        
        def use_connection_pool():
            """Simulate using connection pool"""
            # Get database connection
            db_pool = connection_manager.get_database_connection_pool('test_db')
            
            # Simulate database operation
            time.sleep(0.01)
            
            # Get HTTP client
            http_client = connection_manager.get_http_client_pool('test_service')
            
            # Simulate HTTP request
            time.sleep(0.005)
            
            return True
        
        # Test concurrent connection usage
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(use_connection_pool) for _ in range(100)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        
        # Verify performance
        total_time = end_time - start_time
        operations_per_second = len(results) / total_time
        
        assert all(results), "All connection pool operations should succeed"
        assert operations_per_second >= 50, f"Connection pool too slow: {operations_per_second} ops/sec"
        assert total_time < 5, f"Total time too high: {total_time} seconds"
        
        # Check connection pool stats
        stats = connection_manager.get_connection_pool_stats()
        assert stats['total_pools'] >= 2, "Should have created pools"
        
        print(f"Connection Pool Performance: {operations_per_second:.1f} ops/sec")
    
    def test_cache_performance_under_load(self):
        """Test cache performance under high load"""
        
        cache_manager = self.performance_optimizer.cache_manager
        
        def cache_operations():
            """Perform cache operations"""
            # Set operations
            for i in range(10):
                cache_manager.set_cache(f"key_{i}", f"value_{i}", 300)
            
            # Get operations (mix of hits and misses)
            hits = 0
            for i in range(20):
                value = cache_manager.get_cache(f"key_{i % 15}")  # Some will miss
                if value is not None:
                    hits += 1
            
            return hits
        
        # Test concurrent cache usage
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
            futures = [executor.submit(cache_operations) for _ in range(50)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        
        # Verify performance
        total_time = end_time - start_time
        operations_per_second = (sum(results) * 30) / total_time  # 30 ops per call
        
        assert operations_per_second >= 1000, f"Cache too slow: {operations_per_second} ops/sec"
        assert total_time < 10, f"Cache operations took too long: {total_time} seconds"
        
        # Check cache stats
        stats = cache_manager.get_cache_stats()
        assert stats['hit_rate'] > 0, "Should have some cache hits"
        
        print(f"Cache Performance: {operations_per_second:.1f} ops/sec, "
              f"Hit Rate: {stats['hit_rate']:.2f}")
    
    def test_resource_cleanup_performance(self):
        """Test resource cleanup performance"""
        
        resource_manager = self.performance_optimizer.resource_manager
        
        # Acquire many resources
        resource_ids = []
        for i in range(1000):
            resource_id = f"resource_{i}"
            success = resource_manager.acquire_resource("test_type", resource_id, ttl=0.1)
            if success:
                resource_ids.append(resource_id)
        
        # Wait for resources to expire
        time.sleep(0.2)
        
        # Measure cleanup performance
        start_time = time.time()
        cleaned_count = resource_manager.cleanup_expired_resources()
        end_time = time.time()
        
        cleanup_time = end_time - start_time
        
        # Verify cleanup performance
        assert cleaned_count > 0, "Should have cleaned up expired resources"
        assert cleanup_time < 1.0, f"Cleanup took too long: {cleanup_time} seconds"
        
        # Verify resources were actually cleaned
        stats = resource_manager.get_resource_stats()
        active_count = stats.get('test_type', {}).get('active', 0)
        assert active_count < len(resource_ids), "Resources should have been cleaned up"
        
        print(f"Resource Cleanup: {cleaned_count} resources in {cleanup_time:.3f} seconds")


@pytest.mark.performance
class TestScalabilityTesting:
    """Tests for system scalability under increasing load"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.performance_optimizer = get_performance_optimizer()
        self.monitoring_service = get_monitoring_service()
    
    def test_increasing_load_scalability(self):
        """Test system behavior under increasing load"""
        
        def simulate_request():
            """Simulate a typical request"""
            start_time = time.time()
            
            # Simulate request processing
            time.sleep(0.05)  # 50ms processing time
            
            # Use cache
            cache_key = f"test_key_{time.time()}"
            self.performance_optimizer.cache_manager.set_cache(cache_key, "test_value", 60)
            
            # Use resource
            resource_id = f"resource_{time.time()}"
            self.performance_optimizer.resource_manager.acquire_resource("test_type", resource_id)
            
            end_time = time.time()
            return end_time - start_time
        
        # Test with increasing concurrent users
        load_levels = [5, 10, 20, 30, 40, 50]
        results = {}
        
        for concurrent_users in load_levels:
            print(f"Testing with {concurrent_users} concurrent users...")
            
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(simulate_request) for _ in range(concurrent_users * 5)]
                response_times = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
            
            total_time = end_time - start_time
            total_requests = len(response_times)
            
            results[concurrent_users] = {
                'total_requests': total_requests,
                'total_time': total_time,
                'requests_per_second': total_requests / total_time,
                'avg_response_time': statistics.mean(response_times),
                'p95_response_time': self._percentile(response_times, 95),
                'max_response_time': max(response_times)
            }
        
        # Analyze scalability
        self._analyze_scalability_results(results)
        
        # Verify scalability characteristics
        rps_values = [results[level]['requests_per_second'] for level in load_levels]
        
        # RPS should not degrade significantly with moderate load increases
        assert rps_values[-1] >= rps_values[0] * 0.7, "Significant performance degradation detected"
        
        # Response times should remain reasonable
        for level in load_levels:
            assert results[level]['avg_response_time'] < 0.5, f"High response time at {level} users"
            assert results[level]['p95_response_time'] < 1.0, f"High P95 response time at {level} users"
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _analyze_scalability_results(self, results: Dict[int, Dict[str, float]]):
        """Analyze and report scalability results"""
        print("\nScalability Test Results:")
        print("=" * 80)
        print(f"{'Users':<8} {'RPS':<10} {'Avg RT':<10} {'P95 RT':<10} {'Max RT':<10}")
        print("-" * 80)
        
        for users, metrics in results.items():
            print(f"{users:<8} {metrics['requests_per_second']:<10.1f} "
                  f"{metrics['avg_response_time']*1000:<10.1f} "
                  f"{metrics['p95_response_time']*1000:<10.1f} "
                  f"{metrics['max_response_time']*1000:<10.1f}")
        
        # Save detailed results
        with open('scalability_test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
    
    def test_memory_scalability(self):
        """Test memory usage scalability"""
        
        def memory_intensive_task():
            """Simulate memory-intensive task"""
            data = []
            for i in range(100):
                data.append({
                    'id': i,
                    'content': 'x' * 1000,  # 1KB per item
                    'metadata': {'timestamp': time.time(), 'processed': False}
                })
            
            # Process data
            for item in data:
                item['metadata']['processed'] = True
                item['content'] = item['content'].upper()
            
            return len(data)
        
        # Test with increasing number of concurrent tasks
        task_counts = [10, 25, 50, 75, 100]
        memory_results = {}
        
        for task_count in task_counts:
            initial_memory = self._get_memory_usage()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=task_count) as executor:
                futures = [executor.submit(memory_intensive_task) for _ in range(task_count)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            final_memory = self._get_memory_usage()
            memory_growth = final_memory['rss_mb'] - initial_memory['rss_mb']
            
            memory_results[task_count] = {
                'initial_memory_mb': initial_memory['rss_mb'],
                'final_memory_mb': final_memory['rss_mb'],
                'memory_growth_mb': memory_growth,
                'memory_per_task_mb': memory_growth / task_count if task_count > 0 else 0
            }
        
        # Verify memory scalability
        for task_count, metrics in memory_results.items():
            assert metrics['memory_growth_mb'] < 200, f"Excessive memory growth: {metrics['memory_growth_mb']}MB"
            assert metrics['memory_per_task_mb'] < 5, f"High memory per task: {metrics['memory_per_task_mb']}MB"
        
        print("\nMemory Scalability Results:")
        for task_count, metrics in memory_results.items():
            print(f"Tasks: {task_count}, Growth: {metrics['memory_growth_mb']:.1f}MB, "
                  f"Per Task: {metrics['memory_per_task_mb']:.2f}MB")
    
    def _get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent()
        }