"""
Locust performance testing configuration

This file defines Locust user behaviors for load testing the SFDC service.
Run with: locust -f locustfile.py --host=http://localhost:7071
"""

import json
import random
import time
import uuid
from locust import HttpUser, task, between


class SFDCServiceUser(HttpUser):
    """Simulates a user interacting with the SFDC service"""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Called when a user starts"""
        self.auth_token = None
        self.execution_log_id = None
        self.organization_id = f"test_org_{random.randint(1000, 9999)}"
        self.user_id = f"test_user_{random.randint(1000, 9999)}"
        
        # Authenticate user
        self.authenticate()
    
    def authenticate(self):
        """Authenticate user and get token"""
        auth_data = {
            "username": f"test_user_{random.randint(1, 100)}@example.com",
            "password": "test_password",
            "organization_id": self.organization_id
        }
        
        with self.client.post(
            "/api/auth/login",
            json=auth_data,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("token", "mock_token")
                response.success()
            else:
                response.failure(f"Authentication failed: {response.status_code}")
    
    def get_headers(self):
        """Get headers with authentication"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    @task(3)
    def health_check(self):
        """Check service health - high frequency task"""
        with self.client.get(
            "/api/health",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(2)
    def get_organizations(self):
        """Get user organizations"""
        with self.client.get(
            f"/api/users/{self.user_id}/organizations",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get organizations failed: {response.status_code}")
    
    @task(1)
    def start_sfdc_scan(self):
        """Start SFDC security scan - lower frequency, higher impact"""
        scan_data = {
            "organization_id": self.organization_id,
            "user_id": self.user_id,
            "scan_type": "security_scan",
            "salesforce_credentials": {
                "username": f"sf_user_{random.randint(1, 100)}@example.com",
                "password": "sf_password",
                "security_token": "sf_token",
                "domain": "test"
            },
            "scan_options": {
                "include_profiles": True,
                "include_permission_sets": True,
                "include_pmd_scan": random.choice([True, False])
            }
        }
        
        with self.client.post(
            "/api/scans/start",
            json=scan_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.execution_log_id = data.get("execution_log_id")
                response.success()
            else:
                response.failure(f"Start scan failed: {response.status_code}")
    
    @task(2)
    def check_scan_status(self):
        """Check scan status if we have an execution log ID"""
        if not self.execution_log_id:
            return
        
        with self.client.get(
            f"/api/scans/{self.execution_log_id}/status",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Check scan status failed: {response.status_code}")
    
    @task(1)
    def get_scan_results(self):
        """Get scan results if available"""
        if not self.execution_log_id:
            return
        
        with self.client.get(
            f"/api/scans/{self.execution_log_id}/results",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                # Results not ready yet, this is expected
                response.success()
            else:
                response.failure(f"Get scan results failed: {response.status_code}")
    
    @task(1)
    def test_salesforce_connection(self):
        """Test Salesforce connection"""
        connection_data = {
            "organization_id": self.organization_id,
            "salesforce_credentials": {
                "username": f"sf_test_{random.randint(1, 100)}@example.com",
                "password": "test_password",
                "security_token": "test_token",
                "domain": "test"
            }
        }
        
        with self.client.post(
            "/api/integrations/test-connection",
            json=connection_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Test connection failed: {response.status_code}")


class AdminUser(HttpUser):
    """Simulates an admin user with different usage patterns"""
    
    wait_time = between(2, 5)  # Admins might take more time between actions
    weight = 1  # Lower weight than regular users
    
    def on_start(self):
        """Called when admin user starts"""
        self.auth_token = None
        self.authenticate_admin()
    
    def authenticate_admin(self):
        """Authenticate as admin user"""
        auth_data = {
            "username": "<EMAIL>",
            "password": "admin_password",
            "role": "admin"
        }
        
        with self.client.post(
            "/api/auth/login",
            json=auth_data,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("token", "mock_admin_token")
                response.success()
            else:
                response.failure(f"Admin authentication failed: {response.status_code}")
    
    def get_headers(self):
        """Get headers with admin authentication"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json",
            "X-User-Role": "admin"
        }
    
    @task(2)
    def get_system_metrics(self):
        """Get system metrics - admin only"""
        with self.client.get(
            "/api/admin/metrics",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get metrics failed: {response.status_code}")
    
    @task(1)
    def get_all_organizations(self):
        """Get all organizations - admin only"""
        with self.client.get(
            "/api/admin/organizations",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get all organizations failed: {response.status_code}")
    
    @task(1)
    def get_active_scans(self):
        """Get all active scans - admin only"""
        with self.client.get(
            "/api/admin/scans/active",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get active scans failed: {response.status_code}")
    
    @task(1)
    def cleanup_old_logs(self):
        """Cleanup old execution logs - admin maintenance task"""
        cleanup_data = {
            "max_age_hours": 24,
            "keep_failed": True
        }
        
        with self.client.post(
            "/api/admin/cleanup/execution-logs",
            json=cleanup_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Cleanup failed: {response.status_code}")


class HighVolumeUser(HttpUser):
    """Simulates high-volume API usage"""
    
    wait_time = between(0.1, 0.5)  # Very short wait times
    weight = 2  # Higher weight for more instances
    
    def on_start(self):
        """Called when high-volume user starts"""
        self.auth_token = "high_volume_token"
        self.request_count = 0
    
    def get_headers(self):
        """Get headers for high-volume requests"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json",
            "X-Client-Type": "high-volume"
        }
    
    @task(5)
    def rapid_health_checks(self):
        """Rapid health check requests"""
        self.request_count += 1
        
        with self.client.get(
            "/api/health",
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 429:  # Rate limited
                response.success()  # Expected behavior
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(3)
    def batch_status_checks(self):
        """Batch status check requests"""
        execution_log_ids = [str(uuid.uuid4()) for _ in range(5)]
        
        batch_data = {
            "execution_log_ids": execution_log_ids
        }
        
        with self.client.post(
            "/api/scans/batch-status",
            json=batch_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 429:  # Rate limited
                response.success()  # Expected behavior
            else:
                response.failure(f"Batch status check failed: {response.status_code}")
    
    @task(1)
    def stress_test_endpoint(self):
        """Stress test a specific endpoint"""
        stress_data = {
            "test_type": "stress",
            "iterations": 10,
            "data_size": "large"
        }
        
        with self.client.post(
            "/api/test/stress",
            json=stress_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 429:  # Rate limited
                response.success()  # Expected behavior
            else:
                response.failure(f"Stress test failed: {response.status_code}")


# Custom Locust events for additional monitoring
from locust import events

@events.request_success.add_listener
def on_request_success(request_type, name, response_time, response_length, **kwargs):
    """Handle successful requests"""
    if response_time > 1000:  # Log slow requests
        print(f"Slow request: {name} took {response_time}ms")

@events.request_failure.add_listener
def on_request_failure(request_type, name, response_time, response_length, exception, **kwargs):
    """Handle failed requests"""
    print(f"Request failed: {name} - {exception}")

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Called when test starts"""
    print("Starting SFDC service load test...")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Called when test stops"""
    print("SFDC service load test completed.")
    
    # Generate summary report
    stats = environment.stats
    print(f"\nTest Summary:")
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Failed requests: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"Max response time: {stats.total.max_response_time:.2f}ms")
    print(f"Requests per second: {stats.total.current_rps:.2f}")
    
    # Save detailed stats to file
    with open('locust_results.json', 'w') as f:
        json.dump({
            'total_requests': stats.total.num_requests,
            'failed_requests': stats.total.num_failures,
            'avg_response_time': stats.total.avg_response_time,
            'max_response_time': stats.total.max_response_time,
            'min_response_time': stats.total.min_response_time,
            'current_rps': stats.total.current_rps,
            'failure_rate': stats.total.fail_ratio
        }, f, indent=2)