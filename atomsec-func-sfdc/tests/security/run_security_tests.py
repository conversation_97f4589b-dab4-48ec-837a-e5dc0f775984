#!/usr/bin/env python3
"""
Security Test Runner

This script runs comprehensive security tests and generates security reports.
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime
import argparse


def run_command(command, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return e.returncode, e.stdout, e.stderr


def install_security_test_dependencies():
    """Install security testing dependencies"""
    print("Installing security test dependencies...")
    
    dependencies = [
        "pytest>=7.4.4",
        "pytest-mock>=3.12.0",
        "pytest-security>=0.1.0",
        "bandit>=1.7.5",
        "safety>=2.3.0",
        "semgrep>=1.45.0",
        "requests>=2.31.0",
        "cryptography>=41.0.0"
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        returncode, stdout, stderr = run_command(f"pip install {dep}")
        if returncode != 0:
            print(f"Warning: Failed to install {dep}: {stderr}")


def run_authentication_security_tests():
    """Run authentication and authorization security tests"""
    print("Running authentication security tests...")
    
    command = "python -m pytest tests/security/test_authentication_security.py -v -m security"
    returncode, stdout, stderr = run_command(command)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return returncode == 0


def run_injection_attack_tests():
    """Run injection attack prevention tests"""
    print("Running injection attack tests...")
    
    command = "python -m pytest tests/security/test_injection_attacks.py -v -m security"
    returncode, stdout, stderr = run_command(command)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return returncode == 0


def run_static_security_analysis():
    """Run static security analysis with Bandit"""
    print("Running static security analysis...")
    
    # Run Bandit security scanner
    command = "bandit -r shared/ api/ blueprints/ -f json -o bandit_report.json"
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✓ No high-severity security issues found")
        return True
    else:
        print("⚠ Security issues found - check bandit_report.json")
        print(stdout)
        return False


def run_dependency_security_scan():
    """Run dependency security scan with Safety"""
    print("Running dependency security scan...")
    
    # Run Safety to check for known vulnerabilities
    command = "safety check --json --output safety_report.json"
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✓ No known vulnerabilities in dependencies")
        return True
    else:
        print("⚠ Vulnerabilities found in dependencies - check safety_report.json")
        return False


def run_semgrep_security_scan():
    """Run Semgrep security scan"""
    print("Running Semgrep security scan...")
    
    # Run Semgrep with security rules
    command = "semgrep --config=auto --json --output=semgrep_report.json shared/ api/ blueprints/"
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✓ Semgrep scan completed")
        return True
    else:
        print("⚠ Semgrep found potential issues - check semgrep_report.json")
        return False


def run_secrets_detection():
    """Run secrets detection scan"""
    print("Running secrets detection...")
    
    # Simple secrets detection (in a real scenario, use tools like truffleHog)
    secrets_patterns = [
        r'password\s*=\s*["\'][^"\']+["\']',
        r'api_key\s*=\s*["\'][^"\']+["\']',
        r'secret\s*=\s*["\'][^"\']+["\']',
        r'token\s*=\s*["\'][^"\']+["\']',
        r'-----BEGIN\s+.*PRIVATE\s+KEY-----',
        r'sk_live_[0-9a-zA-Z]{24}',  # Stripe live key
        r'pk_live_[0-9a-zA-Z]{24}',  # Stripe publishable key
        r'AKIA[0-9A-Z]{16}',  # AWS Access Key
        r'[0-9a-f]{32}',  # Generic 32-char hex (potential API key)
    ]
    
    issues_found = []
    
    # Scan Python files
    for file_path in Path('.').rglob('*.py'):
        if 'test' in str(file_path) or '__pycache__' in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for i, line in enumerate(content.split('\n'), 1):
                for pattern in secrets_patterns:
                    import re
                    if re.search(pattern, line, re.IGNORECASE):
                        issues_found.append({
                            'file': str(file_path),
                            'line': i,
                            'content': line.strip(),
                            'pattern': pattern
                        })
        except Exception as e:
            print(f"Warning: Could not scan {file_path}: {e}")
    
    if issues_found:
        print(f"⚠ Found {len(issues_found)} potential secrets:")
        for issue in issues_found:
            print(f"  {issue['file']}:{issue['line']} - {issue['content'][:50]}...")
        
        # Save detailed report
        with open('secrets_report.json', 'w') as f:
            json.dump(issues_found, f, indent=2)
        
        return False
    else:
        print("✓ No potential secrets detected")
        return True


def run_configuration_security_check():
    """Check security configuration"""
    print("Checking security configuration...")
    
    security_checks = []
    
    # Check for secure configuration files
    config_files = [
        'host.json',
        'local.settings.json',
        'requirements.txt'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    content = f.read()
                
                # Check for insecure configurations
                insecure_patterns = [
                    ('debug.*true', 'Debug mode enabled'),
                    ('ssl.*false', 'SSL disabled'),
                    ('secure.*false', 'Security feature disabled'),
                    ('password.*=.*["\'][^"\']{1,8}["\']', 'Weak password detected'),
                ]
                
                for pattern, description in insecure_patterns:
                    import re
                    if re.search(pattern, content, re.IGNORECASE):
                        security_checks.append({
                            'file': config_file,
                            'issue': description,
                            'severity': 'medium'
                        })
                        
            except Exception as e:
                print(f"Warning: Could not check {config_file}: {e}")
    
    # Check Python version for known vulnerabilities
    python_version = sys.version_info
    if python_version < (3, 8):
        security_checks.append({
            'file': 'system',
            'issue': f'Python {python_version.major}.{python_version.minor} has known security vulnerabilities',
            'severity': 'high'
        })
    
    if security_checks:
        print(f"⚠ Found {len(security_checks)} configuration issues:")
        for check in security_checks:
            print(f"  {check['severity'].upper()}: {check['file']} - {check['issue']}")
        
        with open('config_security_report.json', 'w') as f:
            json.dump(security_checks, f, indent=2)
        
        return len([c for c in security_checks if c['severity'] == 'high']) == 0
    else:
        print("✓ No security configuration issues found")
        return True


def generate_security_report(test_results):
    """Generate comprehensive security report"""
    print("Generating security report...")
    
    report = {
        'timestamp': datetime.utcnow().isoformat(),
        'test_results': test_results,
        'summary': {
            'total_tests': len(test_results),
            'passed_tests': sum(1 for result in test_results.values() if result),
            'failed_tests': sum(1 for result in test_results.values() if not result)
        },
        'recommendations': []
    }
    
    # Add recommendations based on failed tests
    if not test_results.get('authentication_tests', True):
        report['recommendations'].append({
            'category': 'Authentication',
            'priority': 'high',
            'recommendation': 'Fix authentication security issues before deployment'
        })
    
    if not test_results.get('injection_tests', True):
        report['recommendations'].append({
            'category': 'Input Validation',
            'priority': 'critical',
            'recommendation': 'Address injection vulnerabilities immediately'
        })
    
    if not test_results.get('static_analysis', True):
        report['recommendations'].append({
            'category': 'Code Security',
            'priority': 'medium',
            'recommendation': 'Review and fix static analysis findings'
        })
    
    if not test_results.get('dependency_scan', True):
        report['recommendations'].append({
            'category': 'Dependencies',
            'priority': 'high',
            'recommendation': 'Update vulnerable dependencies'
        })
    
    if not test_results.get('secrets_detection', True):
        report['recommendations'].append({
            'category': 'Secrets Management',
            'priority': 'critical',
            'recommendation': 'Remove hardcoded secrets and use secure storage'
        })
    
    # Calculate security score
    passed_tests = report['summary']['passed_tests']
    total_tests = report['summary']['total_tests']
    security_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    report['security_score'] = round(security_score, 1)
    
    # Determine overall security status
    if security_score >= 90:
        report['status'] = 'excellent'
    elif security_score >= 75:
        report['status'] = 'good'
    elif security_score >= 60:
        report['status'] = 'fair'
    else:
        report['status'] = 'poor'
    
    # Save report
    with open('security_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print(f"\nSecurity Test Summary:")
    print(f"=" * 50)
    print(f"Security Score: {report['security_score']}% ({report['status'].upper()})")
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Critical Issues: {len([r for r in report['recommendations'] if r['priority'] == 'critical'])}")
    print(f"High Priority Issues: {len([r for r in report['recommendations'] if r['priority'] == 'high'])}")
    print(f"=" * 50)
    
    if report['recommendations']:
        print("\nRecommendations:")
        for rec in report['recommendations']:
            print(f"  {rec['priority'].upper()}: {rec['recommendation']}")
    
    return report['security_score'] >= 75  # Pass if score is 75% or higher


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run comprehensive security tests")
    parser.add_argument("--install-deps", action="store_true", help="Install security test dependencies")
    parser.add_argument("--auth-only", action="store_true", help="Run only authentication tests")
    parser.add_argument("--injection-only", action="store_true", help="Run only injection tests")
    parser.add_argument("--static-only", action="store_true", help="Run only static analysis")
    parser.add_argument("--quick", action="store_true", help="Run quick security checks only")
    
    args = parser.parse_args()
    
    # Change to project directory
    project_root = Path(__file__).parent.parent.parent
    os.chdir(project_root)
    
    success = True
    test_results = {}
    
    try:
        # Install dependencies if requested
        if args.install_deps:
            install_security_test_dependencies()
        
        # Run specific test categories
        if args.auth_only:
            test_results['authentication_tests'] = run_authentication_security_tests()
        elif args.injection_only:
            test_results['injection_tests'] = run_injection_attack_tests()
        elif args.static_only:
            test_results['static_analysis'] = run_static_security_analysis()
        elif args.quick:
            # Quick security checks
            test_results['secrets_detection'] = run_secrets_detection()
            test_results['config_security'] = run_configuration_security_check()
        else:
            # Run all security tests
            test_results['authentication_tests'] = run_authentication_security_tests()
            test_results['injection_tests'] = run_injection_attack_tests()
            test_results['static_analysis'] = run_static_security_analysis()
            test_results['dependency_scan'] = run_dependency_security_scan()
            test_results['semgrep_scan'] = run_semgrep_security_scan()
            test_results['secrets_detection'] = run_secrets_detection()
            test_results['config_security'] = run_configuration_security_check()
        
        # Generate comprehensive report
        success = generate_security_report(test_results)
        
    except KeyboardInterrupt:
        print("\nSecurity test execution interrupted by user")
        success = False
    except Exception as e:
        print(f"Error running security tests: {e}")
        success = False
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()