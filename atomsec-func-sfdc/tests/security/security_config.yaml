# Security Testing Configuration
# This file defines security testing parameters and thresholds

security_testing:
  # Authentication Security Settings
  authentication:
    jwt_secret_min_length: 32
    token_expiration_max_hours: 24
    max_login_attempts: 5
    lockout_duration_minutes: 15
    password_min_length: 8
    require_special_characters: true
    
  # Rate Limiting Settings
  rate_limiting:
    requests_per_minute: 100
    burst_limit: 20
    window_size_minutes: 1
    enable_distributed_limiting: true
    
  # Input Validation Settings
  input_validation:
    max_string_length: 1000
    max_array_size: 100
    max_object_depth: 10
    enable_html_sanitization: true
    enable_sql_injection_detection: true
    enable_xss_protection: true
    
  # Security Headers
  security_headers:
    required_headers:
      - "X-Content-Type-Options"
      - "X-Frame-Options"
      - "X-XSS-Protection"
      - "Strict-Transport-Security"
      - "Content-Security-Policy"
      - "Referrer-Policy"
    
    header_values:
      x_content_type_options: "nosniff"
      x_frame_options: "DENY"
      x_xss_protection: "1; mode=block"
      strict_transport_security: "max-age=31536000; includeSubDomains"
      content_security_policy: "default-src 'self'"
      referrer_policy: "strict-origin-when-cross-origin"
  
  # Encryption Settings
  encryption:
    min_key_length: 256
    allowed_algorithms:
      - "AES-256-GCM"
      - "ChaCha20-Poly1305"
    forbidden_algorithms:
      - "DES"
      - "3DES"
      - "RC4"
      - "MD5"
      - "SHA1"
  
  # File Upload Security
  file_upload:
    max_file_size_mb: 10
    allowed_extensions:
      - ".txt"
      - ".json"
      - ".csv"
      - ".xml"
    forbidden_extensions:
      - ".exe"
      - ".bat"
      - ".sh"
      - ".ps1"
      - ".php"
      - ".jsp"
      - ".asp"
    enable_virus_scanning: true
    quarantine_suspicious_files: true

# Security Test Categories
test_categories:
  authentication:
    enabled: true
    tests:
      - jwt_token_validation
      - password_strength
      - session_management
      - multi_factor_authentication
      - account_lockout
      - privilege_escalation
    
  authorization:
    enabled: true
    tests:
      - role_based_access_control
      - resource_access_control
      - horizontal_privilege_escalation
      - vertical_privilege_escalation
      - scope_validation
    
  input_validation:
    enabled: true
    tests:
      - sql_injection
      - nosql_injection
      - xss_attacks
      - command_injection
      - path_traversal
      - ldap_injection
      - xml_injection
      - json_injection
    
  session_security:
    enabled: true
    tests:
      - session_fixation
      - session_hijacking
      - csrf_protection
      - secure_cookie_settings
      - session_timeout
    
  cryptography:
    enabled: true
    tests:
      - weak_encryption
      - key_management
      - certificate_validation
      - random_number_generation
      - hash_functions
    
  error_handling:
    enabled: true
    tests:
      - information_disclosure
      - stack_trace_exposure
      - error_message_leakage
      - debug_information
    
  configuration:
    enabled: true
    tests:
      - default_credentials
      - debug_mode
      - unnecessary_services
      - file_permissions
      - environment_variables

# Security Scanning Tools Configuration
scanning_tools:
  bandit:
    enabled: true
    severity_threshold: "medium"
    confidence_threshold: "medium"
    excluded_tests: []
    
  safety:
    enabled: true
    ignore_vulnerabilities: []
    check_full_report: true
    
  semgrep:
    enabled: true
    rulesets:
      - "auto"
      - "security"
      - "owasp-top-10"
    exclude_patterns:
      - "tests/"
      - "__pycache__/"
    
  secrets_detection:
    enabled: true
    patterns:
      - "password"
      - "api_key"
      - "secret"
      - "token"
      - "private_key"
    exclude_files:
      - "test_*.py"
      - "mock_*.py"

# Security Thresholds
thresholds:
  security_score:
    excellent: 95
    good: 85
    fair: 70
    poor: 0
    
  vulnerability_limits:
    critical: 0
    high: 2
    medium: 5
    low: 10
    
  test_coverage:
    minimum_percentage: 80
    security_test_percentage: 90

# Reporting Configuration
reporting:
  formats:
    - "json"
    - "html"
    - "pdf"
  
  include_sections:
    - "executive_summary"
    - "test_results"
    - "vulnerability_details"
    - "recommendations"
    - "compliance_status"
  
  compliance_frameworks:
    - "OWASP Top 10"
    - "NIST Cybersecurity Framework"
    - "ISO 27001"
    - "SOC 2"
  
  notification:
    email_recipients: []
    slack_webhook: ""
    teams_webhook: ""

# Environment-Specific Settings
environments:
  development:
    relaxed_thresholds: true
    enable_debug_tests: true
    skip_performance_tests: false
    
  staging:
    production_like_tests: true
    enable_penetration_tests: true
    full_security_scan: true
    
  production:
    strict_thresholds: true
    enable_continuous_monitoring: true
    automated_remediation: false