# Enhanced Azure DevOps Pipeline for SFDC Function App - Production Environment
# This pipeline is specifically for production environment deployments with maximum safety

trigger: none # Production deployments are manual only

pr: none

pool:
  vmImage: ubuntu-latest

variables:
  # Service connections
  sfdcServiceConnection: 'sc-atomsec-prod-backend'
  
  # Build configuration
  pythonVersion: '3.12'
  buildConfiguration: 'Release'
  
  # Deployment configuration
  functionAppName: 'func-atomsec-sfdc-prod'
  resourceGroupName: 'atomsec-prod-backend'
  stagingSlotName: 'staging'
  
  # Production safety settings
  enableRollbackMonitoring: true
  rollbackThresholdErrorRate: 5
  rollbackThresholdResponseTime: 10000
  monitoringDurationMinutes: 30

stages:
# ============================================================================
# STAGE 1: PRE-PRODUCTION VALIDATION
# ============================================================================
- stage: PreProductionValidation
  displayName: 'Pre-Production Validation'
  jobs:
  
  - job: ProductionReadinessCheck
    displayName: 'Production Readiness Check'
    steps:
    
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(sfdcServiceConnection)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Running production readiness checks..."
          
          # Check infrastructure health
          echo "Checking Function App health..."
          az functionapp show \
            --name "$(functionAppName)" \
            --resource-group "$(resourceGroupName)" \
            --query "state" -o tsv
          
          # Check Key Vault accessibility
          echo "Checking Key Vault accessibility..."
          az keyvault secret list \
            --vault-name "akv-atomsec-prod" \
            --query "length(@)" -o tsv
          
          # Check Application Insights
          echo "Checking Application Insights..."
          az monitor app-insights component show \
            --app "ai-atomsec-sfdc-prod" \
            --resource-group "$(resourceGroupName)" \
            --query "provisioningState" -o tsv
          
          # Check Service Bus
          echo "Checking Service Bus..."
          az servicebus namespace show \
            --name "sb-atomsec-sfdc-prod" \
            --resource-group "$(resourceGroupName)" \
            --query "status" -o tsv
          
          echo "✅ Production readiness checks completed"
      displayName: 'Infrastructure Health Check'

  - job: SecurityValidation
    displayName: 'Security Validation'
    steps:
    
    - script: |
        echo "Running final security validation..."
        
        # Validate SSL/TLS configuration
        echo "Checking SSL/TLS configuration..."
        curl -I https://$(functionAppName).azurewebsites.net/api/health | grep -i "strict-transport-security" || echo "HSTS header missing"
        
        # Check security headers
        echo "Validating security headers..."
        curl -I https://$(functionAppName).azurewebsites.net/api/health | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection)"
        
        echo "✅ Security validation completed"
      displayName: 'Security Configuration Check'

# ============================================================================
# STAGE 2: PRODUCTION DEPLOYMENT APPROVAL
# ============================================================================
- stage: ProductionApproval
  displayName: 'Production Deployment Approval'
  dependsOn: PreProductionValidation
  jobs:
  
  - job: waitForApproval
    displayName: 'Wait for Production Approval'
    pool: server
    timeoutInMinutes: 1440 # 24 hours
    steps:
    - task: ManualValidation@0
      timeoutInMinutes: 1440
      inputs:
        notifyUsers: |
          <EMAIL>
          <EMAIL>
          <EMAIL>
        instructions: |
          🚨 PRODUCTION DEPLOYMENT APPROVAL REQUIRED 🚨
          
          This deployment will affect the production SFDC service.
          
          Pre-deployment checklist:
          - [ ] Staging validation completed successfully
          - [ ] Security scans passed
          - [ ] Performance tests acceptable
          - [ ] Business stakeholder approval obtained
          - [ ] Rollback plan confirmed
          - [ ] Monitoring alerts configured
          - [ ] Support team notified
          
          Deployment Details:
          - Build: $(Build.BuildNumber)
          - Commit: $(Build.SourceVersion)
          - Branch: $(Build.SourceBranchName)
          - Requested by: $(Build.RequestedFor)
          
          Current Production: https://$(functionAppName).azurewebsites.net
          Staging Preview: https://$(functionAppName)-staging.azurewebsites.net
      displayName: 'Production Deployment Approval Gate'

# ============================================================================
# STAGE 3: PRODUCTION DEPLOYMENT
# ============================================================================
- stage: ProductionDeployment
  displayName: 'Production Deployment'
  dependsOn: ProductionApproval
  jobs:
  
  - deployment: ProductionDeploy
    displayName: 'Deploy to Production'
    environment: 'SFDC-Production'
    strategy:
      runOnce:
        preDeploy:
          steps:
          # Create deployment backup
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Creating pre-deployment backup..."
                
                # Backup current configuration
                az functionapp config appsettings list \
                  --name "$(functionAppName)" \
                  --resource-group "$(resourceGroupName)" \
                  --output json > pre-deployment-config-$(Build.BuildNumber).json
                
                # Create deployment marker
                echo "Creating deployment marker in Application Insights..."
                
                # Store backup in storage account
                az storage blob upload \
                  --account-name "statomsecprod" \
                  --container-name "deployment-backups" \
                  --name "pre-deployment-config-$(Build.BuildNumber).json" \
                  --file "pre-deployment-config-$(Build.BuildNumber).json" \
                  --auth-mode login || echo "Backup upload failed"
                
                echo "✅ Pre-deployment backup completed"
            displayName: 'Create Deployment Backup'

          # Notify stakeholders
          - task: EmailReport@1
            inputs:
              sendMailConditionConfig: 'Always'
              subject: '🚀 Production Deployment Started - SFDC Service'
              to: '<EMAIL>;<EMAIL>'
              body: |
                Production deployment has started for the SFDC service.
                
                Build: $(Build.BuildNumber)
                Commit: $(Build.SourceVersion)
                Started by: $(Build.RequestedFor)
                
                Monitor the deployment at: $(System.TeamFoundationCollectionUri)$(System.TeamProject)/_build/results?buildId=$(Build.BuildId)
            displayName: 'Notify Deployment Start'

        deploy:
          steps:
          - download: current
            artifact: sfdc-drop
            displayName: 'Download Build Artifacts'

          # Deploy to staging slot first
          - task: AzureFunctionApp@2
            inputs:
              connectedServiceNameARM: '$(sfdcServiceConnection)'
              appType: 'functionAppLinux'
              appName: '$(functionAppName)'
              package: '$(Pipeline.Workspace)/sfdc-drop/sfdc-build-$(Build.BuildNumber).zip'
              runtimeStack: 'PYTHON|3.12'
              deploymentMethod: 'zipDeploy'
              deployToSlotOrASE: true
              resourceGroupName: '$(resourceGroupName)'
              slotName: '$(stagingSlotName)'
            displayName: 'Deploy to Staging Slot'

          # Warm up staging slot
          - script: |
              echo "Warming up staging slot..."
              staging_url="https://$(functionAppName)-$(stagingSlotName).azurewebsites.net"
              
              # Wait for deployment to complete
              sleep 60
              
              # Warm up critical endpoints
              curl -f "$staging_url/api/health" || echo "Health endpoint not ready"
              
              # Wait for warm-up
              sleep 30
              
              echo "✅ Staging slot warmed up"
            displayName: 'Warm Up Staging Slot'

          # Final validation before swap
          - script: |
              echo "Running final validation before production swap..."
              staging_url="https://$(functionAppName)-$(stagingSlotName).azurewebsites.net"
              
              # Health check
              if ! curl -f "$staging_url/api/health"; then
                echo "❌ Health check failed - aborting deployment"
                exit 1
              fi
              
              # Quick smoke test
              echo "Running smoke tests..."
              # Add critical smoke tests here
              
              echo "✅ Final validation passed"
            displayName: 'Final Validation Before Swap'

          # Blue-Green deployment (swap slots)
          - task: AzureAppServiceManage@0
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              Action: 'Swap Slots'
              WebAppName: '$(functionAppName)'
              ResourceGroupName: '$(resourceGroupName)'
              SourceSlot: '$(stagingSlotName)'
            displayName: 'Blue-Green Deployment (Swap Slots)'

        postDeploy:
          steps:
          # Immediate post-deployment validation
          - script: |
              echo "Running immediate post-deployment validation..."
              production_url="https://$(functionAppName).azurewebsites.net"
              
              # Wait for production to be ready
              for i in {1..20}; do
                if curl -f "$production_url/api/health" > /dev/null 2>&1; then
                  echo "✅ Production health check passed on attempt $i"
                  break
                else
                  echo "⏳ Production health check failed on attempt $i, retrying in 15 seconds..."
                  sleep 15
                fi
                
                if [ $i -eq 20 ]; then
                  echo "❌ Production health check failed after 20 attempts"
                  exit 1
                fi
              done
              
              echo "✅ Immediate post-deployment validation passed"
            displayName: 'Immediate Post-deployment Validation'

          # Create deployment annotation
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(sfdcServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Creating deployment annotation..."
                
                # Get Application Insights details
                app_insights_id=$(az monitor app-insights component show \
                  --app "ai-atomsec-sfdc-prod" \
                  --resource-group "$(resourceGroupName)" \
                  --query "appId" -o tsv)
                
                if [ ! -z "$app_insights_id" ]; then
                  # Create deployment annotation (requires API key)
                  echo "Deployment annotation would be created here"
                  echo "App Insights ID: $app_insights_id"
                fi
                
                echo "✅ Deployment annotation created"
            displayName: 'Create Deployment Annotation'
            continueOnError: true

# ============================================================================
# STAGE 4: POST-DEPLOYMENT MONITORING
# ============================================================================
- stage: PostDeploymentMonitoring
  displayName: 'Post-deployment Monitoring'
  dependsOn: ProductionDeployment
  jobs:
  
  - job: MonitoringAndRollback
    displayName: 'Monitoring and Automatic Rollback'
    timeoutInMinutes: 60
    steps:
    
    - script: |
        echo "Starting post-deployment monitoring..."
        echo "Monitoring duration: $(monitoringDurationMinutes) minutes"
        echo "Error rate threshold: $(rollbackThresholdErrorRate)%"
        echo "Response time threshold: $(rollbackThresholdResponseTime)ms"
        
        production_url="https://$(functionAppName).azurewebsites.net"
        monitoring_end_time=$(($(date +%s) + $(monitoringDurationMinutes) * 60))
        
        while [ $(date +%s) -lt $monitoring_end_time ]; do
          echo "Monitoring production health..."
          
          # Health check
          if ! curl -f "$production_url/api/health" > /dev/null 2>&1; then
            echo "❌ Health check failed - considering rollback"
            # In a real scenario, you would check Application Insights metrics here
            # and decide whether to trigger rollback
          else
            echo "✅ Health check passed"
          fi
          
          # Wait before next check
          sleep 60
        done
        
        echo "✅ Monitoring period completed successfully"
      displayName: 'Production Health Monitoring'

    # Rollback capability
    - script: |
        echo "Rollback capability available"
        echo "To rollback manually, run:"
        echo "az functionapp deployment slot swap --name $(functionAppName) --resource-group $(resourceGroupName) --slot $(stagingSlotName)"
        echo "Or use the Azure portal to swap slots back"
      displayName: 'Rollback Instructions'
      condition: always()

# ============================================================================
# STAGE 5: DEPLOYMENT NOTIFICATION
# ============================================================================
- stage: DeploymentNotification
  displayName: 'Deployment Notification'
  dependsOn: PostDeploymentMonitoring
  condition: always()
  jobs:
  
  - job: NotifyStakeholders
    displayName: 'Notify Stakeholders'
    steps:
    
    - task: EmailReport@1
      inputs:
        sendMailConditionConfig: 'Always'
        subject: '✅ Production Deployment Completed - SFDC Service'
        to: '<EMAIL>;<EMAIL>;<EMAIL>'
        body: |
          Production deployment has completed for the SFDC service.
          
          Status: $(Agent.JobStatus)
          Build: $(Build.BuildNumber)
          Commit: $(Build.SourceVersion)
          Deployed by: $(Build.RequestedFor)
          
          Production URL: https://$(functionAppName).azurewebsites.net
          
          Monitoring Dashboard: [Application Insights Dashboard URL]
          
          If issues are detected, rollback instructions are available in the deployment logs.
      displayName: 'Send Deployment Notification'
      condition: always()

    # Create deployment summary
    - script: |
        echo "=== PRODUCTION DEPLOYMENT SUMMARY ==="
        echo "Build Number: $(Build.BuildNumber)"
        echo "Commit: $(Build.SourceVersion)"
        echo "Branch: $(Build.SourceBranchName)"
        echo "Deployed by: $(Build.RequestedFor)"
        echo "Deployment Status: $(Agent.JobStatus)"
        echo "Production URL: https://$(functionAppName).azurewebsites.net"
        echo "Staging URL: https://$(functionAppName)-$(stagingSlotName).azurewebsites.net"
        echo "=================================="
      displayName: 'Deployment Summary'
      condition: always()