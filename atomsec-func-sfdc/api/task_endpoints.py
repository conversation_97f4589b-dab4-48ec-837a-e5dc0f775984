"""
Task Management Endpoints

This module provides task-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/tasks - List tasks with optional filtering
- POST /api/tasks - Create new task
- GET /api/tasks/{id} - Get task by ID
- PUT /api/tasks/{id} - Update task status
- DELETE /api/tasks/{id} - Delete task
- GET /api/tasks/org/{org_id} - Get tasks by organization
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Task
from shared.event_publisher import publish_task_event
from shared.auth_utils import get_user_from_request_or_default
from shared.task_status_service import get_task_status_service

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_task_table_repo = None
_task_sql_repo = None


def _parse_params(params_str: Optional[str]) -> Dict[str, Any]:
    """
    Safely parse the Params field which can be stored as JSON or Python string representation
    
    Args:
        params_str: The params string to parse
        
    Returns:
        Parsed parameters as dictionary
    """
    if not params_str:
        return {}
    
    try:
        # First try to parse as JSON
        return json.loads(params_str)
    except json.JSONDecodeError:
        try:
            # If JSON fails, try to evaluate as Python literal (safely)
            import ast
            return ast.literal_eval(params_str)
        except (ValueError, SyntaxError):
            # If both fail, return empty dict
            logger.warning(f"Failed to parse params: {params_str}")
            return {}


def get_task_table_repo() -> Optional[TableStorageRepository]:
    """Get task table repository for local development"""
    global _task_table_repo
    if _task_table_repo is None:
        try:
            _task_table_repo = TableStorageRepository(table_name="TaskStatus")
            logger.info("Initialized task table repository")
        except Exception as e:
            logger.error(f"Failed to initialize task table repository: {str(e)}")
            _task_table_repo = None
    return _task_table_repo


def get_task_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get task SQL repository for production"""
    global _task_sql_repo
    if _task_sql_repo is None and not is_local_dev():
        try:
            _task_sql_repo = SqlDatabaseRepository(table_name="App_Task")
            logger.info("Initialized task SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize task SQL repository: {str(e)}")
            _task_sql_repo = None
    return _task_sql_repo


def create_task(task_data: Dict[str, Any]) -> Optional[str]:
    """
    Create a new task and enqueue it for processing

    Args:
        task_data: Task data dictionary containing:
            - task_type: Type of task
            - org_id: Organization ID
            - user_id: User ID
            - params: Task parameters
            - priority: Task priority
            - execution_log_id: Execution log ID (should be passed from parent task)

    Returns:
        Task ID if created successfully, None otherwise
    """
    try:
        # Extract required fields
        task_type = task_data.get('task_type')
        org_id = task_data.get('org_id')
        user_id = task_data.get('user_id')
        params = task_data.get('params') or task_data.get('data', {})
        priority = task_data.get('priority', 'medium')
        execution_log_id = task_data.get('execution_log_id')  # Use the provided execution_log_id

        if not task_type or not org_id or not user_id:
            logger.error(f"Missing required task data: task_type={task_type}, org_id={org_id}, user_id={user_id}")
            return None

        if not execution_log_id:
            logger.error(f"Missing execution_log_id for task {task_type}. This is required for proper dependency tracking.")
            return None

        logger.info(f"Creating task {task_type} for org {org_id} with execution_log_id: {execution_log_id}")


        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return None

            # Generate task ID
            task_id = str(uuid.uuid4())

            # Create task object
            task = Task(
                TaskId=task_id,
                TaskType=task_type,
                OrgId=str(org_id),
                UserId=str(user_id),
                Status="pending",
                Priority=priority,
                Progress=0,
                Message="Task created",
                CreatedAt=datetime.now(),
                UpdatedAt=datetime.now(),
                ExecutionLogId=execution_log_id,  # Use the provided execution_log_id
                Params=params
            )

            # Create entity for Table Storage
            entity = {
                "PartitionKey": f"task_{task.OrgId}",
                "RowKey": task.TaskId,
                "TaskId": task.TaskId,
                "TaskType": task.TaskType,
                "OrgId": task.OrgId,
                "UserId": task.UserId,
                "Status": task.Status,
                "Priority": task.Priority,
                "Progress": task.Progress,
                "Message": task.Message,
                "Result": task.Result,
                "CreatedAt": task.CreatedAt.isoformat(),
                "UpdatedAt": task.UpdatedAt.isoformat(),
                "CompletedAt": task.CompletedAt.isoformat() if task.CompletedAt else None,
                "ScheduledTime": task.ScheduledTime.isoformat() if task.ScheduledTime else None,
                "RetryCount": task.RetryCount,
                "ExecutionLogId": task.ExecutionLogId,  # Use the provided execution_log_id
                "Params": str(task.Params) if task.Params else None
            }

            if repo.insert_entity(entity):
                logger.info(f"Created task: {task_id} of type {task.TaskType}")

                # Publish task created event
                publish_task_event("created", {
                    "task_id": task_id,
                    "task_type": task.TaskType,
                    "org_id": task.OrgId,
                    "user_id": task.UserId,
                    "priority": task.Priority,
                    "params": task.Params or {},
                    "execution_log_id": execution_log_id  # Include execution log ID
                })

                # Send task to Service Bus for processing
                try:
                    from shared.service_bus_client import get_db_service_bus_client

                    # Prepare task message for Service Bus
                    task_message = {
                        "task_id": task_id,
                        "task_type": task.TaskType,
                        "org_id": task.OrgId,
                        "user_id": task.UserId,
                        "priority": task.Priority,
                        "params": task.Params or {},
                        "execution_log_id": execution_log_id,
                        "created_at": task.CreatedAt.isoformat()
                    }

                    # Get Service Bus client and send message
                    service_bus_client = get_db_service_bus_client()
                    if service_bus_client.send_task_message(task_message):
                        logger.info(f"Sent task {task_id} to Service Bus with priority {task.Priority}")
                    else:
                        logger.error(f"Failed to send task {task_id} to Service Bus")

                except Exception as service_bus_error:
                    logger.error(f"Error sending task {task_id} to Service Bus: {str(service_bus_error)}")
                    # Don't fail the task creation if Service Bus sending fails

                return task_id
            else:
                logger.error("Failed to insert task entity")
                return None

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return None

            query = """
            SELECT TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data
            FROM App_Task
            WHERE TaskId = ?
            """

            results = repo.execute_query(query, (task_id,))

            if results:
                row = results[0]
                return {
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {}
                }
            else:
                logger.warning(f"Task not found: {task_id}")
                return None

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return None


def get_task_by_id(task_id: str) -> Optional[Dict[str, Any]]:
    """
    Get task by ID

    Args:
        task_id: Task ID

    Returns:
        Task data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return None

            # Query all entities and filter by RowKey since we don't know the org_id
            # Azure Table Storage doesn't support startswith in OData queries
            entities = list(repo.query_entities())
            
            # Find the entity with matching RowKey (task_id)
            target_entity = None
            for entity in entities:
                if entity.get('RowKey') == task_id:
                    target_entity = entity
                    break

            if target_entity:
                return {
                    'task_id': target_entity.get('RowKey'),  # TaskId is stored as RowKey
                    'org_id': target_entity.get('OrgId'),
                    'task_type': target_entity.get('TaskType'),
                    'status': target_entity.get('Status'),
                    'priority': target_entity.get('Priority'),
                    'created_at': target_entity.get('CreatedAt'),
                    'created_by': target_entity.get('CreatedBy'),
                    'started_at': target_entity.get('StartedAt'),
                    'completed_at': target_entity.get('CompletedAt'),
                    'error_message': target_entity.get('ErrorMessage'),
                    'data': _parse_params(target_entity.get('Params'))  # Params, not Data
                }
            else:
                logger.warning(f"Task not found: {task_id}")
                return None

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return None

            query = """
            SELECT TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data
            FROM App_Task
            WHERE TaskId = ?
            """

            results = repo.execute_query(query, (task_id,))

            if results:
                row = results[0]
                return {
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {}
                }
            else:
                logger.warning(f"Task not found: {task_id}")
                return None

    except Exception as e:
        logger.error(f"Error getting task by ID: {str(e)}")
        return None


def get_tasks(org_id: Optional[str] = None, status: Optional[str] = None,
              task_type: Optional[str] = None, priority: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get tasks with optional filtering

    Args:
        org_id: Filter by organization ID
        status: Filter by status
        task_type: Filter by task type
        priority: Filter by priority

    Returns:
        List of task dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return []

            # Build filter query based on org_id
            if org_id is not None:
                # Use exact PartitionKey match when org_id is provided
                filter_query = f"PartitionKey eq 'task_{org_id}'"
                entities = list(repo.query_entities(filter_query))
            else:
                # Query all entities and filter by PartitionKey pattern in memory
                # Azure Table Storage doesn't support startswith in OData queries
                all_entities = list(repo.query_entities())
                entities = [e for e in all_entities if e.get('PartitionKey', '').startswith('task_')]

            tasks = []
            for entity in entities:
                # Apply additional filters
                if status and entity.get('Status') != status:
                    continue
                if task_type and entity.get('TaskType') != task_type:
                    continue
                if priority and entity.get('Priority') != priority:
                    continue

                tasks.append({
                    'task_id': entity.get('RowKey'),  # TaskId is stored as RowKey
                    'org_id': entity.get('OrgId'),
                    'task_type': entity.get('TaskType'),
                    'status': entity.get('Status'),
                    'priority': entity.get('Priority'),
                    'created_at': entity.get('CreatedAt'),
                    'created_by': entity.get('CreatedBy'),
                    'started_at': entity.get('StartedAt'),
                    'completed_at': entity.get('CompletedAt'),
                    'error_message': entity.get('ErrorMessage'),
                    'data': _parse_params(entity.get('Params'))  # Params, not Data
                })

            return tasks

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data
            FROM App_Task WHERE 1=1
            """
            params = []

            if org_id is not None:
                query += " AND OrgId = ?"
                params.append(org_id)

            if status:
                query += " AND Status = ?"
                params.append(status)

            if task_type:
                query += " AND TaskType = ?"
                params.append(task_type)

            if priority:
                query += " AND Priority = ?"
                params.append(priority)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            tasks = []
            for row in results:
                tasks.append({
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {}
                })

            return tasks

    except Exception as e:
        logger.error(f"Error getting tasks: {str(e)}")
        return []


def update_task_status(task_id: str, status: str, error_message: Optional[str] = None,
                      progress: Optional[int] = None, message: Optional[str] = None) -> bool:
    """
    Update task status

    Args:
        task_id: Task ID
        status: New status
        error_message: Error message if status is 'Failed'
        progress: Progress percentage (0-100)
        message: Status message

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return False
            
            # Since we don't know the org_id, we need to query all entities and filter by RowKey
            # Azure Table Storage doesn't support startswith in OData queries
            entities = list(repo.query_entities())
            
            # Find the entity with matching RowKey (task_id)
            target_entity = None
            for entity in entities:
                if entity.get('RowKey') == task_id:
                    target_entity = entity
                    break
            
            if not target_entity:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Update status and timestamps
            target_entity['Status'] = status
            if status == 'Running' and not target_entity.get('StartedAt'):
                target_entity['StartedAt'] = datetime.now().isoformat()
            elif status in ['Completed', 'Failed']:
                target_entity['CompletedAt'] = datetime.now().isoformat()
            if error_message:
                target_entity['ErrorMessage'] = error_message
            if progress is not None:
                target_entity['Progress'] = progress
            if message:
                target_entity['Message'] = message
            
            return repo.update_entity(target_entity)
        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return False

            # Build update query
            update_fields = ["Status = ?"]
            params = [status]

            if status == 'Running':
                update_fields.append("StartedAt = CASE WHEN StartedAt IS NULL THEN ? ELSE StartedAt END")
                params.append(datetime.now())
            elif status in ['Completed', 'Failed']:
                update_fields.append("CompletedAt = ?")
                params.append(datetime.now())

            if error_message:
                update_fields.append("ErrorMessage = ?")
                params.append(error_message)

            if progress is not None:
                update_fields.append("Progress = ?")
                params.append(progress)

            if message:
                update_fields.append("Message = ?")
                params.append(message)

            params.append(task_id)
            query = f"UPDATE App_Task SET {', '.join(update_fields)} WHERE TaskId = ?"

            return repo.execute_non_query(query, tuple(params))

    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        return False


# Create blueprint
bp = func.Blueprint()

@bp.route(route="tasks", methods=["GET"])
def list_tasks(req: func.HttpRequest) -> func.HttpResponse:
    """List tasks with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        status = req.params.get('status')
        task_type = req.params.get('task_type')
        priority = req.params.get('priority')

        tasks = get_tasks(
            org_id=org_id,
            status=status,
            task_type=task_type,
            priority=priority
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": tasks
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks", methods=["POST"])
def create_task_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Create a new task"""
    try:
        # Parse request body
        try:
            task_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ['task_type', 'org_id']
        for field in required_fields:
            if field not in task_data:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Missing required field: {field}"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        # Get user ID from authentication or use provided value
        if 'user_id' not in task_data:
            user_id = get_user_from_request_or_default(req, "system")
            task_data['user_id'] = user_id
            logger.info(f"Creating task for user: {user_id}")

        # Create the task
        task_id = create_task(task_data)

        if task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "task_id": task_id
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create task"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/{task_id}", methods=["GET"])
def get_task_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get task by ID"""
    try:
        task_id = req.route_params.get('task_id')
        if not task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        task = get_task_by_id(task_id)

        if task:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": task
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting task: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/{task_id}/status", methods=["PUT"])
def update_task_status_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Update task status"""
    try:
        task_id = req.route_params.get('task_id')
        if not task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        try:
            update_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not update_data or 'status' not in update_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Status is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        success = update_task_status(
            task_id=task_id,
            status=update_data['status'],
            error_message=update_data.get('error_message'),
            progress=update_data.get('progress'),
            message=update_data.get('message')
        )

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Task status updated successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update task status"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/latest", methods=["GET"])
def get_latest_task_by_type_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get the latest task of a specific type for an organization"""
    try:
        org_id = req.params.get('org_id')
        task_type = req.params.get('task_type')
        status = req.params.get('status')
        execution_log_id = req.params.get('execution_log_id')

        if not org_id or not task_type:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and task_type are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get tasks for the organization
        tasks = get_tasks(org_id=org_id, task_type=task_type, status=status)
        
        if not tasks:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        # Filter by execution_log_id if provided
        if execution_log_id:
            tasks = [t for t in tasks if t.get('execution_log_id') == execution_log_id]
            if not tasks:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Task not found"
                    }),
                    mimetype="application/json",
                    status_code=404
                )

        # Sort by created_at descending and take the first one
        tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        latest_task = tasks[0]

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": latest_task
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting latest task by type: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )