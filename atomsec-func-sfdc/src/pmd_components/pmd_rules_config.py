"""
PMD Rules Configuration Module

This module handles the configuration of PMD rules for Salesforce Apex code analysis.
It supports both built-in PMD rules and custom rules for extensibility.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class PMDRulesConfig:
    """Configuration manager for PMD rules"""
    
    def __init__(self, custom_rules_dir: Optional[str] = None):
        """
        Initialize PMD rules configuration
        
        Args:
            custom_rules_dir: Directory containing custom PMD rules (optional)
        """
        self.custom_rules_dir = custom_rules_dir
        self.built_in_rulesets = self._get_built_in_rulesets()
        self.custom_rulesets = self._get_custom_rulesets()
    
    def _get_built_in_rulesets(self) -> List[str]:
        """Get all built-in PMD rulesets for Salesforce Apex"""
        return [
            # Security Rules
            "category/apex/security.xml",
            
            # Best Practices Rules
            "category/apex/bestpractices.xml",
            
            # Code Style Rules
            "category/apex/codestyle.xml",
            
            # Design Rules
            "category/apex/design.xml",
            
            # Documentation Rules
            "category/apex/documentation.xml",
            
            # Error Prone Rules
            "category/apex/errorprone.xml",
            
            # Performance Rules
            "category/apex/performance.xml"
        ]
    
    def _get_custom_rulesets(self) -> List[str]:
        """Get custom PMD rulesets from the custom rules directory"""
        custom_rulesets = []
        
        if not self.custom_rules_dir or not os.path.exists(self.custom_rules_dir):
            logger.info("No custom rules directory specified or directory doesn't exist")
            return custom_rulesets
        
        try:
            custom_dir = Path(self.custom_rules_dir)
            for xml_file in custom_dir.glob("*.xml"):
                if xml_file.is_file():
                    custom_rulesets.append(str(xml_file))
                    logger.info(f"Found custom ruleset: {xml_file}")
        except Exception as e:
            logger.warning(f"Error scanning custom rules directory: {e}")
        
        return custom_rulesets
    
    def _extract_custom_rule_names(self) -> List[str]:
        """
        Extract rule names from custom ruleset XML files
        
        Returns:
            List of custom rule names
        """
        custom_rule_names = []
        
        if not self.custom_rules_dir or not os.path.exists(self.custom_rules_dir):
            return custom_rule_names
        
        try:
            import xml.etree.ElementTree as ET
            
            custom_dir = Path(self.custom_rules_dir)
            for xml_file in custom_dir.glob("*.xml"):
                if xml_file.is_file():
                    try:
                        tree = ET.parse(str(xml_file))
                        root = tree.getroot()
                        
                        # Look for rule definitions in the XML
                        for rule in root.findall(".//rule"):
                            # Get rule name from name attribute or class attribute
                            rule_name = rule.get("name") or rule.get("class")
                            if rule_name:
                                # Extract just the rule name without package
                                if "." in rule_name:
                                    rule_name = rule_name.split(".")[-1]
                                custom_rule_names.append(rule_name)
                                logger.info(f"Found custom rule: {rule_name} in {xml_file}")
                    except Exception as e:
                        logger.warning(f"Error parsing custom ruleset {xml_file}: {e}")
        except Exception as e:
            logger.warning(f"Error extracting custom rule names: {e}")
        
        return custom_rule_names
    
    def get_all_rulesets(self) -> List[str]:
        """Get all rulesets (built-in + custom)"""
        all_rulesets = self.built_in_rulesets.copy()
        all_rulesets.extend(self.custom_rulesets)
        return all_rulesets
    
    def get_rulesets_by_category(self, categories: List[str], enabled_rules: Optional[List[str]] = None) -> List[str]:
        """
        Get rulesets filtered by category and optionally by enabled individual rules
        
        Args:
            categories: List of categories to include (e.g., ['security', 'performance'])
            enabled_rules: Optional list of enabled individual rule names
        
        Returns:
            List of ruleset paths matching the specified categories
        """
        if not categories:
            return self.get_all_rulesets()
        
        filtered_rulesets = []
        category_lower = [cat.lower() for cat in categories]
        
        for ruleset in self.built_in_rulesets:
            if any(cat in ruleset.lower() for cat in category_lower):
                filtered_rulesets.append(ruleset)
        
        # Always include custom rulesets
        filtered_rulesets.extend(self.custom_rulesets)
        
        return filtered_rulesets
    
    def get_ruleset_string(self, categories: Optional[List[str]] = None, enabled_rules: Optional[List[str]] = None) -> str:
        """
        Get rulesets as a comma-separated string for PMD command line
        
        Args:
            categories: Optional list of categories to filter by
            enabled_rules: Optional list of enabled individual rule names
        
        Returns:
            Comma-separated string of ruleset paths
        """
        if categories:
            rulesets = self.get_rulesets_by_category(categories, enabled_rules)
        else:
            rulesets = self.get_all_rulesets()
        
        return ",".join(rulesets)
    
    def get_ruleset_info(self) -> Dict[str, Any]:
        """
        Get information about all configured rulesets
        
        Returns:
            Dictionary containing ruleset information
        """
        return {
            "built_in_count": len(self.built_in_rulesets),
            "custom_count": len(self.custom_rulesets),
            "total_count": len(self.built_in_rulesets) + len(self.custom_rulesets),
            "built_in_rulesets": self.built_in_rulesets,
            "custom_rulesets": self.custom_rulesets,
            "custom_rules_dir": self.custom_rules_dir
        }
    
    def create_custom_ruleset_with_enabled_rules(self, enabled_rules: List[str], output_file: str) -> str:
        """
        Create a custom ruleset XML file with only the enabled individual rules
        
        Args:
            enabled_rules: List of enabled individual rule names
            output_file: Path to output XML file
        
        Returns:
            Path to the created ruleset file
        """
        import xml.etree.ElementTree as ET
        
        # Get built-in valid rules and custom rules
        valid_builtin_rules = self._get_valid_pmd_rules()
        custom_rules = self._extract_custom_rule_names()
        
        # Combine built-in and custom rules
        all_valid_rules = valid_builtin_rules + custom_rules
        
        # Filter out rules that don't exist in our known valid rules
        filtered_rules = [rule for rule in enabled_rules if rule in all_valid_rules]
        
        # Log which rules were filtered out
        filtered_out = set(enabled_rules) - set(filtered_rules)
        if filtered_out:
            logger.warning(f"Filtered out {len(filtered_out)} invalid PMD rules: {', '.join(filtered_out)}")
        
        if not filtered_rules:
            logger.warning("No valid PMD rules found after filtering. Using security category as fallback.")
            # Create a category-based ruleset as fallback
            ruleset = ET.Element("ruleset")
            ruleset.set("name", "Fallback PMD Ruleset")
            ruleset.set("xmlns", "http://pmd.sourceforge.net/ruleset/2.0.0")
            ruleset.set("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance")
            ruleset.set("xsi:schemaLocation", "http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.net/ruleset_2_0_0.xsd")
            
            # Add description
            description = ET.SubElement(ruleset, "description")
            description.text = "Fallback PMD ruleset using security category"
            
            # Add security category reference
            rule = ET.SubElement(ruleset, "rule")
            rule.set("ref", "category/apex/security.xml")
            
            # Write to file
            tree = ET.ElementTree(ruleset)
            tree.write(output_file, encoding="utf-8", xml_declaration=True)
            
            logger.info(f"Created fallback ruleset with security category: {output_file}")
            return output_file
        
        # Create ruleset XML structure
        ruleset = ET.Element("ruleset")
        ruleset.set("name", "Custom PMD Ruleset")
        ruleset.set("xmlns", "http://pmd.sourceforge.net/ruleset/2.0.0")
        ruleset.set("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance")
        ruleset.set("xsi:schemaLocation", "http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.net/ruleset_2_0_0.xsd")
        
        # Add description
        description = ET.SubElement(ruleset, "description")
        description.text = f"Custom PMD ruleset with {len(filtered_rules)} enabled rules"
        
        # Add rules
        for rule_name in filtered_rules:
            rule = ET.SubElement(ruleset, "rule")
            rule.set("ref", f"category/apex/{self._get_category_for_rule(rule_name)}.xml/{rule_name}")
        
        # Write to file
        tree = ET.ElementTree(ruleset)
        tree.write(output_file, encoding="utf-8", xml_declaration=True)
        
        logger.info(f"Created custom ruleset with {len(filtered_rules)} rules: {output_file}")
        return output_file
    
    def _get_valid_pmd_rules(self) -> List[str]:
        """
        Get a list of valid PMD rules for Apex
        
        Returns:
            List of valid rule names
        """
        # This list contains only rules that are confirmed to exist in PMD 7.14.0
        # It's based on the rule_categories dictionary but excludes rules that caused errors
        return [
            # Security rules
            "ApexBadCrypto",
            "ApexCRUDViolation",
            "ApexDangerousMethods",
            "ApexInsecureEndpoint",
            "ApexOpenRedirect",
            "ApexSharingViolations",
            "ApexSOQLInjection",
            "ApexSuggestUsingNamedCred",
            "ApexXSSFromEscapeFalse",
            "ApexXSSFromURLParam",
            
            # Best practices rules
            "UnusedLocalVariable",
            "AvoidGlobalModifier",
            "ApexUnitTestClassShouldHaveAsserts",
            "ApexAssertionsShouldIncludeMessage",
            "DebugsShouldUseLoggingLevel",
            "ApexUnitTestClassShouldHaveRunAs",
            "ApexUnitTestShouldNotUseSeeAllDataTrue",
            "ApexUnitTestMethodShouldHaveIsTestAnnotation",
            "AvoidLogicInTrigger",
            "QueueableWithoutFinalizer",
            
            # Code style rules
            "PropertyNamingConventions",
            "MethodNamingConventions",
            "LocalVariableNamingConventions",
            "FieldNamingConventions",
            "ClassNamingConventions",
            
            # Design rules
            "ExcessiveClassLength",
            "TooManyFields",
            "ExcessiveParameterList",
            "ExcessivePublicCount",
            
            # Performance rules
            "EagerlyLoadedDescribeSObjectResult",
            "AvoidNonRestrictiveQueries",
            "OperationWithHighCostInLoop",
            "OperationWithLimitsInLoop"
        ]
    
    def _get_category_for_rule(self, rule_name: str) -> str:
        """
        Get the PMD category for a given rule name
        
        Args:
            rule_name: Name of the PMD rule
            
        Returns:
            Category name (e.g., 'security', 'performance')
        """
        # Map rule names to categories - using only rules that exist in PMD 7.14.0
        rule_categories = {
            # Security rules
            "ApexBadCrypto": "security",
            "ApexCRUDViolation": "security",
            "ApexDangerousMethods": "security",
            "ApexInsecureEndpoint": "security",
            "ApexOpenRedirect": "security",
            "ApexSharingViolations": "security",
            "ApexSOQLInjection": "security",
            "ApexSuggestUsingNamedCred": "security",
            "ApexXSSFromEscapeFalse": "security",
            "ApexXSSFromURLParam": "security",
            
            # Best practices rules
            "UnusedLocalVariable": "bestpractices",
            "AvoidGlobalModifier": "bestpractices",
            "ApexUnitTestClassShouldHaveAsserts": "bestpractices",
            "ApexAssertionsShouldIncludeMessage": "bestpractices",
            "DebugsShouldUseLoggingLevel": "bestpractices",
            "ApexUnitTestClassShouldHaveRunAs": "bestpractices",
            "ApexUnitTestShouldNotUseSeeAllDataTrue": "bestpractices",
            "ApexUnitTestMethodShouldHaveIsTestAnnotation": "bestpractices",
            "AvoidLogicInTrigger": "bestpractices",
            "QueueableWithoutFinalizer": "bestpractices",
            
            # Code style rules
            "PropertyNamingConventions": "codestyle",
            "MethodNamingConventions": "codestyle",
            "LocalVariableNamingConventions": "codestyle",
            "FieldNamingConventions": "codestyle",
            "ClassNamingConventions": "codestyle",
            
            # Design rules
            "ExcessiveClassLength": "design",
            "TooManyFields": "design",
            "ExcessiveParameterList": "design",
            "ExcessivePublicCount": "design",
            
            # Performance rules
            "EagerlyLoadedDescribeSObjectResult": "performance",
            "AvoidNonRestrictiveQueries": "performance",
            "OperationWithHighCostInLoop": "performance",
            "OperationWithLimitsInLoop": "performance"
        }
        
        return rule_categories.get(rule_name, "bestpractices")  # Default to bestpractices