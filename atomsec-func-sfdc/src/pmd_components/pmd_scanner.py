"""
PMD Scanner Module

This module handles PMD scanning operations for Salesforce Apex code analysis.
It includes environment detection, command execution, and result parsing.
"""

import logging
import os
import platform
import subprocess
import tempfile
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .pmd_rules_config import PMDRulesConfig

logger = logging.getLogger(__name__)

class PMDScanner:
    """PMD scanner for Salesforce Apex code analysis"""
    
    def __init__(self, custom_rules_dir: Optional[str] = None):
        """
        Initialize PMD scanner
        
        Args:
            custom_rules_dir: Directory containing custom PMD rules (optional)
        """
        self.rules_config = PMDRulesConfig(custom_rules_dir)
        self.is_local = self._is_running_locally()
    
    def _is_running_locally(self) -> bool:
        """Determine if the code is running locally or in Azure"""
        if os.environ.get('WEBSITE_INSTANCE_ID'):
            return False
        return True
    
    def _get_pmd_command(self) -> List[str]:
        """Get the appropriate PMD command based on environment"""
        if self.is_local:
            # Local environment - use installed PMD
            if platform.system() == "Windows":
                cmd = ["pmd.bat"]
            else:
                cmd = ["pmd"]
            
            logger.info("Using local PMD installation")
            return cmd
        else:
            # Production Azure environment - use PMD installed in the function app
            logger.info("Using Azure-deployed PMD installation")
            return ["/opt/pmd/bin/run.sh", "pmd"]
    
    def scan_directory(self, 
                      source_dir: str, 
                      output_file: str,
                      categories: Optional[List[str]] = None,
                      temp_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan a directory of Apex classes using PMD
        
        Args:
            source_dir: Directory containing Apex classes to scan
            output_file: Path to output CSV file
            categories: Optional list of rule categories to include
            temp_dir: Optional temporary directory for PMD output
        
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of directory: {source_dir}")
        
        # Ensure source directory exists
        if not os.path.exists(source_dir):
            error_msg = f"Source directory does not exist: {source_dir}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Count Apex files
        apex_files = self._count_apex_files(source_dir)
        if apex_files == 0:
            logger.warning(f"No Apex files found in directory: {source_dir}")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Apex files found to scan"
            }
        
        # Get rulesets
        ruleset_string = self.rules_config.get_ruleset_string(categories)
        
        # Build PMD command
        base_cmd = self._get_pmd_command()
        pmd_command = base_cmd + [
            'check',
            '-d', source_dir,
            '-R', ruleset_string,
            '-f', 'csv',
            '--report-file', output_file,
            '--debug'  # Add debug flag for detailed error information
        ]
        
        # Log command for debugging
        env_type = "local" if self.is_local else "Azure"
        logger.info(f"Running in {env_type} environment")
        logger.info(f"PMD command: {' '.join(pmd_command)}")
        
        # Execute PMD
        try:
            result = subprocess.run(
                pmd_command, 
                capture_output=True, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # PMD exits with 4 if violations are found (this is normal)
            # PMD exits with 5 if errors occurred but may still have partial results
            if result.returncode not in [0, 4, 5]:
                error_msg = f"PMD execution failed with return code {result.returncode}. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # Check if we have results file even if PMD had errors
            if result.returncode == 5:
                logger.warning(f"PMD completed with errors (return code 5), but checking for partial results")
                if result.stderr:
                    logger.warning(f"PMD stderr: {result.stderr}")
            
            logger.info(f"PMD execution completed with return code {result.returncode}")
            if result.stdout:
                logger.debug(f"PMD stdout: {result.stdout}")
            if result.stderr and result.returncode != 5:  # Don't log stderr again for return code 5
                logger.warning(f"PMD stderr: {result.stderr}")
            
            # Parse results - even if PMD had errors, we might have partial results
            findings = self._parse_pmd_results(output_file)
            
            # If PMD had errors but we got no findings, consider it a failure
            if result.returncode == 5 and len(findings) == 0:
                error_msg = f"PMD execution completed with errors and no findings generated. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # If we have findings, consider it a success (even with PMD errors)
            success = result.returncode in [0, 4] or (result.returncode == 5 and len(findings) > 0)
            
            return {
                "success": success,
                "findings_count": len(findings),
                "files_scanned": apex_files,
                "findings": findings,
                "rulesets_used": self.rules_config.get_ruleset_info(),
                "scan_timestamp": datetime.now().isoformat(),
                "pmd_return_code": result.returncode,
                "pmd_errors": result.stderr if result.returncode == 5 else None
            }
            
        except subprocess.TimeoutExpired:
            error_msg = "PMD scan timed out after 5 minutes"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        except Exception as e:
            error_msg = f"Error during PMD scan: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
    
    def scan_directory_with_ruleset(self, 
                                   source_dir: str, 
                                   output_file: str,
                                   ruleset_file: str,
                                   temp_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan a directory of Apex classes using PMD with a custom ruleset file
        
        Args:
            source_dir: Directory containing Apex classes to scan
            output_file: Path to output CSV file
            ruleset_file: Path to custom ruleset XML file
            temp_dir: Optional temporary directory for PMD output
        
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of directory: {source_dir} with custom ruleset: {ruleset_file}")
        
        # Ensure source directory exists
        if not os.path.exists(source_dir):
            error_msg = f"Source directory does not exist: {source_dir}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Ensure ruleset file exists
        if not os.path.exists(ruleset_file):
            error_msg = f"Custom ruleset file does not exist: {ruleset_file}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Count Apex files
        apex_files = self._count_apex_files(source_dir)
        if apex_files == 0:
            logger.warning(f"No Apex files found in directory: {source_dir}")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Apex files found to scan"
            }
        
        # Build PMD command with custom ruleset
        base_cmd = self._get_pmd_command()
        pmd_command = base_cmd + [
            'check',
            '-d', source_dir,
            '-R', ruleset_file,
            '-f', 'csv',
            '--report-file', output_file,
            '--debug'  # Add debug flag for detailed error information
        ]
        
        # Log command for debugging
        env_type = "local" if self.is_local else "Azure"
        logger.info(f"Running in {env_type} environment")
        logger.info(f"PMD command with custom ruleset: {' '.join(pmd_command)}")
        
        # Execute PMD
        try:
            result = subprocess.run(
                pmd_command, 
                capture_output=True, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # PMD exits with 4 if violations are found (this is normal)
            # PMD exits with 5 if errors occurred but may still have partial results
            if result.returncode not in [0, 4, 5]:
                error_msg = f"PMD execution failed with return code {result.returncode}. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # Check if we have results file even if PMD had errors
            if result.returncode == 5:
                logger.warning(f"PMD completed with errors (return code 5), but checking for partial results")
                if result.stderr:
                    logger.warning(f"PMD stderr: {result.stderr}")
            
            logger.info(f"PMD execution completed with return code {result.returncode}")
            if result.stdout:
                logger.debug(f"PMD stdout: {result.stdout}")
            if result.stderr and result.returncode != 5:  # Don't log stderr again for return code 5
                logger.warning(f"PMD stderr: {result.stderr}")
            
            # Parse results - even if PMD had errors, we might have partial results
            findings = self._parse_pmd_results(output_file)
            
            # If PMD had errors but we got no findings, consider it a failure
            if result.returncode == 5 and len(findings) == 0:
                error_msg = f"PMD execution completed with errors and no findings generated. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # If we have findings, consider it a success (even with PMD errors)
            success = result.returncode in [0, 4] or (result.returncode == 5 and len(findings) > 0)
            
            return {
                "success": success,
                "findings_count": len(findings),
                "files_scanned": apex_files,
                "findings": findings,
                "custom_ruleset_file": ruleset_file,
                "scan_timestamp": datetime.now().isoformat(),
                "pmd_return_code": result.returncode,
                "pmd_errors": result.stderr if result.returncode == 5 else None
            }
            
        except subprocess.TimeoutExpired:
            error_msg = "PMD scan timed out after 5 minutes"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        except Exception as e:
            error_msg = f"Error during PMD scan: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
    
    def _count_apex_files(self, directory: str) -> int:
        """Count the number of Apex files in a directory"""
        count = 0
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.cls'):
                    count += 1
        return count
    
    def _parse_pmd_results(self, csv_file: str) -> List[Dict[str, Any]]:
        """
        Parse PMD CSV results into structured findings
        
        Args:
            csv_file: Path to PMD CSV output file
        
        Returns:
            List of finding dictionaries
        """
        findings = []
        
        if not os.path.exists(csv_file):
            logger.warning(f"PMD results file not found: {csv_file}")
            return findings
        
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                # Skip header line
                next(f, None)
                
                for line_num, line in enumerate(f, 1):
                    try:
                        finding = self._parse_csv_line(line.strip(), line_num)
                        if finding:
                            findings.append(finding)
                    except Exception as e:
                        logger.warning(f"Error parsing line {line_num}: {e}")
                        continue
            
            logger.info(f"Parsed {len(findings)} findings from PMD results")
            
        except Exception as e:
            logger.error(f"Error reading PMD results file: {e}")
        
        return findings
    
    def _parse_csv_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """
        Parse a single CSV line from PMD output
        
        Args:
            line: CSV line to parse
            line_num: Line number for error reporting
        
        Returns:
            Parsed finding dictionary or None if parsing fails
        """
        if not line:
            return None
        
        try:
            # Split by comma, but handle quoted fields
            parts = self._split_csv_line(line)
            
            if len(parts) < 6:
                logger.warning(f"Invalid CSV line {line_num}: insufficient fields")
                return None
            
            # Extract fields (PMD CSV format: Problem,Package,File,Priority,Line,Description,Rule set,Rule)
            problem = parts[0].strip('"') if len(parts) > 0 else ""
            package = parts[1].strip('"') if len(parts) > 1 else ""
            file_path = parts[2].strip('"') if len(parts) > 2 else ""
            priority = parts[3].strip('"') if len(parts) > 3 else ""
            line_number = parts[4].strip('"') if len(parts) > 4 else ""
            description = parts[5].strip('"') if len(parts) > 5 else ""
            rule_set = parts[6].strip('"') if len(parts) > 6 else ""
            rule_name = parts[7].strip('"') if len(parts) > 7 else ""
            
            # Map PMD priority to standardized severity
            severity = self._map_priority_to_severity(priority)
            
            # Extract category and clean rule name
            category, clean_rule_name = self._extract_category_and_rule(rule_name)
            
            # Get file name from path
            file_name = os.path.basename(file_path) if file_path else "Unknown"
            
            return {
                'problem': problem,
                'package': package,
                'file_path': file_path,
                'file_name': file_name,
                'priority': priority,
                'line_number': line_number,
                'description': description,
                'rule_set': rule_set,
                'rule_name': clean_rule_name,
                'category': category,
                'severity': severity,
                'status': 'failed'  # Default status
            }
            
        except Exception as e:
            logger.warning(f"Error parsing CSV line {line_num}: {e}")
            return None
    
    def _split_csv_line(self, line: str) -> List[str]:
        """Split CSV line while handling quoted fields"""
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part)
                current_part = ""
            else:
                current_part += char
        
        parts.append(current_part)
        return parts
    
    def _map_priority_to_severity(self, priority: str) -> str:
        """Map PMD priority to standardized severity levels"""
        try:
            priority_num = int(priority)
            if priority_num <= 2:
                return "High"
            elif priority_num == 3:
                return "Medium"
            else:
                return "Low"
        except (ValueError, TypeError):
            return "Medium"  # Default severity
    
    def _extract_category_and_rule(self, rule_name: str) -> Tuple[str, str]:
        """Extract category and clean rule name from PMD rule name"""
        if ":" in rule_name:
            parts = rule_name.split(":", 1)
            if len(parts) > 1:
                return parts[0].strip(), parts[1].strip()
        
        # Default category if no separator found
        return "Code Quality", rule_name
    
    def get_scanner_info(self) -> Dict[str, Any]:
        """Get information about the scanner configuration"""
        return {
            "environment": "local" if self.is_local else "Azure",
            "pmd_command": self._get_pmd_command(),
            "rules_config": self.rules_config.get_ruleset_info()
        } 