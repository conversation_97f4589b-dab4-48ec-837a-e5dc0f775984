"""
CORS Handler Blueprint

This module provides a global handler for OPTIONS requests to support CORS.
"""

import logging
import azure.functions as func

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

@bp.route(route="{*route}", methods=["OPTIONS"])
def options_handler(req: func.HttpRequest) -> func.HttpResponse:
    """
    Global handler for OPTIONS requests to support CORS

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with CORS headers
    """
    logger.info('Processing OPTIONS request...')

    # Define allowed origins
    allowed_origins = ["http://localhost:3000", "http://localhost:7071", "https://app-atomsec-dev01.azurewebsites.net", "https://login.windows.net", "https://login.microsoftonline.com"]

    # Get the origin from the request
    origin = req.headers.get("Origin", "")

    # Return CORS headers
    return func.HttpResponse(
        status_code=204,
        headers={
            "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Origin, Accept",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400"
        }
    )
