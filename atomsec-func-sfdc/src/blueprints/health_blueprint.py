"""
Health Blueprint for AtomSec SFDC Service

This blueprint handles health-related operations including:
- Health checks
- Metrics
- Status monitoring
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
bp = APIRouter()

@bp.get("/check")
async def health_check():
    """Health check endpoint"""
    try:
        return {
            "status": "healthy",
            "service": "atomsec-func-sfdc",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@bp.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    try:
        return {
            "service": "atomsec-func-sfdc",
            "metrics": {
                "uptime": "running",
                "version": "1.0.0"
            }
        }
    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@bp.get("/status")
async def get_status():
    """Get service status"""
    try:
        return {
            "status": "operational",
            "service": "atomsec-func-sfdc",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}") 