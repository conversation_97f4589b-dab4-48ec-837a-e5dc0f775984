"""
Utilities Blueprint for AtomSec SFDC Service

This blueprint handles utility operations including:
- Tools
- Helpers
- Validators
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
bp = APIRouter()

@bp.get("/tools")
async def get_tools():
    """Get available tools"""
    try:
        return {
            "tools": [
                "salesforce-connector",
                "security-scanner",
                "data-validator"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting tools: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get tools: {str(e)}")

@bp.get("/helpers")
async def get_helpers():
    """Get available helpers"""
    try:
        return {
            "helpers": [
                "connection-validator",
                "data-formatter",
                "error-handler"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting helpers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get helpers: {str(e)}")

@bp.get("/validators")
async def get_validators():
    """Get available validators"""
    try:
        return {
            "validators": [
                "salesforce-url-validator",
                "credential-validator",
                "permission-validator"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting validators: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get validators: {str(e)}") 