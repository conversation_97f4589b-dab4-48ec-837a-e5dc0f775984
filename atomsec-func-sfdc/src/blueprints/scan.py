"""
Scan Results Blueprint

This module provides endpoints for retrieving scan results:
- Fetch accounts with health check scores
- Fetch scan details
- Fetch scan history

Best practices implemented:
- Proper error handling and logging
- Input validation
- Centralized configuration
- Reusable utility functions
"""

import logging
import azure.functions as func
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional

# Import shared modules
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.azure_services import is_local_dev
from src.shared.utils import create_json_response, handle_exception
from src.shared.auth_utils import get_current_user

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_scan_table_repo = None
_scan_sql_repo = None

def get_scan_table_repo():
    """Lazy initialize the scan table repository"""
    global _scan_table_repo
    if _scan_table_repo is None:
        try:
            _scan_table_repo = TableStorageRepository(table_name="Scans")
            logger.info("Initialized scan table repository")
        except Exception as e:
            logger.error(f"Failed to initialize scan table repository: {str(e)}")
            _scan_table_repo = None
    return _scan_table_repo

def get_scan_sql_repo():
    """Lazy initialize the scan SQL repository"""
    global _scan_sql_repo
    if _scan_sql_repo is None and not is_local_dev():
        try:
            _scan_sql_repo = SqlDatabaseRepository(table_name="App_Scan")
            logger.info("Initialized scan SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize scan SQL repository: {str(e)}")
            _scan_sql_repo = None
    return _scan_sql_repo

def get_scan_results(scan_name: str = None, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get scan results

    Args:
        scan_name: Filter by scan name
        limit: Maximum number of results to return

    Returns:
        List[Dict[str, Any]]: Scan results
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            scan_repo = get_scan_table_repo()
            if not scan_repo:
                logger.error("Scan table repository not available")
                return []

            # Build filter query
            filter_query = f"PartitionKey eq 'scan_result'"
            if scan_name:
                filter_query += f" and ScanName eq '{scan_name}'"

            # Query scan results
            entities = scan_repo.query_entities(filter_query, top=limit)

            # Convert to list of dictionaries
            results = []
            for entity in entities:
                result = {
                    "id": entity.get("RowKey"),
                    "scanName": entity.get("ScanName", ""),
                    "accountName": entity.get("AccountName", ""),
                    "instanceUrl": entity.get("InstanceUrl", ""),
                    "healthScore": entity.get("HealthScore", 0),
                    "scanDate": entity.get("ScanDate", ""),
                    "highRisks": entity.get("HighRisks", 0),
                    "mediumRisks": entity.get("MediumRisks", 0),
                    "lowRisks": entity.get("LowRisks", 0)
                }
                results.append(result)

            return results
        else:
            # Use SQL Database for production
            scan_repo = get_scan_sql_repo()
            if not scan_repo:
                logger.error("Scan SQL repository not available")
                return []

            # Build SQL query
            query = """
            SELECT Id, ScanName, AccountName, InstanceUrl, HealthScore, ScanDate, HighRisks, MediumRisks, LowRisks
            FROM App_ScanResult
            """

            where_clauses = []
            params = []

            if scan_name:
                where_clauses.append("ScanName = ?")
                params.append(scan_name)

            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            query += f" ORDER BY ScanDate DESC"

            # Execute query
            results = scan_repo.execute_query(query, tuple(params) if params else None)

            # Convert to list of dictionaries
            scan_results = []
            for row in results:
                result = {
                    "id": row[0],
                    "scanName": row[1],
                    "accountName": row[2],
                    "instanceUrl": row[3],
                    "healthScore": row[4],
                    "scanDate": row[5],
                    "highRisks": row[6],
                    "mediumRisks": row[7],
                    "lowRisks": row[8]
                }
                scan_results.append(result)

            return scan_results
    except Exception as e:
        logger.error(f"Error getting scan results: {str(e)}")
        return []

def get_mock_scan_results(scan_name: str = None, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get mock scan results when no real data is available

    Args:
        scan_name: Filter by scan name
        limit: Maximum number of results to return

    Returns:
        List[Dict[str, Any]]: Mock scan results
    """
    # Generate mock data
    results = []
    for i in range(min(limit, 5)):
        result = {
            "id": f"scan_{i}",
            "scanName": scan_name or "Weekly Security Scan",
            "accountName": f"Salesforce Org {i+1}",
            "instanceUrl": f"https://org{i+1}.my.salesforce.com",
            "healthScore": 85 - (i * 5),
            "scanDate": (datetime.now() - timedelta(days=i)).isoformat(),
            "highRisks": i,
            "mediumRisks": i * 2,
            "lowRisks": i * 3
        }
        results.append(result)

    return results

# Note: The scan_accounts function has been removed as it is no longer used.
# This function was used to get accounts with health check scores, but this functionality
# is now provided by the integration_tabs_bp blueprint.

# Note: The scan_history function has been removed as it is no longer used.
# This function was used to get scan history, but this functionality
# is now provided by the integration_tabs_bp blueprint.
