"""
Account Management Blueprint

This module provides endpoints for managing accounts and users:
- Create, read, update accounts
- Add users to accounts
- Assign roles to users

Best practices implemented:
- Proper error handling and logging
- Input validation
- Centralized configuration
- Reusable utility functions
"""

import logging
import azure.functions as func
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

# Import shared modules
from src.shared.config import is_local_dev
from src.shared.utils import create_json_response, handle_exception
from src.shared.cors_middleware import cors_middleware
from src.shared.auth_utils import require_auth, get_current_user
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.user_repository import create_user, get_user_account_by_id, get_user_account_table_repo

# Create blueprint
bp = func.Blueprint()

# Configure logging
logger = logging.getLogger('account_management')
logger.setLevel(logging.INFO)

# Get repositories
def get_account_table_repo() -> Optional[TableStorageRepository]:
    """Get account table repository for local development"""
    try:
        from src.shared.data_access import get_table_storage_repository
        return get_table_storage_repository('accounts')
    except Exception as e:
        logger.error(f"Error creating account table repository: {str(e)}")
        return None

def get_account_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get account SQL repository for production"""
    try:
        from src.shared.data_access import get_sql_database_repository
        return get_sql_database_repository()
    except Exception as e:
        logger.error(f"Error creating account SQL repository: {str(e)}")
        return None

@bp.route(route="account-management/accounts", methods=["GET"])
@require_auth
def get_accounts(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get all accounts endpoint

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with accounts
    """
    logger.info('Processing get accounts request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get query parameters
        include_inactive = req.params.get("includeInactive", "false").lower() == "true"

        # Get accounts
        accounts = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_account_table_repo()
            if not account_repo:
                logger.error("Account table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get accounts", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Query accounts
            filter_query = None
            if not include_inactive:
                filter_query = "IsActive eq true"

            entities = account_repo.query_entities(filter_query)

            # Convert entities to accounts
            for entity in entities:
                account = {
                    "ID": entity.get("RowKey"),
                    "Name": entity.get("Name", ""),
                    "CreatedAt": entity.get("CreatedAt", ""),
                    "IsActive": entity.get("IsActive", True)
                }
                accounts.append(account)
        else:
            # Use SQL Database for production
            account_repo = get_account_sql_repo()
            if not account_repo:
                logger.error("Account SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get accounts", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Query accounts
            query = "SELECT ID, Name, CreatedAt, IsActive FROM App_Account"
            if not include_inactive:
                query += " WHERE IsActive = 1"

            results = account_repo.execute_query(query)

            # Convert results to accounts
            for result in results:
                account = {
                    "ID": result[0],
                    "Name": result[1],
                    "CreatedAt": result[2].isoformat() if result[2] else "",
                    "IsActive": bool(result[3])
                }
                accounts.append(account)

        # Return accounts
        response = func.HttpResponse(
            json.dumps(create_json_response({"data": accounts})),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "get_accounts")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="account-management/accounts", methods=["POST"])
@require_auth
def create_account(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create account endpoint

    Args:
        req: HTTP request with account data

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing create account request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        name = req_body.get("name")
        if not name:
            return func.HttpResponse(
                json.dumps(create_json_response("Name is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Create account
        account_id = None
        created_at = datetime.utcnow()

        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_account_table_repo()
            if not account_repo:
                logger.error("Account table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to create account", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Generate account ID
            import random
            account_id = str(random.randint(1000, 9999))

            # Create entity
            entity = {
                "PartitionKey": "account",
                "RowKey": account_id,
                "Name": name,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            # Insert entity
            success = account_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert account entity")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to create account", 500)),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            # Use SQL Database for production
            account_repo = get_account_sql_repo()
            if not account_repo:
                logger.error("Account SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to create account", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Insert account
            query = """
            INSERT INTO App_Account (Name, CreatedAt, IsActive)
            VALUES (?, ?, ?);
            SELECT SCOPE_IDENTITY();
            """
            params = (name, created_at, True)

            # Execute query and get inserted ID
            results = account_repo.execute_query(query, params)
            if not results:
                logger.error("Failed to insert account")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to create account", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            account_id = results[0][0]

        # Return created account
        account = {
            "ID": account_id,
            "Name": name,
            "CreatedAt": created_at.isoformat(),
            "IsActive": True
        }

        response = func.HttpResponse(
            json.dumps(create_json_response({"data": account})),
            mimetype="application/json",
            status_code=201
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "create_account")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="account-management/accounts/{account_id}/users", methods=["GET"])
@require_auth
def get_users(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get users for an account endpoint
    """
    logger.info('Processing get users request...')

    try:
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get account ID from route parameters
        account_id = None
        if hasattr(req, 'route_params') and req.route_params:
            account_id = req.route_params.get("account_id")

        if not account_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Account ID is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        users = []
        if is_local_dev():
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get users", 500)),
                    mimetype="application/json",
                    status_code=500
                )
            # Query users by AccountId
            entities = user_repo.query_entities(f"AccountId eq '{account_id}'")
            for entity in entities:
                user = {
                    "UserId": entity.get("UserId"),
                    "FirstName": entity.get("FirstName", ""),
                    "LastName": entity.get("LastName", ""),
                    "Email": entity.get("Email", ""),
                    "Contact": entity.get("Contact", ""),
                    "State": entity.get("State", ""),
                    "Country": entity.get("Country", ""),
                    "Organization": entity.get("Organization", "")
                }
                users.append(user)
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get users", 500)),
                    mimetype="application/json",
                    status_code=500
                )
            query = "SELECT * FROM User_Account WHERE AccountId = ?"
            params = (account_id,)
            results = user_repo.execute_query(query, params)
            for row in results:
                user = {
                    "UserId": row[0],
                    "FirstName": row[1],
                    "LastName": row[3],
                    "Email": row[5],
                    "Contact": row[6],
                    "State": row[7],
                    "Country": row[8],
                    "Organization": row[9] if len(row) > 9 else ""
                }
                users.append(user)

        response = func.HttpResponse(
            json.dumps(create_json_response({"data": users})),
            mimetype="application/json",
            status_code=200
        )
        return response
    except Exception as e:
        error_response = handle_exception(e, "get_users")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )
        return response

@bp.route(route="account-management/users", methods=["POST"])
@require_auth
def create_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create user endpoint
    """
    logger.info('Processing create user request...')

    try:
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        email = req_body.get("email")
        password = req_body.get("password")
        first_name = req_body.get("firstName", "")
        middle_name = req_body.get("middleName", "")
        last_name = req_body.get("lastName", "")
        dob = req_body.get("dob")
        contact = req_body.get("contact")
        state = req_body.get("state", "")
        country = req_body.get("country", "")
        organization = req_body.get("organization", "")

        if not email or not password or not first_name or not last_name:
            return func.HttpResponse(
                json.dumps(create_json_response("Missing required fields", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Create user (account and login)
        user_id = create_user(
            email=email,
            password=password,
            first_name=first_name,
            middle_name=middle_name,
            last_name=last_name,
            dob=dob,
            contact=contact,
            state=state,
            country=country,
            organization=organization
        )

        if not user_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to create user", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Fetch user info for response
        user_account = get_user_account_by_id(user_id)
        user_info = {
            "UserId": user_account.UserId,
            "FirstName": user_account.FirstName,
            "LastName": user_account.LastName,
            "Email": user_account.Email,
            "Contact": user_account.Contact,
            "State": user_account.State,
            "Country": user_account.Country,
            "Organization": user_account.Organization
        }

        response = func.HttpResponse(
            json.dumps(create_json_response({"data": user_info})),
            mimetype="application/json",
            status_code=201
        )
        return response
    except Exception as e:
        error_response = handle_exception(e, "create_user")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )
        return response

@bp.route(route="account-management/users/{user_id}/roles", methods=["POST"])
@require_auth
def assign_user_role(req: func.HttpRequest) -> func.HttpResponse:
    """
    Assign role to user endpoint

    Args:
        req: HTTP request with user ID and role ID

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing assign user role request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get user ID from route parameters
        user_id = None
        if hasattr(req, 'route_params') and req.route_params:
            user_id = req.route_params.get("user_id")

        # If not found in route_params, try to extract from URL
        if not user_id and req.url:
            # Extract user_id from URL path
            url_path = req.url.split('?')[0]
            path_parts = url_path.split('/')
            for i, part in enumerate(path_parts):
                if part == "users" and i + 1 < len(path_parts) and path_parts[i+2] == "roles":
                    user_id = path_parts[i+1]
                    break

        if not user_id:
            return func.HttpResponse(
                json.dumps(create_json_response("User ID is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        role_id = req_body.get("roleId")
        if not role_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Role ID is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Assign role to user
        if is_local_dev():
            # Use Azure Table Storage for local development
            from src.shared.data_access import get_table_storage_repository
            role_repo = get_table_storage_repository('user_roles')
            if not role_repo:
                logger.error("User role table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to assign role", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Check if role assignment already exists
            filter_query = f"UserId eq '{user_id}' and RoleId eq '{role_id}'"
            entities = role_repo.query_entities(filter_query)

            if entities:
                return func.HttpResponse(
                    json.dumps(create_json_response("Role already assigned to user", 400)),
                    mimetype="application/json",
                    status_code=400
                )

            # Get role name
            role_name = "Unknown Role"
            role_entities = role_repo.query_entities(f"RowKey eq '{role_id}'")
            if role_entities:
                role_name = role_entities[0].get("Rolename", "Unknown Role")

            # Create entity
            entity = {
                "PartitionKey": "user_role",
                "RowKey": f"{user_id}_{role_id}",
                "UserId": user_id,
                "RoleId": role_id,
                "Rolename": role_name,
                "AssignedAt": datetime.utcnow().isoformat()
            }

            # Insert entity
            success = role_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert user role entity")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to assign role", 500)),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            # Use SQL Database for production
            user_repo = get_account_sql_repo()
            if not user_repo:
                logger.error("User SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to assign role", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Check if role assignment already exists
            query = "SELECT 1 FROM App_User_Role WHERE UserId = ? AND RoleId = ?"
            params = (user_id, role_id)

            results = user_repo.execute_query(query, params)

            if results:
                return func.HttpResponse(
                    json.dumps(create_json_response("Role already assigned to user", 400)),
                    mimetype="application/json",
                    status_code=400
                )

            # Insert role assignment
            query = """
            INSERT INTO App_User_Role (UserId, RoleId, AssignedAt)
            VALUES (?, ?, ?);
            """
            params = (user_id, role_id, datetime.utcnow())

            # Execute query
            success = user_repo.execute_non_query(query, params)
            if not success:
                logger.error("Failed to insert user role")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to assign role", 500)),
                    mimetype="application/json",
                    status_code=500
                )

        # Return success response
        response = func.HttpResponse(
            json.dumps(create_json_response({"success": True})),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "assign_user_role")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="account-management/roles", methods=["GET"])
@require_auth
def get_roles(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get all roles endpoint

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with roles
    """
    logger.info('Processing get roles request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get roles
        roles = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            from src.shared.data_access import get_table_storage_repository
            role_repo = get_table_storage_repository('roles')
            if not role_repo:
                logger.error("Role table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get roles", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Query roles
            entities = role_repo.query_entities()

            # Convert entities to roles
            for entity in entities:
                role = {
                    "RoleId": entity.get("RowKey"),
                    "Rolename": entity.get("Rolename", ""),
                    "Description": entity.get("Description", "")
                }
                roles.append(role)

            # If no roles found, add default roles
            if not roles:
                default_roles = [
                    {"RoleId": "1", "Rolename": "Admin", "Description": "Administrator role with full access"},
                    {"RoleId": "2", "Rolename": "User", "Description": "Regular user with limited access"},
                    {"RoleId": "3", "Rolename": "Viewer", "Description": "Read-only access to data"}
                ]

                for role in default_roles:
                    entity = {
                        "PartitionKey": "role",
                        "RowKey": role["RoleId"],
                        "Rolename": role["Rolename"],
                        "Description": role["Description"]
                    }
                    role_repo.insert_entity(entity)

                roles = default_roles
        else:
            # Use SQL Database for production
            user_repo = get_account_sql_repo()
            if not user_repo:
                logger.error("Role SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to get roles", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Query roles
            query = "SELECT RoleId, Rolename, Description FROM Role"

            results = user_repo.execute_query(query)

            # Convert results to roles
            for result in results:
                role = {
                    "RoleId": result[0],
                    "Rolename": result[1],
                    "Description": result[2]
                }
                roles.append(role)

        # Return roles
        response = func.HttpResponse(
            json.dumps(create_json_response({"data": roles})),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "get_roles")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
