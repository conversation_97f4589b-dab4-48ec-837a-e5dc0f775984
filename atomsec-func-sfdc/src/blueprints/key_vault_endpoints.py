"""
Key Vault Endpoints Blueprint

This module provides endpoints for managing Azure Key Vault secrets.
It uses the functions from src.shared.azure_services module.
"""

import json
import logging
import os
import azure.functions as func
from src.shared.azure_services import (
    set_secret, get_secret, store_client_credentials,
    create_key_vault_if_not_exists, add_access_policy, get_current_object_id
)
from src.shared.utils import create_json_response, handle_exception
from src.shared.cors_middleware import cors_middleware
from src.shared.auth_utils import require_auth

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Note: The following UI-related functions have been removed as they should be handled by the frontend:
# - key_vault_form: Serves the Key Vault form HTML page
# - key_vault_management: Serves the Key Vault management HTML page

@bp.route(route="api/key-vault/secrets", methods=["POST"])
@require_auth
def store_secret_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """
    Store a secret in Azure Key Vault

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing request to store a secret in Key Vault...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate request
        if not req_body or 'secret_name' not in req_body or 'secret_value' not in req_body:
            return func.HttpResponse(
                json.dumps({"error": "Invalid request. Please provide secret_name and secret_value."}),
                mimetype="application/json",
                status_code=400
            )

        # Extract secret details
        secret_name = req_body.get('secret_name')
        secret_value = req_body.get('secret_value')
        content_type = req_body.get('content_type', 'text/plain')
        tags = req_body.get('tags')

        # Store secret in Key Vault
        success = set_secret(secret_name, secret_value, req_body.get('vault_url'), content_type, tags)

        if success:
            response_data = {
                "message": f"Secret '{secret_name}' stored successfully in Key Vault",
                "secret_name": secret_name
            }

            response = func.HttpResponse(
                json.dumps(create_json_response(response_data)),
                mimetype="application/json",
                status_code=201
            )
        else:
            response = func.HttpResponse(
                json.dumps({"error": f"Failed to store secret '{secret_name}' in Key Vault"}),
                mimetype="application/json",
                status_code=500
            )

        # Apply CORS middleware
        return cors_middleware(req, response)

    except Exception as e:
        error_response = handle_exception(e, "store_secret")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="api/key-vault/create", methods=["POST"])
@require_auth
def create_key_vault_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a Key Vault if it doesn't exist

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing request to create a Key Vault...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate request
        if not req_body or 'vault_name' not in req_body or 'resource_group' not in req_body:
            return func.HttpResponse(
                json.dumps({"error": "Invalid request. Please provide vault_name and resource_group."}),
                mimetype="application/json",
                status_code=400
            )

        # Extract parameters
        vault_name = req_body.get('vault_name')
        resource_group = req_body.get('resource_group')
        location = req_body.get('location', 'eastus')

        # Create Key Vault
        try:
            vault_url = create_key_vault_if_not_exists(vault_name, resource_group, location)

            # Add current user to access policies if requested
            if req_body.get('add_current_user', False):
                object_id = get_current_object_id()
                add_access_policy(vault_name, resource_group, object_id)

            response_data = {
                "message": f"Key Vault '{vault_name}' created or already exists",
                "vault_url": vault_url
            }

            response = func.HttpResponse(
                json.dumps(create_json_response(response_data)),
                mimetype="application/json",
                status_code=200
            )
        except Exception as e:
            response = func.HttpResponse(
                json.dumps({"error": f"Failed to create Key Vault: {str(e)}"}),
                mimetype="application/json",
                status_code=500
            )

        # Apply CORS middleware
        return cors_middleware(req, response)

    except Exception as e:
        error_response = handle_exception(e, "create_key_vault")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="api/key-vault/access-policy", methods=["POST"])
@require_auth
def add_access_policy_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """
    Add an access policy to a Key Vault

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing request to add an access policy to a Key Vault...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate request
        if not req_body or 'vault_name' not in req_body or 'resource_group' not in req_body or 'object_id' not in req_body:
            return func.HttpResponse(
                json.dumps({"error": "Invalid request. Please provide vault_name, resource_group, and object_id."}),
                mimetype="application/json",
                status_code=400
            )

        # Extract parameters
        vault_name = req_body.get('vault_name')
        resource_group = req_body.get('resource_group')
        object_id = req_body.get('object_id')
        tenant_id = req_body.get('tenant_id')

        # Add access policy
        success = add_access_policy(vault_name, resource_group, object_id, tenant_id)

        if success:
            response_data = {
                "message": f"Access policy added for object ID '{object_id}' to Key Vault '{vault_name}'"
            }

            response = func.HttpResponse(
                json.dumps(create_json_response(response_data)),
                mimetype="application/json",
                status_code=200
            )
        else:
            response = func.HttpResponse(
                json.dumps({"error": f"Failed to add access policy to Key Vault '{vault_name}'"}),
                mimetype="application/json",
                status_code=500
            )

        # Apply CORS middleware
        return cors_middleware(req, response)

    except Exception as e:
        error_response = handle_exception(e, "add_access_policy")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="api/key-vault/client-credentials", methods=["POST"])
@require_auth
def store_client_credentials_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """
    Store client ID and client secret in Azure Key Vault

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing request to store client credentials in Key Vault...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate request
        if not req_body or 'client_id' not in req_body or 'client_secret' not in req_body:
            return func.HttpResponse(
                json.dumps({"error": "Invalid request. Please provide client_id and client_secret."}),
                mimetype="application/json",
                status_code=400
            )

        # Extract credentials
        client_id = req_body.get('client_id')
        client_secret = req_body.get('client_secret')

        # Optional service name parameter to prefix the secret names
        service_name = req_body.get('service_name', 'default')

        # Store client credentials using the function from azure_services.py
        result = store_client_credentials(client_id, client_secret, service_name, req_body.get('vault_url'))

        if result.get('success', False):
            response = func.HttpResponse(
                json.dumps(create_json_response(result)),
                mimetype="application/json",
                status_code=201
            )
        else:
            response = func.HttpResponse(
                json.dumps({"error": result.get('message', 'Failed to store client credentials in Key Vault')}),
                mimetype="application/json",
                status_code=500
            )

        # Apply CORS middleware
        return cors_middleware(req, response)

    except Exception as e:
        error_response = handle_exception(e, "store_client_credentials")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
