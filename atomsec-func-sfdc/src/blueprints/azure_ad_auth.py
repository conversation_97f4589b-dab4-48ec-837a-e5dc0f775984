"""
Azure AD Authentication Blueprint

This module provides Azure AD authentication endpoints for the application.
It implements the OAuth 2.0 authorization code flow for Web applications (not SPA).

This implementation is designed for Azure AD App registrations of type 'Web',
which require server-side token exchange with a client secret.
"""

import logging
import json
import os
import secrets
import requests
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional
import urllib.parse
import azure.functions as func

# Import shared modules
from src.shared.azure_services import get_secret
from src.shared.utils import create_json_response, handle_exception
from src.shared.cors_middleware import cors_middleware
from src.shared.user_repository import get_user_account_by_email, update_last_login
from src.blueprints.auth import create_access_token, create_refresh_token, store_refresh_token

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Constants
STATE_TIMEOUT_MINUTES = 30  # Increase timeout to 30 minutes
STATES = {}  # In-memory state storage (replace with more durable storage in production)

# Debug flag
DEBUG_MODE = True  # Set to True to enable debug logging

# Import the centralized configuration module
from src.shared.config import get_azure_ad_config

def cleanup_expired_states():
    """
    Clean up expired state values
    """
    now = datetime.now(timezone.utc)
    expired_states = [state for state, data in STATES.items()
                     if now > data.get("expires_at", now)]

    for state in expired_states:
        STATES.pop(state, None)

@bp.route(route="auth/azure/login", methods=["GET"])
def azure_login(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure AD login endpoint

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with redirect to Azure AD login
    """
    logger.info('Processing Azure AD login request...')

    try:
        # Clean up expired states
        cleanup_expired_states()

        # Get redirect_uri from query parameters or use default
        redirect_uri = req.params.get("redirect_uri", "/")

        # Generate state parameter for CSRF protection
        state = secrets.token_hex(16)
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=STATE_TIMEOUT_MINUTES)

        # Store state with redirect URI
        STATES[state] = {
            "redirect_uri": redirect_uri,
            "expires_at": expires_at
        }

        # Get Azure AD configuration
        azure_config = get_azure_ad_config()

        # Build authorization URL
        # For local development, use the backend callback URL
        from src.shared.config import is_local_dev, get_backend_url

        # Determine the correct redirect URI
        callback_uri = azure_config['redirect_uri']
        if is_local_dev():
            # For local development, use the registered redirect URI
            callback_uri = azure_config['redirect_uri']
            logger.info(f"Using local development callback URI: {callback_uri}")
        else:
            # For production, use the configured redirect URI
            callback_uri = azure_config['redirect_uri']
            logger.info(f"Using production callback URI: {callback_uri}")

        auth_url = (
            f"{azure_config['authority']}/oauth2/v2.0/authorize"
            f"?client_id={azure_config['client_id']}"
            f"&response_type=code"
            f"&redirect_uri={urllib.parse.quote(callback_uri)}"
            f"&scope={urllib.parse.quote(azure_config['scope'])}"
            f"&state={state}"
            f"&response_mode=query"
            f"&prompt=select_account"
        )

        # Log the authorization URL for debugging
        logger.info(f"Redirecting to Azure AD login URL: {auth_url}")

        # Log the redirect URI for debugging
        logger.info(f"Using redirect URI: {azure_config['redirect_uri']}")

        # Redirect to Azure AD login
        response = func.HttpResponse(status_code=302)
        response.headers['Location'] = auth_url

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "azure_login")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

# Use the original route
@bp.route(route="auth/azure/callback", methods=["GET"])
def azure_callback(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure AD callback endpoint

    Args:
        req: HTTP request with authorization code

    Returns:
        func.HttpResponse: HTTP response with redirect to frontend
    """
    logger.info('Processing Azure AD callback request...')

    try:
        # Log all request parameters for debugging
        logger.info(f"Callback request params: {dict(req.params)}")
        logger.info(f"Callback request headers: {dict(req.headers)}")

        # Get authorization code and state from query parameters
        code = req.params.get("code")
        state = req.params.get("state")
        error = req.params.get("error")
        error_description = req.params.get("error_description")

        # Log the code and state
        if code:
            logger.info(f"Received authorization code: {code[:10]}...")
        if state:
            logger.info(f"Received state: {state}")

        # Check for errors
        if error:
            logger.error(f"Azure AD authentication error: {error} - {error_description}")
            return func.HttpResponse(
                f"<html><body><h1>Authentication Error</h1><p>{error}: {error_description}</p></body></html>",
                mimetype="text/html",
                status_code=400
            )

        # Validate state parameter
        if not state:
            logger.error("No state parameter provided")
            return func.HttpResponse(
                json.dumps(create_json_response("No state parameter provided", 400)),
                mimetype="application/json",
                status_code=400
            )

        if state not in STATES:
            # In debug mode, accept any state for testing
            if DEBUG_MODE:
                logger.warning(f"State not found in STATES, but accepting in DEBUG_MODE: {state}")
                # Create a temporary state entry
                STATES[state] = {
                    "redirect_uri": "/",
                    "expires_at": datetime.now(timezone.utc) + timedelta(minutes=5)
                }
            else:
                logger.error(f"Invalid or expired state parameter: {state}")
                logger.error(f"Available states: {list(STATES.keys())}")
                return func.HttpResponse(
                    json.dumps(create_json_response("Invalid or expired state parameter", 400)),
                    mimetype="application/json",
                    status_code=400
                )

        # Get redirect URI from state
        redirect_uri = STATES[state].get("redirect_uri", "/")

        # Remove state from storage
        STATES.pop(state, None)

        # Check for authorization code
        if not code:
            logger.error("No authorization code received")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No authorization code received</p></body></html>",
                mimetype="text/html",
                status_code=400
            )

        # Exchange authorization code for tokens
        azure_config = get_azure_ad_config()
        token_url = f"{azure_config['authority']}/oauth2/v2.0/token"

        # Log client ID and redirect URI for debugging
        logger.info(f"Using client_id: {azure_config['client_id']}")
        logger.info(f"Using redirect_uri: {azure_config['redirect_uri']}")

        # Check if client secret is set
        if not azure_config['client_secret']:
            logger.error("Client secret is not set!")
            return func.HttpResponse(
                json.dumps(create_json_response("Client secret is not configured", 500)),
                mimetype="application/json",
                status_code=500
            )
        else:
            # Log that client secret is set (don't log the actual secret)
            logger.info("Client secret is set")

        # Determine the correct redirect URI for token exchange
        from src.shared.config import is_local_dev, get_backend_url

        # Use the same callback URI as in the authorization request
        callback_uri = azure_config['redirect_uri']
        if is_local_dev():
            # For local development, use the registered redirect URI
            callback_uri = azure_config['redirect_uri']
            logger.info(f"Using local development callback URI for token exchange: {callback_uri}")
        else:
            # For production, use the configured redirect URI
            callback_uri = azure_config['redirect_uri']
            logger.info(f"Using production callback URI for token exchange: {callback_uri}")

        token_data = {
            "client_id": azure_config["client_id"],
            "client_secret": azure_config["client_secret"],
            "code": code,
            "redirect_uri": callback_uri,
            "grant_type": "authorization_code"
        }

        # Log the token request data (except client_secret)
        debug_token_data = token_data.copy()
        debug_token_data["client_secret"] = "[REDACTED]"
        logger.info(f"Token request data: {debug_token_data}")

        # Make the token request
        logger.info(f"Making token request to: {token_url}")
        token_response = requests.post(token_url, data=token_data)

        if token_response.status_code != 200:
            error_text = token_response.text
            logger.error(f"Token exchange failed: {error_text}")

            # Try to parse the error response
            try:
                error_json = token_response.json()
                error_code = error_json.get('error', '')
                error_description = error_json.get('error_description', 'Unknown error')

                # Handle already redeemed code error
                if error_code == 'invalid_grant' and 'AADSTS54005' in error_description:
                    logger.info("Authorization code was already redeemed, redirecting to frontend")

                    # Get the frontend URL for redirection
                    from src.shared.config import get_frontend_url
                    frontend_url = get_frontend_url()
                    logger.info(f"Using frontend URL: {frontend_url}")

                    # Redirect to frontend with error message
                    redirect_url = f"{frontend_url}?auth_error=code_already_redeemed"

                    response = func.HttpResponse(status_code=302)
                    response.headers['Location'] = redirect_url
                    return response
            except:
                error_description = error_text[:200] if len(error_text) > 200 else error_text

            # Return JSON error response instead of HTML
            error_response = {
                "success": False,
                "error": "token_exchange_failed",
                "error_description": error_description
            }

            return func.HttpResponse(
                json.dumps(error_response),
                mimetype="application/json",
                status_code=500
            )

        # Parse token response
        token_data = token_response.json()
        access_token = token_data.get("access_token")
        id_token = token_data.get("id_token")

        if not access_token or not id_token:
            logger.error("No tokens received")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No tokens received</p></body></html>",
                mimetype="text/html",
                status_code=500
            )

        # Get user info from Microsoft Graph API
        graph_url = "https://graph.microsoft.com/v1.0/me"
        headers = {"Authorization": f"Bearer {access_token}"}

        graph_response = requests.get(graph_url, headers=headers)

        if graph_response.status_code != 200:
            logger.error(f"Graph API request failed: {graph_response.text}")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>Failed to get user information</p></body></html>",
                mimetype="text/html",
                status_code=500
            )

        # Parse user info
        user_info = graph_response.json()
        email = user_info.get("userPrincipalName") or user_info.get("mail")
        name = user_info.get("displayName")

        if not email:
            logger.error("No email found in user info")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No email found in user information</p></body></html>",
                mimetype="text/html",
                status_code=500
            )

        # Check if user exists in our system
        user = get_user_account_by_email(email)

        # If user doesn't exist, create a new user record
        if not user:
            # In a real application, you might want to create a user record here
            # For now, we'll just use the email as the user identifier
            logger.info(f"User not found in database, using Azure AD user: {email}")
        else:
            # Update last login timestamp
            update_last_login(email)

        # Create application access token
        app_access_token = create_access_token({"sub": email})

        # Create refresh token
        refresh_token = create_refresh_token()
        store_refresh_token(email, refresh_token)

        # Log successful token exchange
        logger.info(f"Successfully exchanged authorization code for tokens for user: {email}")

        # Create JSON response with tokens
        response_data = {
            "success": True,
            "data": {
                "access_token": app_access_token,
                "refresh_token": refresh_token,
                "email": email,
                "name": name if name else ""
            }
        }

        # Get the frontend URL for redirection
        from src.shared.config import get_frontend_url
        frontend_url = get_frontend_url()
        logger.info(f"Using frontend URL for successful auth redirect: {frontend_url}")

        # Create a redirect response to the frontend with tokens as query parameters
        # In a production app, you might want to use a more secure method like cookies or post message
        redirect_url = f"{frontend_url}/auth-callback?access_token={app_access_token}&refresh_token={refresh_token}&email={urllib.parse.quote(email)}&name={urllib.parse.quote(name if name else '')}"

        logger.info(f"Redirecting to frontend after successful authentication: {redirect_url}")
        logger.info(f"Frontend URL: {frontend_url}")

        # For local development, ensure we're redirecting to localhost:3000
        if 'localhost' in frontend_url and 'localhost' in req.url:
            logger.info("Local development environment detected")
            # Make sure we're redirecting to the correct localhost URL
            if not frontend_url.startswith('http://localhost:3000'):
                frontend_url = 'http://localhost:3000'
                redirect_url = f"{frontend_url}/auth-callback?access_token={app_access_token}&refresh_token={refresh_token}&email={urllib.parse.quote(email)}&name={urllib.parse.quote(name if name else '')}"
                logger.info(f"Updated redirect URL for local development: {redirect_url}")

        response = func.HttpResponse(status_code=302)
        response.headers['Location'] = redirect_url

        # Log the successful authentication
        logger.info(f"Successfully authenticated user: {email}")

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "azure_callback")

        # Return JSON error response
        error_json = {
            "success": False,
            "error": "authentication_error",
            "error_description": str(e)
        }

        response = func.HttpResponse(
            json.dumps(error_json),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="auth/azure/me", methods=["GET"])
def azure_me(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get current user information

    Args:
        req: HTTP request with access token

    Returns:
        func.HttpResponse: HTTP response with user information
    """
    logger.info('Processing Azure AD me request...')

    try:
        # Get authorization header
        auth_header = req.headers.get("Authorization")

        if not auth_header or not auth_header.startswith("Bearer "):
            logger.warning("No authorization header found")
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Extract token
        token = auth_header.replace("Bearer ", "")

        # Validate token using our application's JWT validation
        # For Web app type registrations, we use our own JWT tokens after
        # validating the Azure AD tokens during the callback
        try:
            # Import here to avoid circular imports
            from src.shared.auth_utils import decode_token

            # Decode token
            payload = decode_token(token)

            if not payload:
                logger.warning("Invalid token")
                return func.HttpResponse(
                    json.dumps(create_json_response("Invalid token", 401)),
                    mimetype="application/json",
                    status_code=401
                )

            # Extract user information
            email = payload.get("sub")

            if not email:
                logger.warning("No email found in token")
                return func.HttpResponse(
                    json.dumps(create_json_response("Invalid token", 401)),
                    mimetype="application/json",
                    status_code=401
                )

            # Get user from database
            user = get_user_account_by_email(email)

            # Get user roles
            from src.shared.user_repository import get_user_roles, is_user_admin
            roles = get_user_roles(email)
            is_admin = is_user_admin(email)

            # Return user information
            response_data = {
                "email": email,
                "name": user.FirstName + " " + user.LastName if user else "",
                "accessToken": token,
                "idToken": token,  # In a real application, you would use the actual ID token
                "roles": roles,
                "isAdmin": is_admin
            }

            response = func.HttpResponse(
                json.dumps(create_json_response(response_data)),
                mimetype="application/json",
                status_code=200
            )

            # Apply CORS middleware
            return cors_middleware(req, response)
        except Exception as e:
            logger.error(f"Error decoding token: {str(e)}")
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid token", 401)),
                mimetype="application/json",
                status_code=401
            )
    except Exception as e:
        error_response = handle_exception(e, "azure_me")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
