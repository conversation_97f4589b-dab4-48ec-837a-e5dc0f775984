"""
Integration Blueprint

This module provides endpoints for managing integrations with external systems like Salesforce.
"""

import json
import logging
import uuid
import azure.functions as func
import os
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional, List
from datetime import datetime

# Import shared modules
from src.shared.azure_services import store_client_credentials, get_secret
from src.shared.utils import create_json_response, handle_exception
from src.shared.auth_utils import get_current_user, require_auth
from src.shared.salesforce_utils import test_salesforce_connection
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository, BlobStorageRepository, get_table_storage_repository, create_default_policies_and_rules_for_integration
from src.shared.common import is_local_dev
from src.shared.auth_service import authenticate_salesforce_integration
# Import the metadata utils module
from src.shared.metadata_utils import get_salesforce_metadata
# Import BackgroundProcessor and task types
from src.shared.background_processor import (\
    BackgroundProcessor,\
    TASK_TYPE_SFDC_AUTHENTICATE,\
    TASK_PRIORITY_HIGH\
)
from src.shared.user_repository import get_user_account_by_id
from src.shared.db_service_client import get_db_client

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_integration_table_repo = None
_integration_sql_repo = None
_metadata_blob_repo = None

def get_integration_table_repo():
    """Lazy initialize the integration table repository"""
    global _integration_table_repo
    if _integration_table_repo is None:
        try:
            _integration_table_repo = TableStorageRepository(table_name="Integrations")
            logger.info("Initialized integration table repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration table repository: {str(e)}")
            _integration_table_repo = None
    return _integration_table_repo

def get_integration_sql_repo():
    """Lazy initialize the integration SQL repository"""
    global _integration_sql_repo
    if _integration_sql_repo is None and not is_local_dev():
        try:
            _integration_sql_repo = SqlDatabaseRepository(table_name="App_Integration")
            logger.info("Initialized integration SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration SQL repository: {str(e)}")
            _integration_sql_repo = None
    return _integration_sql_repo

def get_metadata_blob_repo():
    """Lazy initialize the metadata blob repository"""
    global _metadata_blob_repo
    if _metadata_blob_repo is None:
        try:
            logger.info("Initializing metadata blob repository...")
            _metadata_blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
            logger.info(f"Successfully initialized metadata blob repository: {type(_metadata_blob_repo)}")

            # Test the blob repository by listing blobs
            try:
                blobs = _metadata_blob_repo.list_blobs()
                logger.info(f"Blob repository test successful. Found {len(blobs)} blobs.")
            except Exception as test_e:
                logger.warning(f"Blob repository test failed: {str(test_e)}")
        except Exception as e:
            logger.error(f"Failed to initialize metadata blob repository: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            _metadata_blob_repo = None
    else:
        logger.info("Using existing metadata blob repository instance")

    if _metadata_blob_repo is None:
        logger.error("Metadata blob repository is None")

    return _metadata_blob_repo

def create_integration(name: str, tenant_url: str, integration_type: str = "Salesforce",
                      description: str = "", environment: str = "production",
                      user_email: str = None, user_id: str = None, is_active: bool = True) -> Optional[str]:
    """
    Create a new integration

    Args:
        name: Integration name
        tenant_url: Tenant URL
        integration_type: Integration type (default: Salesforce)
        description: Integration description
        environment: Environment (production or sandbox)
        user_email: User email
        user_id: User ID (required for proper user-integration relationship)
        is_active: Whether the integration is active (default: True)

    Returns:
        str: Integration ID if successful, None otherwise
    """
    try:
        # Validate user_id is provided
        if not user_id:
            logger.error("User ID is required for creating an integration")
            return None

        # Generate a unique ID
        integration_id = str(uuid.uuid4())

        if is_local_dev():
            # Use Azure Table Storage for local development
            integration_repo = get_integration_table_repo()
            if not integration_repo:
                logger.error("Integration table repository not available")
                return None

            # Verify user exists and get account ID from UserAccount table
            user_account = get_user_account_by_id(int(user_id))
            if user_account:
                account_id = getattr(user_account, "AccountId", None)
                logger.info(f"User {user_id} belongs to account {account_id}")
            else:
                logger.error(f"User with ID {user_id} not found in UserAccount table")
                return None

            # Create integration entity
            integration_entity = {
                "PartitionKey": "integration",
                "RowKey": integration_id,
                "Name": name,
                "TenantUrl": tenant_url,
                "Type": integration_type,
                "Description": description,
                "Environment": environment,
                "IsActive": is_active,
                "LastScan": "",
                "CreatedAt": datetime.now().isoformat(),
                "UserEmail": user_email,
                "UserId": user_id,  # Store UserId
                "AccountId": account_id
            }

            # Insert integration
            success = integration_repo.insert_entity(integration_entity)
            integration_id_result = integration_id if success else None
            # Automatically create default policies and rules if integration creation succeeded
            if integration_id_result:
                create_default_policies_and_rules_for_integration(user_id, integration_id_result)
            return integration_id_result
        else:
            # Use SQL Database for production
            integration_repo = get_integration_sql_repo()
            if not integration_repo:
                logger.error("Integration SQL repository not available")
                return None

            # Insert integration with UserId
            query = """
            INSERT INTO App_Integration (Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail, UserId)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                integration_id,
                name,
                tenant_url,
                integration_type,
                description,
                environment,
                is_active,
                "",
                datetime.now().isoformat(),
                user_email,
                user_id
            )

            success = integration_repo.execute_non_query(query, params)
            integration_id_result = integration_id if success else None
            # Automatically create default policies and rules if integration creation succeeded
            if integration_id_result:
                create_default_policies_and_rules_for_integration(user_id, integration_id_result)
            return integration_id_result
    except Exception as e:
        logger.error(f"Error creating integration: {str(e)}")
        return None

@bp.route(route="integration/test-connection", methods=["POST"])
@require_auth
def test_connection(req: func.HttpRequest) -> func.HttpResponse:
    """
    Test connection to an integration using the central auth service.
    """
    logger.info("Testing integration connection (using central auth service)...")
    try:
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 400, "error": "Invalid request body"}),
                mimetype="application/json",
                status_code=400
            )

        tenant_url = req_body.get("tenantUrl")
        client_id = req_body.get("clientId")
        client_secret = req_body.get("clientSecret")
        environment = req_body.get("environment", "production")
        use_jwt = req_body.get("useJwt", False)
        username = req_body.get("username")
        private_key_str = req_body.get("privateKey")

        auth_flow_type = "jwt" if use_jwt else "client_credentials"

        # Parameter validation (simplified, actual validation can be more robust)
        if not tenant_url or not client_id or (use_jwt and (not username or not private_key_str)) or (not use_jwt and not client_secret):
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 400, "error": "Missing required connection parameters."}),
                mimetype="application/json",
                status_code=400
            )
        
        logger.info(f"Calling central auth for test. Flow: {auth_flow_type}, Tenant: {tenant_url}, Env: {environment}")
        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow_type,
            tenant_url=tenant_url,
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=username,
            private_key=private_key_str
        )

        integration_id_from_req = req_body.get("integrationId")

        if auth_success and sf_instance:
            logger.info(f"Central auth test successful. Instance: {connection_details.get('instance_url')}")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": True,
                    "message": "Connection successful via central auth service.",
                    "details": connection_details,
                    "integrationId": integration_id_from_req,
                    "testOnly": True
                })),
                mimetype="application/json",
                status_code=200
            )
        else:
            logger.warning(f"Central auth test failed: {auth_error_message}")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Connection failed via central auth service: {auth_error_message}",
                    "error": auth_error_message,
                    "integrationId": integration_id_from_req,
                    "testOnly": True
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

    except Exception as e:
        error_response = handle_exception(e, "test_connection_central_auth")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="integration/connect", methods=["POST"])
@require_auth
def connect_integration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Connect to an integration, store credentials, and trigger initial scan.
    Uses central auth service for connection testing.
    """
    logger.info("Connecting to integration (using central auth service)...")
    try:
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 400, "error": "Invalid request body"}),
                mimetype="application/json",
                status_code=400
            )

        name = req_body.get("orgName")
        tenant_url = req_body.get("tenantUrl")
        client_id = req_body.get("clientId")
        client_secret = req_body.get("clientSecret")
        description = req_body.get("description", "")
        integration_type_param = req_body.get("type", "Salesforce")
        environment = req_body.get("environment", "production")
        use_jwt = req_body.get("useJwt", False)
        username = req_body.get("username")
        private_key_str = req_body.get("privateKey")
        integration_id_from_req = req_body.get("integrationId")

        auth_flow_type = "jwt" if use_jwt else "client_credentials"

        if not name or not tenant_url or not client_id or (use_jwt and (not username or not private_key_str)) or (not use_jwt and not client_secret):
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 400, "error": "Missing required parameters for connection."}),
                mimetype="application/json",
                status_code=400
            )

        is_fixing_connection = bool(integration_id_from_req)
        db_integration_id = integration_id_from_req

        logger.info(f"Calling central auth for connect/fix. Flow: {auth_flow_type}, Tenant: {tenant_url}, Env: {environment}")
        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow_type,
            tenant_url=tenant_url,
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=username,
            private_key=private_key_str
        )

        if not auth_success:
            logger.warning(f"Central auth connection test failed: {auth_error_message}")
            if is_fixing_connection and db_integration_id:
                if is_local_dev():
                    integration_repo_table = get_integration_table_repo()
                    if integration_repo_table: integration_repo_table.update_entity({"PartitionKey": "integration", "RowKey": db_integration_id, "IsActive": False})
                else:
                    integration_repo_sql = get_integration_sql_repo()
                    if integration_repo_sql: integration_repo_sql.execute_non_query("UPDATE App_Integration SET IsActive = ? WHERE Id = ?", (False, db_integration_id))
                logger.info(f"Marked existing integration {db_integration_id} as inactive due to failed connection test.")
            
            return func.HttpResponse(
                json.dumps(create_json_response({"success": False, "message": f"Connection test failed: {auth_error_message}", "error": auth_error_message, "integrationId": db_integration_id}, 400)),
                mimetype="application/json",
                status_code=400
            )

        if not is_fixing_connection:
            logger.info(f"Creating new integration record for {name}...")
            db_integration_id = create_integration(
                name=name, tenant_url=tenant_url, integration_type=integration_type_param, 
                description=description, environment=environment, user_email=current_user.get("email"), user_id=current_user.get("id"), is_active=True
            )
            if not db_integration_id:
                logger.error("Failed to create integration record in DB.")
                return func.HttpResponse(json.dumps(create_json_response({"success": False, "message": "Failed to create integration record after successful connection test."}, 500)), mimetype="application/json", status_code=500)
            logger.info(f"New integration record created with ID: {db_integration_id}")
        else:
            logger.info(f"Connection test succeeded for existing integration {db_integration_id}, marking active.")
            if is_local_dev():
                integration_repo_table = get_integration_table_repo()
                if integration_repo_table: integration_repo_table.update_entity({"PartitionKey": "integration", "RowKey": db_integration_id, "IsActive": True, "TenantUrl": tenant_url, "Environment": environment})
            else:
                integration_repo_sql = get_integration_sql_repo()
                if integration_repo_sql: integration_repo_sql.execute_non_query("UPDATE App_Integration SET IsActive = ?, TenantUrl = ?, Environment = ? WHERE Id = ?", (True, tenant_url, environment, db_integration_id))
        
        service_name = f"salesforce-{db_integration_id}"
        if use_jwt:
            cred_data_jwt = {
                "client-id": {"Value": client_id, "AuthFlow": "jwt"},
                "username": {"Value": username},
                "private-key": {"Value": private_key_str}
            }
            if is_local_dev():
                cred_repo = get_table_storage_repository("Credentials")
                for key, val_dict in cred_data_jwt.items():
                    cred_repo.table_client.upsert_entity(entity={"PartitionKey": service_name, "RowKey": key, **val_dict})
                logger.info(f"Credentials for JWT (service: {service_name}) upserted to local table via table_client.")
            else:
                store_client_credentials(
                    client_id=client_id, 
                    service_name=service_name, 
                    use_jwt=True, 
                    username=username, 
                    private_key=private_key_str,
                    auth_flow_type="jwt"
                )
        else:
            cred_data_cc = {
                "client-id": {"Value": client_id, "AuthFlow": "client_credentials"},
                "client-secret": {"Value": client_secret}
            }
            if is_local_dev():
                cred_repo = get_table_storage_repository("Credentials")
                for key, val_dict in cred_data_cc.items():
                    cred_repo.table_client.upsert_entity(entity={"PartitionKey": service_name, "RowKey": key, **val_dict})
                logger.info(f"Credentials for CC (service: {service_name}) upserted to local table via table_client.")
            else:
                store_client_credentials(
                    client_id=client_id, 
                    client_secret=client_secret, 
                    service_name=service_name,
                    auth_flow_type="client_credentials"
                )

        logger.info(f"Credentials for {service_name} ({auth_flow_type}) stored/updated.")

        auth_task_enqueued = trigger_initial_scan(db_integration_id, current_user.get("id"))
        if not auth_task_enqueued:
            logger.warning(f"Failed to enqueue initial authentication task for {db_integration_id}. Manual scan may be required.")

        return func.HttpResponse(
            json.dumps(create_json_response({
                "success": True, 
                "message": "Integration connected/updated successfully and scan initiated.", 
                "id": db_integration_id, "name": name, "tenantUrl": tenant_url, 
                "type": integration_type_param, "environment": environment
            })),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        error_response = handle_exception(e, "connect_integration_central_auth")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

# Helper function to trigger the initial authentication task
def trigger_initial_scan(integration_id: str, user_id: str) -> bool:
    """
    Enqueues the initial TASK_TYPE_SFDC_AUTHENTICATE for a given integration.

    Args:
        integration_id: The ID of the integration.
        user_id: The ID of the user initiating the action.

    Returns:
        bool: True if the task was enqueued successfully, False otherwise.
    """
    try:
        # Generate a single execution_log_id for the entire scan
        execution_log_id = str(uuid.uuid4())

        # Create execution log record for the scan
        try:
            db_client = get_db_client()
            db_client.create_execution_log({
                'ExecutionLogId': execution_log_id,
                'OrgId': integration_id,
                'ExecutionType': 'Integration_Scan',
                'Status': 'Pending',
                'Priority': 'High',
                'StartTime': datetime.now(),
                'ExecutedBy': user_id,
                'CreatedAt': datetime.now(),
                'UpdatedAt': datetime.now()
            })
            logger.info(f"Created execution log record: {execution_log_id}")
        except Exception as e:
            logger.warning(f"Failed to create execution log record: {e}, continuing with scan")

        # Need to fetch minimal integration details like TenantUrl and Environment for the auth task params
        integration_repo_table = get_integration_table_repo()
        integration_repo_sql = get_integration_sql_repo()
        integration_details = None
        if is_local_dev() and integration_repo_table:
            integration_details = integration_repo_table.get_entity("integration", integration_id)
        elif not is_local_dev() and integration_repo_sql:
            query = "SELECT TenantUrl, Environment FROM App_Integration WHERE Id = ?"
            results = integration_repo_sql.execute_query(query, (integration_id,))
            if results and len(results) > 0:
                integration_details = {"TenantUrl": results[0][0], "Environment": results[0][1]}
        
        if not integration_details:
            logger.error(f"trigger_initial_scan: Could not retrieve details for integration {integration_id} to enqueue auth task.")
            return False

        processor = BackgroundProcessor()
        auth_task_params = {
            "integration_id": integration_id,
            "tenant_url": integration_details.get("TenantUrl"),
            "environment": integration_details.get("Environment"),
            "execution_log_id": execution_log_id
        }
        # Ensure user_id is not None
        if not user_id:
            user_id = "system"  # Use a default user ID if none is provided
            logger.info(f"No user_id provided, using default 'system' for task")
            
        logger.info(f"Enqueueing initial SFDC authentication task for {integration_id} by user {user_id} with execution_log_id: {execution_log_id}")
        auth_task_id = processor.enqueue_task(
            task_type=TASK_TYPE_SFDC_AUTHENTICATE,
            org_id=integration_id,
            user_id=user_id,
            params=auth_task_params,
            priority=TASK_PRIORITY_HIGH,
            execution_log_id=execution_log_id
        )
        if auth_task_id:
            logger.info(f"Successfully enqueued initial SFDC auth task {auth_task_id} for {integration_id}")
            return True
        else:
            logger.error(f"Failed to enqueue initial SFDC auth task for {integration_id}")
            return False
    except Exception as e:
        logger.error(f"Exception in trigger_initial_scan for {integration_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

@bp.route(route="integration/scan/{id}", methods=["POST"])
@require_auth
def scan_integration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Scan an integration to fetch data from Salesforce.
    This will now enqueue a high-priority authentication task.
    """
    logger.info("Scanning integration (new flow)...")

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            logger.error("User not authenticated")
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Get integration ID from route
        integration_id = req.route_params.get("id")
        if not integration_id:
            logger.error("Integration ID is missing from route")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration ID is required in the URL path"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Attempting to enqueue authentication task for integration ID: {integration_id}")

        # Get integration to ensure it exists and is active
        integration_repo_table = get_integration_table_repo()
        integration_repo_sql = get_integration_sql_repo()
        integration = None

        if is_local_dev():
            if integration_repo_table:
                integration = integration_repo_table.get_entity("integration", integration_id)
        else:
            if integration_repo_sql:
                # Assuming a method like get_by_id exists or adapt to query
                query = "SELECT Id, Name, TenantUrl, Type, Environment, IsActive FROM App_Integration WHERE Id = ?"
                results = integration_repo_sql.execute_query(query, (integration_id,))
                if results and len(results) > 0:
                    row = results[0]
                    integration = {
                        "RowKey": row[0], "Name": row[1], "TenantUrl": row[2],
                        "Type": row[3], "Environment": row[4], "IsActive": row[5]
                    }
        
        if not integration:
            logger.error(f"Integration with ID {integration_id} not found.")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False, "message": f"Integration with ID {integration_id} not found."
                }, 404)),
                mimetype="application/json",
                status_code=404
            )

        if not integration.get("IsActive", False):
            logger.warning(f"Integration {integration_id} is not active. Scan aborted.")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False, "message": "Cannot scan inactive integration. Please fix the connection first."
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Enqueue the authentication task
        try:
            # Generate a single execution_log_id for the entire scan
            execution_log_id = str(uuid.uuid4())

            # Create execution log record for the scan
            try:
                db_client = get_db_client()
                db_client.create_execution_log({
                    'ExecutionLogId': execution_log_id,
                    'OrgId': integration_id,
                    'ExecutionType': 'Integration_Scan',
                    'Status': 'Pending',
                    'Priority': 'High',
                    'StartTime': datetime.now(),
                    'ExecutedBy': current_user.get("id", "system"),
                    'CreatedAt': datetime.now(),
                    'UpdatedAt': datetime.now()
                })
                logger.info(f"Created execution log record: {execution_log_id}")
            except Exception as e:
                logger.warning(f"Failed to create execution log record: {e}, continuing with scan")

            processor = BackgroundProcessor()
            auth_task_params = {
                "integration_id": integration_id, 
                "tenant_url": integration.get("TenantUrl"),
                "environment": integration.get("Environment"),
                "execution_log_id": execution_log_id
            }

            logger.info(f"Enqueueing SFDC authentication task for integration {integration_id} with execution_log_id: {execution_log_id}")
            auth_task_id = processor.enqueue_task(
                task_type=TASK_TYPE_SFDC_AUTHENTICATE,
                org_id=integration_id,
                user_id=current_user.get("id", "system"), # Use "system" or a relevant ID if user not available
                params=auth_task_params,
                priority=TASK_PRIORITY_HIGH,
                execution_log_id=execution_log_id
            )

            if not auth_task_id:
                logger.error(f"Failed to enqueue SFDC authentication task for integration {integration_id}")
                return func.HttpResponse(
                    json.dumps(create_json_response({
                        "success": False,
                        "message": "Failed to enqueue Salesforce authentication task",
                        "integrationId": integration_id
                    }, 500)),
                    mimetype="application/json",
                    status_code=500
                )

            logger.info(f"Successfully enqueued SFDC authentication task {auth_task_id} for integration {integration_id}")
            
            # Update LastScanAttempt timestamp on the integration record
            now_iso = datetime.now().isoformat()
            db_client = get_db_client()
            db_client.update_integration(integration_id, {"last_scan_attempt": now_iso})

            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": True,
                    "message": "Salesforce scan initiated. Authentication task has been queued.",
                    "integrationId": integration_id,
                    "auth_task_id": auth_task_id,
                    "execution_log_id": execution_log_id
                })),
                mimetype="application/json",
                status_code=202  # Accepted
            )

        except Exception as e:
            logger.error(f"Error enqueueing SFDC authentication task for {integration_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Error initiating Salesforce scan: {str(e)}",
                    "integrationId": integration_id
                }, 500)),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Unhandled error in scan_integration for ID {req.route_params.get('id')}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # Ensure a response is always returned
        return func.HttpResponse(
            json.dumps(create_json_response({"success": False, "message": "An unexpected error occurred during scan initiation."}, 500)),
            mimetype="application/json",
            status_code=500
        )

# Function to get all integrations
@bp.route(route="integrations", methods=["GET"]) # Matches the path in function_app.py
@require_auth
def get_integrations(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get all integrations, with optional filters.
    """
    logger.info("Fetching all integrations...")
    try:
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get query parameters
        include_inactive = req.params.get("includeInactive", "false").lower() == "true"
        integration_type_filter = req.params.get("type") # e.g., "Salesforce"

        integrations_list = []
        
        if is_local_dev():
            integration_repo = get_integration_table_repo()
            if not integration_repo:
                logger.error("Integration table repository not available for local dev.")
                return func.HttpResponse(
                    json.dumps(create_json_response("Integration repository not available", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            filter_parts = ["PartitionKey eq 'integration'"] # Base filter
            # Add user email filter to only show integrations for the current user
            # Assuming user_email is stored in the integration record
            user_email = current_user.get("email")
            if user_email:
                filter_parts.append(f"UserEmail eq '{user_email}'")

            if not include_inactive:
                filter_parts.append("IsActive eq true")
            if integration_type_filter:
                filter_parts.append(f"Type eq '{integration_type_filter}'")
            
            filter_query = " and ".join(filter_parts)
            logger.info(f"Local dev: Querying Integrations table with filter: {filter_query}")
            entities = integration_repo.query_entities(filter_query)
            
            for entity in entities:
                integrations_list.append({
                    "id": entity.get("RowKey"),
                    "Name": entity.get("Name"),
                    "TenantUrl": entity.get("TenantUrl"),
                    "Type": entity.get("Type"),
                    "Description": entity.get("Description"),
                    "Environment": entity.get("Environment"),
                    "IsActive": entity.get("IsActive"),
                    "LastScan": entity.get("LastScan"),
                    "LastScanAttempt": entity.get("LastScanAttempt"),
                    "HealthScore": entity.get("HealthScore"),
                    "CreatedAt": entity.get("CreatedAt"),
                    "UserEmail": entity.get("UserEmail")
                    # Add other relevant fields
                })
        else:
            integration_repo_sql = get_integration_sql_repo()
            if not integration_repo_sql:
                logger.error("Integration SQL repository not available for production.")
                return func.HttpResponse(
                    json.dumps(create_json_response("Integration repository not available", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            query_fields = "Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, LastScanAttempt, HealthScore, CreatedAt, UserEmail" # Add all fields
            base_query = f"SELECT {query_fields} FROM App_Integration"
            
            where_clauses = []
            params_sql = []

            # Add user email filter
            user_email = current_user.get("email")
            if user_email:
                where_clauses.append("UserEmail = ?")
                params_sql.append(user_email)

            if not include_inactive:
                where_clauses.append("IsActive = ?")
                params_sql.append(True)
            if integration_type_filter:
                where_clauses.append("Type = ?")
                params_sql.append(integration_type_filter)

            if where_clauses:
                base_query += " WHERE " + " AND ".join(where_clauses)
            
            logger.info(f"Production: Executing SQL query for integrations: {base_query} with params {params_sql}")
            results = integration_repo_sql.execute_query(base_query, tuple(params_sql))

            for row in results:
                integrations_list.append({
                    "id": row[0],
                    "Name": row[1],
                    "TenantUrl": row[2],
                    "Type": row[3],
                    "Description": row[4],
                    "Environment": row[5],
                    "IsActive": row[6],
                    "LastScan": row[7],
                    "LastScanAttempt": row[8],
                    "HealthScore": row[9],
                    "CreatedAt": row[10],
                    "UserEmail": row[11]
                    # Ensure order matches query_fields
                })
        
        logger.info(f"Found {len(integrations_list)} integrations.")
        return func.HttpResponse(
            json.dumps(create_json_response({"integrations": integrations_list, "count": len(integrations_list)})),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error fetching integrations: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return func.HttpResponse(
            json.dumps(create_json_response(f"Error fetching integrations: {str(e)}", 500)),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="integration/scan-metadata/{id}", methods=["POST"])
def scan_metadata(req: func.HttpRequest) -> func.HttpResponse:
    # Placeholder: Ensure actual scan_metadata logic is here
    logger.info(f"scan_metadata called for ID: {req.route_params.get('id')}")
    orchestration_context = f"scan_metadata:{req.route_params.get('id')}:{datetime.now().isoformat()}"
    get_salesforce_metadata(..., orchestration_context=orchestration_context)
    logger.info(f"[ORCHESTRATION] get_salesforce_metadata called from scan_metadata. Context: {orchestration_context}")
    return func.HttpResponse(json.dumps({"message": "scan_metadata placeholder"}), mimetype="application/json")


@bp.route(route="integration/{id}", methods=["DELETE"])
@require_auth
def delete_integration(req: func.HttpRequest) -> func.HttpResponse:
    # Placeholder: Ensure actual delete_integration logic is here
    logger.info(f"delete_integration called for ID: {req.route_params.get('id')}")
    return func.HttpResponse(json.dumps({"message": "delete_integration placeholder"}), mimetype="application/json")
    
# ... (any other functions that should be in this blueprint)
