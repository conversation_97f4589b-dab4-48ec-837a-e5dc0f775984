"""
Data Protection Service

This module provides comprehensive data protection features including:
- Data encryption at rest and in transit
- Data classification and handling procedures
- GDPR compliance features (data export, deletion)
- Data anonymization and pseudonymization
- Data retention policy enforcement

Requirements addressed: 1.2, 1.4
"""

import logging
import json
import hashlib
import secrets
import base64
from typing import Dict, Any, Optional, List, Set, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import re
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

from src.shared.azure_services import get_secret
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"


class DataCategory(Enum):
    """GDPR data categories"""
    PERSONAL_DATA = "personal_data"
    SENSITIVE_PERSONAL_DATA = "sensitive_personal_data"
    PSEUDONYMIZED_DATA = "pseudonymized_data"
    ANONYMOUS_DATA = "anonymous_data"
    TECHNICAL_DATA = "technical_data"


class ProcessingPurpose(Enum):
    """Data processing purposes for GDPR"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    SECURITY_MONITORING = "security_monitoring"
    AUDIT_LOGGING = "audit_logging"
    PERFORMANCE_MONITORING = "performance_monitoring"
    BUSINESS_ANALYTICS = "business_analytics"
    LEGAL_COMPLIANCE = "legal_compliance"


class RetentionPeriod(Enum):
    """Data retention periods"""
    IMMEDIATE = 0  # Delete immediately
    SHORT_TERM = 30  # 30 days
    MEDIUM_TERM = 365  # 1 year
    LONG_TERM = 2555  # 7 years
    PERMANENT = -1  # Keep permanently


@dataclass
class DataClassificationRule:
    """Data classification rule"""
    field_pattern: str
    classification: DataClassification
    category: DataCategory
    processing_purposes: List[ProcessingPurpose]
    retention_period: RetentionPeriod
    encryption_required: bool = True
    anonymization_required: bool = False
    description: str = ""


@dataclass
class EncryptionContext:
    """Encryption context information"""
    algorithm: str
    key_id: str
    iv: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class DataProcessingRecord:
    """GDPR data processing record"""
    record_id: str
    data_subject_id: str
    data_categories: List[DataCategory]
    processing_purposes: List[ProcessingPurpose]
    legal_basis: str
    data_sources: List[str]
    data_recipients: List[str]
    retention_period: RetentionPeriod
    processing_start: datetime
    processing_end: Optional[datetime] = None
    consent_given: bool = False
    consent_timestamp: Optional[datetime] = None
    automated_decision_making: bool = False
    cross_border_transfer: bool = False
    security_measures: List[str] = None
    
    def __post_init__(self):
        if self.security_measures is None:
            self.security_measures = []


@dataclass
class GDPRRequest:
    """GDPR data subject request"""
    request_id: str
    request_type: str  # access, rectification, erasure, portability, restriction, objection
    data_subject_id: str
    request_timestamp: datetime
    verification_status: str = "pending"
    processing_status: str = "received"
    completion_timestamp: Optional[datetime] = None
    response_data: Optional[Dict[str, Any]] = None
    notes: str = ""


class DataProtectionService:
    """
    Comprehensive data protection service implementing encryption,
    classification, and GDPR compliance features
    """
    
    def __init__(self):
        self.monitoring = get_monitoring_service()
        
        # Encryption keys and contexts
        self._encryption_keys: Dict[str, bytes] = {}
        self._key_rotation_schedule: Dict[str, datetime] = {}
        
        # Data classification and processing records
        self._classification_rules: List[DataClassificationRule] = []
        self._processing_records: Dict[str, DataProcessingRecord] = {}
        self._gdpr_requests: Dict[str, GDPRRequest] = {}
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Configuration
        self.config = {
            'default_encryption_algorithm': 'AES-256-GCM',
            'key_rotation_days': 90,
            'data_retention_check_hours': 24,
            'gdpr_response_days': 30,
            'anonymization_salt_length': 32,
            'pseudonymization_key_length': 32,
            'audit_all_operations': True,
            'require_consent_for_analytics': True,
        }
        
        # Initialize default classification rules
        self._initialize_classification_rules()
        
        # Initialize encryption keys
        self._initialize_encryption_keys()
    
    def encrypt_data(self, data: Union[str, bytes, Dict[str, Any]], 
                    classification: DataClassification = DataClassification.CONFIDENTIAL,
                    key_id: str = "default") -> Dict[str, Any]:
        """
        Encrypt data with specified classification level
        
        Args:
            data: Data to encrypt
            classification: Data classification level
            key_id: Encryption key identifier
            
        Returns:
            Dict containing encrypted data and metadata
            
        Raises:
            Exception: If encryption fails
        """
        try:
            with self._lock:
                # Convert data to bytes if needed
                if isinstance(data, dict):
                    data_bytes = json.dumps(data, sort_keys=True).encode('utf-8')
                elif isinstance(data, str):
                    data_bytes = data.encode('utf-8')
                else:
                    data_bytes = data
                
                # Get encryption key
                encryption_key = self._get_encryption_key(key_id)
                
                # Create Fernet cipher
                fernet = Fernet(encryption_key)
                
                # Encrypt data
                encrypted_data = fernet.encrypt(data_bytes)
                
                # Create encryption context
                context = EncryptionContext(
                    algorithm=self.config['default_encryption_algorithm'],
                    key_id=key_id,
                    timestamp=datetime.utcnow()
                )
                
                # Audit encryption operation
                if self.config['audit_all_operations']:
                    self._audit_operation('data_encryption', {
                        'classification': classification.value,
                        'key_id': key_id,
                        'data_size_bytes': len(data_bytes)
                    })
                
                return {
                    'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
                    'encryption_context': asdict(context),
                    'classification': classification.value,
                    'checksum': hashlib.sha256(data_bytes).hexdigest()
                }
                
        except Exception as e:
            logger.error(f"Data encryption failed: {str(e)}")
            raise Exception(f"Encryption failed: {str(e)}")
    
    def decrypt_data(self, encrypted_package: Dict[str, Any]) -> Union[str, bytes, Dict[str, Any]]:
        """
        Decrypt data package
        
        Args:
            encrypted_package: Encrypted data package from encrypt_data
            
        Returns:
            Decrypted data
            
        Raises:
            Exception: If decryption fails
        """
        try:
            with self._lock:
                # Extract components
                encrypted_data_b64 = encrypted_package['encrypted_data']
                context = encrypted_package['encryption_context']
                classification = encrypted_package['classification']
                original_checksum = encrypted_package.get('checksum')
                
                # Decode encrypted data
                encrypted_data = base64.b64decode(encrypted_data_b64.encode('utf-8'))
                
                # Get decryption key
                key_id = context['key_id']
                decryption_key = self._get_encryption_key(key_id)
                
                # Create Fernet cipher
                fernet = Fernet(decryption_key)
                
                # Decrypt data
                decrypted_bytes = fernet.decrypt(encrypted_data)
                
                # Verify checksum if provided
                if original_checksum:
                    current_checksum = hashlib.sha256(decrypted_bytes).hexdigest()
                    if current_checksum != original_checksum:
                        raise Exception("Data integrity check failed")
                
                # Try to parse as JSON, fallback to string
                try:
                    decrypted_data = json.loads(decrypted_bytes.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    try:
                        decrypted_data = decrypted_bytes.decode('utf-8')
                    except UnicodeDecodeError:
                        decrypted_data = decrypted_bytes
                
                # Audit decryption operation
                if self.config['audit_all_operations']:
                    self._audit_operation('data_decryption', {
                        'classification': classification,
                        'key_id': key_id,
                        'data_size_bytes': len(decrypted_bytes)
                    })
                
                return decrypted_data
                
        except Exception as e:
            logger.error(f"Data decryption failed: {str(e)}")
            raise Exception(f"Decryption failed: {str(e)}")
    
    def classify_data(self, data: Dict[str, Any]) -> Dict[str, DataClassificationRule]:
        """
        Classify data fields according to classification rules
        
        Args:
            data: Data dictionary to classify
            
        Returns:
            Dict mapping field names to classification rules
        """
        try:
            classifications = {}
            
            for field_name, field_value in data.items():
                # Find matching classification rule
                for rule in self._classification_rules:
                    if re.match(rule.field_pattern, field_name, re.IGNORECASE):
                        classifications[field_name] = rule
                        break
                else:
                    # Default classification for unmatched fields
                    classifications[field_name] = DataClassificationRule(
                        field_pattern=field_name,
                        classification=DataClassification.INTERNAL,
                        category=DataCategory.TECHNICAL_DATA,
                        processing_purposes=[ProcessingPurpose.BUSINESS_ANALYTICS],
                        retention_period=RetentionPeriod.MEDIUM_TERM,
                        encryption_required=False,
                        description="Default classification for unmatched field"
                    )
            
            return classifications
            
        except Exception as e:
            logger.error(f"Data classification failed: {str(e)}")
            return {}
    
    def anonymize_data(self, data: Dict[str, Any], 
                      fields_to_anonymize: List[str] = None) -> Dict[str, Any]:
        """
        Anonymize sensitive data fields
        
        Args:
            data: Data to anonymize
            fields_to_anonymize: Specific fields to anonymize (optional)
            
        Returns:
            Anonymized data dictionary
        """
        try:
            anonymized_data = data.copy()
            
            # Get data classifications
            classifications = self.classify_data(data)
            
            # Determine fields to anonymize
            if fields_to_anonymize is None:
                fields_to_anonymize = [
                    field for field, rule in classifications.items()
                    if rule.anonymization_required or 
                    rule.category in [DataCategory.PERSONAL_DATA, DataCategory.SENSITIVE_PERSONAL_DATA]
                ]
            
            # Anonymize specified fields
            for field in fields_to_anonymize:
                if field in anonymized_data:
                    original_value = anonymized_data[field]
                    anonymized_data[field] = self._anonymize_value(original_value, field)
            
            # Audit anonymization
            if self.config['audit_all_operations']:
                self._audit_operation('data_anonymization', {
                    'fields_anonymized': fields_to_anonymize,
                    'total_fields': len(data)
                })
            
            return anonymized_data
            
        except Exception as e:
            logger.error(f"Data anonymization failed: {str(e)}")
            return data
    
    def pseudonymize_data(self, data: Dict[str, Any], 
                         user_id: str,
                         fields_to_pseudonymize: List[str] = None) -> Tuple[Dict[str, Any], str]:
        """
        Pseudonymize data with reversible transformation
        
        Args:
            data: Data to pseudonymize
            user_id: User identifier for pseudonymization key
            fields_to_pseudonymize: Specific fields to pseudonymize
            
        Returns:
            Tuple of (pseudonymized_data, pseudonymization_key_id)
        """
        try:
            pseudonymized_data = data.copy()
            
            # Generate pseudonymization key for user
            pseudo_key_id = f"pseudo_{user_id}_{int(datetime.utcnow().timestamp())}"
            pseudo_key = self._generate_pseudonymization_key(user_id, pseudo_key_id)
            
            # Get data classifications
            classifications = self.classify_data(data)
            
            # Determine fields to pseudonymize
            if fields_to_pseudonymize is None:
                fields_to_pseudonymize = [
                    field for field, rule in classifications.items()
                    if rule.category == DataCategory.PERSONAL_DATA
                ]
            
            # Pseudonymize specified fields
            for field in fields_to_pseudonymize:
                if field in pseudonymized_data:
                    original_value = pseudonymized_data[field]
                    pseudonymized_data[field] = self._pseudonymize_value(
                        original_value, pseudo_key, field
                    )
            
            # Store pseudonymization key securely
            self._store_pseudonymization_key(pseudo_key_id, pseudo_key, user_id)
            
            # Audit pseudonymization
            if self.config['audit_all_operations']:
                self._audit_operation('data_pseudonymization', {
                    'user_id': user_id,
                    'pseudo_key_id': pseudo_key_id,
                    'fields_pseudonymized': fields_to_pseudonymize
                })
            
            return pseudonymized_data, pseudo_key_id
            
        except Exception as e:
            logger.error(f"Data pseudonymization failed: {str(e)}")
            return data, ""
    
    def create_processing_record(self, data_subject_id: str,
                               data_categories: List[DataCategory],
                               processing_purposes: List[ProcessingPurpose],
                               legal_basis: str,
                               data_sources: List[str] = None,
                               consent_given: bool = False) -> str:
        """
        Create GDPR data processing record
        
        Args:
            data_subject_id: Data subject identifier
            data_categories: Categories of data being processed
            processing_purposes: Purposes for processing
            legal_basis: Legal basis for processing
            data_sources: Sources of the data
            consent_given: Whether consent was given
            
        Returns:
            Processing record ID
        """
        try:
            with self._lock:
                record_id = f"proc_{data_subject_id}_{int(datetime.utcnow().timestamp())}"
                
                # Determine retention period based on purposes
                retention_period = self._determine_retention_period(processing_purposes)
                
                record = DataProcessingRecord(
                    record_id=record_id,
                    data_subject_id=data_subject_id,
                    data_categories=data_categories,
                    processing_purposes=processing_purposes,
                    legal_basis=legal_basis,
                    data_sources=data_sources or [],
                    data_recipients=[],
                    retention_period=retention_period,
                    processing_start=datetime.utcnow(),
                    consent_given=consent_given,
                    consent_timestamp=datetime.utcnow() if consent_given else None,
                    security_measures=['encryption', 'access_control', 'audit_logging']
                )
                
                self._processing_records[record_id] = record
                
                # Track in monitoring
                self.monitoring.track_custom_metric(
                    'gdpr_processing_record_created',
                    1.0,
                    {
                        'data_subject_id': data_subject_id,
                        'legal_basis': legal_basis,
                        'consent_given': consent_given
                    }
                )
                
                logger.info(f"GDPR processing record created: {record_id}")
                return record_id
                
        except Exception as e:
            logger.error(f"Failed to create processing record: {str(e)}")
            return ""
    
    def handle_gdpr_request(self, request_type: str, data_subject_id: str,
                          verification_data: Dict[str, Any] = None) -> str:
        """
        Handle GDPR data subject request
        
        Args:
            request_type: Type of request (access, rectification, erasure, etc.)
            data_subject_id: Data subject identifier
            verification_data: Data for identity verification
            
        Returns:
            Request ID
        """
        try:
            with self._lock:
                request_id = f"gdpr_{request_type}_{data_subject_id}_{int(datetime.utcnow().timestamp())}"
                
                request = GDPRRequest(
                    request_id=request_id,
                    request_type=request_type,
                    data_subject_id=data_subject_id,
                    request_timestamp=datetime.utcnow(),
                    verification_status="pending",
                    processing_status="received"
                )
                
                self._gdpr_requests[request_id] = request
                
                # Process request based on type
                if request_type == "access":
                    self._process_data_access_request(request)
                elif request_type == "erasure":
                    self._process_data_erasure_request(request)
                elif request_type == "portability":
                    self._process_data_portability_request(request)
                elif request_type == "rectification":
                    self._process_data_rectification_request(request)
                
                # Track in monitoring
                self.monitoring.track_custom_metric(
                    'gdpr_request_received',
                    1.0,
                    {
                        'request_type': request_type,
                        'data_subject_id': data_subject_id
                    }
                )
                
                logger.info(f"GDPR request received: {request_type} for {data_subject_id}")
                return request_id
                
        except Exception as e:
            logger.error(f"Failed to handle GDPR request: {str(e)}")
            return ""
    
    def export_user_data(self, data_subject_id: str) -> Dict[str, Any]:
        """
        Export all data for a data subject (GDPR Article 20)
        
        Args:
            data_subject_id: Data subject identifier
            
        Returns:
            Dict containing all user data
        """
        try:
            with self._lock:
                export_data = {
                    'data_subject_id': data_subject_id,
                    'export_timestamp': datetime.utcnow().isoformat(),
                    'processing_records': [],
                    'personal_data': {},
                    'activity_logs': [],
                    'consent_records': []
                }
                
                # Get processing records
                user_records = [
                    asdict(record) for record in self._processing_records.values()
                    if record.data_subject_id == data_subject_id
                ]
                export_data['processing_records'] = user_records
                
                # Get personal data (would integrate with actual data stores)
                # This is a placeholder - in real implementation, would query databases
                export_data['personal_data'] = {
                    'note': 'Personal data would be collected from all relevant data stores'
                }
                
                # Audit export operation
                self._audit_operation('data_export', {
                    'data_subject_id': data_subject_id,
                    'records_exported': len(user_records)
                })
                
                return export_data
                
        except Exception as e:
            logger.error(f"Data export failed: {str(e)}")
            return {'error': str(e)}
    
    def delete_user_data(self, data_subject_id: str, 
                        verification_confirmed: bool = False) -> Dict[str, Any]:
        """
        Delete all data for a data subject (GDPR Article 17)
        
        Args:
            data_subject_id: Data subject identifier
            verification_confirmed: Whether identity verification is confirmed
            
        Returns:
            Dict containing deletion results
        """
        try:
            if not verification_confirmed:
                return {
                    'success': False,
                    'error': 'Identity verification required for data deletion'
                }
            
            with self._lock:
                deletion_results = {
                    'data_subject_id': data_subject_id,
                    'deletion_timestamp': datetime.utcnow().isoformat(),
                    'deleted_records': 0,
                    'anonymized_records': 0,
                    'retained_records': 0,
                    'details': []
                }
                
                # Process each processing record
                records_to_delete = []
                for record_id, record in self._processing_records.items():
                    if record.data_subject_id == data_subject_id:
                        # Check if data can be deleted based on legal basis and retention
                        if self._can_delete_record(record):
                            records_to_delete.append(record_id)
                            deletion_results['deleted_records'] += 1
                        elif self._should_anonymize_record(record):
                            # Anonymize instead of delete
                            self._anonymize_processing_record(record)
                            deletion_results['anonymized_records'] += 1
                        else:
                            # Must retain for legal reasons
                            deletion_results['retained_records'] += 1
                            deletion_results['details'].append({
                                'record_id': record_id,
                                'reason': 'Legal retention requirement',
                                'retention_until': self._calculate_retention_end(record).isoformat()
                            })
                
                # Delete records
                for record_id in records_to_delete:
                    del self._processing_records[record_id]
                
                # In real implementation, would delete from all data stores
                # This is a placeholder
                deletion_results['details'].append({
                    'action': 'Database deletion',
                    'status': 'Would be performed in production implementation'
                })
                
                # Audit deletion operation
                self._audit_operation('data_deletion', {
                    'data_subject_id': data_subject_id,
                    'deleted_records': deletion_results['deleted_records'],
                    'anonymized_records': deletion_results['anonymized_records'],
                    'retained_records': deletion_results['retained_records']
                })
                
                return deletion_results
                
        except Exception as e:
            logger.error(f"Data deletion failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def check_retention_compliance(self) -> Dict[str, Any]:
        """
        Check data retention compliance and identify data for deletion
        
        Returns:
            Dict containing compliance status and actions needed
        """
        try:
            with self._lock:
                compliance_report = {
                    'check_timestamp': datetime.utcnow().isoformat(),
                    'total_records': len(self._processing_records),
                    'expired_records': 0,
                    'expiring_soon_records': 0,
                    'compliant_records': 0,
                    'actions_needed': []
                }
                
                current_time = datetime.utcnow()
                warning_threshold = current_time + timedelta(days=30)  # 30 days warning
                
                for record_id, record in self._processing_records.items():
                    retention_end = self._calculate_retention_end(record)
                    
                    if current_time > retention_end:
                        compliance_report['expired_records'] += 1
                        compliance_report['actions_needed'].append({
                            'record_id': record_id,
                            'action': 'delete_or_anonymize',
                            'reason': 'Retention period expired',
                            'expired_date': retention_end.isoformat()
                        })
                    elif warning_threshold > retention_end:
                        compliance_report['expiring_soon_records'] += 1
                        compliance_report['actions_needed'].append({
                            'record_id': record_id,
                            'action': 'prepare_for_deletion',
                            'reason': 'Retention period expiring soon',
                            'expiry_date': retention_end.isoformat()
                        })
                    else:
                        compliance_report['compliant_records'] += 1
                
                return compliance_report
                
        except Exception as e:
            logger.error(f"Retention compliance check failed: {str(e)}")
            return {'error': str(e)}
    
    def rotate_encryption_keys(self) -> Dict[str, Any]:
        """
        Rotate encryption keys according to schedule
        
        Returns:
            Dict containing rotation results
        """
        try:
            with self._lock:
                rotation_results = {
                    'rotation_timestamp': datetime.utcnow().isoformat(),
                    'keys_rotated': 0,
                    'keys_scheduled': 0,
                    'errors': []
                }
                
                current_time = datetime.utcnow()
                rotation_threshold = timedelta(days=self.config['key_rotation_days'])
                
                for key_id, last_rotation in self._key_rotation_schedule.items():
                    if current_time - last_rotation > rotation_threshold:
                        try:
                            # Generate new key
                            new_key = Fernet.generate_key()
                            
                            # Store old key for decryption of existing data
                            old_key_id = f"{key_id}_old_{int(last_rotation.timestamp())}"
                            self._encryption_keys[old_key_id] = self._encryption_keys[key_id]
                            
                            # Update with new key
                            self._encryption_keys[key_id] = new_key
                            self._key_rotation_schedule[key_id] = current_time
                            
                            rotation_results['keys_rotated'] += 1
                            
                            # Audit key rotation
                            self._audit_operation('key_rotation', {
                                'key_id': key_id,
                                'old_key_id': old_key_id
                            })
                            
                        except Exception as e:
                            rotation_results['errors'].append({
                                'key_id': key_id,
                                'error': str(e)
                            })
                
                return rotation_results
                
        except Exception as e:
            logger.error(f"Key rotation failed: {str(e)}")
            return {'error': str(e)}
    
    # Private helper methods
    
    def _initialize_classification_rules(self):
        """Initialize default data classification rules"""
        self._classification_rules = [
            # Personal identifiers
            DataClassificationRule(
                field_pattern=r'.*(email|mail).*',
                classification=DataClassification.CONFIDENTIAL,
                category=DataCategory.PERSONAL_DATA,
                processing_purposes=[ProcessingPurpose.AUTHENTICATION, ProcessingPurpose.AUTHORIZATION],
                retention_period=RetentionPeriod.LONG_TERM,
                encryption_required=True,
                description="Email addresses"
            ),
            DataClassificationRule(
                field_pattern=r'.*(phone|mobile|tel).*',
                classification=DataClassification.CONFIDENTIAL,
                category=DataCategory.PERSONAL_DATA,
                processing_purposes=[ProcessingPurpose.AUTHENTICATION],
                retention_period=RetentionPeriod.MEDIUM_TERM,
                encryption_required=True,
                description="Phone numbers"
            ),
            DataClassificationRule(
                field_pattern=r'.*(name|firstname|lastname|fullname).*',
                classification=DataClassification.CONFIDENTIAL,
                category=DataCategory.PERSONAL_DATA,
                processing_purposes=[ProcessingPurpose.AUTHENTICATION, ProcessingPurpose.AUTHORIZATION],
                retention_period=RetentionPeriod.LONG_TERM,
                encryption_required=True,
                description="Personal names"
            ),
            
            # Sensitive data
            DataClassificationRule(
                field_pattern=r'.*(password|secret|key|token).*',
                classification=DataClassification.TOP_SECRET,
                category=DataCategory.SENSITIVE_PERSONAL_DATA,
                processing_purposes=[ProcessingPurpose.AUTHENTICATION],
                retention_period=RetentionPeriod.SHORT_TERM,
                encryption_required=True,
                anonymization_required=True,
                description="Authentication credentials"
            ),
            DataClassificationRule(
                field_pattern=r'.*(ssn|social.*security|tax.*id).*',
                classification=DataClassification.TOP_SECRET,
                category=DataCategory.SENSITIVE_PERSONAL_DATA,
                processing_purposes=[ProcessingPurpose.LEGAL_COMPLIANCE],
                retention_period=RetentionPeriod.LONG_TERM,
                encryption_required=True,
                description="Government identifiers"
            ),
            
            # Technical data
            DataClassificationRule(
                field_pattern=r'.*(ip.*address|user.*agent|session.*id).*',
                classification=DataClassification.INTERNAL,
                category=DataCategory.TECHNICAL_DATA,
                processing_purposes=[ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.AUDIT_LOGGING],
                retention_period=RetentionPeriod.MEDIUM_TERM,
                encryption_required=False,
                description="Technical metadata"
            ),
            
            # Activity data
            DataClassificationRule(
                field_pattern=r'.*(log|audit|activity|event).*',
                classification=DataClassification.INTERNAL,
                category=DataCategory.TECHNICAL_DATA,
                processing_purposes=[ProcessingPurpose.AUDIT_LOGGING, ProcessingPurpose.SECURITY_MONITORING],
                retention_period=RetentionPeriod.LONG_TERM,
                encryption_required=False,
                description="Activity and audit logs"
            )
        ]
    
    def _initialize_encryption_keys(self):
        """Initialize encryption keys"""
        try:
            # Generate default encryption key
            default_key = Fernet.generate_key()
            self._encryption_keys['default'] = default_key
            self._key_rotation_schedule['default'] = datetime.utcnow()
            
            # In production, keys would be loaded from Azure Key Vault
            logger.info("Encryption keys initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption keys: {str(e)}")
    
    def _get_encryption_key(self, key_id: str) -> bytes:
        """Get encryption key by ID"""
        if key_id not in self._encryption_keys:
            # In production, would retrieve from Azure Key Vault
            raise Exception(f"Encryption key not found: {key_id}")
        
        return self._encryption_keys[key_id]
    
    def _anonymize_value(self, value: Any, field_name: str) -> str:
        """Anonymize a field value"""
        if value is None:
            return None
        
        # Generate consistent anonymized value based on field type
        if 'email' in field_name.lower():
            return f"user{abs(hash(str(value))) % 10000}@anonymized.local"
        elif 'phone' in field_name.lower():
            return f"******-{abs(hash(str(value))) % 10000:04d}"
        elif 'name' in field_name.lower():
            return f"User{abs(hash(str(value))) % 10000}"
        else:
            # Generic anonymization
            return f"ANON_{abs(hash(str(value))) % 100000}"
    
    def _pseudonymize_value(self, value: Any, pseudo_key: bytes, field_name: str) -> str:
        """Pseudonymize a field value (reversible)"""
        if value is None:
            return None
        
        # Create pseudonym using HMAC
        import hmac
        pseudonym = hmac.new(pseudo_key, str(value).encode('utf-8'), hashlib.sha256).hexdigest()
        return f"PSEUDO_{pseudonym[:16]}"
    
    def _generate_pseudonymization_key(self, user_id: str, key_id: str) -> bytes:
        """Generate pseudonymization key for user"""
        # In production, would use proper key derivation
        seed = f"{user_id}_{key_id}_{self.config['pseudonymization_key_length']}"
        return hashlib.sha256(seed.encode('utf-8')).digest()
    
    def _store_pseudonymization_key(self, key_id: str, key: bytes, user_id: str):
        """Store pseudonymization key securely"""
        # In production, would store in Azure Key Vault
        logger.debug(f"Pseudonymization key stored: {key_id} for user {user_id}")
    
    def _determine_retention_period(self, purposes: List[ProcessingPurpose]) -> RetentionPeriod:
        """Determine retention period based on processing purposes"""
        # Legal compliance requires longest retention
        if ProcessingPurpose.LEGAL_COMPLIANCE in purposes:
            return RetentionPeriod.LONG_TERM
        elif ProcessingPurpose.AUDIT_LOGGING in purposes:
            return RetentionPeriod.LONG_TERM
        elif ProcessingPurpose.SECURITY_MONITORING in purposes:
            return RetentionPeriod.MEDIUM_TERM
        else:
            return RetentionPeriod.MEDIUM_TERM
    
    def _calculate_retention_end(self, record: DataProcessingRecord) -> datetime:
        """Calculate when data retention period ends"""
        if record.retention_period == RetentionPeriod.PERMANENT:
            return datetime.max
        elif record.retention_period == RetentionPeriod.IMMEDIATE:
            return record.processing_start
        else:
            return record.processing_start + timedelta(days=record.retention_period.value)
    
    def _can_delete_record(self, record: DataProcessingRecord) -> bool:
        """Check if record can be deleted"""
        # Cannot delete if legal compliance purpose and within retention period
        if ProcessingPurpose.LEGAL_COMPLIANCE in record.processing_purposes:
            return datetime.utcnow() > self._calculate_retention_end(record)
        
        # Can delete if retention period expired
        return datetime.utcnow() > self._calculate_retention_end(record)
    
    def _should_anonymize_record(self, record: DataProcessingRecord) -> bool:
        """Check if record should be anonymized instead of deleted"""
        # Anonymize if needed for business analytics but personal data no longer needed
        return (ProcessingPurpose.BUSINESS_ANALYTICS in record.processing_purposes and
                DataCategory.PERSONAL_DATA in record.data_categories)
    
    def _anonymize_processing_record(self, record: DataProcessingRecord):
        """Anonymize a processing record"""
        # Remove personal data categories
        record.data_categories = [
            cat for cat in record.data_categories 
            if cat not in [DataCategory.PERSONAL_DATA, DataCategory.SENSITIVE_PERSONAL_DATA]
        ]
        record.data_categories.append(DataCategory.ANONYMOUS_DATA)
        
        # Update processing purposes
        record.processing_purposes = [ProcessingPurpose.BUSINESS_ANALYTICS]
        
        logger.info(f"Processing record anonymized: {record.record_id}")
    
    def _process_data_access_request(self, request: GDPRRequest):
        """Process data access request"""
        try:
            # Export user data
            user_data = self.export_user_data(request.data_subject_id)
            
            request.response_data = user_data
            request.processing_status = "completed"
            request.completion_timestamp = datetime.utcnow()
            
        except Exception as e:
            request.processing_status = "failed"
            request.notes = f"Processing failed: {str(e)}"
    
    def _process_data_erasure_request(self, request: GDPRRequest):
        """Process data erasure request"""
        try:
            # Delete user data (would require verification in production)
            deletion_result = self.delete_user_data(
                request.data_subject_id, 
                verification_confirmed=True  # Simplified for demo
            )
            
            request.response_data = deletion_result
            request.processing_status = "completed"
            request.completion_timestamp = datetime.utcnow()
            
        except Exception as e:
            request.processing_status = "failed"
            request.notes = f"Processing failed: {str(e)}"
    
    def _process_data_portability_request(self, request: GDPRRequest):
        """Process data portability request"""
        try:
            # Export data in portable format
            user_data = self.export_user_data(request.data_subject_id)
            
            # Convert to portable format (JSON)
            portable_data = {
                'format': 'JSON',
                'version': '1.0',
                'data': user_data
            }
            
            request.response_data = portable_data
            request.processing_status = "completed"
            request.completion_timestamp = datetime.utcnow()
            
        except Exception as e:
            request.processing_status = "failed"
            request.notes = f"Processing failed: {str(e)}"
    
    def _process_data_rectification_request(self, request: GDPRRequest):
        """Process data rectification request"""
        # Placeholder - would implement data correction logic
        request.processing_status = "manual_review_required"
        request.notes = "Data rectification requires manual review"
    
    def _audit_operation(self, operation: str, details: Dict[str, Any]):
        """Audit data protection operation"""
        audit_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'operation': operation,
            'details': details
        }
        
        # Send to monitoring system
        self.monitoring.track_custom_metric(
            f'data_protection_{operation}',
            1.0,
            details
        )
        
        logger.info(f"Data protection audit: {operation}", extra=details)


# Global service instance
_data_protection_service = None


def get_data_protection_service() -> DataProtectionService:
    """Get the global data protection service instance"""
    global _data_protection_service
    if _data_protection_service is None:
        _data_protection_service = DataProtectionService()
    return _data_protection_service