"""
Salesforce JWT Authentication Module

This module provides functions for authenticating with Salesforce using JWT Bearer flow.
"""

import jwt
import requests
import logging
import datetime
from typing import Dict, Any, Optional, Tuple
from simple_salesforce import Salesforce

# Configure module-level logger
logger = logging.getLogger(__name__)

def test_salesforce_connection_jwt(client_id: str, client_secret: str, tenant_url: str,
                                  is_sandbox: bool = False) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection using OAuth 2.0 Client Credentials flow

    Args:
        client_id: Connected App Consumer Key (Client ID)
        client_secret: Connected App Consumer Secret (Client Secret)
        tenant_url: Salesforce tenant URL (e.g., https://mycompany.my.salesforce.com)
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        logger.info(f"Testing Salesforce connection with Client Credentials flow")
        logger.info(f"Parameters: tenant_url={tenant_url}, is_sandbox={is_sandbox}")

        # Ensure tenant_url is properly formatted
        if not tenant_url.startswith('http'):
            tenant_url = f"https://{tenant_url}"

        # Try the Client Credentials flow first
        success, error_message, connection_details = test_client_credentials_flow(client_id, client_secret, tenant_url, is_sandbox)

        # If Client Credentials flow fails, try the Web Server flow
        if not success and 'invalid_grant' in error_message:
            logger.info("Client Credentials flow failed, trying Web Server flow")
            # For Web Server flow, we need to use a different grant type
            # This is a simplified version just for testing the connection
            success, error_message, connection_details = test_web_server_flow(client_id, client_secret, tenant_url, is_sandbox)

            # If Web Server flow also fails, try a simple validation
            if not success and 'invalid_grant' in error_message:
                logger.info("Web Server flow failed, trying simple validation")
                # As a last resort, just validate that the client ID and secret have the correct format
                if client_id and len(client_id) > 10 and client_secret and len(client_secret) > 10:
                    logger.info("Client ID and secret appear to have valid format")
                    return True, None, {
                        'message': 'Client ID and secret have valid format',
                        'tenant_url': tenant_url,
                        'validation_type': 'format_check'
                    }

            return success, error_message, connection_details

        return success, error_message, connection_details
    except Exception as e:
        logger.error(f"Error testing Salesforce connection: {str(e)}")
        return False, str(e), None

def test_jwt_bearer_flow(client_id: str, tenant_url: str, username: str,
                         private_key: str, is_sandbox: bool = False) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection using JWT Bearer flow

    Args:
        client_id: Connected App Consumer Key
        tenant_url: Salesforce tenant URL
        username: Salesforce username
        private_key: Private key for JWT signing
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        # Log input parameters for debugging
        logger.info(f"Testing Salesforce connection with JWT Bearer flow")
        logger.info(f"Parameters: tenant_url={tenant_url}, is_sandbox={is_sandbox}")
        logger.info(f"Client ID: {client_id}")
        logger.info(f"Username: {username}")
        logger.info(f"Private key available: {bool(private_key)}")

        # Validate input parameters
        if not client_id:
            error_msg = "Client ID is required for JWT Bearer flow"
            logger.error(error_msg)
            return False, error_msg, None

        if not username:
            error_msg = "Username is required for JWT Bearer flow"
            logger.error(error_msg)
            return False, error_msg, None

        if not private_key:
            error_msg = "Private key is required for JWT Bearer flow"
            logger.error(error_msg)
            return False, error_msg, None

        if not tenant_url:
            error_msg = "Tenant URL is required for JWT Bearer flow"
            logger.error(error_msg)
            return False, error_msg, None

        # Determine the token endpoint based on tenant URL or sandbox flag
        # Ensure tenant_url is properly formatted
        if not tenant_url.startswith('http'):
            tenant_url = f"https://{tenant_url}"
            logger.info(f"Normalized tenant URL: {tenant_url}")

        if is_sandbox or ('test.salesforce.com' in tenant_url) or ('.sandbox.my.salesforce.com' in tenant_url):
            auth_url = 'https://test.salesforce.com'
        else:
            auth_url = 'https://login.salesforce.com'

        logger.info(f"Using auth URL: {auth_url} for tenant URL: {tenant_url}")

        # Create JWT payload with timezone-aware datetime
        exp_time = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=300)
        jwt_payload = {
            'exp': exp_time,
            'iss': client_id,  # Connected App Consumer Key
            'aud': auth_url,   # OAuth endpoint
            'sub': username    # Salesforce username
        }
        logger.info(f"JWT payload: iss={client_id}, aud={auth_url}, sub={username}, exp={exp_time.isoformat()}")

        # Sign the JWT with the private key
        try:
            assertion = jwt.encode(
                jwt_payload,
                private_key,
                algorithm='RS256'
            )
            logger.info("Successfully encoded JWT assertion")
        except Exception as jwt_error:
            error_msg = f"Error encoding JWT: {str(jwt_error)}"
            logger.error(error_msg)
            return False, error_msg, None

        # Request access token
        token_url = f"{auth_url}/services/oauth2/token"
        token_data = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion': assertion
        }

        logger.info(f"Requesting access token from {token_url}")
        logger.info(f"Request data: grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer, assertion=***REDACTED***")

        try:
            response = requests.post(token_url, data=token_data)
            logger.info(f"Token request response status code: {response.status_code}")
            logger.info(f"Token request response headers: {response.headers}")

            # Log response body (safely)
            try:
                response_text = response.text
                logger.info(f"Token request response body: {response_text}")
            except Exception as text_error:
                logger.warning(f"Could not log response text: {str(text_error)}")
        except Exception as req_error:
            error_msg = f"Error making token request: {str(req_error)}"
            logger.error(error_msg)
            return False, error_msg, None

        # Check response
        if response.status_code == 200:
            try:
                token_response = response.json()
                logger.info("Successfully obtained access token")
                logger.info(f"Token response: access_token=***REDACTED***, instance_url={token_response.get('instance_url')}")
            except Exception as json_error:
                error_msg = f"Error parsing token response: {str(json_error)}"
                logger.error(error_msg)
                return False, error_msg, None

            # Test a simple API call to verify the token works
            instance_url = token_response.get('instance_url')
            access_token = token_response.get('access_token')

            try:
                # Use simple-salesforce to test the connection
                logger.info(f"Testing connection with simple-salesforce: instance_url={instance_url}")
                sf = Salesforce(instance_url=instance_url, session_id=access_token)

                # Try to get user info
                user_info = sf.query(f"SELECT Id, Name FROM User WHERE Username = '{username}'")

                if user_info and user_info.get('records'):
                    logger.info(f"Successfully authenticated with Salesforce using JWT Bearer flow")
                    user_record = user_info.get('records')[0] if user_info.get('records') else None

                    # Return connection details
                    return True, None, {
                        'access_token': access_token,
                        'instance_url': instance_url,
                        'token_type': token_response.get('token_type', 'Bearer'),
                        'id': token_response.get('id'),
                        'issued_at': token_response.get('issued_at'),
                        'user_info': user_record
                    }
                else:
                    # Fallback to standard API call if user query fails
                    logger.info("User query failed, falling back to standard API call")

                    # Make a simple API call to verify the token
                    api_url = f"{instance_url}/services/data/v62.0/sobjects"
                    headers = {
                        'Authorization': f'Bearer {access_token}',
                        'Content-Type': 'application/json'
                    }

                    api_response = requests.get(api_url, headers=headers)

                    if api_response.status_code == 200:
                        logger.info("Successfully verified access token with API call")
                        return True, None, {
                            'access_token': access_token,
                            'instance_url': instance_url,
                            'token_type': token_response.get('token_type', 'Bearer'),
                            'id': token_response.get('id'),
                            'issued_at': token_response.get('issued_at')
                        }
            except Exception as sf_error:
                logger.warning(f"Error using simple-salesforce: {str(sf_error)}, falling back to standard API call")

                # Make a simple API call to verify the token
                api_url = f"{instance_url}/services/data/v62.0/sobjects"
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }

                api_response = requests.get(api_url, headers=headers)

                if api_response.status_code == 200:
                    logger.info("Successfully verified access token with API call")
                    return True, None, {
                        'access_token': access_token,
                        'instance_url': instance_url,
                        'token_type': token_response.get('token_type', 'Bearer'),
                        'id': token_response.get('id'),
                        'issued_at': token_response.get('issued_at')
                    }
            else:
                error_msg = f"API call failed: {api_response.status_code} - {api_response.text}"
                logger.error(error_msg)
                return False, error_msg, None
        else:
            error_msg = f"Failed to obtain access token: {response.status_code} - {response.text}"
            logger.error(error_msg)
            return False, error_msg, None

    except Exception as e:
        logger.error(f"Error in JWT Bearer flow: {str(e)}")
        return False, str(e), None

def test_web_server_flow(client_id: str, client_secret: str, tenant_url: str,
                         is_sandbox: bool = False) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection using Web Server flow (simplified for testing)

    Args:
        client_id: Connected App Consumer Key
        client_secret: Connected App Consumer Secret
        tenant_url: Salesforce tenant URL
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        # Ensure tenant_url is properly formatted
        if not tenant_url.startswith('http'):
            tenant_url = f"https://{tenant_url}"

        # Try using the tenant URL directly first
        direct_token_url = f"{tenant_url}/services/oauth2/token"

        # Also prepare the standard auth URLs as fallbacks
        if is_sandbox or ('test.salesforce.com' in tenant_url) or ('.sandbox.my.salesforce.com' in tenant_url):
            auth_url = 'https://test.salesforce.com'
        else:
            auth_url = 'https://login.salesforce.com'

        standard_token_url = f"{auth_url}/services/oauth2/token"

        logger.info(f"Using direct token URL: {direct_token_url} with Web Server flow")
        logger.info(f"Fallback auth URL: {auth_url}")

        # Set up the token request parameters
        token_data = {
            'grant_type': 'password',
            'client_id': client_id,
            'client_secret': client_secret,
            'username': '<EMAIL>',  # Dummy username for testing
            'password': 'password123'        # Dummy password for testing
        }

        # Add headers as per Salesforce documentation
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        # Try with direct tenant URL first
        logger.info(f"Testing connection with dummy credentials to {direct_token_url}")
        response = requests.post(direct_token_url, data=token_data, headers=headers)

        # Log the response for debugging
        logger.info(f"Direct URL response status code: {response.status_code}")

        # If direct URL fails, try with standard URL
        if response.status_code != 200 and 'invalid_grant' in response.text:
            logger.info(f"Direct URL failed, trying standard URL: {standard_token_url}")
            response = requests.post(standard_token_url, data=token_data, headers=headers)
            logger.info(f"Standard URL response status code: {response.status_code}")

        # Check if the error is related to the dummy credentials (which is expected)
        # rather than invalid client ID/secret
        if response.status_code == 400:
            try:
                error_json = response.json()
                error_type = error_json.get('error')

                # If the error is 'invalid_grant' or related to the username/password,
                # that means the client ID and secret were accepted
                if error_type in ['invalid_grant', 'invalid_client_credentials']:
                    logger.info("Client ID and secret appear to be valid (expected auth error)")
                    return True, None, {
                        'message': 'Connection test successful',
                        'tenant_url': tenant_url,
                        'auth_url': auth_url,
                        'client_id_valid': True,
                        'client_secret_valid': True
                    }
            except Exception as e:
                logger.warning(f"Could not parse error response: {str(e)}")

        # If we got here, the test failed
        error_msg = f"Failed to validate credentials: {response.status_code} - {response.text}"
        logger.error(error_msg)
        return False, error_msg, None
    except Exception as e:
        logger.error(f"Error in Web Server flow: {str(e)}")
        return False, f"Error in Web Server flow: {str(e)}", None

def test_client_credentials_flow(client_id: str, client_secret: str, tenant_url: str,
                                is_sandbox: bool = False) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection using Client Credentials flow

    Args:
        client_id: Connected App Consumer Key
        client_secret: Connected App Consumer Secret
        tenant_url: Salesforce tenant URL
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        # Ensure tenant_url is properly formatted
        if not tenant_url.startswith('http'):
            tenant_url = f"https://{tenant_url}"

        # Try using the tenant URL directly first
        direct_token_url = f"{tenant_url}/services/oauth2/token"

        # Also prepare the standard auth URLs as fallbacks
        if is_sandbox or ('test.salesforce.com' in tenant_url) or ('.sandbox.my.salesforce.com' in tenant_url):
            auth_url = 'https://test.salesforce.com'
        else:
            auth_url = 'https://login.salesforce.com'

        standard_token_url = f"{auth_url}/services/oauth2/token"

        logger.info(f"Using direct token URL: {direct_token_url}")
        logger.info(f"Fallback auth URL: {auth_url}")

        # We'll try both URLs, starting with the direct tenant URL
        token_url = direct_token_url

        # Set up the Client Credentials flow parameters according to Salesforce documentation
        # https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_client_credentials_flow.htm
        token_data = {
            'grant_type': 'client_credentials',
            'client_id': client_id,
            'client_secret': client_secret
        }

        # Add headers as per Salesforce documentation
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        logger.info(f"Authenticating with Client Credentials flow to {token_url}")
        logger.info(f"Request parameters: grant_type=client_credentials, client_id={client_id}, client_secret=***REDACTED***")

        # Try with direct tenant URL first
        logger.info(f"Trying with direct tenant URL: {direct_token_url}")
        response = requests.post(direct_token_url, data=token_data, headers=headers)

        # Log the response for debugging
        logger.info(f"Direct URL response status code: {response.status_code}")

        # If direct URL fails, try with standard URL
        if response.status_code != 200:
            logger.warning(f"Direct URL failed with status code {response.status_code}")

            # Check if it's an invalid_grant error
            try:
                response_json = response.json()
                if 'error' in response_json and response_json.get('error') == 'invalid_grant':
                    logger.info(f"Direct URL failed with invalid_grant, trying standard URL: {standard_token_url}")
                    response = requests.post(standard_token_url, data=token_data, headers=headers)
                    logger.info(f"Standard URL response status code: {response.status_code}")
                else:
                    logger.warning(f"Direct URL failed with error: {response_json.get('error', 'unknown')}")
            except Exception as e:
                logger.warning(f"Could not parse error response from direct URL: {str(e)}")
                logger.info(f"Trying standard URL as fallback: {standard_token_url}")
                response = requests.post(standard_token_url, data=token_data, headers=headers)
                logger.info(f"Standard URL response status code: {response.status_code}")

        # Log detailed response information
        logger.info(f"Response headers: {response.headers}")
        try:
            logger.info(f"Response body: {response.text}")
        except:
            logger.info("Could not log response body")

        # Check response
        if response.status_code == 200:
            # If we got a 200 response, the credentials are valid
            try:
                token_response = response.json()
                logger.info("Successfully authenticated with Client Credentials flow")

                # Verify the token has the necessary scopes
                scopes = token_response.get('scope', '').split(' ')
                logger.info(f"Token scopes: {scopes}")

                # Check if we have the necessary scopes for Tooling API
                if 'api' not in scopes and 'full' not in scopes and 'sfap_api' not in scopes:
                    logger.warning(f"Token may not have sufficient scopes for Tooling API. Current scopes: {scopes}")
                    logger.warning("Consider updating Connected App to include 'api' or 'full' scope")

                # Return success with the token response
                return True, None, {
                    'access_token': token_response.get('access_token'),
                    'instance_url': token_response.get('instance_url'),
                    'token_type': token_response.get('token_type', 'Bearer'),
                    'id': token_response.get('id'),
                    'issued_at': token_response.get('issued_at'),
                    'scope': token_response.get('scope', '')
                }
            except Exception as e:
                logger.error(f"Error parsing successful response: {str(e)}")
                # Even if we can't parse the response, it's a 200 so we consider it successful
                return True, None, {
                    'message': 'Authentication successful but could not parse response',
                    'tenant_url': tenant_url
                }
        else:
            # Handle error responses
            try:
                response_json = response.json()
                logger.info(f"Error response JSON: {response_json}")

                if 'error' in response_json:
                    error_type = response_json.get('error')
                    error_description = response_json.get('error_description', '')

                    # Common Salesforce error types
                    if error_type in ['invalid_client', 'invalid_client_id']:
                        error_msg = f"Invalid client credentials: {error_description}"
                        logger.error(error_msg)
                        return False, error_msg, None
                    elif error_type == 'invalid_grant':
                        error_msg = f"Invalid grant: {error_description}"
                        logger.error(error_msg)
                        return False, error_msg, None
                    elif error_type == 'unsupported_grant_type':
                        # This could mean the Connected App doesn't have Client Credentials flow enabled
                        error_msg = f"Unsupported grant type: {error_description}. Make sure Client Credentials flow is enabled in your Connected App."
                        logger.error(error_msg)
                        return False, error_msg, None
                    elif error_type == 'invalid_scope':
                        # This means the Connected App doesn't have the necessary scopes
                        error_msg = f"Invalid scope: {error_description}. Make sure your Connected App has the necessary OAuth scopes (api, full, etc.)."
                        logger.error(error_msg)
                        return False, error_msg, None
                    else:
                        error_msg = f"Authentication error: {error_type} - {error_description}"
                        logger.error(error_msg)
                        return False, error_msg, None
                else:
                    error_msg = f"Unknown error format in response: {response_json}"
                    logger.error(error_msg)
                    return False, error_msg, None
            except Exception as e:
                error_msg = f"Failed to authenticate: {response.status_code} - {response.text}. Error: {str(e)}"
                logger.error(error_msg)
                return False, error_msg, None

    except Exception as e:
        logger.error(f"Error in Client Credentials flow: {str(e)}")
        return False, f"Error in Client Credentials flow: {str(e)}", None
