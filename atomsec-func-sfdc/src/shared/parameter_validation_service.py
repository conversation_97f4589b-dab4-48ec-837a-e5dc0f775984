"""
Parameter Validation Service Module

This module provides comprehensive parameter validation and sanitization services
for handling task parameters across the SFDC service. It includes robust error
handling for malformed parameters and ensures consistent JSON serialization.

Features:
- Comprehensive parameter validation
- Malformed parameter recovery
- JSON serialization safety
- Type conversion and sanitization
- Detailed error reporting
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, date
import ast
import re

# Configure module-level logger
logger = logging.getLogger(__name__)

class ParameterValidationService:
    """
    Comprehensive service for parameter validation and sanitization
    """
    
    def __init__(self):
        """Initialize the parameter validation service"""
        self.validation_errors = []
    
    def validate_and_sanitize_parameters(
        self, 
        params: Any, 
        required_fields: Optional[List[str]] = None,
        field_types: Optional[Dict[str, type]] = None
    ) -> Dict[str, Any]:
        """
        Validate and sanitize parameters with comprehensive error handling
        
        Args:
            params: Parameters to validate (dict, str, or any format)
            required_fields: List of required field names
            field_types: Dict mapping field names to expected types
            
        Returns:
            Dict[str, Any]: Validated and sanitized parameters
        """
        try:
            # Parse parameters from any format
            parsed_params = self._parse_parameters(params)
            
            # Validate required fields
            if required_fields:
                missing_fields = [f for f in required_fields if f not in parsed_params]
                if missing_fields:
                    raise ValueError(f"Missing required fields: {missing_fields}")
            
            # Validate field types
            if field_types:
                type_errors = []
                for field, expected_type in field_types.items():
                    if field in parsed_params:
                        actual_value = parsed_params[field]
                        if not isinstance(actual_value, expected_type):
                            try:
                                # Attempt type conversion
                                if expected_type == int:
                                    parsed_params[field] = int(actual_value)
                                elif expected_type == float:
                                    parsed_params[field] = float(actual_value)
                                elif expected_type == str:
                                    parsed_params[field] = str(actual_value)
                                elif expected_type == bool:
                                    parsed_params[field] = bool(actual_value)
                                elif expected_type == list:
                                    if isinstance(actual_value, str):
                                        parsed_params[field] = [actual_value]
                                    else:
                                        parsed_params[field] = list(actual_value)
                                elif expected_type == dict:
                                    if isinstance(actual_value, str):
                                        parsed_params[field] = self._parse_parameters(actual_value)
                                    else:
                                        parsed_params[field] = dict(actual_value)
                            except (ValueError, TypeError) as e:
                                type_errors.append(f"Field '{field}': expected {expected_type.__name__}, got {type(actual_value).__name__}")
                
                if type_errors:
                    raise ValueError(f"Type validation errors: {type_errors}")
            
            # Sanitize parameters
            sanitized_params = self._sanitize_parameters(parsed_params)
            
            # Ensure JSON serializability
            self._ensure_json_serializable(sanitized_params)
            
            return sanitized_params
            
        except Exception as e:
            logger.error(f"Parameter validation failed: {str(e)}")
            self.validation_errors.append(str(e))
            return self._create_safe_fallback(params)
    
    def _parse_parameters(self, params: Any) -> Dict[str, Any]:
        """Parse parameters from any input format"""
        if params is None:
            return {}
        
        if isinstance(params, dict):
            return params.copy()
        
        if isinstance(params, str):
            return self._parse_string_parameters(params)
        
        # Handle other types by converting to dict
        try:
            return dict(params)
        except (ValueError, TypeError):
            logger.warning(f"Converting non-dict parameter to dict: {type(params)}")
            return {"value": params}
    
    def _parse_string_parameters(self, params_str: str) -> Dict[str, Any]:
        """Parse parameters from string format"""
        if not params_str.strip():
            return {}
        
        # Try JSON first
        try:
            return json.loads(params_str)
        except json.JSONDecodeError:
            pass
        
        # Try Python literal evaluation
        try:
            result = ast.literal_eval(params_str)
            if isinstance(result, dict):
                return result
            else:
                return {"value": result}
        except (ValueError, SyntaxError):
            pass
        
        # Try regex-based parsing for Python dict format
        try:
            return self._parse_python_dict_format(params_str)
        except Exception:
            pass
        
        logger.warning(f"Failed to parse parameter string: {params_str[:100]}...")
        return {}
    
    def _parse_python_dict_format(self, params_str: str) -> Dict[str, Any]:
        """Parse Python dictionary format with single quotes"""
        # Clean up the string
        cleaned = params_str.strip()
        
        # Handle simple key-value pairs
        if cleaned.startswith('{') and cleaned.endswith('}'):
            cleaned = cleaned[1:-1].strip()
        
        # Split into key-value pairs
        pairs = re.findall(r"['\"]?([^'\":,]+)['\"]?\s*:\s*([^,]+)(?:,|$)", cleaned)
        
        result = {}
        for key, value in pairs:
            key = key.strip()
            value = value.strip()
            
            # Clean up value
            if value.startswith("'") and value.endswith("'"):
                value = value[1:-1]
            elif value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            elif value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif self._is_float(value):
                value = float(value)
            elif value.startswith('[') and value.endswith(']'):
                try:
                    value = ast.literal_eval(value)
                except:
                    value = value
            
            result[key] = value
        
        return result
    
    def _is_float(self, value: str) -> bool:
        """Check if string represents a float"""
        try:
            float(value)
            return '.' in value
        except ValueError:
            return False
    
    def _sanitize_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize parameters for JSON compatibility"""
        sanitized = {}
        
        for key, value in params.items():
            try:
                # Handle None values
                if value is None:
                    sanitized[key] = None
                    continue
                
                # Handle datetime objects
                if hasattr(value, 'isoformat'):
                    sanitized[key] = value.isoformat()
                # Handle bytes
                elif isinstance(value, bytes):
                    sanitized[key] = value.decode('utf-8', errors='ignore')
                # Handle sets
                elif isinstance(value, set):
                    sanitized[key] = list(value)
                # Handle datetime/date objects in lists/dicts
                elif isinstance(value, list):
                    sanitized[key] = [self._sanitize_value(item) for item in value]
                elif isinstance(value, dict):
                    sanitized[key] = self._sanitize_parameters(value)
                else:
                    # Test JSON serializability
                    json.dumps({key: value})
                    sanitized[key] = value
                    
            except (TypeError, ValueError):
                # Convert non-serializable types to string
                sanitized[key] = str(value)
                logger.warning(f"Converted non-serializable parameter {key} to string")
        
        return sanitized
    
    def _sanitize_value(self, value: Any) -> Any:
        """Sanitize individual values"""
        if value is None:
            return None
        if hasattr(value, 'isoformat'):
            return value.isoformat()
        if isinstance(value, bytes):
            return value.decode('utf-8', errors='ignore')
        if isinstance(value, set):
            return list(value)
        if isinstance(value, dict):
            return self._sanitize_parameters(value)
        if isinstance(value, list):
            return [self._sanitize_value(item) for item in value]
        
        try:
            json.dumps(value)
            return value
        except (TypeError, ValueError):
            return str(value)
    
    def _ensure_json_serializable(self, params: Dict[str, Any]) -> None:
        """Ensure parameters are JSON serializable"""
        try:
            json.dumps(params)
        except (TypeError, ValueError) as e:
            raise ValueError(f"Parameters not JSON serializable: {e}")
    
    def _create_safe_fallback(self, original_params: Any) -> Dict[str, Any]:
        """Create a safe fallback for failed parameter parsing"""
        try:
            # Try to extract basic info
            if isinstance(original_params, str):
                return {
                    "original_string": original_params,
                    "parse_error": True,
                    "fallback": True
                }
            elif original_params is not None:
                return {
                    "original_type": str(type(original_params)),
                    "original_repr": str(original_params)[:200],
                    "parse_error": True,
                    "fallback": True
                }
            else:
                return {"parse_error": True, "fallback": True}
        except Exception:
            return {"parse_error": True, "fallback": True}
    
    def get_validation_errors(self) -> List[str]:
        """Get list of validation errors"""
        return self.validation_errors.copy()
    
    def clear_validation_errors(self) -> None:
        """Clear validation error history"""
        self.validation_errors.clear()
    
    def validate_json_string(self, json_str: str) -> Dict[str, Any]:
        """
        Validate and parse a JSON string
        
        Args:
            json_str: JSON string to validate
            
        Returns:
            Dict[str, Any]: Parsed JSON or error information
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            return {
                "error": "Invalid JSON",
                "details": str(e),
                "line": e.lineno,
                "column": e.colno,
                "position": e.pos
            }


# Global service instance
_parameter_validation_service = None


def get_parameter_validation_service() -> ParameterValidationService:
    """
    Get the global parameter validation service instance
    
    Returns:
        ParameterValidationService: The service instance
    """
    global _parameter_validation_service
    
    if _parameter_validation_service is None:
        _parameter_validation_service = ParameterValidationService()
    
    return _parameter_validation_service