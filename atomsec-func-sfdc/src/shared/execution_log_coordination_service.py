"""
Execution Log Coordination Service

This module provides execution log coordination across sequential tasks:
- Execution log ID management across task sequences
- Task sequence state management and tracking
- Execution log validation and security checks
- Coordination to ensure all tasks use same execution_log_id for proper tracking

Best practices implemented:
- Centralized execution log management
- Task sequence state tracking
- Security validation for execution logs
- Correlation across distributed task execution
- Audit trail for task sequence execution
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List, Union, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import threading
import hashlib

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import existing modules
from src.shared.common import is_local_dev, is_test_env
from src.shared.task_sequence_configuration import TaskType, TaskStatus, ValidationResult, ValidationSeverity

class ExecutionLogStatus(Enum):
    """Execution log status"""
    CREATED = "created"
    STARTED = "started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

class TaskExecutionStatus(Enum):
    """Individual task execution status"""
    PENDING = "pending"
    STARTED = "started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

@dataclass
class TaskExecutionInfo:
    """Information about individual task execution"""
    task_type: TaskType
    task_name: str
    status: TaskExecutionStatus = TaskExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    retry_count: int = 0
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    result_data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ExecutionLogEntry:
    """Execution log entry for task sequence tracking"""
    execution_log_id: str
    sequence_name: str
    status: ExecutionLogStatus = ExecutionLogStatus.CREATED
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    total_duration_seconds: Optional[float] = None
    user_id: Optional[str] = None
    organization_id: Optional[str] = None
    request_source: Optional[str] = None
    tasks: List[TaskExecutionInfo] = field(default_factory=list)
    current_task_index: int = 0
    total_tasks: int = 0
    success_count: int = 0
    failure_count: int = 0
    skip_count: int = 0
    correlation_id: Optional[str] = None
    parent_execution_id: Optional[str] = None
    child_execution_ids: List[str] = field(default_factory=list)
    security_context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ExecutionLogFilter:
    """Filter for querying execution logs"""
    execution_log_ids: Optional[List[str]] = None
    sequence_names: Optional[List[str]] = None
    statuses: Optional[List[ExecutionLogStatus]] = None
    user_ids: Optional[List[str]] = None
    organization_ids: Optional[List[str]] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    task_types: Optional[List[TaskType]] = None
    limit: int = 100
    offset: int = 0

@dataclass
class ExecutionLogSummary:
    """Summary statistics for execution logs"""
    total_executions: int = 0
    completed_executions: int = 0
    failed_executions: int = 0
    running_executions: int = 0
    average_duration_seconds: float = 0.0
    success_rate_percent: float = 0.0
    most_common_failures: List[Dict[str, Any]] = field(default_factory=list)
    task_performance: Dict[str, Dict[str, Any]] = field(default_factory=dict)

class ExecutionLogCoordinationService:
    """
    Service to manage execution_log_id across sequential tasks
    and provide task sequence state management and tracking
    """
    
    def __init__(self, storage_backend: str = "memory", cleanup_interval_hours: int = 24):
        """
        Initialize the execution log coordination service
        
        Args:
            storage_backend: Storage backend ("memory", "database", "redis")
            cleanup_interval_hours: Hours after which to clean up old logs
        """
        self._storage_backend = storage_backend
        self._cleanup_interval = timedelta(hours=cleanup_interval_hours)
        self._execution_logs: Dict[str, ExecutionLogEntry] = {}
        self._lock = threading.RLock()
        self._last_cleanup = datetime.now()
        
        logger.info(f"Initializing Execution Log Coordination Service with {storage_backend} backend")
        
        # Initialize storage backend
        self._initialize_storage()
    
    def _initialize_storage(self):
        """Initialize storage backend"""
        if self._storage_backend == "memory":
            logger.info("Using in-memory storage for execution logs")
        elif self._storage_backend == "database":
            logger.info("Using database storage for execution logs")
            # TODO: Initialize database connection
        elif self._storage_backend == "redis":
            logger.info("Using Redis storage for execution logs")
            # TODO: Initialize Redis connection
        else:
            logger.warning(f"Unknown storage backend '{self._storage_backend}', falling back to memory")
            self._storage_backend = "memory"
    
    def create_execution_log(self, 
                           sequence_name: str,
                           user_id: Optional[str] = None,
                           organization_id: Optional[str] = None,
                           request_source: Optional[str] = None,
                           correlation_id: Optional[str] = None,
                           parent_execution_id: Optional[str] = None,
                           security_context: Optional[Dict[str, Any]] = None,
                           metadata: Optional[Dict[str, Any]] = None,
                           expiration_hours: int = 24) -> str:
        """
        Create a new execution log for task sequence tracking
        
        Args:
            sequence_name: Name of the task sequence
            user_id: User ID initiating the execution
            organization_id: Organization ID
            request_source: Source of the request (e.g., "api", "scheduler")
            correlation_id: Correlation ID for distributed tracing
            parent_execution_id: Parent execution ID for nested executions
            security_context: Security context information
            metadata: Additional metadata
            expiration_hours: Hours after which the log expires
            
        Returns:
            str: Generated execution log ID
        """
        try:
            with self._lock:
                # Generate unique execution log ID
                execution_log_id = str(uuid.uuid4())
                
                # Calculate expiration time
                expires_at = datetime.now() + timedelta(hours=expiration_hours)
                
                # Create execution log entry
                log_entry = ExecutionLogEntry(
                    execution_log_id=execution_log_id,
                    sequence_name=sequence_name,
                    status=ExecutionLogStatus.CREATED,
                    expires_at=expires_at,
                    user_id=user_id,
                    organization_id=organization_id,
                    request_source=request_source,
                    correlation_id=correlation_id or str(uuid.uuid4()),
                    parent_execution_id=parent_execution_id,
                    security_context=security_context or {},
                    metadata=metadata or {}
                )
                
                # Store execution log
                self._execution_logs[execution_log_id] = log_entry
                
                # Update parent execution if specified
                if parent_execution_id and parent_execution_id in self._execution_logs:
                    parent_log = self._execution_logs[parent_execution_id]
                    parent_log.child_execution_ids.append(execution_log_id)
                
                logger.info(f"Created execution log {execution_log_id} for sequence '{sequence_name}'")
                
                # Perform cleanup if needed
                self._cleanup_expired_logs()
                
                return execution_log_id
                
        except Exception as e:
            logger.error(f"Error creating execution log: {str(e)}")
            raise
    
    def start_execution(self, execution_log_id: str, task_sequence: List[TaskType]) -> bool:
        """
        Start execution tracking for a task sequence
        
        Args:
            execution_log_id: Execution log ID
            task_sequence: List of tasks in the sequence
            
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log {execution_log_id} not found")
                    return False
                
                log_entry = self._execution_logs[execution_log_id]
                
                # Validate log is in correct state
                if log_entry.status != ExecutionLogStatus.CREATED:
                    logger.error(f"Execution log {execution_log_id} is not in CREATED state (current: {log_entry.status})")
                    return False
                
                # Check if log has expired
                if log_entry.expires_at and datetime.now() > log_entry.expires_at:
                    logger.error(f"Execution log {execution_log_id} has expired")
                    log_entry.status = ExecutionLogStatus.EXPIRED
                    return False
                
                # Initialize task tracking
                log_entry.tasks = []
                for task_type in task_sequence:
                    task_info = TaskExecutionInfo(
                        task_type=task_type,
                        task_name=task_type.value,
                        status=TaskExecutionStatus.PENDING
                    )
                    log_entry.tasks.append(task_info)
                
                # Update log entry
                log_entry.status = ExecutionLogStatus.STARTED
                log_entry.started_at = datetime.now()
                log_entry.total_tasks = len(task_sequence)
                log_entry.current_task_index = 0
                
                logger.info(f"Started execution tracking for log {execution_log_id} with {len(task_sequence)} tasks")
                return True
                
        except Exception as e:
            logger.error(f"Error starting execution: {str(e)}")
            return False
    
    def start_task(self, execution_log_id: str, task_type: TaskType, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Mark a task as started in the execution log
        
        Args:
            execution_log_id: Execution log ID
            task_type: Type of task being started
            metadata: Additional metadata for the task
            
        Returns:
            bool: True if task started successfully, False otherwise
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log {execution_log_id} not found")
                    return False
                
                log_entry = self._execution_logs[execution_log_id]
                
                # Find the task in the sequence
                task_info = self._find_task_info(log_entry, task_type)
                if not task_info:
                    logger.error(f"Task {task_type.value} not found in execution log {execution_log_id}")
                    return False
                
                # Update task status
                task_info.status = TaskExecutionStatus.STARTED
                task_info.started_at = datetime.now()
                if metadata:
                    task_info.metadata.update(metadata)
                
                # Update log status
                if log_entry.status == ExecutionLogStatus.STARTED:
                    log_entry.status = ExecutionLogStatus.RUNNING
                
                logger.info(f"Started task {task_type.value} in execution log {execution_log_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error starting task: {str(e)}")
            return False
    
    def complete_task(self, 
                     execution_log_id: str, 
                     task_type: TaskType, 
                     success: bool = True,
                     error_message: Optional[str] = None,
                     error_code: Optional[str] = None,
                     result_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Mark a task as completed in the execution log
        
        Args:
            execution_log_id: Execution log ID
            task_type: Type of task being completed
            success: Whether the task completed successfully
            error_message: Error message if task failed
            error_code: Error code if task failed
            result_data: Result data from task execution
            
        Returns:
            bool: True if task completed successfully, False otherwise
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log {execution_log_id} not found")
                    return False
                
                log_entry = self._execution_logs[execution_log_id]
                
                # Find the task in the sequence
                task_info = self._find_task_info(log_entry, task_type)
                if not task_info:
                    logger.error(f"Task {task_type.value} not found in execution log {execution_log_id}")
                    return False
                
                # Update task status
                completed_at = datetime.now()
                task_info.completed_at = completed_at
                task_info.error_message = error_message
                task_info.error_code = error_code
                
                if result_data:
                    task_info.result_data.update(result_data)
                
                # Calculate duration
                if task_info.started_at:
                    task_info.duration_seconds = (completed_at - task_info.started_at).total_seconds()
                
                # Update status and counters
                if success:
                    task_info.status = TaskExecutionStatus.COMPLETED
                    log_entry.success_count += 1
                else:
                    task_info.status = TaskExecutionStatus.FAILED
                    log_entry.failure_count += 1
                
                # Update current task index
                task_index = self._get_task_index(log_entry, task_type)
                if task_index is not None and task_index >= log_entry.current_task_index:
                    log_entry.current_task_index = task_index + 1
                
                # Check if all tasks are completed
                self._check_execution_completion(log_entry)
                
                logger.info(f"Completed task {task_type.value} in execution log {execution_log_id} (success: {success})")
                return True
                
        except Exception as e:
            logger.error(f"Error completing task: {str(e)}")
            return False
    
    def skip_task(self, execution_log_id: str, task_type: TaskType, reason: str) -> bool:
        """
        Mark a task as skipped in the execution log
        
        Args:
            execution_log_id: Execution log ID
            task_type: Type of task being skipped
            reason: Reason for skipping the task
            
        Returns:
            bool: True if task skipped successfully, False otherwise
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log {execution_log_id} not found")
                    return False
                
                log_entry = self._execution_logs[execution_log_id]
                
                # Find the task in the sequence
                task_info = self._find_task_info(log_entry, task_type)
                if not task_info:
                    logger.error(f"Task {task_type.value} not found in execution log {execution_log_id}")
                    return False
                
                # Update task status
                task_info.status = TaskExecutionStatus.SKIPPED
                task_info.completed_at = datetime.now()
                task_info.metadata["skip_reason"] = reason
                
                log_entry.skip_count += 1
                
                # Update current task index
                task_index = self._get_task_index(log_entry, task_type)
                if task_index is not None and task_index >= log_entry.current_task_index:
                    log_entry.current_task_index = task_index + 1
                
                # Check if all tasks are completed
                self._check_execution_completion(log_entry)
                
                logger.info(f"Skipped task {task_type.value} in execution log {execution_log_id}: {reason}")
                return True
                
        except Exception as e:
            logger.error(f"Error skipping task: {str(e)}")
            return False
    
    def cancel_execution(self, execution_log_id: str, reason: str) -> bool:
        """
        Cancel execution and mark all pending tasks as cancelled
        
        Args:
            execution_log_id: Execution log ID
            reason: Reason for cancellation
            
        Returns:
            bool: True if cancelled successfully, False otherwise
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log {execution_log_id} not found")
                    return False
                
                log_entry = self._execution_logs[execution_log_id]
                
                # Update log status
                log_entry.status = ExecutionLogStatus.CANCELLED
                log_entry.completed_at = datetime.now()
                log_entry.metadata["cancellation_reason"] = reason
                
                # Calculate total duration
                if log_entry.started_at:
                    log_entry.total_duration_seconds = (log_entry.completed_at - log_entry.started_at).total_seconds()
                
                # Cancel all pending and running tasks
                for task_info in log_entry.tasks:
                    if task_info.status in [TaskExecutionStatus.PENDING, TaskExecutionStatus.STARTED, TaskExecutionStatus.RUNNING]:
                        task_info.status = TaskExecutionStatus.CANCELLED
                        task_info.completed_at = datetime.now()
                        task_info.metadata["cancellation_reason"] = reason
                
                logger.info(f"Cancelled execution log {execution_log_id}: {reason}")
                return True
                
        except Exception as e:
            logger.error(f"Error cancelling execution: {str(e)}")
            return False
    
    def get_execution_log(self, execution_log_id: str) -> Optional[ExecutionLogEntry]:
        """
        Get execution log entry by ID
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            ExecutionLogEntry: Execution log entry or None if not found
        """
        with self._lock:
            return self._execution_logs.get(execution_log_id)
    
    def get_execution_status(self, execution_log_id: str) -> Optional[ExecutionLogStatus]:
        """
        Get execution status by log ID
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            ExecutionLogStatus: Execution status or None if not found
        """
        log_entry = self.get_execution_log(execution_log_id)
        return log_entry.status if log_entry else None
    
    def get_current_task(self, execution_log_id: str) -> Optional[TaskExecutionInfo]:
        """
        Get current task being executed
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            TaskExecutionInfo: Current task info or None if not found
        """
        log_entry = self.get_execution_log(execution_log_id)
        if log_entry and 0 <= log_entry.current_task_index < len(log_entry.tasks):
            return log_entry.tasks[log_entry.current_task_index]
        return None
    
    def get_task_status(self, execution_log_id: str, task_type: TaskType) -> Optional[TaskExecutionStatus]:
        """
        Get status of a specific task
        
        Args:
            execution_log_id: Execution log ID
            task_type: Type of task
            
        Returns:
            TaskExecutionStatus: Task status or None if not found
        """
        log_entry = self.get_execution_log(execution_log_id)
        if log_entry:
            task_info = self._find_task_info(log_entry, task_type)
            return task_info.status if task_info else None
        return None
    
    def validate_execution_log_security(self, 
                                      execution_log_id: str, 
                                      user_id: Optional[str] = None,
                                      organization_id: Optional[str] = None,
                                      required_permissions: Optional[List[str]] = None) -> ValidationResult:
        """
        Validate security context for execution log access
        
        Args:
            execution_log_id: Execution log ID
            user_id: User ID requesting access
            organization_id: Organization ID
            required_permissions: Required permissions for access
            
        Returns:
            ValidationResult: Security validation result
        """
        try:
            log_entry = self.get_execution_log(execution_log_id)
            if not log_entry:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="Execution log not found",
                    code="EXECUTION_LOG_NOT_FOUND"
                )
            
            # Check if log has expired
            if log_entry.expires_at and datetime.now() > log_entry.expires_at:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="Execution log has expired",
                    code="EXECUTION_LOG_EXPIRED"
                )
            
            # Validate user access
            if user_id and log_entry.user_id and user_id != log_entry.user_id:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="User does not have access to this execution log",
                    code="ACCESS_DENIED"
                )
            
            # Validate organization access
            if organization_id and log_entry.organization_id and organization_id != log_entry.organization_id:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="Organization does not have access to this execution log",
                    code="ACCESS_DENIED"
                )
            
            # TODO: Implement permission checking if required_permissions is provided
            # This would involve checking user roles and permissions against the required permissions
            
            return ValidationResult(
                is_valid=True,
                severity=ValidationSeverity.INFO,
                message="Security validation passed",
                code="ACCESS_GRANTED"
            )
            
        except Exception as e:
            logger.error(f"Error validating execution log security: {str(e)}")
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Security validation error: {str(e)}",
                code="VALIDATION_ERROR"
            )
    
    def query_execution_logs(self, filter_criteria: ExecutionLogFilter) -> List[ExecutionLogEntry]:
        """
        Query execution logs with filtering
        
        Args:
            filter_criteria: Filter criteria for the query
            
        Returns:
            List[ExecutionLogEntry]: Filtered execution logs
        """
        try:
            with self._lock:
                results = []
                
                for log_entry in self._execution_logs.values():
                    if self._matches_filter(log_entry, filter_criteria):
                        results.append(log_entry)
                
                # Sort by creation time (newest first)
                results.sort(key=lambda x: x.created_at, reverse=True)
                
                # Apply pagination
                start_index = filter_criteria.offset
                end_index = start_index + filter_criteria.limit
                
                return results[start_index:end_index]
                
        except Exception as e:
            logger.error(f"Error querying execution logs: {str(e)}")
            return []
    
    def get_execution_summary(self, filter_criteria: Optional[ExecutionLogFilter] = None) -> ExecutionLogSummary:
        """
        Get summary statistics for execution logs
        
        Args:
            filter_criteria: Optional filter criteria
            
        Returns:
            ExecutionLogSummary: Summary statistics
        """
        try:
            with self._lock:
                logs_to_analyze = []
                
                if filter_criteria:
                    logs_to_analyze = self.query_execution_logs(filter_criteria)
                else:
                    logs_to_analyze = list(self._execution_logs.values())
                
                if not logs_to_analyze:
                    return ExecutionLogSummary()
                
                # Calculate summary statistics
                total_executions = len(logs_to_analyze)
                completed_executions = sum(1 for log in logs_to_analyze if log.status == ExecutionLogStatus.COMPLETED)
                failed_executions = sum(1 for log in logs_to_analyze if log.status == ExecutionLogStatus.FAILED)
                running_executions = sum(1 for log in logs_to_analyze if log.status in [ExecutionLogStatus.RUNNING, ExecutionLogStatus.STARTED])
                
                # Calculate average duration
                completed_logs_with_duration = [log for log in logs_to_analyze if log.total_duration_seconds is not None]
                average_duration = sum(log.total_duration_seconds for log in completed_logs_with_duration) / len(completed_logs_with_duration) if completed_logs_with_duration else 0.0
                
                # Calculate success rate
                success_rate = (completed_executions / total_executions * 100) if total_executions > 0 else 0.0
                
                # Analyze common failures
                failure_counts = {}
                task_performance = {}
                
                for log in logs_to_analyze:
                    for task in log.tasks:
                        task_type_str = task.task_type.value
                        
                        # Track task performance
                        if task_type_str not in task_performance:
                            task_performance[task_type_str] = {
                                "total_executions": 0,
                                "successful_executions": 0,
                                "failed_executions": 0,
                                "average_duration_seconds": 0.0,
                                "success_rate_percent": 0.0
                            }
                        
                        perf = task_performance[task_type_str]
                        perf["total_executions"] += 1
                        
                        if task.status == TaskExecutionStatus.COMPLETED:
                            perf["successful_executions"] += 1
                        elif task.status == TaskExecutionStatus.FAILED:
                            perf["failed_executions"] += 1
                            
                            # Track failure reasons
                            error_key = task.error_code or task.error_message or "Unknown error"
                            failure_counts[error_key] = failure_counts.get(error_key, 0) + 1
                        
                        # Track duration
                        if task.duration_seconds is not None:
                            current_avg = perf["average_duration_seconds"]
                            current_count = perf["successful_executions"] + perf["failed_executions"]
                            perf["average_duration_seconds"] = ((current_avg * (current_count - 1)) + task.duration_seconds) / current_count
                
                # Calculate success rates for tasks
                for task_type_str, perf in task_performance.items():
                    if perf["total_executions"] > 0:
                        perf["success_rate_percent"] = (perf["successful_executions"] / perf["total_executions"]) * 100
                
                # Get most common failures
                most_common_failures = [
                    {"error": error, "count": count}
                    for error, count in sorted(failure_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                ]
                
                return ExecutionLogSummary(
                    total_executions=total_executions,
                    completed_executions=completed_executions,
                    failed_executions=failed_executions,
                    running_executions=running_executions,
                    average_duration_seconds=average_duration,
                    success_rate_percent=success_rate,
                    most_common_failures=most_common_failures,
                    task_performance=task_performance
                )
                
        except Exception as e:
            logger.error(f"Error generating execution summary: {str(e)}")
            return ExecutionLogSummary()
    
    def cleanup_expired_logs(self) -> int:
        """
        Clean up expired execution logs
        
        Returns:
            int: Number of logs cleaned up
        """
        return self._cleanup_expired_logs()
    
    def _find_task_info(self, log_entry: ExecutionLogEntry, task_type: TaskType) -> Optional[TaskExecutionInfo]:
        """Find task info in execution log"""
        for task_info in log_entry.tasks:
            if task_info.task_type == task_type:
                return task_info
        return None
    
    def _get_task_index(self, log_entry: ExecutionLogEntry, task_type: TaskType) -> Optional[int]:
        """Get task index in execution log"""
        for i, task_info in enumerate(log_entry.tasks):
            if task_info.task_type == task_type:
                return i
        return None
    
    def _check_execution_completion(self, log_entry: ExecutionLogEntry):
        """Check if execution is completed and update status"""
        completed_statuses = {TaskExecutionStatus.COMPLETED, TaskExecutionStatus.FAILED, TaskExecutionStatus.SKIPPED, TaskExecutionStatus.CANCELLED}
        
        all_completed = all(task.status in completed_statuses for task in log_entry.tasks)
        
        if all_completed:
            log_entry.completed_at = datetime.now()
            
            # Calculate total duration
            if log_entry.started_at:
                log_entry.total_duration_seconds = (log_entry.completed_at - log_entry.started_at).total_seconds()
            
            # Determine final status
            if log_entry.failure_count > 0:
                log_entry.status = ExecutionLogStatus.FAILED
            else:
                log_entry.status = ExecutionLogStatus.COMPLETED
            
            logger.info(f"Execution log {log_entry.execution_log_id} completed with status {log_entry.status}")
    
    def _matches_filter(self, log_entry: ExecutionLogEntry, filter_criteria: ExecutionLogFilter) -> bool:
        """Check if log entry matches filter criteria"""
        if filter_criteria.execution_log_ids and log_entry.execution_log_id not in filter_criteria.execution_log_ids:
            return False
        
        if filter_criteria.sequence_names and log_entry.sequence_name not in filter_criteria.sequence_names:
            return False
        
        if filter_criteria.statuses and log_entry.status not in filter_criteria.statuses:
            return False
        
        if filter_criteria.user_ids and log_entry.user_id not in filter_criteria.user_ids:
            return False
        
        if filter_criteria.organization_ids and log_entry.organization_id not in filter_criteria.organization_ids:
            return False
        
        if filter_criteria.created_after and log_entry.created_at < filter_criteria.created_after:
            return False
        
        if filter_criteria.created_before and log_entry.created_at > filter_criteria.created_before:
            return False
        
        if filter_criteria.task_types:
            task_types_in_log = {task.task_type for task in log_entry.tasks}
            if not any(task_type in task_types_in_log for task_type in filter_criteria.task_types):
                return False
        
        return True
    
    def _cleanup_expired_logs(self) -> int:
        """Clean up expired execution logs"""
        try:
            if datetime.now() - self._last_cleanup < timedelta(hours=1):
                return 0  # Don't cleanup too frequently
            
            with self._lock:
                now = datetime.now()
                expired_ids = []
                
                for log_id, log_entry in self._execution_logs.items():
                    if log_entry.expires_at and now > log_entry.expires_at:
                        expired_ids.append(log_id)
                
                # Remove expired logs
                for log_id in expired_ids:
                    del self._execution_logs[log_id]
                
                self._last_cleanup = now
                
                if expired_ids:
                    logger.info(f"Cleaned up {len(expired_ids)} expired execution logs")
                
                return len(expired_ids)
                
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            return 0
    
    def export_execution_log(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Export execution log as dictionary for serialization
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict: Serializable execution log data or None if not found
        """
        log_entry = self.get_execution_log(execution_log_id)
        if not log_entry:
            return None
        
        try:
            # Convert to dictionary with proper serialization
            log_dict = asdict(log_entry)
            
            # Convert datetime objects to ISO strings
            for key, value in log_dict.items():
                if isinstance(value, datetime):
                    log_dict[key] = value.isoformat()
            
            # Convert task datetime objects
            for task in log_dict.get("tasks", []):
                for key, value in task.items():
                    if isinstance(value, datetime):
                        task[key] = value.isoformat()
            
            # Convert enum values to strings
            log_dict["status"] = log_entry.status.value
            for task in log_dict.get("tasks", []):
                task["task_type"] = task["task_type"]  # Already converted by asdict
                task["status"] = task["status"]  # Already converted by asdict
            
            return log_dict
            
        except Exception as e:
            logger.error(f"Error exporting execution log: {str(e)}")
            return None

# Global coordination service instance
_coordination_service: Optional[ExecutionLogCoordinationService] = None

def get_execution_log_coordination_service() -> ExecutionLogCoordinationService:
    """Get the global execution log coordination service instance"""
    global _coordination_service
    if _coordination_service is None:
        storage_backend = os.environ.get("EXECUTION_LOG_STORAGE", "memory")
        cleanup_interval = int(os.environ.get("EXECUTION_LOG_CLEANUP_HOURS", "24"))
        _coordination_service = ExecutionLogCoordinationService(storage_backend, cleanup_interval)
    return _coordination_service