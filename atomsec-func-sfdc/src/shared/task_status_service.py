"""
Task Status Service Module

This module provides centralized management of task status for tracking
asynchronous and long-running tasks throughout their lifecycle.

Features:
- Create and manage task status records
- Track task progress and completion
- Support for task retry mechanisms
- Integration with execution logs
- Support for both local development and production environments
- Proper error handling and logging
- Clean code principles and best practices

Best practices implemented:
- Single responsibility principle
- Dependency injection
- Environment-aware configuration
- Comprehensive error handling
- Consistent logging patterns
- Type hints for better code clarity
"""

import logging
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

# Import shared modules
from src.shared.azure_services import is_local_dev
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.database_models_new import Task
from src.shared.common import is_local_dev
from src.shared.parameter_validator import get_parameter_validator
from src.shared.parameter_validation_service import get_parameter_validation_service

# Configure module-level logger
logger = logging.getLogger(__name__)

# Task status constants
TASK_STATUS_PENDING = "pending"
TASK_STATUS_RUNNING = "running"
TASK_STATUS_COMPLETED = "completed"
TASK_STATUS_FAILED = "failed"
TASK_STATUS_RETRY = "retry"
TASK_STATUS_CANCELLED = "cancelled"

# Task priority constants
TASK_PRIORITY_LOW = "low"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_CRITICAL = "critical"

# Global repository instances (lazy initialized)
_task_status_table_repo = None
_task_status_sql_repo = None


class TaskStatusService:
    """
    Service class for managing task status and lifecycle
    
    This service provides a clean interface for creating, updating, and querying
    task status while abstracting the underlying storage mechanism.
    """
    
    def __init__(self):
        """Initialize the task status service"""
        self.table_repo = None
        self.sql_repo = None
        self.validator = get_parameter_validator()
        self.validation_service = get_parameter_validation_service()
        self._initialize_repositories()
    
    def _initialize_repositories(self) -> None:
        """Initialize the appropriate repository based on environment"""
        try:
            if is_local_dev():
                # Use Table Storage for local development
                self.table_repo = self._get_table_repository()
                logger.info("Initialized task status service with Table Storage for local development")
            else:
                # Use SQL Database for production
                self.sql_repo = self._get_sql_repository()
                logger.info("Initialized task status service with SQL Database for production")
        except Exception as e:
            logger.error(f"Error initializing task status service repositories: {str(e)}")
    
    def _get_table_repository(self) -> Optional[TableStorageRepository]:
        """Get or create Table Storage repository"""
        global _task_status_table_repo
        
        if _task_status_table_repo is None:
            try:
                _task_status_table_repo = TableStorageRepository("TaskStatus")
                logger.debug("Created Table Storage repository for task status")
            except Exception as e:
                logger.error(f"Failed to create Table Storage repository: {str(e)}")
                return None
        
        return _task_status_table_repo
    
    def _get_sql_repository(self) -> Optional[SqlDatabaseRepository]:
        """Get or create SQL Database repository"""
        global _task_status_sql_repo
        
        if _task_status_sql_repo is None:
            try:
                _task_status_sql_repo = SqlDatabaseRepository("Tasks")
                logger.debug("Created SQL Database repository for task status")
            except Exception as e:
                logger.error(f"Failed to create SQL Database repository: {str(e)}")
                return None
        
        return _task_status_sql_repo
    
    def create_task(
        self,
        task_type: str,
        org_id: Union[str, int],
        user_id: Union[str, int],
        priority: str = TASK_PRIORITY_MEDIUM,
        execution_log_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        scheduled_time: Optional[datetime] = None
    ) -> Optional[str]:
        """
        Create a new task
        
        Args:
            task_type: Type of task (e.g., "Integration_Scan", "Data_Export")
            org_id: Organization ID
            user_id: User ID who created the task
            priority: Task priority (low, medium, high, critical)
            execution_log_id: Optional execution log ID to associate with
            params: Optional parameters for the task
            scheduled_time: Optional scheduled execution time
        
        Returns:
            str: Task ID if successful, None otherwise
        """
        try:
            # Generate unique task ID
            task_id = str(uuid.uuid4())
            
            # Convert IDs to appropriate types
            org_id_str = str(org_id)
            user_id_str = str(user_id)
            
            # Validate and sanitize parameters using comprehensive service
            try:
                validated_params = self.validation_service.validate_and_sanitize_parameters(params)
            except Exception as e:
                logger.error(f"Error validating parameters: {e}. Using empty dict.")
                validated_params = {}
            
            # Create task object
            task = Task(
                TaskId=task_id,
                TaskType=task_type,
                OrgId=org_id_str,
                UserId=user_id_str,
                Status=TASK_STATUS_PENDING,
                Priority=priority,
                Progress=0,
                Message="Task created",
                CreatedAt=datetime.now(),
                UpdatedAt=datetime.now(),
                ExecutionLogId=execution_log_id,
                Params=validated_params,
                ScheduledTime=scheduled_time
            )
            
            if is_local_dev():
                return self._create_task_table(task)
            else:
                return self._create_task_sql(task)
                
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            return None
    
    def _create_task_table(self, task: Task) -> Optional[str]:
        """Create task in Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None
            
            # Ensure TaskId is properly set
            if not task.TaskId:
                task.TaskId = str(uuid.uuid4())
                logger.warning(f"TaskId was empty, generated new UUID: {task.TaskId}")
            
            # Create entity for Table Storage with explicit TaskId
            entity = {
                "PartitionKey": f"task_{task.OrgId}",
                "RowKey": task.TaskId,
                "TaskId": task.TaskId,  # Ensure this is explicitly set
                "TaskType": task.TaskType,
                "OrgId": task.OrgId,
                "UserId": task.UserId,
                "Status": task.Status,
                "Priority": task.Priority,
                "Progress": task.Progress,
                "Message": task.Message,
                "Result": task.Result or "",
                "CreatedAt": task.CreatedAt.isoformat(),
                "UpdatedAt": task.UpdatedAt.isoformat(),
                "CompletedAt": task.CompletedAt.isoformat() if task.CompletedAt else "",
                "ScheduledTime": task.ScheduledTime.isoformat() if task.ScheduledTime else "",
                "RetryCount": task.RetryCount,
                "ExecutionLogId": task.ExecutionLogId or "",
                "Params": json.dumps(task.Params) if task.Params else "{}"
            }
            
            # Log the entity being created for debugging
            logger.info(f"Creating task entity: {entity}")
            
            success = self.table_repo.insert_entity(entity)
            if success:
                logger.info(f"Successfully created task in Table Storage: {task.TaskId}")
                
                # Verify the task was created by querying it back
                try:
                    verify_query = f"RowKey eq '{task.TaskId}'"
                    verification_entities = list(self.table_repo.query_entities(verify_query))
                    if verification_entities and verification_entities[0].get('TaskId') == task.TaskId:
                        logger.info(f"Verified task creation: TaskId={task.TaskId}")
                    else:
                        logger.warning(f"Task created but verification failed: TaskId={task.TaskId}")
                except Exception as verify_error:
                    logger.warning(f"Error verifying task creation: {verify_error}")
                    
                return task.TaskId
            else:
                logger.error(f"Failed to create task in Table Storage: {task.TaskId}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating task in Table Storage: {str(e)}")
            return None
    
    def _create_task_sql(self, task: Task) -> Optional[str]:
        """Create task in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None
            
            # Note: For SQL, we'll use a Tasks table that matches the Task model
            # This assumes the table exists with appropriate schema
            query = """
            INSERT INTO Tasks (TaskId, TaskType, OrgId, UserId, Status, Priority, Progress, 
                             Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime, 
                             RetryCount, ExecutionLogId, Params)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                task.TaskId,
                task.TaskType,
                task.OrgId,
                task.UserId,
                task.Status,
                task.Priority,
                task.Progress,
                task.Message,
                task.Result,
                task.CreatedAt.isoformat(),
                task.UpdatedAt.isoformat(),
                task.CompletedAt.isoformat() if task.CompletedAt else None,
                task.ScheduledTime.isoformat() if task.ScheduledTime else None,
                task.RetryCount,
                task.ExecutionLogId,
                json.dumps(task.Params) if task.Params else None
            )
            
            success = self.sql_repo.execute_non_query(query, params)
            if success:
                logger.info(f"Created task in SQL Database: {task.TaskId}")
                return task.TaskId
            else:
                logger.error(f"Failed to create task in SQL Database: {task.TaskId}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating task in SQL Database: {str(e)}")
            return None

    def update_task_status(
        self,
        task_id: str,
        status: str,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        result: Optional[str] = None,
        error: Optional[str] = None,
        max_retries: int = 3
    ) -> bool:
        """
        Update task status and related fields with retry mechanism

        Args:
            task_id: ID of the task to update
            status: New status
            progress: Optional progress percentage (0-100)
            message: Optional status message
            result: Optional result data
            error: Optional error message
            max_retries: Maximum number of retry attempts

        Returns:
            bool: True if successful, False otherwise
        """
        import time
        import random
        
        for attempt in range(max_retries + 1):
            try:
                # Validate inputs
                if not task_id:
                    logger.error("Task ID is required for status update")
                    return False
                
                if not status:
                    logger.error("Status is required for task update")
                    return False
                
                # Validate status value
                valid_statuses = [
                    TASK_STATUS_PENDING, TASK_STATUS_RUNNING, TASK_STATUS_COMPLETED,
                    TASK_STATUS_FAILED, TASK_STATUS_RETRY, TASK_STATUS_CANCELLED
                ]
                if status not in valid_statuses:
                    logger.error(f"Invalid status '{status}'. Valid statuses: {valid_statuses}")
                    return False
                
                # Validate progress
                if progress is not None:
                    if not isinstance(progress, int) or progress < 0 or progress > 100:
                        logger.warning(f"Invalid progress value {progress}, using 0")
                        progress = 0
                
                completed_at = None
                if status in [TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED]:
                    completed_at = datetime.now()
                
                success = False
                if is_local_dev():
                    success = self._update_task_status_table(task_id, status, progress, message, result, error, completed_at)
                else:
                    success = self._update_task_status_sql(task_id, status, progress, message, result, error, completed_at)
                
                if success:
                    return True
                
                # If we get here, the update failed but didn't throw an exception
                if attempt < max_retries:
                    delay = (2 ** attempt) + random.uniform(0, 1)  # Exponential backoff with jitter
                    logger.warning(f"Task status update failed, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                
            except Exception as e:
                if attempt < max_retries:
                    delay = (2 ** attempt) + random.uniform(0, 1)  # Exponential backoff with jitter
                    logger.warning(f"Task status update error, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries}): {str(e)}")
                    time.sleep(delay)
                else:
                    logger.error(f"Final attempt failed for task status update: {str(e)}")
                    return False
        
        logger.error(f"Failed to update task status after {max_retries} retries: {task_id}")
        return False
    
    def _update_task_status_table(
        self,
        task_id: str,
        status: str,
        progress: Optional[int],
        message: Optional[str],
        result: Optional[str],
        error: Optional[str],
        completed_at: Optional[datetime]
    ) -> bool:
        """Update task status in Table Storage with improved error handling"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return False
            
            # Try to find task by RowKey first
            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))
            
            if not entities:
                # Fallback: search all entities
                logger.warning(f"Task not found by RowKey, searching all entities: {task_id}")
                all_entities = list(self.table_repo.query_entities())
                entities = [entity for entity in all_entities if entity.get('RowKey') == task_id]
            
            if not entities:
                logger.error(f"Task not found in Table Storage: {task_id}")
                return False
            
            target_entity = entities[0]
            
            # Build update data
            update_data = {
                'Status': status,
                'UpdatedAt': datetime.now().isoformat()
            }
            
            if progress is not None:
                update_data['Progress'] = progress
            if message:
                update_data['Message'] = message[:1000]  # Truncate long messages
            if result:
                update_data['Result'] = result[:4000]  # Truncate long results
            if error:
                update_data['ErrorMessage'] = error[:1000]  # Truncate long errors
            if completed_at:
                update_data['CompletedAt'] = completed_at.isoformat()
            
            # Update entity
            target_entity.update(update_data)
            
            success = self.table_repo.update_entity(target_entity)
            if success:
                logger.info(f"Updated task status in Table Storage: {task_id} -> {status}")
                return True
            else:
                logger.error(f"Failed to update task status in Table Storage: {task_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating task status in Table Storage: {str(e)}")
            return False

    def _update_task_status_sql(
        self,
        task_id: str,
        status: str,
        progress: Optional[int],
        message: Optional[str],
        result: Optional[str],
        error: Optional[str],
        completed_at: Optional[datetime]
    ) -> bool:
        """Update task status in SQL Database with improved error handling"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            # Build dynamic update query based on provided parameters
            update_fields = ["Status = ?", "UpdatedAt = ?"]
            params = [status, datetime.now().isoformat()]

            # Handle string truncation for SQL fields
            if progress is not None:
                update_fields.append("Progress = ?")
                params.append(progress)
            if message is not None:
                update_fields.append("Message = ?")
                params.append(message[:1000])  # Truncate long messages
            if result is not None:
                update_fields.append("Result = ?")
                params.append(result[:4000])  # Truncate long results
            if error is not None:
                # Store error separately if Message is already used
                update_fields.append("ErrorMessage = ?")
                params.append(error[:1000])  # Truncate long errors
            if completed_at is not None:
                update_fields.append("CompletedAt = ?")
                params.append(completed_at.isoformat())

            # Add task_id as the last parameter for WHERE clause
            params.append(task_id)

            query = f"""
            UPDATE Tasks
            SET {', '.join(update_fields)}
            WHERE TaskId = ?
            """

            # Check if task exists first
            check_query = "SELECT COUNT(*) FROM Tasks WHERE TaskId = ?"
            count_result = self.sql_repo.execute_query(check_query, (task_id,))
            
            if not count_result or count_result[0][0] == 0:
                logger.warning(f"Task not found in SQL Database: {task_id}")
                return False

            success = self.sql_repo.execute_non_query(query, tuple(params))
            if success:
                # Verify the update
                verify_query = "SELECT Status FROM Tasks WHERE TaskId = ?"
                verify_result = self.sql_repo.execute_query(verify_query, (task_id,))
                if verify_result and verify_result[0][0] == status:
                    logger.info(f"Verified task status update in SQL Database: {task_id} -> {status}")
                    return True
                else:
                    logger.warning(f"Task status update verification failed: {task_id}")
                    return False
            else:
                logger.error(f"Failed to update task status in SQL Database: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error updating task status in SQL Database: {str(e)}")
            return False

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID with improved error handling and fallback mechanisms

        Args:
            task_id: ID of the task to retrieve

        Returns:
            Dict[str, Any]: Task data if found, None otherwise
        """
        try:
            if not task_id:
                logger.error("Task ID is required for retrieval")
                return None
            
            # Normalize task ID
            task_id = str(task_id).strip()
            
            if is_local_dev():
                return self._get_task_table(task_id)
            else:
                return self._get_task_sql(task_id)
        except Exception as e:
            logger.error(f"Error getting task: {str(e)}")
            return None
    
    def _get_task_table(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task from Table Storage with improved search"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None
            
            # Try direct query by RowKey first
            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))
            
            if entities:
                entity = entities[0]
                logger.debug(f"Retrieved task from Table Storage: {task_id}")
                return dict(entity)
            
            # Fallback: search all entities (for backward compatibility)
            logger.warning(f"Task not found by direct query, searching all entities: {task_id}")
            all_entities = list(self.table_repo.query_entities())
            
            for entity in all_entities:
                if entity.get('RowKey') == task_id:
                    logger.debug(f"Found task via fallback search: {task_id}")
                    return dict(entity)
            
            logger.warning(f"Task not found in Table Storage: {task_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting task from Table Storage: {str(e)}")
            return None

    def _get_task_sql(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task from SQL Database with improved error handling"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None

            # Normalize task ID
            task_id = str(task_id).strip()
            
            # Try exact match first
            query = """
            SELECT TaskId, TaskType, OrgId, UserId, Status, Priority, Progress,
                   Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime,
                   RetryCount, ExecutionLogId, Params
            FROM Tasks
            WHERE TaskId = ?
            """
            params = (task_id,)

            results = self.sql_repo.execute_query(query, params)
            if results and len(results) > 0:
                row = results[0]
                task = {
                    "TaskId": row[0],
                    "TaskType": row[1],
                    "OrgId": row[2],
                    "UserId": row[3],
                    "Status": row[4],
                    "Priority": row[5],
                    "Progress": row[6],
                    "Message": row[7],
                    "Result": row[8],
                    "CreatedAt": row[9],
                    "UpdatedAt": row[10],
                    "CompletedAt": row[11],
                    "ScheduledTime": row[12],
                    "RetryCount": row[13],
                    "ExecutionLogId": row[14],
                    "Params": safe_parse_params(row[15])
                }
                logger.debug(f"Retrieved task from SQL Database: {task_id}")
                return task
            
            # Try case-insensitive search as fallback
            fallback_query = """
            SELECT TaskId, TaskType, OrgId, UserId, Status, Priority, Progress,
                   Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime,
                   RetryCount, ExecutionLogId, Params
            FROM Tasks
            WHERE LOWER(TaskId) = LOWER(?)
            """
            fallback_results = self.sql_repo.execute_query(fallback_query, (task_id,))
            
            if fallback_results and len(fallback_results) > 0:
                row = fallback_results[0]
                task = {
                    "TaskId": row[0],
                    "TaskType": row[1],
                    "OrgId": row[2],
                    "UserId": row[3],
                    "Status": row[4],
                    "Priority": row[5],
                    "Progress": row[6],
                    "Message": row[7],
                    "Result": row[8],
                    "CreatedAt": row[9],
                    "UpdatedAt": row[10],
                    "CompletedAt": row[11],
                    "ScheduledTime": row[12],
                    "RetryCount": row[13],
                    "ExecutionLogId": row[14],
                    "Params": safe_parse_params(row[15])
                }
                logger.debug(f"Retrieved task via fallback search: {task_id}")
                return task
            
            logger.warning(f"Task not found in SQL Database: {task_id}")
            return None

        except Exception as e:
            logger.error(f"Error getting task from SQL Database: {str(e)}")
            return None

    def get_tasks_by_org(
        self,
        org_id: Union[str, int],
        status_filter: Optional[str] = None,
        limit: int = 50,
        include_completed: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get tasks for a specific organization

        Args:
            org_id: Organization ID
            status_filter: Optional status filter
            limit: Maximum number of tasks to return
            include_completed: Whether to include completed tasks

        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            org_id_str = str(org_id)

            if is_local_dev():
                filter_query = f"PartitionKey eq 'task_{org_id}'"
                if status_filter:
                    filter_query += f" and Status eq '{status_filter}'"
                elif not include_completed:
                    filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"
                entities = list(self.table_repo.query_entities(filter_query))
                sorted_entities = sorted(entities, key=lambda x: x.get("CreatedAt", ""), reverse=True)[:limit]
                logger.debug(f"Retrieved {len(sorted_entities)} tasks for org {org_id}")
                return [dict(entity) for entity in sorted_entities]
            else:
                return self._get_tasks_by_org_sql(org_id_str, status_filter, limit, include_completed)

        except Exception as e:
            logger.error(f"Error getting tasks by org: {str(e)}")
            return []

    def _get_tasks_by_org_sql(
        self,
        org_id: str,
        status_filter: Optional[str],
        limit: int,
        include_completed: bool
    ) -> List[Dict[str, Any]]:
        """Get tasks by organization from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return []

            where_conditions = ["OrgId = ?"]
            params = [org_id]

            if status_filter:
                where_conditions.append("Status = ?")
                params.append(status_filter)
            elif not include_completed:
                where_conditions.append("Status NOT IN (?, ?, ?)")
                params.extend([TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED])

            where_clause = " AND ".join(where_conditions)

            query = f"""
            SELECT TOP {limit} TaskId, TaskType, OrgId, UserId, Status, Priority, Progress,
                   Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime,
                   RetryCount, ExecutionLogId, Params
            FROM Tasks
            WHERE {where_clause}
            ORDER BY CreatedAt DESC
            """

            results = self.sql_repo.execute_query(query, tuple(params))
            tasks = []

            for row in results:
                task = {
                    "TaskId": row[0],
                    "TaskType": row[1],
                    "OrgId": row[2],
                    "UserId": row[3],
                    "Status": row[4],
                    "Priority": row[5],
                    "Progress": row[6],
                    "Message": row[7],
                    "Result": row[8],
                    "CreatedAt": row[9],
                    "UpdatedAt": row[10],
                    "CompletedAt": row[11],
                    "ScheduledTime": row[12],
                    "RetryCount": row[13],
                    "ExecutionLogId": row[14],
                    "Params": safe_parse_params(row[15])
                }
                tasks.append(task)

            logger.debug(f"Retrieved {len(tasks)} tasks for org {org_id}")
            return tasks

        except Exception as e:
            logger.error(f"Error getting tasks from SQL Database: {str(e)}")
            return []

    def retry_task(self, task_id: str, max_retries: int = 3) -> bool:
        """
        Retry a failed task

        Args:
            task_id: ID of the task to retry
            max_retries: Maximum number of retries allowed

        Returns:
            bool: True if retry was initiated, False otherwise
        """
        try:
            # Get current task
            task = self.get_task(task_id)
            if not task:
                logger.warning(f"Task not found for retry: {task_id}")
                return False

            # Check if task can be retried
            current_retry_count = task.get("RetryCount", 0)
            if current_retry_count >= max_retries:
                logger.warning(f"Task {task_id} has exceeded maximum retries ({max_retries})")
                return False

            # Update task for retry
            new_retry_count = current_retry_count + 1
            retry_message = f"Retry attempt {new_retry_count}/{max_retries}"

            success = self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RETRY,
                progress=0,
                message=retry_message
            )

            if success:
                # Update retry count separately
                if is_local_dev():
                    self._update_retry_count_table(task_id, new_retry_count)
                else:
                    self._update_retry_count_sql(task_id, new_retry_count)

                logger.info(f"Task {task_id} marked for retry (attempt {new_retry_count})")
                return True
            else:
                logger.error(f"Failed to update task status for retry: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")
            return False

    def _update_retry_count_table(self, task_id: str, retry_count: int) -> bool:
        """Update retry count in Table Storage"""
        try:
            if not self.table_repo:
                return False

            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if entities:
                entity = entities[0]
                entity["RetryCount"] = retry_count
                return self.table_repo.update_entity(entity)

            return False
        except Exception as e:
            logger.error(f"Error updating retry count in Table Storage: {str(e)}")
            return False

    def _update_retry_count_sql(self, task_id: str, retry_count: int) -> bool:
        """Update retry count in SQL Database"""
        try:
            if not self.sql_repo:
                return False

            query = "UPDATE Tasks SET RetryCount = ? WHERE TaskId = ?"
            params = (retry_count, task_id)

            return self.sql_repo.execute_non_query(query, params)
        except Exception as e:
            logger.error(f"Error updating retry count in SQL Database: {str(e)}")
            return False

    def cancel_task(self, task_id: str, reason: str = "Cancelled by user") -> bool:
        """
        Cancel a task

        Args:
            task_id: ID of the task to cancel
            reason: Reason for cancellation

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_CANCELLED,
                message=reason
            )
        except Exception as e:
            logger.error(f"Error cancelling task: {str(e)}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task

        Args:
            task_id: ID of the task to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if is_local_dev():
                return self._delete_task_table(task_id)
            else:
                return self._delete_task_sql(task_id)

        except Exception as e:
            logger.error(f"Error deleting task: {str(e)}")
            return False

    def create_parent_task(
        self,
        execution_log_id: str,
        task_type: str,
        org_id: Union[str, int],
        user_id: Union[str, int],
        priority: str = TASK_PRIORITY_MEDIUM,
        params: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Create a parent task for execution context
        
        Args:
            execution_log_id: Execution log ID for parent-child relationship
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            priority: Task priority
            params: Task parameters
            
        Returns:
            str: Task ID if successful, None otherwise
        """
        try:
            enhanced_params = {
                **(params or {}),
                "is_parent_task": True,
                "parent_execution_log_id": execution_log_id
            }
            
            return self.create_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                priority=priority,
                execution_log_id=execution_log_id,
                params=enhanced_params
            )
            
        except Exception as e:
            logger.error(f"Error creating parent task: {str(e)}")
            return None

    def create_child_task(
        self,
        parent_execution_log_id: str,
        parent_task_id: str,
        task_type: str,
        org_id: Union[str, int],
        user_id: Union[str, int],
        priority: str = TASK_PRIORITY_MEDIUM,
        params: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Create a child task linked to a parent task
        
        Args:
            parent_execution_log_id: Parent execution log ID
            parent_task_id: Parent task ID
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            priority: Task priority
            params: Task parameters
            
        Returns:
            str: Task ID if successful, None otherwise
        """
        try:
            enhanced_params = {
                **(params or {}),
                "is_child_task": True,
                "parent_execution_log_id": parent_execution_log_id,
                "parent_task_id": parent_task_id
            }
            
            return self.create_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                priority=priority,
                execution_log_id=parent_execution_log_id,
                params=enhanced_params
            )
            
        except Exception as e:
            logger.error(f"Error creating child task: {str(e)}")
            return None

    def update_parent_completion_status(self, execution_log_id: str) -> bool:
        """
        Update parent task completion status based on child tasks
        
        Args:
            execution_log_id: Execution log ID to check
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Updating parent completion status for execution log: {execution_log_id}")
            
            # Get all tasks for this execution log
            tasks = self.get_tasks_by_org(
                org_id=None,
                include_completed=True
            )
            
            execution_tasks = [
                task for task in tasks
                if task.get("ExecutionLogId") == execution_log_id
            ]
            
            if not execution_tasks:
                logger.warning(f"No tasks found for execution log: {execution_log_id}")
                return False
            
            # Find parent task
            parent_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_parent_task")]
            if not parent_tasks:
                logger.warning(f"No parent task found for execution log: {execution_log_id}")
                return False
            
            parent_task = parent_tasks[0]
            parent_task_id = parent_task.get("TaskId")
            
            # Get child tasks
            child_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_child_task")]
            
            if not child_tasks:
                # No child tasks, mark parent as completed
                return self.update_task_status(
                    task_id=parent_task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message="Task completed successfully"
                )
            
            # Count child task statuses
            completed_count = sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_COMPLETED)
            failed_count = sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_FAILED)
            total_children = len(child_tasks)
            
            # Determine parent status
            if failed_count > 0:
                # Any child failure means parent fails
                return self.update_task_status(
                    task_id=parent_task_id,
                    status=TASK_STATUS_FAILED,
                    progress=0,
                    message=f"Task failed: {failed_count} of {total_children} child tasks failed"
                )
            elif completed_count == total_children:
                # All children completed successfully
                return self.update_task_status(
                    task_id=parent_task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message=f"Task completed successfully: {completed_count} child tasks completed"
                )
            else:
                # Still in progress
                progress = int((completed_count / total_children) * 100)
                return self.update_task_status(
                    task_id=parent_task_id,
                    status=TASK_STATUS_RUNNING,
                    progress=progress,
                    message=f"Task in progress: {completed_count} of {total_children} child tasks completed"
                )
                
        except Exception as e:
            logger.error(f"Error updating parent completion status: {str(e)}")
            return False

    def get_task_hierarchy(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task hierarchy for an execution log
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict: Task hierarchy with parent and child tasks
        """
        try:
            logger.debug(f"Getting task hierarchy for execution log: {execution_log_id}")
            
            # Get all tasks for this execution log
            tasks = self.get_tasks_by_org(
                org_id=None,
                include_completed=True
            )
            
            execution_tasks = [
                task for task in tasks
                if task.get("ExecutionLogId") == execution_log_id
            ]
            
            if not execution_tasks:
                logger.warning(f"No tasks found for execution log: {execution_log_id}")
                return None
            
            # Separate parent and child tasks
            parent_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_parent_task")]
            child_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_child_task")]
            
            # Build hierarchy
            hierarchy = {
                "execution_log_id": execution_log_id,
                "parent_task": parent_tasks[0] if parent_tasks else None,
                "child_tasks": child_tasks,
                "total_child_tasks": len(child_tasks),
                "completed_child_tasks": sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_COMPLETED),
                "failed_child_tasks": sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_FAILED),
                "running_child_tasks": sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_RUNNING),
                "pending_child_tasks": sum(1 for task in child_tasks if task.get("Status") == TASK_STATUS_PENDING)
            }
            
            return hierarchy
            
        except Exception as e:
            logger.error(f"Error getting task hierarchy: {str(e)}")
            return None

    def get_tasks_by_execution_log(
        self,
        execution_log_id: str,
        include_completed: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get all tasks for a specific execution log
        
        Args:
            execution_log_id: Execution log ID
            include_completed: Whether to include completed tasks
            
        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            logger.debug(f"Getting tasks for execution log: {execution_log_id}")
            
            # Get all tasks and filter by execution log ID
            all_tasks = self.get_tasks_by_org(
                org_id=None,
                include_completed=include_completed
            )
            
            execution_tasks = [
                task for task in all_tasks
                if task.get("ExecutionLogId") == execution_log_id
            ]
            
            logger.debug(f"Retrieved {len(execution_tasks)} tasks for execution log {execution_log_id}")
            return execution_tasks
            
        except Exception as e:
            logger.error(f"Error getting tasks by execution log: {str(e)}")
            return []

    def _delete_task_table(self, task_id: str) -> bool:
        """Delete task from Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return False

            # Find the entity first to get the partition key
            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if not entities:
                logger.warning(f"Task not found: {task_id}")
                return False

            entity = entities[0]
            success = self.table_repo.delete_entity(entity["PartitionKey"], entity["RowKey"])

            if success:
                logger.info(f"Deleted task from Table Storage: {task_id}")
                return True
            else:
                logger.error(f"Failed to delete task from Table Storage: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting task from Table Storage: {str(e)}")
            return False

    def _delete_task_sql(self, task_id: str) -> bool:
        """Delete task from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            query = "DELETE FROM Tasks WHERE TaskId = ?"
            params = (task_id,)

            success = self.sql_repo.execute_non_query(query, params)

            if success:
                logger.info(f"Deleted task from SQL Database: {task_id}")
                return True
            else:
                logger.error(f"Failed to delete task from SQL Database: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting task from SQL Database: {str(e)}")
            return False


# Global service instance
_task_status_service = None


def get_task_status_service() -> TaskStatusService:
    """
    Get the global task status service instance

    Returns:
        TaskStatusService: The service instance
    """
    global _task_status_service

    if _task_status_service is None:
        _task_status_service = TaskStatusService()
        logger.debug("Created global task status service instance")

    return _task_status_service


def safe_parse_params(params):
    """
    Safely parse task parameters from various formats to dict
    
    Args:
        params: Parameters to parse (dict, str, or None)
        
    Returns:
        dict: Parsed parameters as dictionary
    """
    if isinstance(params, dict):
        return params
    if isinstance(params, str):
        try:
            # Try standard JSON parsing first
            return json.loads(params)
        except json.JSONDecodeError:
            try:
                # Try Python literal evaluation
                import ast
                return ast.literal_eval(params)
            except (ValueError, SyntaxError):
                try:
                    # Try parameter validator
                    validator = get_parameter_validator()
                    return validator.parse_malformed_parameters(params)
                except Exception:
                    try:
                        # Last resort: try to fix common Python dict format issues
                        import re
                        fixed_str = re.sub(r"'([^']*)':", r'"\1":', params)
                        fixed_str = re.sub(r":\s*'([^']*)'", r': "\1"', fixed_str)
                        return json.loads(fixed_str)
                    except Exception:
                        logger.warning(f"Failed to parse params: {params}")
                        return {}
    return {}
