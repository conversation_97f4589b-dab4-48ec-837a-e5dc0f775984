"""
Authentication Utilities Module

This module provides utilities for authentication and authorization.

Best practices implemented:
- JWT-based authentication
- Proper error handling and logging
- Reusable authentication functions
"""

import logging
import jwt
from typing import Dict, Any, Optional
import azure.functions as func
from fastapi import Request

# Import shared modules
from src.shared.config import get_jwt_config
from src.shared.user_repository import get_user_account_by_email

# Configure module-level logger
logger = logging.getLogger(__name__)

# JWT configuration
_jwt_config = None

def get_jwt_secret():
    """Get JWT secret from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
        logger.info(f"[JWT DEBUG] Loaded JWT config: algorithm={_jwt_config.get('algorithm')}, secret_length={len(_jwt_config.get('secret', ''))}")
    return _jwt_config["secret"]

def get_jwt_algorithm():
    """Get JWT algorithm from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
    return _jwt_config["algorithm"]

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode a JWT token

    Args:
        token: JWT token to decode

    Returns:
        Dict: Decoded token payload or None if invalid
    """
    try:
        jwt_secret = get_jwt_secret()
        jwt_algorithm = get_jwt_algorithm()
        logger.info(f"[JWT DEBUG] Using JWT secret: {jwt_secret[:10]}... (length: {len(jwt_secret)})")
        logger.info(f"[JWT DEBUG] Using JWT algorithm: {jwt_algorithm}")

        payload = jwt.decode(token, jwt_secret, algorithms=[jwt_algorithm])
        logger.info(f"[JWT DEBUG] Successfully decoded token payload: {payload}")
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error decoding token: {str(e)}")
        return None

def get_token_from_header(req: func.HttpRequest) -> Optional[str]:
    """
    Extract token from Authorization header

    Args:
        req: HTTP request

    Returns:
        str: Token or None if not found
    """
    auth_header = req.headers.get("Authorization")
    if not auth_header:
        return None

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None

    return parts[1]

def get_current_user(req: func.HttpRequest) -> Optional[Dict[str, Any]]:
    """
    Get current user from request

    Args:
        req: HTTP request

    Returns:
        Dict: User information or None if not authenticated
    """
    # Get token from header
    token = get_token_from_header(req)
    logger.info(f"[AUTH DEBUG] Token from header: {token}")
    if not token:
        logger.warning("No token found in request")
        return None

    # Decode token
    payload = decode_token(token)
    logger.info(f"[AUTH DEBUG] Decoded payload: {payload}")
    if not payload:
        logger.warning("Invalid token")
        return None

    # Extract user information
    email = payload.get("sub")
    logger.info(f"[AUTH DEBUG] Email from token: {email}")
    if not email:
        logger.warning("No email found in token")
        return None

    # Fetch user from DB to get user ID
    user_account = get_user_account_by_email(email)
    logger.info(f"[AUTH DEBUG] User account from DB: {user_account}")
    if not user_account:
        logger.warning("No user found in DB for email")
        return None

    # Return user information with ID
    user_info = {
        "email": email,
        "id": user_account.UserId  # UserId is the unique user identifier
    }
    logger.info(f"[AUTH DEBUG] Returning user info: {user_info}")
    return user_info

def get_user_id_from_request(req: func.HttpRequest) -> Optional[str]:
    """
    Extract user ID from authenticated request

    Args:
        req: HTTP request

    Returns:
        str: User ID or None if not authenticated
    """
    user = get_current_user(req)
    if not user:
        return None

    # Try to get user_id from token first
    user_id = user.get("id")
    if user_id:
        logger.debug(f"Found user_id in token: {user_id}")
        return str(user_id)

    # Fallback to email as user identifier
    email = user.get("email")
    if email:
        logger.debug(f"Using email as user identifier: {email}")
        return email

    logger.warning("No user identifier found in token")
    return None

def get_user_from_request_or_default(req: func.HttpRequest, default_user_id: str = "system") -> str:
    """
    Get user ID from request or return default

    Args:
        req: HTTP request
        default_user_id: Default user ID to use if authentication fails

    Returns:
        str: User ID or default value
    """
    user_id = get_user_id_from_request(req)
    if user_id:
        return user_id

    logger.debug(f"No authenticated user found, using default: {default_user_id}")
    return default_user_id

def require_auth(original_func):
    """
    Decorator to require authentication

    Args:
        original_func: Function to decorate

    Returns:
        Function: Decorated function
    """
    def wrapper(req):
        # Import here to avoid circular imports
        from src.shared.common import is_local_dev

        # In local development mode, create a mock user for testing
        local_dev_status = is_local_dev()
        logger.info(f"[AUTH DEBUG] is_local_dev() returned: {local_dev_status}")

        if local_dev_status:
            logger.info("[AUTH DEBUG] Local development mode detected, using mock authentication")
            mock_user = {
                "email": "<EMAIL>",
                "id": "dev-user-id"
            }
            setattr(req, "user", mock_user)
            logger.info("[AUTH DEBUG] Mock user set, calling original function")
            return original_func(req)

        # Get current user for production
        logger.info("[AUTH DEBUG] Production mode, attempting to get current user")
        current_user = get_current_user(req)
        logger.info(f"[AUTH DEBUG] get_current_user returned: {current_user}")
        if not current_user:
            logger.warning("[AUTH DEBUG] No current user found, returning 401")
            # Import here to avoid circular imports
            import azure.functions as func_module
            import json
            return func_module.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Add user to request
        setattr(req, "user", current_user)

        # Call original function
        return original_func(req)

    # Copy function attributes to wrapper
    import functools
    functools.update_wrapper(wrapper, original_func)

    return wrapper
