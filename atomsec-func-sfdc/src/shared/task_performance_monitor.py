"""
Task Performance Monitor

This module provides performance monitoring decorators and utilities
for sequential task processing optimization.
"""

import logging
import time
import functools
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import threading

from src.shared.task_sequence_coordinator import get_task_sequence_coordinator

logger = logging.getLogger(__name__)


class TaskPerformanceMonitor:
    """Monitor and optimize task performance"""
    
    def __init__(self):
        self.coordinator = get_task_sequence_coordinator()
        self._active_tasks = {}
        self._lock = threading.Lock()
    
    def monitor_task_execution(self, task_type: str, execution_log_id: str):
        """
        Decorator to monitor task execution performance
        
        Args:
            task_type: Type of task being monitored
            execution_log_id: Execution log ID
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Start performance tracking
                tracking_id = self.coordinator.start_task_execution_tracking(
                    task_type, execution_log_id
                )
                
                start_time = time.time()
                success = False
                error_message = None
                result = None
                
                try:
                    # Track active task
                    with self._lock:
                        self._active_tasks[tracking_id] = {
                            'task_type': task_type,
                            'execution_log_id': execution_log_id,
                            'start_time': start_time,
                            'thread_id': threading.get_ident()
                        }
                    
                    # Execute the task
                    result = func(*args, **kwargs)
                    success = True
                    
                    return result
                    
                except Exception as e:
                    error_message = str(e)
                    logger.error(f"Task {task_type} failed: {error_message}")
                    raise
                    
                finally:
                    # End performance tracking
                    self.coordinator.end_task_execution_tracking(
                        tracking_id, success, error_message
                    )
                    
                    # Remove from active tasks
                    with self._lock:
                        self._active_tasks.pop(tracking_id, None)
                    
                    # Log performance
                    duration = time.time() - start_time
                    logger.info(f"Task {task_type} completed in {duration:.2f}s (success: {success})")
            
            return wrapper
        return decorator
    
    def get_active_tasks(self) -> Dict[str, Any]:
        """Get currently active tasks"""
        with self._lock:
            return self._active_tasks.copy()
    
    def get_task_health_status(self, task_type: str) -> Dict[str, Any]:
        """
        Get health status for a specific task type
        
        Args:
            task_type: Type of task to check
            
        Returns:
            Dict[str, Any]: Health status information
        """
        stats = self.coordinator.get_task_performance_stats(task_type)
        
        if not stats or stats.get('total_executions', 0) == 0:
            return {
                'status': 'unknown',
                'message': 'No execution data available',
                'task_type': task_type
            }
        
        success_rate = stats.get('success_rate', 0)
        avg_duration = stats.get('average_duration', 0)
        
        # Determine health status
        if success_rate >= 95 and avg_duration <= 300:  # 5 minutes
            status = 'healthy'
            message = 'Task performance is optimal'
        elif success_rate >= 90 and avg_duration <= 600:  # 10 minutes
            status = 'warning'
            message = 'Task performance is acceptable but could be improved'
        else:
            status = 'critical'
            message = 'Task performance needs attention'
        
        return {
            'status': status,
            'message': message,
            'task_type': task_type,
            'success_rate': success_rate,
            'average_duration': avg_duration,
            'total_executions': stats.get('total_executions', 0),
            'last_updated': datetime.utcnow().isoformat()
        }


# Global monitor instance
_performance_monitor = None

def get_task_performance_monitor() -> TaskPerformanceMonitor:
    """Get global task performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = TaskPerformanceMonitor()
    return _performance_monitor


def monitor_sequential_task(task_type: str, execution_log_id: str):
    """
    Decorator for monitoring sequential task performance
    
    Args:
        task_type: Type of task being monitored
        execution_log_id: Execution log ID
    """
    monitor = get_task_performance_monitor()
    return monitor.monitor_task_execution(task_type, execution_log_id)


def track_task_metrics(func: Callable) -> Callable:
    """
    Decorator to automatically track task metrics based on function name
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Extract task type from function name
        task_type = func.__name__.replace('_', ' ').title()
        
        # Try to extract execution_log_id from kwargs
        execution_log_id = kwargs.get('execution_log_id', 'unknown')
        
        # Apply monitoring
        monitor = get_task_performance_monitor()
        monitored_func = monitor.monitor_task_execution(task_type, execution_log_id)(func)
        
        return monitored_func(*args, **kwargs)
    
    return wrapper


class SequencePerformanceAnalyzer:
    """Analyze and optimize sequence performance"""
    
    def __init__(self):
        self.coordinator = get_task_sequence_coordinator()
    
    def analyze_sequence_bottlenecks(self, execution_log_id: str) -> Dict[str, Any]:
        """
        Analyze bottlenecks in a task sequence
        
        Args:
            execution_log_id: Execution log ID to analyze
            
        Returns:
            Dict[str, Any]: Bottleneck analysis
        """
        metrics = self.coordinator.get_sequence_performance_metrics(execution_log_id)
        if not metrics:
            return {'error': 'No metrics available for sequence'}
        
        analysis = {
            'execution_log_id': execution_log_id,
            'bottlenecks': [],
            'recommendations': [],
            'performance_summary': {},
            'timestamp': datetime.utcnow().isoformat()
        }
        
        task_durations = metrics.get('task_durations', {})
        if not task_durations:
            return analysis
        
        # Find bottlenecks
        total_time = sum(task_durations.values())
        avg_time = total_time / len(task_durations)
        
        for task_type, duration in task_durations.items():
            percentage = (duration / total_time) * 100
            
            if duration > avg_time * 2:  # Task takes more than 2x average
                analysis['bottlenecks'].append({
                    'task_type': task_type,
                    'duration': duration,
                    'percentage_of_total': percentage,
                    'severity': 'high' if percentage > 50 else 'medium'
                })
        
        # Generate recommendations
        if analysis['bottlenecks']:
            for bottleneck in analysis['bottlenecks']:
                task_type = bottleneck['task_type']
                if bottleneck['severity'] == 'high':
                    analysis['recommendations'].append(
                        f"Optimize {task_type} - it consumes {bottleneck['percentage_of_total']:.1f}% of total execution time"
                    )
                else:
                    analysis['recommendations'].append(
                        f"Consider optimizing {task_type} for better overall performance"
                    )
        else:
            analysis['recommendations'].append("No significant bottlenecks detected")
        
        # Performance summary
        analysis['performance_summary'] = {
            'total_execution_time': total_time,
            'average_task_time': avg_time,
            'slowest_task': metrics.get('slowest_task'),
            'fastest_task': metrics.get('fastest_task'),
            'task_count': len(task_durations)
        }
        
        return analysis
    
    def get_optimization_suggestions(self, sequence_type: str) -> Dict[str, Any]:
        """
        Get optimization suggestions for a sequence type
        
        Args:
            sequence_type: Type of sequence to optimize
            
        Returns:
            Dict[str, Any]: Optimization suggestions
        """
        return self.coordinator.optimize_task_sequence(sequence_type)


# Global analyzer instance
_sequence_analyzer = None

def get_sequence_performance_analyzer() -> SequencePerformanceAnalyzer:
    """Get global sequence performance analyzer instance"""
    global _sequence_analyzer
    if _sequence_analyzer is None:
        _sequence_analyzer = SequencePerformanceAnalyzer()
    return _sequence_analyzer