"""
Cache Performance Analytics Service

This module provides comprehensive analytics and optimization for cache performance,
including real-time monitoring, trend analysis, and optimization recommendations.

Features:
- Real-time cache performance monitoring
- Cache hit/miss ratio analysis
- Memory usage optimization
- Cache warming effectiveness analysis
- Performance trend analysis
- Automated optimization recommendations
"""

import logging
import time
import statistics
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import json

# Configure module-level logger
logger = logging.getLogger(__name__)

class MetricType(Enum):
    """Cache metric types"""
    HIT_RATE = "hit_rate"
    MISS_RATE = "miss_rate"
    RESPONSE_TIME = "response_time"
    MEMORY_USAGE = "memory_usage"
    EVICTION_RATE = "eviction_rate"
    WARMING_EFFECTIVENESS = "warming_effectiveness"
    THROUGHPUT = "throughput"

@dataclass
class CacheMetric:
    """Cache performance metric"""
    timestamp: float
    metric_type: MetricType
    value: float
    metadata: Dict[str, Any]

@dataclass
class PerformanceTrend:
    """Performance trend analysis"""
    metric_type: MetricType
    trend_direction: str  # 'improving', 'degrading', 'stable'
    trend_strength: float  # 0.0 to 1.0
    current_value: float
    average_value: float
    min_value: float
    max_value: float
    data_points: int

@dataclass
class OptimizationRecommendation:
    """Cache optimization recommendation"""
    priority: str  # 'high', 'medium', 'low'
    category: str  # 'size', 'ttl', 'warming', 'eviction'
    title: str
    description: str
    expected_impact: str
    implementation_effort: str
    metrics_affected: List[MetricType]

class CachePerformanceAnalytics:
    """Advanced cache performance analytics service"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize cache performance analytics"""
        self.config = config
        self.metrics_buffer = deque(maxlen=config.get('max_metrics', 10000))
        self.aggregated_metrics = defaultdict(list)
        self.performance_baselines = {}
        self.alert_thresholds = config.get('alert_thresholds', {})
        self.lock = threading.RLock()
        
        # Analytics configuration
        self.analysis_window = config.get('analysis_window', 3600)  # 1 hour
        self.trend_analysis_points = config.get('trend_analysis_points', 100)
        self.baseline_calculation_interval = config.get('baseline_interval', 86400)  # 24 hours
        
        # Performance tracking
        self.performance_snapshots = deque(maxlen=1000)
        self.anomaly_detection_enabled = config.get('anomaly_detection', True)
        self.anomalies = deque(maxlen=100)
        
        # Start background analytics
        self._start_analytics_worker()
    
    def record_metric(self, metric_type: MetricType, value: float, 
                     metadata: Optional[Dict[str, Any]] = None):
        """Record a cache performance metric"""
        try:
            metric = CacheMetric(
                timestamp=time.time(),
                metric_type=metric_type,
                value=value,
                metadata=metadata or {}
            )
            
            with self.lock:
                self.metrics_buffer.append(metric)
                self.aggregated_metrics[metric_type].append(metric)
                
                # Keep only recent metrics for aggregation
                cutoff = time.time() - self.analysis_window
                self.aggregated_metrics[metric_type] = [
                    m for m in self.aggregated_metrics[metric_type]
                    if m.timestamp > cutoff
                ]
            
            # Check for anomalies
            if self.anomaly_detection_enabled:
                self._check_for_anomalies(metric)
                
        except Exception as e:
            logger.error(f"Error recording metric {metric_type}: {e}")
    
    def get_performance_summary(self, time_window: Optional[int] = None) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        time_window = time_window or self.analysis_window
        cutoff = time.time() - time_window
        
        try:
            with self.lock:
                # Filter metrics by time window
                recent_metrics = [m for m in self.metrics_buffer if m.timestamp > cutoff]
                
                if not recent_metrics:
                    return {'status': 'no_data', 'time_window': time_window}
                
                # Group metrics by type
                metrics_by_type = defaultdict(list)
                for metric in recent_metrics:
                    metrics_by_type[metric.metric_type].append(metric.value)
                
                # Calculate summary statistics
                summary = {
                    'time_window': time_window,
                    'total_metrics': len(recent_metrics),
                    'metrics_by_type': {},
                    'overall_health': 'good'
                }
                
                for metric_type, values in metrics_by_type.items():
                    if values:
                        summary['metrics_by_type'][metric_type.value] = {
                            'count': len(values),
                            'current': values[-1],
                            'average': statistics.mean(values),
                            'median': statistics.median(values),
                            'min': min(values),
                            'max': max(values),
                            'std_dev': statistics.stdev(values) if len(values) > 1 else 0
                        }
                
                # Determine overall health
                summary['overall_health'] = self._calculate_overall_health(summary['metrics_by_type'])
                
                return summary
                
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def analyze_trends(self, metric_type: MetricType, 
                      time_window: Optional[int] = None) -> PerformanceTrend:
        """Analyze performance trends for a specific metric"""
        time_window = time_window or self.analysis_window
        cutoff = time.time() - time_window
        
        try:
            with self.lock:
                # Get recent metrics for the specified type
                metrics = [
                    m for m in self.aggregated_metrics[metric_type]
                    if m.timestamp > cutoff
                ]
                
                if len(metrics) < 2:
                    return PerformanceTrend(
                        metric_type=metric_type,
                        trend_direction='insufficient_data',
                        trend_strength=0.0,
                        current_value=0.0,
                        average_value=0.0,
                        min_value=0.0,
                        max_value=0.0,
                        data_points=len(metrics)
                    )
                
                values = [m.value for m in metrics]
                timestamps = [m.timestamp for m in metrics]
                
                # Calculate trend using linear regression
                trend_direction, trend_strength = self._calculate_trend(timestamps, values)
                
                return PerformanceTrend(
                    metric_type=metric_type,
                    trend_direction=trend_direction,
                    trend_strength=trend_strength,
                    current_value=values[-1],
                    average_value=statistics.mean(values),
                    min_value=min(values),
                    max_value=max(values),
                    data_points=len(values)
                )
                
        except Exception as e:
            logger.error(f"Error analyzing trends for {metric_type}: {e}")
            return PerformanceTrend(
                metric_type=metric_type,
                trend_direction='error',
                trend_strength=0.0,
                current_value=0.0,
                average_value=0.0,
                min_value=0.0,
                max_value=0.0,
                data_points=0
            )
    
    def _calculate_trend(self, timestamps: List[float], values: List[float]) -> Tuple[str, float]:
        """Calculate trend direction and strength using linear regression"""
        try:
            n = len(timestamps)
            if n < 2:
                return 'insufficient_data', 0.0
            
            # Normalize timestamps to start from 0
            min_time = min(timestamps)
            x = [t - min_time for t in timestamps]
            y = values
            
            # Calculate linear regression
            x_mean = statistics.mean(x)
            y_mean = statistics.mean(y)
            
            numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
            
            if denominator == 0:
                return 'stable', 0.0
            
            slope = numerator / denominator
            
            # Calculate correlation coefficient for trend strength
            y_variance = sum((y[i] - y_mean) ** 2 for i in range(n))
            if y_variance == 0:
                return 'stable', 0.0
            
            r_squared = (numerator ** 2) / (denominator * y_variance)
            trend_strength = abs(r_squared) ** 0.5
            
            # Determine trend direction
            if abs(slope) < 0.001:  # Very small slope
                trend_direction = 'stable'
            elif slope > 0:
                trend_direction = 'improving' if 'hit_rate' in str(values) else 'degrading'
            else:
                trend_direction = 'degrading' if 'hit_rate' in str(values) else 'improving'
            
            return trend_direction, min(trend_strength, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return 'error', 0.0
    
    def generate_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Generate cache optimization recommendations based on analytics"""
        recommendations = []
        
        try:
            # Get current performance summary
            summary = self.get_performance_summary()
            
            if summary.get('status') != 'no_data':
                metrics = summary.get('metrics_by_type', {})
                
                # Analyze hit rate
                hit_rate_data = metrics.get('hit_rate')
                if hit_rate_data and hit_rate_data['average'] < 0.7:
                    recommendations.append(OptimizationRecommendation(
                        priority='high',
                        category='size',
                        title='Increase Cache Size',
                        description=f"Hit rate is {hit_rate_data['average']:.2%}, below optimal threshold of 70%",
                        expected_impact='Improve hit rate by 10-20%',
                        implementation_effort='low',
                        metrics_affected=[MetricType.HIT_RATE, MetricType.MISS_RATE]
                    ))
                
                # Analyze response time
                response_time_data = metrics.get('response_time')
                if response_time_data and response_time_data['average'] > 0.1:
                    recommendations.append(OptimizationRecommendation(
                        priority='medium',
                        category='warming',
                        title='Implement Cache Warming',
                        description=f"Average response time is {response_time_data['average']:.3f}s, consider cache warming",
                        expected_impact='Reduce response time by 30-50%',
                        implementation_effort='medium',
                        metrics_affected=[MetricType.RESPONSE_TIME, MetricType.HIT_RATE]
                    ))
                
                # Analyze memory usage
                memory_data = metrics.get('memory_usage')
                if memory_data and memory_data['average'] > 0.9:
                    recommendations.append(OptimizationRecommendation(
                        priority='high',
                        category='size',
                        title='Optimize Memory Usage',
                        description=f"Memory usage is {memory_data['average']:.1%}, approaching capacity",
                        expected_impact='Prevent cache thrashing and improve stability',
                        implementation_effort='medium',
                        metrics_affected=[MetricType.MEMORY_USAGE, MetricType.EVICTION_RATE]
                    ))
                
                # Analyze eviction rate
                eviction_data = metrics.get('eviction_rate')
                if eviction_data and eviction_data['average'] > 0.1:
                    recommendations.append(OptimizationRecommendation(
                        priority='medium',
                        category='eviction',
                        title='Optimize Eviction Policy',
                        description=f"High eviction rate of {eviction_data['average']:.2%}",
                        expected_impact='Reduce unnecessary evictions by 20-40%',
                        implementation_effort='low',
                        metrics_affected=[MetricType.EVICTION_RATE, MetricType.HIT_RATE]
                    ))
                
                # Analyze warming effectiveness
                warming_data = metrics.get('warming_effectiveness')
                if warming_data and warming_data['average'] < 0.5:
                    recommendations.append(OptimizationRecommendation(
                        priority='low',
                        category='warming',
                        title='Improve Cache Warming Strategy',
                        description=f"Cache warming effectiveness is {warming_data['average']:.1%}",
                        expected_impact='Improve warming effectiveness by 30-50%',
                        implementation_effort='high',
                        metrics_affected=[MetricType.WARMING_EFFECTIVENESS, MetricType.HIT_RATE]
                    ))
            
            # Sort recommendations by priority
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            recommendations.sort(key=lambda r: priority_order.get(r.priority, 3))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating optimization recommendations: {e}")
            return []
    
    def _calculate_overall_health(self, metrics: Dict[str, Any]) -> str:
        """Calculate overall cache health score"""
        try:
            health_score = 0
            total_weight = 0
            
            # Hit rate (weight: 40%)
            hit_rate = metrics.get('hit_rate', {}).get('average', 0)
            if hit_rate >= 0.8:
                health_score += 40
            elif hit_rate >= 0.6:
                health_score += 30
            elif hit_rate >= 0.4:
                health_score += 20
            else:
                health_score += 10
            total_weight += 40
            
            # Response time (weight: 30%)
            response_time = metrics.get('response_time', {}).get('average', 0)
            if response_time <= 0.05:
                health_score += 30
            elif response_time <= 0.1:
                health_score += 25
            elif response_time <= 0.2:
                health_score += 15
            else:
                health_score += 5
            total_weight += 30
            
            # Memory usage (weight: 20%)
            memory_usage = metrics.get('memory_usage', {}).get('average', 0)
            if memory_usage <= 0.7:
                health_score += 20
            elif memory_usage <= 0.85:
                health_score += 15
            elif memory_usage <= 0.95:
                health_score += 10
            else:
                health_score += 0
            total_weight += 20
            
            # Eviction rate (weight: 10%)
            eviction_rate = metrics.get('eviction_rate', {}).get('average', 0)
            if eviction_rate <= 0.05:
                health_score += 10
            elif eviction_rate <= 0.1:
                health_score += 7
            elif eviction_rate <= 0.2:
                health_score += 4
            else:
                health_score += 0
            total_weight += 10
            
            # Calculate final health score
            final_score = health_score / total_weight if total_weight > 0 else 0
            
            if final_score >= 0.8:
                return 'excellent'
            elif final_score >= 0.6:
                return 'good'
            elif final_score >= 0.4:
                return 'fair'
            else:
                return 'poor'
                
        except Exception as e:
            logger.error(f"Error calculating overall health: {e}")
            return 'unknown'
    
    def _check_for_anomalies(self, metric: CacheMetric):
        """Check for performance anomalies"""
        try:
            # Get baseline for this metric type
            baseline = self.performance_baselines.get(metric.metric_type)
            if not baseline:
                return
            
            # Check if current value is anomalous
            threshold_multiplier = 2.0  # 2 standard deviations
            lower_bound = baseline['mean'] - (threshold_multiplier * baseline['std_dev'])
            upper_bound = baseline['mean'] + (threshold_multiplier * baseline['std_dev'])
            
            if metric.value < lower_bound or metric.value > upper_bound:
                anomaly = {
                    'timestamp': metric.timestamp,
                    'metric_type': metric.metric_type.value,
                    'value': metric.value,
                    'expected_range': [lower_bound, upper_bound],
                    'severity': 'high' if abs(metric.value - baseline['mean']) > 3 * baseline['std_dev'] else 'medium'
                }
                
                with self.lock:
                    self.anomalies.append(anomaly)
                
                logger.warning(f"Performance anomaly detected: {anomaly}")
                
        except Exception as e:
            logger.error(f"Error checking for anomalies: {e}")
    
    def _start_analytics_worker(self):
        """Start background analytics worker"""
        def analytics_worker():
            while True:
                try:
                    self._update_performance_baselines()
                    self._create_performance_snapshot()
                    time.sleep(300)  # Run every 5 minutes
                except Exception as e:
                    logger.error(f"Analytics worker error: {e}")
                    time.sleep(300)
        
        worker_thread = threading.Thread(target=analytics_worker, daemon=True)
        worker_thread.start()
    
    def _update_performance_baselines(self):
        """Update performance baselines for anomaly detection"""
        try:
            cutoff = time.time() - self.baseline_calculation_interval
            
            with self.lock:
                for metric_type in MetricType:
                    metrics = [
                        m for m in self.aggregated_metrics[metric_type]
                        if m.timestamp > cutoff
                    ]
                    
                    if len(metrics) >= 10:  # Need minimum data points
                        values = [m.value for m in metrics]
                        self.performance_baselines[metric_type] = {
                            'mean': statistics.mean(values),
                            'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                            'min': min(values),
                            'max': max(values),
                            'updated_at': time.time()
                        }
                        
        except Exception as e:
            logger.error(f"Error updating performance baselines: {e}")
    
    def _create_performance_snapshot(self):
        """Create performance snapshot for historical analysis"""
        try:
            snapshot = {
                'timestamp': time.time(),
                'summary': self.get_performance_summary(300),  # 5-minute window
                'trends': {},
                'anomalies_count': len(self.anomalies)
            }
            
            # Add trend analysis for key metrics
            for metric_type in [MetricType.HIT_RATE, MetricType.RESPONSE_TIME, MetricType.MEMORY_USAGE]:
                trend = self.analyze_trends(metric_type, 1800)  # 30-minute window
                snapshot['trends'][metric_type.value] = asdict(trend)
            
            with self.lock:
                self.performance_snapshots.append(snapshot)
                
        except Exception as e:
            logger.error(f"Error creating performance snapshot: {e}")
    
    def get_historical_analysis(self, hours: int = 24) -> Dict[str, Any]:
        """Get historical performance analysis"""
        try:
            cutoff = time.time() - (hours * 3600)
            
            with self.lock:
                recent_snapshots = [
                    s for s in self.performance_snapshots
                    if s['timestamp'] > cutoff
                ]
            
            if not recent_snapshots:
                return {'status': 'no_data', 'hours': hours}
            
            # Analyze historical trends
            historical_analysis = {
                'time_period_hours': hours,
                'snapshots_analyzed': len(recent_snapshots),
                'performance_evolution': {},
                'anomaly_summary': {},
                'recommendations': []
            }
            
            # Track performance evolution
            for metric_type in MetricType:
                metric_values = []
                for snapshot in recent_snapshots:
                    summary = snapshot.get('summary', {})
                    metrics = summary.get('metrics_by_type', {})
                    if metric_type.value in metrics:
                        metric_values.append(metrics[metric_type.value]['average'])
                
                if metric_values:
                    historical_analysis['performance_evolution'][metric_type.value] = {
                        'start_value': metric_values[0],
                        'end_value': metric_values[-1],
                        'change_percent': ((metric_values[-1] - metric_values[0]) / metric_values[0]) * 100 if metric_values[0] != 0 else 0,
                        'trend': 'improving' if metric_values[-1] > metric_values[0] else 'degrading',
                        'volatility': statistics.stdev(metric_values) if len(metric_values) > 1 else 0
                    }
            
            # Anomaly summary
            recent_anomalies = [a for a in self.anomalies if a['timestamp'] > cutoff]
            anomaly_by_type = defaultdict(int)
            for anomaly in recent_anomalies:
                anomaly_by_type[anomaly['metric_type']] += 1
            
            historical_analysis['anomaly_summary'] = {
                'total_anomalies': len(recent_anomalies),
                'anomalies_by_type': dict(anomaly_by_type),
                'anomaly_rate': len(recent_anomalies) / len(recent_snapshots) if recent_snapshots else 0
            }
            
            return historical_analysis
            
        except Exception as e:
            logger.error(f"Error generating historical analysis: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def export_analytics_data(self, format: str = 'json') -> str:
        """Export analytics data for external analysis"""
        try:
            export_data = {
                'export_timestamp': time.time(),
                'config': self.config,
                'performance_summary': self.get_performance_summary(),
                'trends': {},
                'recommendations': [asdict(r) for r in self.generate_optimization_recommendations()],
                'anomalies': list(self.anomalies),
                'baselines': {k.value: v for k, v in self.performance_baselines.items()}
            }
            
            # Add trend analysis
            for metric_type in MetricType:
                trend = self.analyze_trends(metric_type)
                export_data['trends'][metric_type.value] = asdict(trend)
            
            if format.lower() == 'json':
                return json.dumps(export_data, indent=2, default=str)
            else:
                return str(export_data)
                
        except Exception as e:
            logger.error(f"Error exporting analytics data: {e}")
            return json.dumps({'error': str(e)})

# Global analytics instance
_analytics_instance = None

def get_cache_analytics(config: Optional[Dict[str, Any]] = None) -> CachePerformanceAnalytics:
    """Get global cache analytics instance"""
    global _analytics_instance
    
    if _analytics_instance is None:
        default_config = {
            'max_metrics': 10000,
            'analysis_window': 3600,
            'trend_analysis_points': 100,
            'baseline_interval': 86400,
            'anomaly_detection': True,
            'alert_thresholds': {
                'hit_rate_min': 0.7,
                'response_time_max': 0.1,
                'memory_usage_max': 0.9,
                'eviction_rate_max': 0.1
            }
        }
        
        if config:
            default_config.update(config)
        
        _analytics_instance = CachePerformanceAnalytics(default_config)
    
    return _analytics_instance