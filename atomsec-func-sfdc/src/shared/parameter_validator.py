"""
Parameter Validation Module

This module provides utilities for validating task parameters to ensure
they are properly formatted as JSON before storage.

Enhanced with sequential task processing validation and execution log tracking.
"""

import json
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class ParameterValidator:
    """
    Utility class for validating and sanitizing task parameters
    
    Enhanced with sequential task processing validation and execution log tracking.
    """
    
    def __init__(self):
        # Task sequence definitions for validation
        self.task_sequences = {
            'full_scan': [
                'sfdc_authenticate',
                'health_check', 
                'metadata_extraction',
                'profiles',
                'permission_sets',
                'overview',
                'mfa_enforcement',
                'device_activation',
                'login_ip_ranges',
                'login_hours',
                'session_timeout',
                'api_whitelisting',
                'password_policy',
                'pmd_apex_security'
            ],
            'security_scan': [
                'sfdc_authenticate',
                'health_check',
                'mfa_enforcement',
                'device_activation',
                'login_ip_ranges',
                'login_hours',
                'session_timeout',
                'api_whitelisting',
                'password_policy'
            ],
            'metadata_scan': [
                'sfdc_authenticate',
                'metadata_extraction',
                'profiles',
                'permission_sets'
            ]
        }
        
        # Task dependencies - which tasks must complete before others can start
        self.task_dependencies = {
            'health_check': ['sfdc_authenticate'],
            'metadata_extraction': ['sfdc_authenticate'],
            'profiles': ['sfdc_authenticate', 'metadata_extraction'],
            'permission_sets': ['sfdc_authenticate', 'metadata_extraction'],
            'overview': ['sfdc_authenticate', 'health_check'],
            'mfa_enforcement': ['sfdc_authenticate'],
            'device_activation': ['sfdc_authenticate'],
            'login_ip_ranges': ['sfdc_authenticate'],
            'login_hours': ['sfdc_authenticate'],
            'session_timeout': ['sfdc_authenticate'],
            'api_whitelisting': ['sfdc_authenticate'],
            'password_policy': ['sfdc_authenticate'],
            'pmd_apex_security': ['sfdc_authenticate', 'metadata_extraction']
        }
        
        # Required parameters for each task type
        self.task_parameter_requirements = {
            'sfdc_authenticate': {
                'required': ['client_id', 'client_secret', 'instance_url', 'org_id'],
                'optional': ['is_sandbox', 'username', 'private_key']
            },
            'health_check': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['timeout']
            },
            'metadata_extraction': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['metadata_types', 'timeout']
            },
            'profiles': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names', 'include_permissions']
            },
            'permission_sets': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['permission_set_names']
            },
            'overview': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': []
            },
            'mfa_enforcement': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'device_activation': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'login_ip_ranges': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'login_hours': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'session_timeout': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'api_whitelisting': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'password_policy': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['profile_names']
            },
            'pmd_apex_security': {
                'required': ['access_token', 'instance_url', 'org_id'],
                'optional': ['rules', 'timeout']
            }
        }
    
    @staticmethod
    def validate_json_parameters(params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that parameters can be serialized to JSON
        
        Args:
            params: Parameters to validate
            
        Returns:
            Dict[str, Any]: Validated parameters
            
        Raises:
            ValueError: If parameters cannot be serialized to JSON
        """
        try:
            # Attempt to serialize to JSON
            json_str = json.dumps(params)
            # Attempt to deserialize back
            parsed = json.loads(json_str)
            return parsed
        except (TypeError, ValueError) as e:
            logger.error(f"Invalid JSON parameters: {e}")
            raise ValueError(f"Parameters contain non-JSON serializable data: {e}")
    
    @staticmethod
    def sanitize_parameters(params: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Sanitize parameters to ensure JSON compatibility
        
        Args:
            params: Parameters to sanitize
            
        Returns:
            Dict[str, Any]: Sanitized parameters
        """
        if params is None:
            return {}
        
        # Handle common serialization issues
        sanitized = {}
        for key, value in params.items():
            try:
                # Handle datetime objects
                if hasattr(value, 'isoformat'):
                    sanitized[key] = value.isoformat()
                # Handle bytes
                elif isinstance(value, bytes):
                    sanitized[key] = value.decode('utf-8', errors='ignore')
                # Handle sets
                elif isinstance(value, set):
                    sanitized[key] = list(value)
                # Handle other non-serializable types
                else:
                    json.dumps({key: value})
                    sanitized[key] = value
            except (TypeError, ValueError):
                logger.warning(f"Converting non-JSON serializable parameter {key} to string")
                sanitized[key] = str(value)
        
        return sanitized
    
    @staticmethod
    def is_json_serializable(obj: Any) -> bool:
        """
        Check if an object is JSON serializable
        
        Args:
            obj: Object to check
            
        Returns:
            bool: True if JSON serializable, False otherwise
        """
        try:
            json.dumps(obj)
            return True
        except (TypeError, ValueError):
            return False
    
    def validate_sequential_task_parameters(self, params: Dict[str, Any], 
                                          task_type: str,
                                          execution_log_id: str) -> Dict[str, Any]:
        """
        Validate parameters for sequential task processing
        
        Args:
            params: Task parameters to validate
            task_type: Type of task being processed
            execution_log_id: Execution log ID for task sequence tracking
            
        Returns:
            Dict[str, Any]: Validated parameters
            
        Raises:
            ValueError: If validation fails
        """
        try:
            logger.info(f"Validating sequential task parameters for {task_type}")
            
            # Validate execution_log_id format
            if not self.validate_execution_log_id(execution_log_id):
                raise ValueError(f"Invalid execution_log_id format: {execution_log_id}")
            
            # Ensure execution_log_id is in parameters
            validated_params = params.copy()
            validated_params['execution_log_id'] = execution_log_id
            
            # Validate task-specific parameters
            validated_params = self.validate_task_type_parameters(validated_params, task_type)
            
            # Validate task dependencies
            self.validate_task_dependencies(task_type, execution_log_id)
            
            # Ensure parameters are JSON serializable
            validated_params = self.validate_json_parameters(validated_params)
            
            logger.info(f"Sequential task parameters validated for {task_type}")
            return validated_params
            
        except Exception as e:
            logger.error(f"Sequential task parameter validation failed for {task_type}: {str(e)}")
            raise ValueError(f"Sequential task validation failed: {str(e)}")
    
    def validate_execution_log_id(self, execution_log_id: str) -> bool:
        """
        Validate execution log ID format
        
        Args:
            execution_log_id: Execution log ID to validate
            
        Returns:
            bool: True if valid
        """
        try:
            # Validate UUID format
            uuid.UUID(execution_log_id)
            return True
        except (ValueError, TypeError):
            return False
    
    def validate_task_type_parameters(self, params: Dict[str, Any], task_type: str) -> Dict[str, Any]:
        """
        Validate parameters specific to a task type
        
        Args:
            params: Parameters to validate
            task_type: Type of task
            
        Returns:
            Dict[str, Any]: Validated parameters
            
        Raises:
            ValueError: If required parameters are missing
        """
        if task_type not in self.task_parameter_requirements:
            logger.warning(f"Unknown task type: {task_type}")
            return params
        
        requirements = self.task_parameter_requirements[task_type]
        required_params = requirements['required']
        
        # Check for required parameters
        missing_params = [param for param in required_params if param not in params]
        if missing_params:
            raise ValueError(f"Task {task_type} missing required parameters: {missing_params}")
        
        # Validate parameter formats
        validated_params = params.copy()
        
        # Validate org_id
        if 'org_id' in validated_params:
            org_id = validated_params['org_id']
            if not org_id or not isinstance(org_id, (str, int)):
                raise ValueError("org_id must be a non-empty string or integer")
            validated_params['org_id'] = str(org_id)
        
        # Validate instance_url format
        if 'instance_url' in validated_params:
            instance_url = validated_params['instance_url']
            if not instance_url or not isinstance(instance_url, str):
                raise ValueError("instance_url must be a non-empty string")
            
            # Basic URL format validation
            if not (instance_url.startswith('https://') and 'salesforce.com' in instance_url):
                raise ValueError("instance_url must be a valid Salesforce URL")
        
        # Validate access_token format
        if 'access_token' in validated_params:
            access_token = validated_params['access_token']
            if not access_token or not isinstance(access_token, str):
                raise ValueError("access_token must be a non-empty string")
            
            # Basic token format validation
            if len(access_token) < 50:
                raise ValueError("access_token appears to be too short")
        
        # Validate client credentials for authentication tasks
        if task_type == 'sfdc_authenticate':
            for cred_field in ['client_id', 'client_secret']:
                if cred_field in validated_params:
                    cred_value = validated_params[cred_field]
                    if not cred_value or not isinstance(cred_value, str):
                        raise ValueError(f"{cred_field} must be a non-empty string")
                    if len(cred_value) < 20:
                        raise ValueError(f"{cred_field} appears to be too short")
        
        return validated_params
    
    def validate_task_dependencies(self, task_type: str, execution_log_id: str):
        """
        Validate that task dependencies are met for sequential processing
        
        Args:
            task_type: Type of task
            execution_log_id: Execution log ID for tracking
            
        Note:
            This is a placeholder for dependency validation.
            In a full implementation, this would check the task status service
            to ensure prerequisite tasks have completed.
        """
        if task_type not in self.task_dependencies:
            return  # No dependencies defined
        
        dependencies = self.task_dependencies[task_type]
        logger.info(f"Task {task_type} has dependencies: {dependencies}")
        
        # In a full implementation, you would check the task status service here
        # to ensure all dependency tasks have completed for this execution_log_id
        
        # For now, just log the dependency check
        logger.info(f"Dependency validation for {task_type} with execution_log_id {execution_log_id}")
    
    def get_task_sequence(self, sequence_type: str = 'full_scan') -> List[str]:
        """
        Get the task sequence for a given sequence type
        
        Args:
            sequence_type: Type of sequence ('full_scan', 'security_scan', 'metadata_scan')
            
        Returns:
            List[str]: List of task types in sequence order
        """
        return self.task_sequences.get(sequence_type, [])
    
    def validate_task_sequence_order(self, task_type: str, completed_tasks: List[str]) -> bool:
        """
        Validate that a task can be executed based on completed tasks in sequence
        
        Args:
            task_type: Type of task to validate
            completed_tasks: List of already completed task types
            
        Returns:
            bool: True if task can be executed
        """
        if task_type not in self.task_dependencies:
            return True  # No dependencies
        
        dependencies = self.task_dependencies[task_type]
        
        # Check if all dependencies are in completed tasks
        for dependency in dependencies:
            if dependency not in completed_tasks:
                logger.warning(f"Task {task_type} cannot execute: dependency {dependency} not completed")
                return False
        
        return True
    
    def generate_execution_log_id(self) -> str:
        """
        Generate a new execution log ID for task sequence tracking
        
        Returns:
            str: New UUID-based execution log ID
        """
        execution_log_id = str(uuid.uuid4())
        logger.info(f"Generated new execution_log_id: {execution_log_id}")
        return execution_log_id
    
    @staticmethod
    def parse_malformed_parameters(params_str: str) -> Dict[str, Any]:
        """
        Parse malformed parameter strings that might be Python dict format
        
        Args:
            params_str: Parameter string to parse
            
        Returns:
            Dict[str, Any]: Parsed parameters
        """
        if not params_str or not isinstance(params_str, str):
            return {}
        
        try:
            # First try standard JSON parsing
            return json.loads(params_str)
        except json.JSONDecodeError:
            try:
                # Handle Python dict format with single quotes
                # Replace single quotes with double quotes, but be careful with apostrophes
                import ast
                return ast.literal_eval(params_str)
            except (ValueError, SyntaxError):
                try:
                    # Handle common Python dict patterns
                    import re
                    # Replace single quotes with double quotes for JSON compatibility
                    json_str = re.sub(r"'([^']*)':", r'"\1":', params_str)
                    json_str = re.sub(r":\s*'([^']*)'", r': "\1"', json_str)
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse malformed parameters: {params_str[:100]}...")
                    return {}


# Global validator instance
_validator = ParameterValidator()


def get_parameter_validator() -> ParameterValidator:
    """
    Get the global parameter validator instance
    
    Returns:
        ParameterValidator: The validator instance
    """
    return _validator


def validate_sequential_task_params(params: Dict[str, Any], 
                                   task_type: str,
                                   execution_log_id: str) -> Dict[str, Any]:
    """
    Convenience function for sequential task parameter validation
    
    Args:
        params: Task parameters to validate
        task_type: Type of task being processed
        execution_log_id: Execution log ID for task sequence tracking
        
    Returns:
        Dict[str, Any]: Validated parameters
    """
    validator = get_parameter_validator()
    return validator.validate_sequential_task_parameters(params, task_type, execution_log_id)


def validate_execution_log_tracking(execution_log_id: str, task_sequence: List[str]) -> bool:
    """
    Convenience function for execution log ID validation
    
    Args:
        execution_log_id: Execution log ID to validate
        task_sequence: List of task types in the sequence
        
    Returns:
        bool: True if valid for sequence tracking
    """
    validator = get_parameter_validator()
    return validator.validate_execution_log_id(execution_log_id) and len(task_sequence) > 0


def get_task_sequence(sequence_type: str = 'full_scan') -> List[str]:
    """
    Convenience function to get task sequence
    
    Args:
        sequence_type: Type of sequence
        
    Returns:
        List[str]: List of task types in sequence order
    """
    validator = get_parameter_validator()
    return validator.get_task_sequence(sequence_type)


def generate_execution_log_id() -> str:
    """
    Convenience function to generate execution log ID
    
    Returns:
        str: New execution log ID
    """
    validator = get_parameter_validator()
    return validator.generate_execution_log_id()