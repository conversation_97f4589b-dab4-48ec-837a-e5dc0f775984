import os
import json
import logging
import xml.etree.ElementTree as ET
import re
import csv
import subprocess
from typing import List, Dict, Any

def normalize_value(val):
    if val is None:
        return None
    val = val.strip().lower()
    if val in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    if val in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val

def normalize_permission_name(name):
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_user_type(val):
    return (val or '').strip().lower().replace(' ', '')

def extract_user_license_from_xml(xml_bytes):
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<userLicense>(.*?)</userLicense>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
    except Exception as e:
        logging.error(f"Error extracting userLicense: {e}")
    return ''

def parse_profile_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except Exception as e:
        logging.error(f"Error parsing userPermissions: {e}")
    return perms

def load_best_practices_xml(xml_path):
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        best_practices = []
        for usertype_elem in root.findall('UserType'):
            usertype = usertype_elem.attrib.get('name', '')
            for practice in usertype_elem.findall('Practice'):
                bp = {child.tag: child.text for child in practice}
                bp['UserType'] = usertype
                best_practices.append(bp)
        return best_practices
    except Exception as e:
        logging.error(f"Error loading best-practices XML: {e}")
        return []

def sfdx_query(soql, org_alias="myOrgAlias"):
    cmd = [
        "sfdx", "force:data:soql:query",
        "-q", soql,
        "-u", org_alias,
        "--json"
    ]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            json_start = result.stdout.find('{')
            if json_start != -1:
                try:
                    return json.loads(result.stdout[json_start:])
                except Exception as e:
                    logging.error(f"Error parsing SFDX JSON output: {e}")
                    logging.error(f"Raw SFDX stdout: {result.stdout}")
            logging.error("Could not find valid JSON in SFDX output.")
            logging.error(f"Raw SFDX stdout: {result.stdout}")
            return None
        else:
            logging.error(f"SFDX error: {result.stderr}")
            logging.error(f"Raw SFDX stdout: {result.stdout}")
            return None
    except Exception as e:
        logging.error(f"Exception running SFDX query: {e}")
        return None

def process_profiles_from_local(folder_path: str, best_practices_path: str) -> dict:
    logging.info(f"Processing profiles from {folder_path}")
    best_practices = load_best_practices_xml(best_practices_path)
    if not best_practices:
        logging.error("No best-practices loaded. Exiting.")
        return {}
    results = {}
    for fname in os.listdir(folder_path):
        if not fname.endswith('.profile'):
            continue
        profile_name = fname.replace('.profile', '')
        fpath = os.path.join(folder_path, fname)
        try:
            with open(fpath, 'rb') as f:
                xml_bytes = f.read()
        except Exception as e:
            logging.error(f"Failed to read profile file {fpath}: {e}")
            continue
        user_license = extract_user_license_from_xml(xml_bytes)
        user_permissions = parse_profile_permissions(xml_bytes)
        results_arr = []
        profile_user_type = normalize_user_type(user_license)
        relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == profile_user_type]
        if not relevant_bps:
            relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == 'blank']
        for bp in relevant_bps:
            bp_setting = (bp.get('SalesforceSetting') or '').strip()
            bp_standard_value = (bp.get('StandardValue') or '').strip()
            normalized_bp_setting = normalize_permission_name(bp_setting)
            profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
            if profile_perm is not None:
                profile_value = profile_perm['enabled']
                match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                results_arr.append({
                    'SalesforceSetting': bp_setting,
                    'StandardValue': bp_standard_value,
                    'ProfileValue': profile_value,
                    'Match': match,
                    'Description': bp.get('Description'),
                    'OWASP': bp.get('OWASP'),
                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                })
        results[profile_name] = results_arr
    return results

def write_results_to_csv(results: dict, filename: str):
    try:
        with open(filename, "w", newline='') as csvfile:
            if not results:
                return
            all_keys = set()
            for obj_list in results.values():
                for obj in obj_list:
                    all_keys.update(obj.keys())
            fieldnames = ["ProfileName"] + sorted(all_keys)
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for name, obj_list in results.items():
                for obj in obj_list:
                    row = {"ProfileName": name}
                    row.update(obj)
                    writer.writerow(row)
    except Exception as e:
        logging.error(f"Error writing results to CSV: {e}")

def get_active_user_counts_per_profile_sfdx(org_alias="myOrgAlias"):
    soql = "SELECT ProfileId, Profile.Name, COUNT(Id) cnt FROM User WHERE IsActive = TRUE GROUP BY ProfileId, Profile.Name"
    result = sfdx_query(soql, org_alias)
    logging.info("Raw SFDX output for profile summary:")
    logging.info(result)
    profile_counts = []
    if result and 'result' in result and 'records' in result['result']:
        for rec in result['result']['records']:
            profile_id = rec.get('ProfileId')
            profile_name = rec.get('Name')
            count = rec.get('cnt')
            if profile_id and profile_name and count is not None:
                profile_counts.append({
                    'ProfileId': profile_id,
                    'ProfileName': profile_name,
                    'ActiveUserCount': int(count)
                })
    return profile_counts

def write_profile_summary_csv(profile_counts, filename):
    try:
        with open(filename, "w", newline='') as csvfile:
            fieldnames = ['ProfileId', 'ProfileName', 'ActiveUserCount']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for row in profile_counts:
                writer.writerow(row)
    except Exception as e:
        logging.error(f"Error writing profile summary to CSV: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    org_alias = "myOrgAlias"  # Set your SFDX org alias here
    profiles_folder = "./profiles"
    best_practices_path = "./best_practices/Profiles_PermissionSetRisks-BestPractice.xml"

    # --- PROFILE BEST PRACTICES ANALYSIS ---
    logging.info("--- PROFILE BEST PRACTICES ANALYSIS ---")
    profile_results = process_profiles_from_local(profiles_folder, best_practices_path)
    with open("profile_analysis_output.json", "w") as f:
        json.dump(profile_results, f, indent=2)
    write_results_to_csv(profile_results, "profile_analysis_output.csv")
    logging.info("Wrote profile best-practices analysis outputs.")

    # --- PROFILE SUMMARY (NO PII, ACTIVE USERS ONLY) ---
    logging.info("--- PROFILE SUMMARY (NO PII, ACTIVE USERS ONLY) ---")
    profile_counts = get_active_user_counts_per_profile_sfdx(org_alias)
    write_profile_summary_csv(profile_counts, "profile_summary_active_users.csv")
    logging.info(f"Wrote profile summary for {len(profile_counts)} profiles.") 