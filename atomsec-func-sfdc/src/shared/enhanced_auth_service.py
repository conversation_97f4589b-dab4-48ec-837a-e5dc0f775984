"""
Enhanced Authentication Service

This module provides enhanced authentication and authorization services
specifically for SFDC operations, including Salesforce API authentication,
rate limiting, and security monitoring.
"""

import logging
import time
import hashlib
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading

# Import existing modules
from src.shared.auth_utils import (
    decode_token, get_current_user, get_user_id_from_request,
    get_user_from_request_or_default
)
from src.shared.salesforce_client import SalesforceClient
from src.shared.azure_services import get_secret

logger = logging.getLogger(__name__)


class AuthenticationError(Exception):
    """Custom exception for authentication failures"""
    pass


class RateLimitExceededError(Exception):
    """Custom exception for rate limit violations"""
    pass


class SalesforceAuthenticationError(Exception):
    """Custom exception for Salesforce authentication failures"""
    pass


class EnhancedAuthService:
    """
    Enhanced authentication service for SFDC operations
    
    This service provides:
    - Enhanced JWT token validation
    - Salesforce API authentication
    - Rate limiting for API interactions
    - Security monitoring and logging
    - Token caching and refresh
    """
    
    def __init__(self):
        self._token_cache = {}
        self._rate_limit_cache = defaultdict(deque)
        self._auth_attempts = defaultdict(list)
        self._security_events = []
        self._lock = threading.Lock()
        
        # Rate limiting configuration
        self.rate_limits = {
            'salesforce_api': {'requests': 100, 'window': 3600},  # 100 requests per hour
            'authentication': {'requests': 10, 'window': 300},    # 10 auth attempts per 5 minutes
            'token_validation': {'requests': 1000, 'window': 3600} # 1000 validations per hour
        }
    
    def validate_jwt_token_enhanced(self, token: str, 
                                  required_scopes: List[str] = None) -> Dict[str, Any]:
        """
        Enhanced JWT token validation with security checks
        
        Args:
            token: JWT token to validate
            required_scopes: Required scopes for the token
            
        Returns:
            Dict[str, Any]: Token payload with user information
            
        Raises:
            AuthenticationError: If token validation fails
        """
        try:
            # Check rate limiting for token validation
            self._check_rate_limit('token_validation', token)
            
            # Decode token using existing utility
            payload = decode_token(token)
            if not payload:
                self._log_security_event('token_validation_failed', {
                    'reason': 'invalid_token',
                    'token_hash': self._hash_token(token)
                })
                raise AuthenticationError("Invalid or expired token")
            
            # Validate required scopes if specified
            if required_scopes:
                token_scopes = payload.get('scopes', [])
                missing_scopes = [scope for scope in required_scopes if scope not in token_scopes]
                if missing_scopes:
                    self._log_security_event('insufficient_scopes', {
                        'required': required_scopes,
                        'provided': token_scopes,
                        'missing': missing_scopes
                    })
                    raise AuthenticationError(f"Insufficient scopes: {missing_scopes}")
            
            # Cache successful validation
            token_hash = self._hash_token(token)
            self._token_cache[token_hash] = {
                'payload': payload,
                'validated_at': datetime.utcnow(),
                'expires_at': datetime.utcfromtimestamp(payload.get('exp', 0))
            }
            
            self._log_security_event('token_validation_success', {
                'user_id': payload.get('sub'),
                'scopes': payload.get('scopes', [])
            })
            
            return payload
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            raise AuthenticationError(f"Token validation failed: {str(e)}")
    
    def authenticate_salesforce_api(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authenticate with Salesforce API and return connection details
        
        Args:
            credentials: Salesforce credentials (client_id, client_secret, instance_url, etc.)
            
        Returns:
            Dict[str, Any]: Authentication result with access token and instance URL
            
        Raises:
            SalesforceAuthenticationError: If authentication fails
        """
        try:
            # Check rate limiting for Salesforce authentication
            user_key = credentials.get('client_id', 'unknown')
            self._check_rate_limit('authentication', user_key)
            
            # Extract credentials
            client_id = credentials.get('client_id')
            client_secret = credentials.get('client_secret')
            instance_url = credentials.get('instance_url')
            is_sandbox = credentials.get('is_sandbox', False)
            
            if not all([client_id, client_secret, instance_url]):
                raise SalesforceAuthenticationError(
                    "Missing required credentials: client_id, client_secret, instance_url"
                )
            
            # Create Salesforce client
            sf_client = SalesforceClient(
                client_id=client_id,
                client_secret=client_secret,
                instance_url=instance_url,
                is_sandbox=is_sandbox
            )
            
            # Test connection using client credentials flow
            success, error_message, connection_details = sf_client.test_client_credentials_flow()
            
            if not success:
                self._log_security_event('salesforce_auth_failed', {
                    'client_id': client_id[:10] + '...',  # Partial client_id for logging
                    'instance_url': instance_url,
                    'error': error_message
                })
                raise SalesforceAuthenticationError(f"Salesforce authentication failed: {error_message}")
            
            # Cache successful authentication
            auth_key = self._hash_credentials(credentials)
            self._token_cache[auth_key] = {
                'connection_details': connection_details,
                'authenticated_at': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(hours=2)  # Salesforce tokens typically expire in 2 hours
            }
            
            self._log_security_event('salesforce_auth_success', {
                'client_id': client_id[:10] + '...',
                'instance_url': instance_url,
                'token_type': connection_details.get('token_type')
            })
            
            return connection_details
            
        except SalesforceAuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Salesforce authentication error: {str(e)}")
            raise SalesforceAuthenticationError(f"Authentication failed: {str(e)}")
    
    def validate_salesforce_token(self, access_token: str, 
                                instance_url: str) -> bool:
        """
        Validate Salesforce access token by making a test API call
        
        Args:
            access_token: Salesforce access token
            instance_url: Salesforce instance URL
            
        Returns:
            bool: True if token is valid
        """
        try:
            # Check rate limiting
            self._check_rate_limit('salesforce_api', access_token)
            
            # Create client with existing token
            sf_client = SalesforceClient(
                instance_url=instance_url,
                access_token=access_token
            )
            
            # Test with a simple query
            result = sf_client.query("SELECT Id FROM User LIMIT 1")
            
            if result and 'records' in result:
                self._log_security_event('salesforce_token_valid', {
                    'instance_url': instance_url,
                    'token_hash': self._hash_token(access_token)
                })
                return True
            else:
                self._log_security_event('salesforce_token_invalid', {
                    'instance_url': instance_url,
                    'token_hash': self._hash_token(access_token)
                })
                return False
                
        except Exception as e:
            logger.error(f"Salesforce token validation error: {str(e)}")
            self._log_security_event('salesforce_token_validation_error', {
                'instance_url': instance_url,
                'error': str(e)
            })
            return False
    
    def _check_rate_limit(self, limit_type: str, identifier: str):
        """
        Check rate limiting for a specific operation
        
        Args:
            limit_type: Type of rate limit to check
            identifier: Unique identifier for the request (user, token, etc.)
            
        Raises:
            RateLimitExceededError: If rate limit is exceeded
        """
        with self._lock:
            if limit_type not in self.rate_limits:
                return  # No rate limit configured
            
            config = self.rate_limits[limit_type]
            max_requests = config['requests']
            window_seconds = config['window']
            
            # Create a unique key for this limit type and identifier
            key = f"{limit_type}:{self._hash_token(identifier)}"
            
            # Clean old entries
            current_time = time.time()
            cutoff_time = current_time - window_seconds
            
            # Remove old entries
            while self._rate_limit_cache[key] and self._rate_limit_cache[key][0] < cutoff_time:
                self._rate_limit_cache[key].popleft()
            
            # Check if limit is exceeded
            if len(self._rate_limit_cache[key]) >= max_requests:
                self._log_security_event('rate_limit_exceeded', {
                    'limit_type': limit_type,
                    'identifier_hash': self._hash_token(identifier),
                    'current_count': len(self._rate_limit_cache[key]),
                    'max_requests': max_requests,
                    'window_seconds': window_seconds
                })
                raise RateLimitExceededError(
                    f"Rate limit exceeded for {limit_type}: {max_requests} requests per {window_seconds} seconds"
                )
            
            # Add current request
            self._rate_limit_cache[key].append(current_time)
    
    def _hash_token(self, token: str) -> str:
        """
        Create a hash of a token for logging and caching
        
        Args:
            token: Token to hash
            
        Returns:
            str: Hash of the token
        """
        return hashlib.sha256(token.encode()).hexdigest()[:16]
    
    def _hash_credentials(self, credentials: Dict[str, Any]) -> str:
        """
        Create a hash of credentials for caching
        
        Args:
            credentials: Credentials to hash
            
        Returns:
            str: Hash of the credentials
        """
        # Create a stable string representation
        cred_str = f"{credentials.get('client_id', '')}{credentials.get('instance_url', '')}"
        return hashlib.sha256(cred_str.encode()).hexdigest()[:16]
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log security events for monitoring
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details
        }
        
        self._security_events.append(event)
        
        # Log to application logger
        logger.info(f"Security event: {event_type}", extra=details)
        
        # Keep only recent events (last 1000)
        if len(self._security_events) > 1000:
            self._security_events = self._security_events[-1000:]
    
    def get_security_events(self, event_type: str = None, 
                          since: datetime = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get security events for monitoring
        
        Args:
            event_type: Filter by event type
            since: Filter events since this timestamp
            limit: Maximum number of events to return
            
        Returns:
            List[Dict[str, Any]]: Filtered security events
        """
        events = self._security_events.copy()
        
        if event_type:
            events = [e for e in events if e['event_type'] == event_type]
        
        if since:
            since_str = since.isoformat()
            events = [e for e in events if e['timestamp'] >= since_str]
        
        return events[-limit:] if limit else events
    
    def get_rate_limit_status(self, limit_type: str, identifier: str) -> Dict[str, Any]:
        """
        Get current rate limit status
        
        Args:
            limit_type: Type of rate limit
            identifier: Unique identifier
            
        Returns:
            Dict[str, Any]: Rate limit status
        """
        if limit_type not in self.rate_limits:
            return {'error': 'Unknown rate limit type'}
        
        config = self.rate_limits[limit_type]
        key = f"{limit_type}:{self._hash_token(identifier)}"
        
        with self._lock:
            current_count = len(self._rate_limit_cache[key])
            
            return {
                'limit_type': limit_type,
                'max_requests': config['requests'],
                'window_seconds': config['window'],
                'current_count': current_count,
                'remaining': max(0, config['requests'] - current_count),
                'reset_time': time.time() + config['window'] if current_count > 0 else None
            }
    
    def clear_caches(self):
        """Clear authentication caches"""
        with self._lock:
            self._token_cache.clear()
            self._rate_limit_cache.clear()
            self._auth_attempts.clear()
        logger.debug("Authentication caches cleared")


# Global enhanced auth service instance
_enhanced_auth_service = None


def get_enhanced_auth_service() -> EnhancedAuthService:
    """
    Get the global enhanced authentication service instance
    
    Returns:
        EnhancedAuthService: The enhanced auth service instance
    """
    global _enhanced_auth_service
    if _enhanced_auth_service is None:
        _enhanced_auth_service = EnhancedAuthService()
    return _enhanced_auth_service


def validate_jwt_token_enhanced(token: str, 
                               required_scopes: List[str] = None) -> Dict[str, Any]:
    """
    Convenience function for enhanced JWT token validation
    
    Args:
        token: JWT token to validate
        required_scopes: Required scopes for the token
        
    Returns:
        Dict[str, Any]: Token payload
    """
    auth_service = get_enhanced_auth_service()
    return auth_service.validate_jwt_token_enhanced(token, required_scopes)


def authenticate_salesforce_api(credentials: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function for Salesforce API authentication
    
    Args:
        credentials: Salesforce credentials
        
    Returns:
        Dict[str, Any]: Authentication result
    """
    auth_service = get_enhanced_auth_service()
    return auth_service.authenticate_salesforce_api(credentials)