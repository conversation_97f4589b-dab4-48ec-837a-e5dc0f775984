"""
Internal Service Security Module

This module provides security features for internal service-to-service
communication between the DB service and SFDC service.
"""

import logging
import hashlib
import hmac
import time
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import azure.functions as func

# Import existing modules
from src.shared.azure_services import get_secret, is_local_dev

logger = logging.getLogger(__name__)


class InternalServiceSecurityError(Exception):
    """Custom exception for internal service security failures"""
    pass


class InternalServiceSecurity:
    """
    Internal service security handler for SFDC service
    
    This service provides:
    - Service-to-service authentication
    - Request validation for internal calls
    - Security headers for internal communication
    - Monitoring of internal API calls
    """
    
    def __init__(self):
        self._trusted_services = {
            'atomsec-func-db-r': {
                'name': 'DB Service',
                'allowed_endpoints': [
                    '/task/high',
                    '/task/medium', 
                    '/task/low',
                    '/api/tasks/secure',
                    '/api/tasks/sequential/*',
                    '/api/tasks/validate-credentials'
                ],
                'required_headers': [
                    'X-Service-Name',
                    'X-Service-Version',
                    'X-Request-ID'
                ]
            }
        }
        
        self._security_events = []
        self._request_signatures = {}
        
        # Load service secrets for authentication
        self._service_secrets = self._load_service_secrets()
    
    def _load_service_secrets(self) -> Dict[str, str]:
        """
        Load service secrets for authentication
        
        Returns:
            Dict[str, str]: Service secrets
        """
        try:
            if is_local_dev():
                # Use default secrets for local development
                return {
                    'atomsec-func-db-r': 'dev-secret-key-db-service-2024',
                    'internal-api-key': 'dev-internal-api-key-2024'
                }
            else:
                # Load from Key Vault in production
                return {
                    'atomsec-func-db-r': get_secret('INTERNAL-SERVICE-DB-SECRET'),
                    'internal-api-key': get_secret('INTERNAL-API-SECRET')
                }
        except Exception as e:
            logger.error(f"Error loading service secrets: {str(e)}")
            return {}
    
    def validate_internal_service_request(self, req: func.HttpRequest) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate internal service request
        
        Args:
            req: HTTP request from internal service
            
        Returns:
            Tuple[bool, str, Dict[str, Any]]: (is_valid, message, service_info)
        """
        try:
            # Check if request is from trusted service
            service_name = req.headers.get('X-Service-Name')
            if not service_name:
                self._log_security_event('missing_service_header', {
                    'url': req.url,
                    'method': req.method,
                    'headers': dict(req.headers)
                })
                return False, "Missing X-Service-Name header", {}
            
            if service_name not in self._trusted_services:
                self._log_security_event('untrusted_service', {
                    'service_name': service_name,
                    'url': req.url,
                    'method': req.method
                })
                return False, f"Untrusted service: {service_name}", {}
            
            service_config = self._trusted_services[service_name]
            
            # Validate required headers
            missing_headers = []
            for header in service_config['required_headers']:
                if not req.headers.get(header):
                    missing_headers.append(header)
            
            if missing_headers:
                self._log_security_event('missing_required_headers', {
                    'service_name': service_name,
                    'missing_headers': missing_headers,
                    'url': req.url
                })
                return False, f"Missing required headers: {missing_headers}", {}
            
            # Validate endpoint access
            request_path = req.url.split('?')[0]  # Remove query parameters
            if not self._is_endpoint_allowed(request_path, service_config['allowed_endpoints']):
                self._log_security_event('unauthorized_endpoint', {
                    'service_name': service_name,
                    'endpoint': request_path,
                    'allowed_endpoints': service_config['allowed_endpoints']
                })
                return False, f"Endpoint not allowed for service {service_name}", {}
            
            # Validate service authentication
            auth_valid, auth_message = self._validate_service_authentication(req, service_name)
            if not auth_valid:
                return False, auth_message, {}
            
            # Log successful validation
            service_info = {
                'service_name': service_name,
                'service_display_name': service_config['name'],
                'request_id': req.headers.get('X-Request-ID'),
                'service_version': req.headers.get('X-Service-Version'),
                'validated_at': datetime.utcnow().isoformat()
            }
            
            self._log_security_event('internal_service_validated', {
                'service_name': service_name,
                'endpoint': request_path,
                'request_id': service_info['request_id']
            })
            
            return True, "Internal service request validated", service_info
            
        except Exception as e:
            logger.error(f"Error validating internal service request: {str(e)}")
            return False, f"Validation error: {str(e)}", {}
    
    def _is_endpoint_allowed(self, request_path: str, allowed_endpoints: List[str]) -> bool:
        """
        Check if endpoint is allowed for the service
        
        Args:
            request_path: Request path to check
            allowed_endpoints: List of allowed endpoint patterns
            
        Returns:
            bool: True if endpoint is allowed
        """
        for pattern in allowed_endpoints:
            if pattern.endswith('/*'):
                # Wildcard pattern
                prefix = pattern[:-2]
                if request_path.startswith(prefix):
                    return True
            elif pattern == request_path:
                # Exact match
                return True
        
        return False
    
    def _validate_service_authentication(self, req: func.HttpRequest, service_name: str) -> Tuple[bool, str]:
        """
        Validate service authentication using HMAC signatures
        
        Args:
            req: HTTP request
            service_name: Name of the calling service
            
        Returns:
            Tuple[bool, str]: (is_valid, message)
        """
        try:
            # Get service secret
            service_secret = self._service_secrets.get(service_name)
            if not service_secret:
                return False, f"No secret configured for service {service_name}"
            
            # Check for authentication header
            auth_header = req.headers.get('X-Service-Auth')
            if not auth_header:
                # For local development, allow requests without authentication
                if is_local_dev():
                    logger.warning(f"Allowing unauthenticated request from {service_name} in local dev mode")
                    return True, "Local development mode - authentication bypassed"
                else:
                    return False, "Missing X-Service-Auth header"
            
            # Parse authentication header (format: "timestamp:signature")
            try:
                timestamp_str, signature = auth_header.split(':', 1)
                timestamp = int(timestamp_str)
            except ValueError:
                return False, "Invalid X-Service-Auth header format"
            
            # Check timestamp (prevent replay attacks)
            current_time = int(time.time())
            if abs(current_time - timestamp) > 300:  # 5 minutes tolerance
                return False, "Request timestamp too old or too far in future"
            
            # Generate expected signature
            request_body = req.get_body()
            message = f"{req.method}:{req.url}:{timestamp}:{request_body.decode('utf-8') if request_body else ''}"
            expected_signature = hmac.new(
                service_secret.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            if not hmac.compare_digest(signature, expected_signature):
                return False, "Invalid service authentication signature"
            
            return True, "Service authentication validated"
            
        except Exception as e:
            logger.error(f"Error validating service authentication: {str(e)}")
            return False, f"Authentication validation error: {str(e)}"
    
    def add_internal_service_headers(self, response: func.HttpResponse, 
                                   service_info: Dict[str, Any]) -> func.HttpResponse:
        """
        Add security headers for internal service responses
        
        Args:
            response: HTTP response
            service_info: Information about the calling service
            
        Returns:
            func.HttpResponse: Response with security headers
        """
        try:
            # Add internal service security headers
            response.headers['X-SFDC-Service'] = 'atomsec-func-sfdc'
            response.headers['X-SFDC-Version'] = '1.0.0'
            response.headers['X-Response-Time'] = datetime.utcnow().isoformat()
            
            # Add correlation headers
            if service_info.get('request_id'):
                response.headers['X-Correlation-ID'] = service_info['request_id']
            
            # Add security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            
            # Add service-specific headers
            response.headers['X-Calling-Service'] = service_info.get('service_name', 'unknown')
            
            return response
            
        except Exception as e:
            logger.error(f"Error adding internal service headers: {str(e)}")
            return response
    
    def generate_service_auth_header(self, method: str, url: str, body: str = '') -> str:
        """
        Generate authentication header for outgoing internal service requests
        
        Args:
            method: HTTP method
            url: Request URL
            body: Request body
            
        Returns:
            str: Authentication header value
        """
        try:
            # Get internal API secret
            api_secret = self._service_secrets.get('internal-api-key', '')
            if not api_secret:
                logger.warning("No internal API secret configured")
                return ''
            
            # Generate timestamp
            timestamp = int(time.time())
            
            # Create message to sign
            message = f"{method}:{url}:{timestamp}:{body}"
            
            # Generate signature
            signature = hmac.new(
                api_secret.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return f"{timestamp}:{signature}"
            
        except Exception as e:
            logger.error(f"Error generating service auth header: {str(e)}")
            return ''
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log internal service security events
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details,
            'source': 'internal_service_security'
        }
        
        self._security_events.append(event)
        
        # Log to application logger
        logger.info(f"Internal service security event: {event_type}", extra=details)
        
        # Keep only recent events (last 500)
        if len(self._security_events) > 500:
            self._security_events = self._security_events[-500:]
    
    def get_security_events(self, event_type: str = None, 
                          since: datetime = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get internal service security events
        
        Args:
            event_type: Filter by event type
            since: Filter events since this timestamp
            limit: Maximum number of events to return
            
        Returns:
            List[Dict[str, Any]]: Filtered security events
        """
        events = self._security_events.copy()
        
        if event_type:
            events = [e for e in events if e['event_type'] == event_type]
        
        if since:
            since_str = since.isoformat()
            events = [e for e in events if e['timestamp'] >= since_str]
        
        return events[-limit:] if limit else events
    
    def get_trusted_services(self) -> Dict[str, Any]:
        """
        Get information about trusted services
        
        Returns:
            Dict[str, Any]: Trusted services configuration
        """
        return {
            service_name: {
                'name': config['name'],
                'allowed_endpoints': config['allowed_endpoints'],
                'required_headers': config['required_headers']
            }
            for service_name, config in self._trusted_services.items()
        }


# Global internal service security instance
_internal_service_security = None


def get_internal_service_security() -> InternalServiceSecurity:
    """
    Get the global internal service security instance
    
    Returns:
        InternalServiceSecurity: The security instance
    """
    global _internal_service_security
    if _internal_service_security is None:
        _internal_service_security = InternalServiceSecurity()
    return _internal_service_security


def validate_internal_service_call(req: func.HttpRequest) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Convenience function for internal service validation
    
    Args:
        req: HTTP request
        
    Returns:
        Tuple[bool, str, Dict[str, Any]]: (is_valid, message, service_info)
    """
    security = get_internal_service_security()
    return security.validate_internal_service_request(req)


def add_internal_response_headers(response: func.HttpResponse, 
                                service_info: Dict[str, Any]) -> func.HttpResponse:
    """
    Convenience function for adding internal service response headers
    
    Args:
        response: HTTP response
        service_info: Service information
        
    Returns:
        func.HttpResponse: Response with headers
    """
    security = get_internal_service_security()
    return security.add_internal_service_headers(response, service_info)