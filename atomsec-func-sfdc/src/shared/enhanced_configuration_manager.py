"""
Enhanced Configuration Manager

This module provides a comprehensive configuration management system with:
- Environment-aware configuration loading
- Secure configuration retrieval from Key Vault
- Configuration validation and error handling
- Configuration caching and reload capabilities
- Type-safe configuration access

Best practices implemented:
- Environment-specific configuration profiles
- Secure secrets management via Key Vault
- Configuration validation at startup
- Centralized configuration access
- Configuration change detection and reload
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union, List, Type, TypeVar, Generic
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
from pathlib import Path

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import existing modules
from src.shared.common import is_local_dev, is_test_env
from src.shared.azure_services import get_secret, get_keyvault_client

T = TypeVar('T')

class Environment(Enum):
    """Environment types"""
    LOCAL = "local"
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class ConfigurationError(Exception):
    """Configuration-related errors"""
    pass

class ValidationError(ConfigurationError):
    """Configuration validation errors"""
    pass

@dataclass
class ConfigurationValue(Generic[T]):
    """Represents a configuration value with metadata"""
    value: T
    source: str  # environment, keyvault, file, default
    last_updated: datetime = field(default_factory=datetime.now)
    is_secret: bool = False
    validation_rules: List[str] = field(default_factory=list)

@dataclass
class SecurityConfiguration:
    """Security configuration settings"""
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    rate_limit_requests_per_minute: int = 100
    allowed_origins: List[str] = field(default_factory=list)
    require_https: bool = True
    enable_audit_logging: bool = True
    max_request_size_mb: int = 10
    session_timeout_minutes: int = 60

@dataclass
class PerformanceConfiguration:
    """Performance optimization settings"""
    connection_pool_size: int = 10
    request_timeout_seconds: int = 30
    cache_ttl_seconds: int = 300
    max_concurrent_requests: int = 100
    enable_compression: bool = True
    enable_caching: bool = True
    memory_limit_mb: int = 512
    cpu_limit_percent: int = 80

@dataclass
class MonitoringConfiguration:
    """Monitoring and observability settings"""
    application_insights_key: str = ""
    log_level: str = "INFO"
    enable_custom_metrics: bool = True
    health_check_interval_seconds: int = 30
    enable_distributed_tracing: bool = True
    metrics_retention_days: int = 30
    enable_performance_counters: bool = True
    alert_thresholds: Dict[str, float] = field(default_factory=dict)

@dataclass
class DatabaseConfiguration:
    """Database configuration settings"""
    connection_string: str
    connection_pool_size: int = 10
    connection_timeout_seconds: int = 30
    command_timeout_seconds: int = 60
    retry_attempts: int = 3
    retry_delay_seconds: int = 1
    enable_connection_pooling: bool = True

@dataclass
class StorageConfiguration:
    """Storage configuration settings"""
    connection_string: str
    container_name: str = "default"
    enable_encryption: bool = True
    retention_days: int = 90
    max_file_size_mb: int = 100

@dataclass
class ServiceConfiguration:
    """External service configuration"""
    base_url: str
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay_seconds: int = 1
    api_key: Optional[str] = None
    user_agent: str = "atomsec-func-sfdc/1.0.0"

class EnhancedConfigurationManager:
    """
    Enhanced configuration manager with environment-aware loading,
    secure secrets management, and validation capabilities
    """
    
    def __init__(self, environment: Optional[Environment] = None):
        """
        Initialize the configuration manager
        
        Args:
            environment: Target environment (auto-detected if not provided)
        """
        self._environment = environment or self._detect_environment()
        self._config_cache: Dict[str, ConfigurationValue] = {}
        self._last_reload = datetime.now()
        self._reload_interval = timedelta(minutes=5)
        self._lock = threading.RLock()
        self._validation_rules: Dict[str, List[callable]] = {}
        
        logger.info(f"Initializing Enhanced Configuration Manager for environment: {self._environment.value}")
        
        # Load initial configuration
        self._load_configuration()
    
    def _detect_environment(self) -> Environment:
        """Detect the current environment"""
        if is_test_env():
            return Environment.TESTING
        elif is_local_dev():
            return Environment.LOCAL
        else:
            env_name = os.environ.get("ENVIRONMENT", "production").lower()
            try:
                return Environment(env_name)
            except ValueError:
                logger.warning(f"Unknown environment '{env_name}', defaulting to production")
                return Environment.PRODUCTION
    
    def _load_configuration(self):
        """Load configuration from all sources"""
        with self._lock:
            try:
                # Load configuration in order of precedence:
                # 1. Environment variables (highest priority)
                # 2. Key Vault secrets
                # 3. Configuration files
                # 4. Default values (lowest priority)
                
                self._load_default_configuration()
                self._load_file_configuration()
                self._load_keyvault_configuration()
                self._load_environment_configuration()
                
                # Validate configuration after loading
                self._validate_configuration()
                
                self._last_reload = datetime.now()
                logger.info("Configuration loaded successfully")
                
            except Exception as e:
                logger.error(f"Error loading configuration: {str(e)}")
                raise ConfigurationError(f"Failed to load configuration: {str(e)}")
    
    def _load_default_configuration(self):
        """Load default configuration values"""
        defaults = {
            # Security defaults
            "security.jwt_algorithm": "HS256",
            "security.jwt_access_token_expire_minutes": 30,
            "security.jwt_refresh_token_expire_days": 7,
            "security.rate_limit_requests_per_minute": 100,
            "security.require_https": True,
            "security.enable_audit_logging": True,
            "security.max_request_size_mb": 10,
            "security.session_timeout_minutes": 60,
            
            # Performance defaults
            "performance.connection_pool_size": 10,
            "performance.request_timeout_seconds": 30,
            "performance.cache_ttl_seconds": 300,
            "performance.max_concurrent_requests": 100,
            "performance.enable_compression": True,
            "performance.enable_caching": True,
            "performance.memory_limit_mb": 512,
            "performance.cpu_limit_percent": 80,
            
            # Monitoring defaults
            "monitoring.log_level": "INFO",
            "monitoring.enable_custom_metrics": True,
            "monitoring.health_check_interval_seconds": 30,
            "monitoring.enable_distributed_tracing": True,
            "monitoring.metrics_retention_days": 30,
            "monitoring.enable_performance_counters": True,
            
            # Database defaults
            "database.connection_pool_size": 10,
            "database.connection_timeout_seconds": 30,
            "database.command_timeout_seconds": 60,
            "database.retry_attempts": 3,
            "database.retry_delay_seconds": 1,
            "database.enable_connection_pooling": True,
            
            # Storage defaults
            "storage.container_name": "default",
            "storage.enable_encryption": True,
            "storage.retention_days": 90,
            "storage.max_file_size_mb": 100,
        }
        
        for key, value in defaults.items():
            self._config_cache[key] = ConfigurationValue(
                value=value,
                source="default",
                is_secret=False
            )
    
    def _load_file_configuration(self):
        """Load configuration from files"""
        try:
            config_dir = Path(__file__).parent.parent / "config"
            
            # Load environment-specific configuration file
            config_file = config_dir / f"{self._environment.value}.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    self._merge_configuration(file_config, "file")
                    logger.info(f"Loaded configuration from {config_file}")
            
            # Load common configuration file
            common_config_file = config_dir / "common.json"
            if common_config_file.exists():
                with open(common_config_file, 'r') as f:
                    common_config = json.load(f)
                    self._merge_configuration(common_config, "file")
                    logger.info(f"Loaded common configuration from {common_config_file}")
                    
        except Exception as e:
            logger.warning(f"Error loading file configuration: {str(e)}")
    
    def _load_keyvault_configuration(self):
        """Load configuration from Key Vault"""
        if self._environment == Environment.LOCAL:
            logger.info("Skipping Key Vault configuration for local environment")
            return
        
        try:
            # Define secrets to load from Key Vault
            keyvault_secrets = {
                "security.jwt_secret_key": "jwt-secret",
                "database.connection_string": "sql-connection-string",
                "storage.connection_string": "storage-connection-string",
                "monitoring.application_insights_key": "application-insights-key",
                "azure_ad.client_id": "azure-ad-client-id",
                "azure_ad.client_secret": "azure-ad-client-secret",
                "azure_ad.tenant_id": "azure-ad-tenant-id",
                "db_service.api_key": "db-service-api-key",
            }
            
            for config_key, secret_name in keyvault_secrets.items():
                try:
                    secret_value = get_secret(secret_name)
                    if secret_value:
                        self._config_cache[config_key] = ConfigurationValue(
                            value=secret_value,
                            source="keyvault",
                            is_secret=True
                        )
                        logger.debug(f"Loaded secret '{config_key}' from Key Vault")
                    else:
                        logger.warning(f"Secret '{secret_name}' not found in Key Vault")
                except Exception as e:
                    logger.warning(f"Error loading secret '{secret_name}' from Key Vault: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error loading Key Vault configuration: {str(e)}")
    
    def _load_environment_configuration(self):
        """Load configuration from environment variables"""
        try:
            # Define environment variable mappings
            env_mappings = {
                # Security
                "JWT_SECRET": "security.jwt_secret_key",
                "JWT_ALGORITHM": "security.jwt_algorithm",
                "JWT_ACCESS_TOKEN_EXPIRE_MINUTES": "security.jwt_access_token_expire_minutes",
                "RATE_LIMIT_RPM": "security.rate_limit_requests_per_minute",
                "REQUIRE_HTTPS": "security.require_https",
                "ENABLE_AUDIT_LOGGING": "security.enable_audit_logging",
                
                # Performance
                "CONNECTION_POOL_SIZE": "performance.connection_pool_size",
                "REQUEST_TIMEOUT": "performance.request_timeout_seconds",
                "CACHE_TTL": "performance.cache_ttl_seconds",
                "MAX_CONCURRENT_REQUESTS": "performance.max_concurrent_requests",
                "ENABLE_COMPRESSION": "performance.enable_compression",
                "ENABLE_CACHING": "performance.enable_caching",
                
                # Monitoring
                "APPLICATION_INSIGHTS_KEY": "monitoring.application_insights_key",
                "LOG_LEVEL": "monitoring.log_level",
                "ENABLE_CUSTOM_METRICS": "monitoring.enable_custom_metrics",
                "HEALTH_CHECK_INTERVAL": "monitoring.health_check_interval_seconds",
                
                # Database
                "SQL_CONNECTION_STRING": "database.connection_string",
                "DB_CONNECTION_POOL_SIZE": "database.connection_pool_size",
                "DB_CONNECTION_TIMEOUT": "database.connection_timeout_seconds",
                "DB_COMMAND_TIMEOUT": "database.command_timeout_seconds",
                
                # Storage
                "AZURE_STORAGE_CONNECTION_STRING": "storage.connection_string",
                "STORAGE_CONTAINER_NAME": "storage.container_name",
                "STORAGE_RETENTION_DAYS": "storage.retention_days",
                
                # Azure AD
                "AZURE_AD_CLIENT_ID": "azure_ad.client_id",
                "AZURE_AD_CLIENT_SECRET": "azure_ad.client_secret",
                "AZURE_AD_TENANT_ID": "azure_ad.tenant_id",
                
                # External Services
                "DB_SERVICE_URL": "db_service.base_url",
                "DB_SERVICE_TIMEOUT": "db_service.timeout_seconds",
                "DB_SERVICE_API_KEY": "db_service.api_key",
            }
            
            for env_var, config_key in env_mappings.items():
                env_value = os.environ.get(env_var)
                if env_value is not None:
                    # Convert string values to appropriate types
                    converted_value = self._convert_value(env_value, config_key)
                    
                    self._config_cache[config_key] = ConfigurationValue(
                        value=converted_value,
                        source="environment",
                        is_secret=self._is_secret_key(config_key)
                    )
                    
                    if not self._is_secret_key(config_key):
                        logger.debug(f"Loaded environment variable '{env_var}' -> '{config_key}': {converted_value}")
                    else:
                        logger.debug(f"Loaded secret environment variable '{env_var}' -> '{config_key}'")
                        
        except Exception as e:
            logger.error(f"Error loading environment configuration: {str(e)}")
    
    def _merge_configuration(self, config_dict: Dict[str, Any], source: str):
        """Merge configuration dictionary into cache"""
        def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
            items = []
            for k, v in d.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                if isinstance(v, dict):
                    items.extend(flatten_dict(v, new_key, sep=sep).items())
                else:
                    items.append((new_key, v))
            return dict(items)
        
        flattened = flatten_dict(config_dict)
        for key, value in flattened.items():
            self._config_cache[key] = ConfigurationValue(
                value=value,
                source=source,
                is_secret=self._is_secret_key(key)
            )
    
    def _convert_value(self, value: str, config_key: str) -> Any:
        """Convert string value to appropriate type based on config key"""
        # Boolean conversions
        if any(keyword in config_key.lower() for keyword in ['enable', 'require', 'use']):
            return value.lower() in ('true', '1', 'yes', 'on')
        
        # Integer conversions
        if any(keyword in config_key.lower() for keyword in ['timeout', 'size', 'limit', 'port', 'minutes', 'days', 'seconds', 'attempts', 'delay', 'interval', 'retention']):
            try:
                return int(value)
            except ValueError:
                logger.warning(f"Could not convert '{value}' to integer for key '{config_key}'")
                return value
        
        # Float conversions
        if any(keyword in config_key.lower() for keyword in ['percent', 'ratio', 'rate']):
            try:
                return float(value)
            except ValueError:
                logger.warning(f"Could not convert '{value}' to float for key '{config_key}'")
                return value
        
        # List conversions (comma-separated)
        if 'origins' in config_key.lower() or 'list' in config_key.lower():
            return [item.strip() for item in value.split(',') if item.strip()]
        
        return value
    
    def _is_secret_key(self, key: str) -> bool:
        """Determine if a configuration key contains sensitive information"""
        secret_keywords = ['secret', 'key', 'password', 'token', 'connection_string', 'client_secret']
        return any(keyword in key.lower() for keyword in secret_keywords)
    
    def _validate_configuration(self):
        """Validate loaded configuration"""
        errors = []
        
        try:
            # Validate required configuration
            required_keys = [
                "security.jwt_secret_key",
                "database.connection_string",
                "storage.connection_string"
            ]
            
            for key in required_keys:
                if key not in self._config_cache or not self._config_cache[key].value:
                    errors.append(f"Required configuration '{key}' is missing or empty")
            
            # Validate value ranges
            validations = {
                "security.jwt_access_token_expire_minutes": (1, 1440),  # 1 minute to 24 hours
                "security.rate_limit_requests_per_minute": (1, 10000),
                "performance.connection_pool_size": (1, 100),
                "performance.request_timeout_seconds": (1, 300),
                "performance.cache_ttl_seconds": (1, 86400),  # 1 second to 24 hours
                "monitoring.health_check_interval_seconds": (1, 3600),  # 1 second to 1 hour
            }
            
            for key, (min_val, max_val) in validations.items():
                if key in self._config_cache:
                    value = self._config_cache[key].value
                    if isinstance(value, (int, float)) and not (min_val <= value <= max_val):
                        errors.append(f"Configuration '{key}' value {value} is outside valid range [{min_val}, {max_val}]")
            
            # Custom validation rules
            for key, rules in self._validation_rules.items():
                if key in self._config_cache:
                    value = self._config_cache[key].value
                    for rule in rules:
                        try:
                            if not rule(value):
                                errors.append(f"Configuration '{key}' failed custom validation")
                        except Exception as e:
                            errors.append(f"Configuration '{key}' validation error: {str(e)}")
            
            if errors:
                error_message = "Configuration validation failed:\n" + "\n".join(f"  - {error}" for error in errors)
                logger.error(error_message)
                raise ValidationError(error_message)
            
            logger.info("Configuration validation passed")
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logger.error(f"Error during configuration validation: {str(e)}")
            raise ValidationError(f"Configuration validation error: {str(e)}")
    
    def get(self, key: str, default: Any = None, required: bool = False) -> Any:
        """
        Get configuration value
        
        Args:
            key: Configuration key (dot-separated)
            default: Default value if key not found
            required: Whether the key is required (raises error if missing)
            
        Returns:
            Configuration value
        """
        with self._lock:
            # Check if reload is needed
            if datetime.now() - self._last_reload > self._reload_interval:
                try:
                    self.reload_configuration()
                except Exception as e:
                    logger.warning(f"Failed to reload configuration: {str(e)}")
            
            if key in self._config_cache:
                config_value = self._config_cache[key]
                if not config_value.is_secret:
                    logger.debug(f"Retrieved configuration '{key}': {config_value.value} (source: {config_value.source})")
                else:
                    logger.debug(f"Retrieved secret configuration '{key}' (source: {config_value.source})")
                return config_value.value
            
            if required:
                raise ConfigurationError(f"Required configuration key '{key}' not found")
            
            logger.debug(f"Configuration key '{key}' not found, using default: {default}")
            return default
    
    def get_typed(self, key: str, value_type: Type[T], default: Optional[T] = None, required: bool = False) -> T:
        """
        Get configuration value with type checking
        
        Args:
            key: Configuration key
            value_type: Expected value type
            default: Default value
            required: Whether the key is required
            
        Returns:
            Typed configuration value
        """
        value = self.get(key, default, required)
        
        if value is not None and not isinstance(value, value_type):
            try:
                # Attempt type conversion
                if value_type == bool and isinstance(value, str):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif value_type in (int, float) and isinstance(value, str):
                    value = value_type(value)
                else:
                    value = value_type(value)
            except (ValueError, TypeError) as e:
                raise ConfigurationError(f"Configuration key '{key}' cannot be converted to {value_type.__name__}: {str(e)}")
        
        return value
    
    def get_security_config(self) -> SecurityConfiguration:
        """Get security configuration"""
        return SecurityConfiguration(
            jwt_secret_key=self.get("security.jwt_secret_key", required=True),
            jwt_algorithm=self.get("security.jwt_algorithm", "HS256"),
            jwt_access_token_expire_minutes=self.get_typed("security.jwt_access_token_expire_minutes", int, 30),
            jwt_refresh_token_expire_days=self.get_typed("security.jwt_refresh_token_expire_days", int, 7),
            rate_limit_requests_per_minute=self.get_typed("security.rate_limit_requests_per_minute", int, 100),
            allowed_origins=self.get("security.allowed_origins", []),
            require_https=self.get_typed("security.require_https", bool, True),
            enable_audit_logging=self.get_typed("security.enable_audit_logging", bool, True),
            max_request_size_mb=self.get_typed("security.max_request_size_mb", int, 10),
            session_timeout_minutes=self.get_typed("security.session_timeout_minutes", int, 60)
        )
    
    def get_performance_config(self) -> PerformanceConfiguration:
        """Get performance configuration"""
        return PerformanceConfiguration(
            connection_pool_size=self.get_typed("performance.connection_pool_size", int, 10),
            request_timeout_seconds=self.get_typed("performance.request_timeout_seconds", int, 30),
            cache_ttl_seconds=self.get_typed("performance.cache_ttl_seconds", int, 300),
            max_concurrent_requests=self.get_typed("performance.max_concurrent_requests", int, 100),
            enable_compression=self.get_typed("performance.enable_compression", bool, True),
            enable_caching=self.get_typed("performance.enable_caching", bool, True),
            memory_limit_mb=self.get_typed("performance.memory_limit_mb", int, 512),
            cpu_limit_percent=self.get_typed("performance.cpu_limit_percent", int, 80)
        )
    
    def get_monitoring_config(self) -> MonitoringConfiguration:
        """Get monitoring configuration"""
        return MonitoringConfiguration(
            application_insights_key=self.get("monitoring.application_insights_key", ""),
            log_level=self.get("monitoring.log_level", "INFO"),
            enable_custom_metrics=self.get_typed("monitoring.enable_custom_metrics", bool, True),
            health_check_interval_seconds=self.get_typed("monitoring.health_check_interval_seconds", int, 30),
            enable_distributed_tracing=self.get_typed("monitoring.enable_distributed_tracing", bool, True),
            metrics_retention_days=self.get_typed("monitoring.metrics_retention_days", int, 30),
            enable_performance_counters=self.get_typed("monitoring.enable_performance_counters", bool, True),
            alert_thresholds=self.get("monitoring.alert_thresholds", {})
        )
    
    def get_database_config(self) -> DatabaseConfiguration:
        """Get database configuration"""
        return DatabaseConfiguration(
            connection_string=self.get("database.connection_string", required=True),
            connection_pool_size=self.get_typed("database.connection_pool_size", int, 10),
            connection_timeout_seconds=self.get_typed("database.connection_timeout_seconds", int, 30),
            command_timeout_seconds=self.get_typed("database.command_timeout_seconds", int, 60),
            retry_attempts=self.get_typed("database.retry_attempts", int, 3),
            retry_delay_seconds=self.get_typed("database.retry_delay_seconds", int, 1),
            enable_connection_pooling=self.get_typed("database.enable_connection_pooling", bool, True)
        )
    
    def get_storage_config(self) -> StorageConfiguration:
        """Get storage configuration"""
        return StorageConfiguration(
            connection_string=self.get("storage.connection_string", required=True),
            container_name=self.get("storage.container_name", "default"),
            enable_encryption=self.get_typed("storage.enable_encryption", bool, True),
            retention_days=self.get_typed("storage.retention_days", int, 90),
            max_file_size_mb=self.get_typed("storage.max_file_size_mb", int, 100)
        )
    
    def get_service_config(self, service_name: str) -> ServiceConfiguration:
        """Get external service configuration"""
        return ServiceConfiguration(
            base_url=self.get(f"{service_name}.base_url", required=True),
            timeout_seconds=self.get_typed(f"{service_name}.timeout_seconds", int, 30),
            retry_attempts=self.get_typed(f"{service_name}.retry_attempts", int, 3),
            retry_delay_seconds=self.get_typed(f"{service_name}.retry_delay_seconds", int, 1),
            api_key=self.get(f"{service_name}.api_key"),
            user_agent=self.get(f"{service_name}.user_agent", "atomsec-func-sfdc/1.0.0")
        )
    
    def set_validation_rule(self, key: str, validation_func: callable):
        """
        Add custom validation rule for a configuration key
        
        Args:
            key: Configuration key
            validation_func: Function that takes value and returns bool
        """
        if key not in self._validation_rules:
            self._validation_rules[key] = []
        self._validation_rules[key].append(validation_func)
        logger.info(f"Added validation rule for configuration key '{key}'")
    
    def reload_configuration(self):
        """Reload configuration from all sources"""
        logger.info("Reloading configuration...")
        self._load_configuration()
    
    def get_configuration_info(self) -> Dict[str, Any]:
        """Get information about loaded configuration"""
        with self._lock:
            info = {
                "environment": self._environment.value,
                "last_reload": self._last_reload.isoformat(),
                "total_keys": len(self._config_cache),
                "sources": {},
                "secrets_count": 0
            }
            
            for key, config_value in self._config_cache.items():
                source = config_value.source
                if source not in info["sources"]:
                    info["sources"][source] = 0
                info["sources"][source] += 1
                
                if config_value.is_secret:
                    info["secrets_count"] += 1
            
            return info
    
    def validate_configuration_key(self, key: str) -> bool:
        """
        Validate a specific configuration key
        
        Args:
            key: Configuration key to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            if key not in self._config_cache:
                return False
            
            value = self._config_cache[key].value
            
            # Run custom validation rules
            if key in self._validation_rules:
                for rule in self._validation_rules[key]:
                    if not rule(value):
                        return False
            
            return True
        except Exception as e:
            logger.error(f"Error validating configuration key '{key}': {str(e)}")
            return False

# Global configuration manager instance
_config_manager: Optional[EnhancedConfigurationManager] = None

def get_configuration_manager() -> EnhancedConfigurationManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = EnhancedConfigurationManager()
    return _config_manager

def get_config(key: str, default: Any = None, required: bool = False) -> Any:
    """Convenience function to get configuration value"""
    return get_configuration_manager().get(key, default, required)

def get_typed_config(key: str, value_type: Type[T], default: Optional[T] = None, required: bool = False) -> T:
    """Convenience function to get typed configuration value"""
    return get_configuration_manager().get_typed(key, value_type, default, required)