import logging
import azure.functions as func
from urllib.parse import urlparse, parse_qs
import re

class AsgiMiddleware:
    """
    Middleware to handle ASGI requests from Azure Functions
    """
    def __init__(self, app):
        self.app = app
        self.logger = logging.getLogger('asgi_middleware')

    async def handle(self, req: func.HttpRequest) -> func.HttpResponse:
        """
        Handle an Azure Functions HTTP request by passing it to the ASGI app
        """
        self.logger.info('Processing request for %s', req.url)
        self.logger.info('Request method: %s', req.method)

        # Parse the URL
        parsed_url = urlparse(req.url)
        original_path = parsed_url.path

        # Ensure the path passed to FastAPI does not include the initial /api/
        # Azure Functions host adds /api/, but FastAPI app routes are defined relative to its own root.
        if original_path.startswith('/api/'):
            path_for_fastapi = original_path[4:] # Strip /api prefix
            if not path_for_fastapi.startswith('/'): # ensure it's an absolute path for FastAPI
                path_for_fastapi = '/' + path_for_fastapi
        else:
            # This case should ideally not be hit if requests always come through /api/
            path_for_fastapi = original_path

        # Log the path transformation
        self.logger.info(f"Original path from req.url: {original_path}")
        self.logger.info(f"Path passed to FastAPI: {path_for_fastapi}")

        # Log the routes
        self.logger.info('Original request route: %s', original_path)
        self.logger.info('Processed route for FastAPI: %s', path_for_fastapi)

        # Log headers
        headers = {k.lower(): v for k, v in req.headers.items()}
        self.logger.info('Request headers: %s', headers)

        # Log body if present
        if req.get_body():
            self.logger.info('Request body: %s', req.get_body().decode('utf-8'))
        else:
            self.logger.info('Request body: No body')

        # Log query parameters
        params = parse_qs(parsed_url.query)
        self.logger.info('Request params: %s', params)

        # Log route parameters if present
        route_params = getattr(req, 'route_params', {}) or {}
        self.logger.info('Request route_params: %s', route_params)

        # Extract path parameters from route_params if present
        # For example, if route is "api/accounts/{account_id}", extract account_id
        path_params = {}
        if route_params:
            # Use regex to extract path parameters from the route
            # This handles routes like "api/accounts/{account_id}/users"
            route_pattern = getattr(req, 'route', None)
            if route_pattern:
                # Convert {param} to (?P<param>[^/]+)
                regex_pattern = re.sub(r'{([^}]+)}', r'(?P<\1>[^/]+)', route_pattern)
                match = re.match(regex_pattern, original_path.lstrip('/'))
                if match:
                    path_params = match.groupdict()

        self.logger.info('Extracted path params: %s', path_params)

        # Log available routes in the FastAPI app
        routes = [{"path": route.path, "name": route.name, "methods": route.methods} for route in self.app.routes]
        self.logger.info('Available FastAPI routes: %s', routes)

        try:
            # Create a mock ASGI scope
            scope = {
                'type': 'http',
                'http_version': '1.1',
                'method': req.method,
                'path': path_for_fastapi, # Use the transformed path
                'raw_path': path_for_fastapi.encode('utf-8'), # Use the transformed path
                'query_string': parsed_url.query.encode('utf-8'),
                'headers': [(k.lower().encode('utf-8'), v.encode('utf-8')) for k, v in req.headers.items()],
                'client': ('127.0.0.1', 0),
                'server': (parsed_url.netloc, 443 if parsed_url.scheme == 'https' else 80),
                'scheme': parsed_url.scheme or 'http',
                'root_path': '',
                'path_params': {}, # Always pass empty path_params; FastAPI will populate this
            }

            # Create a response object to capture the ASGI response
            response = {
                'status_code': 200,
                'headers': [],
                'body': b'',
            }

            # Define the receive function (always returns None for now)
            async def receive():
                if req.get_body():
                    return {
                        'type': 'http.request',
                        'body': req.get_body(),
                        'more_body': False,
                    }
                return {
                    'type': 'http.request',
                    'body': b'',
                    'more_body': False,
                }

            # Define the send function to capture the response
            async def send(message):
                if message['type'] == 'http.response.start':
                    response['status_code'] = message['status']
                    response['headers'] = message.get('headers', [])
                elif message['type'] == 'http.response.body':
                    response['body'] += message.get('body', b'')

            # Call the ASGI app
            async def call_asgi():
                await self.app(scope, receive, send)

            await call_asgi()

            # Convert headers to a dictionary
            headers_dict = {}
            for key, value in response['headers']:
                key = key.decode('utf-8')
                value = value.decode('utf-8')
                headers_dict[key] = value

            # Log the response
            self.logger.info('Response status code: %s', response['status_code'])
            self.logger.info('Response headers: %s', headers_dict)

            # Create and return the Azure Functions HTTP response
            func_response = func.HttpResponse(
                body=response['body'],
                status_code=response['status_code'],
                headers=headers_dict,
            )

            return func_response
        except Exception as e:
            self.logger.error('Error processing ASGI request: %s', str(e))
            import traceback
            self.logger.error(traceback.format_exc())

            # Return a 500 error response
            return func.HttpResponse(
                body=f'{{"error": "Internal Server Error: {str(e)}"}}',
                status_code=500,
                mimetype='application/json',
            )
