"""
Security Monitoring and Alerting Service

This module provides comprehensive security monitoring including:
- Real-time security event detection
- Intrusion detection and prevention
- Security incident response automation
- Threat intelligence integration
- Security metrics and reporting

Requirements addressed: 1.7
"""

import logging
import json
import time
import hashlib
from typing import Dict, Any, Optional, List, Set, Tuple, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import re
import ipaddress
from statistics import mean, stdev

from src.shared.advanced_auth_service import get_advanced_auth_service
from src.shared.user_activity_monitor import get_user_activity_monitor, ActivityType
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


class ThreatLevel(Enum):
    """Security threat levels"""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class IncidentStatus(Enum):
    """Security incident status"""
    DETECTED = "detected"
    INVESTIGATING = "investigating"
    CONTAINED = "contained"
    RESOLVED = "resolved"
    FALSE_POSITIVE = "false_positive"


class AlertType(Enum):
    """Types of security alerts"""
    AUTHENTICATION_FAILURE = "authentication_failure"
    BRUTE_FORCE_ATTACK = "brute_force_attack"
    SUSPICIOUS_LOGIN = "suspicious_login"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_EXFILTRATION = "data_exfiltration"
    MALICIOUS_REQUEST = "malicious_request"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    UNUSUAL_ACTIVITY = "unusual_activity"
    SECURITY_POLICY_VIOLATION = "security_policy_violation"
    INTRUSION_ATTEMPT = "intrusion_attempt"


@dataclass
class SecurityEvent:
    """Security event data structure"""
    event_id: str
    event_type: AlertType
    timestamp: datetime
    source_ip: str
    user_id: Optional[str]
    session_id: Optional[str]
    endpoint: str
    user_agent: str
    threat_level: ThreatLevel
    description: str
    raw_data: Dict[str, Any]
    indicators: List[str]
    mitre_tactics: List[str] = None
    mitre_techniques: List[str] = None
    
    def __post_init__(self):
        if self.mitre_tactics is None:
            self.mitre_tactics = []
        if self.mitre_techniques is None:
            self.mitre_techniques = []


@dataclass
class SecurityIncident:
    """Security incident data structure"""
    incident_id: str
    title: str
    description: str
    threat_level: ThreatLevel
    status: IncidentStatus
    created_at: datetime
    updated_at: datetime
    assigned_to: Optional[str]
    events: List[str]  # Event IDs
    indicators_of_compromise: List[str]
    affected_systems: List[str]
    response_actions: List[str]
    resolution_notes: str = ""
    
    def __post_init__(self):
        if not self.events:
            self.events = []
        if not self.indicators_of_compromise:
            self.indicators_of_compromise = []
        if not self.affected_systems:
            self.affected_systems = []
        if not self.response_actions:
            self.response_actions = []


@dataclass
class ThreatIntelligence:
    """Threat intelligence data"""
    indicator: str
    indicator_type: str  # ip, domain, hash, etc.
    threat_type: str
    confidence: float
    source: str
    first_seen: datetime
    last_seen: datetime
    description: str
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class SecurityMonitoringService:
    """
    Comprehensive security monitoring and alerting service
    """
    
    def __init__(self):
        self.auth_service = get_advanced_auth_service()
        self.activity_monitor = get_user_activity_monitor()
        self.monitoring = get_monitoring_service()
        
        # Security data storage
        self._security_events: deque = deque(maxlen=100000)  # Keep last 100k events
        self._security_incidents: Dict[str, SecurityIncident] = {}
        self._threat_intelligence: Dict[str, ThreatIntelligence] = {}
        self._blocked_ips: Set[str] = set()
        self._suspicious_ips: Dict[str, Dict[str, Any]] = {}
        self._alert_rules: List[Dict[str, Any]] = []
        
        # Real-time monitoring
        self._active_attacks: Dict[str, Dict[str, Any]] = {}
        self._rate_limiters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._failed_attempts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Configuration
        self.config = {
            'max_failed_attempts_per_hour': 10,
            'brute_force_threshold': 20,
            'rate_limit_threshold': 100,  # requests per minute
            'suspicious_activity_threshold': 5.0,
            'auto_block_enabled': True,
            'auto_block_duration_minutes': 60,
            'incident_auto_creation': True,
            'threat_intel_enabled': True,
            'real_time_monitoring': True,
        }
        
        # Initialize components
        self._initialize_alert_rules()
        self._initialize_threat_intelligence()
        
        # Start monitoring thread
        if self.config['real_time_monitoring']:
            self._start_monitoring_thread()
    
    def process_security_event(self, event_data: Dict[str, Any]) -> Optional[str]:
        """
        Process incoming security event
        
        Args:
            event_data: Security event data
            
        Returns:
            Event ID if processed successfully
        """
        try:
            with self._lock:
                # Extract event information
                event_id = f"sec_{int(time.time() * 1000000)}"
                
                # Determine event type and threat level
                event_type, threat_level = self._classify_security_event(event_data)
                
                # Create security event
                security_event = SecurityEvent(
                    event_id=event_id,
                    event_type=event_type,
                    timestamp=datetime.utcnow(),
                    source_ip=event_data.get('source_ip', 'unknown'),
                    user_id=event_data.get('user_id'),
                    session_id=event_data.get('session_id'),
                    endpoint=event_data.get('endpoint', 'unknown'),
                    user_agent=event_data.get('user_agent', 'unknown'),
                    threat_level=threat_level,
                    description=event_data.get('description', ''),
                    raw_data=event_data,
                    indicators=self._extract_indicators(event_data)
                )
                
                # Add MITRE ATT&CK mapping
                security_event.mitre_tactics, security_event.mitre_techniques = self._map_to_mitre(event_type)
                
                # Store event
                self._security_events.append(security_event)
                
                # Check against threat intelligence
                self._check_threat_intelligence(security_event)
                
                # Apply security rules
                self._apply_security_rules(security_event)
                
                # Update real-time tracking
                self._update_real_time_tracking(security_event)
                
                # Auto-create incident if needed
                if (self.config['incident_auto_creation'] and 
                    threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]):
                    self._auto_create_incident(security_event)
                
                # Send to monitoring system
                self.monitoring.track_custom_metric(
                    'security_event_processed',
                    1.0,
                    {
                        'event_type': event_type.value,
                        'threat_level': threat_level.value,
                        'source_ip': security_event.source_ip,
                        'user_id': security_event.user_id or 'anonymous'
                    }
                )
                
                logger.info(f"Security event processed: {event_type.value} (threat: {threat_level.value})")
                return event_id
                
        except Exception as e:
            logger.error(f"Failed to process security event: {str(e)}")
            return None
    
    def detect_brute_force_attack(self, source_ip: str, user_id: str = None) -> bool:
        """
        Detect brute force attack patterns
        
        Args:
            source_ip: Source IP address
            user_id: User identifier (optional)
            
        Returns:
            True if brute force attack detected
        """
        try:
            with self._lock:
                current_time = datetime.utcnow()
                hour_ago = current_time - timedelta(hours=1)
                
                # Count failed attempts from IP
                ip_failures = [
                    event for event in self._security_events
                    if (event.source_ip == source_ip and
                        event.event_type == AlertType.AUTHENTICATION_FAILURE and
                        event.timestamp >= hour_ago)
                ]
                
                # Count failed attempts for user
                user_failures = []
                if user_id:
                    user_failures = [
                        event for event in self._security_events
                        if (event.user_id == user_id and
                            event.event_type == AlertType.AUTHENTICATION_FAILURE and
                            event.timestamp >= hour_ago)
                    ]
                
                # Check thresholds
                ip_threshold_exceeded = len(ip_failures) >= self.config['brute_force_threshold']
                user_threshold_exceeded = len(user_failures) >= self.config['brute_force_threshold']
                
                if ip_threshold_exceeded or user_threshold_exceeded:
                    # Create brute force event
                    self.process_security_event({
                        'event_type': 'brute_force_attack',
                        'source_ip': source_ip,
                        'user_id': user_id,
                        'description': f'Brute force attack detected: {len(ip_failures)} IP failures, {len(user_failures)} user failures',
                        'ip_failure_count': len(ip_failures),
                        'user_failure_count': len(user_failures)
                    })
                    
                    # Auto-block if enabled
                    if self.config['auto_block_enabled']:
                        self._block_ip(source_ip, 'brute_force_attack')
                    
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Brute force detection failed: {str(e)}")
            return False
    
    def detect_suspicious_login(self, user_id: str, source_ip: str, 
                              user_agent: str, location_data: Dict[str, Any] = None) -> float:
        """
        Detect suspicious login patterns
        
        Args:
            user_id: User identifier
            source_ip: Source IP address
            user_agent: User agent string
            location_data: Location information
            
        Returns:
            Suspicion score (0.0 - 10.0)
        """
        try:
            suspicion_score = 0.0
            indicators = []
            
            # Get user's historical login data
            user_events = [
                event for event in self._security_events
                if event.user_id == user_id and event.timestamp >= datetime.utcnow() - timedelta(days=30)
            ]
            
            if not user_events:
                return 0.0  # No historical data
            
            # Check for new IP address
            historical_ips = set(event.source_ip for event in user_events)
            if source_ip not in historical_ips:
                suspicion_score += 2.0
                indicators.append('new_ip_address')
            
            # Check for new user agent
            historical_agents = set(event.user_agent for event in user_events)
            if user_agent not in historical_agents:
                suspicion_score += 1.0
                indicators.append('new_user_agent')
            
            # Check for unusual time
            login_hours = [event.timestamp.hour for event in user_events]
            current_hour = datetime.utcnow().hour
            if login_hours and current_hour not in login_hours:
                suspicion_score += 1.5
                indicators.append('unusual_time')
            
            # Check for impossible travel (if location data available)
            if location_data and user_events:
                last_location_event = max(user_events, key=lambda e: e.timestamp)
                if self._is_impossible_travel(last_location_event, location_data):
                    suspicion_score += 5.0
                    indicators.append('impossible_travel')
            
            # Check against threat intelligence
            if self._is_malicious_ip(source_ip):
                suspicion_score += 8.0
                indicators.append('malicious_ip')
            
            # Check for suspicious user agent patterns
            if self._is_suspicious_user_agent(user_agent):
                suspicion_score += 3.0
                indicators.append('suspicious_user_agent')
            
            # Create suspicious login event if score is high
            if suspicion_score >= self.config['suspicious_activity_threshold']:
                self.process_security_event({
                    'event_type': 'suspicious_login',
                    'source_ip': source_ip,
                    'user_id': user_id,
                    'user_agent': user_agent,
                    'description': f'Suspicious login detected (score: {suspicion_score:.1f})',
                    'suspicion_score': suspicion_score,
                    'indicators': indicators,
                    'location_data': location_data
                })
            
            return suspicion_score
            
        except Exception as e:
            logger.error(f"Suspicious login detection failed: {str(e)}")
            return 0.0
    
    def detect_data_exfiltration(self, user_id: str, endpoint: str,
                               response_size: int, request_data: Dict[str, Any]) -> bool:
        """
        Detect potential data exfiltration attempts
        
        Args:
            user_id: User identifier
            endpoint: Endpoint accessed
            response_size: Size of response in bytes
            request_data: Request data
            
        Returns:
            True if potential exfiltration detected
        """
        try:
            indicators = []
            risk_score = 0.0
            
            # Check for large data downloads
            if response_size > 10 * 1024 * 1024:  # 10MB threshold
                risk_score += 3.0
                indicators.append('large_download')
            
            # Check for bulk data requests
            if 'limit' in request_data and isinstance(request_data['limit'], int):
                if request_data['limit'] > 1000:
                    risk_score += 2.0
                    indicators.append('bulk_request')
            
            # Check for sensitive endpoints
            sensitive_patterns = ['export', 'download', 'backup', 'dump', 'all']
            if any(pattern in endpoint.lower() for pattern in sensitive_patterns):
                risk_score += 2.0
                indicators.append('sensitive_endpoint')
            
            # Check user's normal behavior
            recent_activities = self.activity_monitor.get_user_activity_summary(user_id, hours=24)
            if recent_activities.get('total_activities', 0) > 100:  # Unusually high activity
                risk_score += 1.5
                indicators.append('high_activity_volume')
            
            # Check for off-hours access
            current_hour = datetime.utcnow().hour
            if current_hour < 6 or current_hour > 22:
                risk_score += 1.0
                indicators.append('off_hours_access')
            
            # Create exfiltration event if risk is high
            if risk_score >= 4.0:
                self.process_security_event({
                    'event_type': 'data_exfiltration',
                    'source_ip': request_data.get('source_ip', 'unknown'),
                    'user_id': user_id,
                    'endpoint': endpoint,
                    'description': f'Potential data exfiltration detected (risk: {risk_score:.1f})',
                    'risk_score': risk_score,
                    'indicators': indicators,
                    'response_size_bytes': response_size,
                    'request_data': request_data
                })
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Data exfiltration detection failed: {str(e)}")
            return False
    
    def create_security_incident(self, title: str, description: str,
                               threat_level: ThreatLevel,
                               event_ids: List[str] = None) -> str:
        """
        Create security incident
        
        Args:
            title: Incident title
            description: Incident description
            threat_level: Threat level
            event_ids: Related event IDs
            
        Returns:
            Incident ID
        """
        try:
            with self._lock:
                incident_id = f"inc_{int(time.time() * 1000)}"
                
                incident = SecurityIncident(
                    incident_id=incident_id,
                    title=title,
                    description=description,
                    threat_level=threat_level,
                    status=IncidentStatus.DETECTED,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    assigned_to=None,
                    events=event_ids or [],
                    indicators_of_compromise=[],
                    affected_systems=[],
                    response_actions=[]
                )
                
                self._security_incidents[incident_id] = incident
                
                # Auto-assign response actions based on threat level
                self._assign_response_actions(incident)
                
                # Send alert
                self._send_security_alert(incident)
                
                # Track in monitoring
                self.monitoring.track_custom_metric(
                    'security_incident_created',
                    1.0,
                    {
                        'incident_id': incident_id,
                        'threat_level': threat_level.value,
                        'event_count': len(event_ids) if event_ids else 0
                    }
                )
                
                logger.warning(f"Security incident created: {incident_id} - {title}")
                return incident_id
                
        except Exception as e:
            logger.error(f"Failed to create security incident: {str(e)}")
            return ""
    
    def update_incident_status(self, incident_id: str, status: IncidentStatus,
                             notes: str = "", assigned_to: str = None) -> bool:
        """
        Update security incident status
        
        Args:
            incident_id: Incident identifier
            status: New status
            notes: Update notes
            assigned_to: Assigned user
            
        Returns:
            True if updated successfully
        """
        try:
            with self._lock:
                incident = self._security_incidents.get(incident_id)
                if not incident:
                    return False
                
                incident.status = status
                incident.updated_at = datetime.utcnow()
                
                if notes:
                    incident.resolution_notes += f"\n{datetime.utcnow().isoformat()}: {notes}"
                
                if assigned_to:
                    incident.assigned_to = assigned_to
                
                # Track status change
                self.monitoring.track_custom_metric(
                    'security_incident_updated',
                    1.0,
                    {
                        'incident_id': incident_id,
                        'new_status': status.value,
                        'assigned_to': assigned_to or 'unassigned'
                    }
                )
                
                logger.info(f"Security incident updated: {incident_id} -> {status.value}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update incident status: {str(e)}")
            return False
    
    def get_security_dashboard(self) -> Dict[str, Any]:
        """
        Get security monitoring dashboard data
        
        Returns:
            Dict containing dashboard metrics
        """
        try:
            with self._lock:
                current_time = datetime.utcnow()
                last_24h = current_time - timedelta(hours=24)
                last_7d = current_time - timedelta(days=7)
                
                # Recent events
                recent_events = [e for e in self._security_events if e.timestamp >= last_24h]
                
                # Event breakdown by type
                event_breakdown = defaultdict(int)
                threat_breakdown = defaultdict(int)
                
                for event in recent_events:
                    event_breakdown[event.event_type.value] += 1
                    threat_breakdown[event.threat_level.value] += 1
                
                # Active incidents
                active_incidents = [
                    i for i in self._security_incidents.values()
                    if i.status not in [IncidentStatus.RESOLVED, IncidentStatus.FALSE_POSITIVE]
                ]
                
                # Top source IPs
                ip_counts = defaultdict(int)
                for event in recent_events:
                    ip_counts[event.source_ip] += 1
                
                top_ips = sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                
                # Security metrics
                dashboard = {
                    'timestamp': current_time.isoformat(),
                    'summary': {
                        'total_events_24h': len(recent_events),
                        'critical_events_24h': len([e for e in recent_events if e.threat_level == ThreatLevel.CRITICAL]),
                        'high_events_24h': len([e for e in recent_events if e.threat_level == ThreatLevel.HIGH]),
                        'active_incidents': len(active_incidents),
                        'blocked_ips': len(self._blocked_ips),
                        'suspicious_ips': len(self._suspicious_ips)
                    },
                    'event_breakdown': dict(event_breakdown),
                    'threat_breakdown': dict(threat_breakdown),
                    'top_source_ips': [{'ip': ip, 'count': count} for ip, count in top_ips],
                    'active_incidents': [
                        {
                            'incident_id': i.incident_id,
                            'title': i.title,
                            'threat_level': i.threat_level.value,
                            'status': i.status.value,
                            'created_at': i.created_at.isoformat(),
                            'event_count': len(i.events)
                        }
                        for i in active_incidents
                    ],
                    'recent_events': [
                        {
                            'event_id': e.event_id,
                            'event_type': e.event_type.value,
                            'threat_level': e.threat_level.value,
                            'timestamp': e.timestamp.isoformat(),
                            'source_ip': e.source_ip,
                            'description': e.description
                        }
                        for e in sorted(recent_events, key=lambda x: x.timestamp, reverse=True)[:20]
                    ]
                }
                
                return dashboard
                
        except Exception as e:
            logger.error(f"Failed to generate security dashboard: {str(e)}")
            return {'error': str(e)}
    
    def block_ip(self, ip_address: str, reason: str, duration_minutes: int = None) -> bool:
        """
        Block IP address
        
        Args:
            ip_address: IP address to block
            reason: Reason for blocking
            duration_minutes: Block duration (None for permanent)
            
        Returns:
            True if blocked successfully
        """
        try:
            with self._lock:
                self._blocked_ips.add(ip_address)
                
                # Schedule unblock if duration specified
                if duration_minutes:
                    # In production, would use a proper scheduler
                    pass
                
                # Log blocking action
                self.process_security_event({
                    'event_type': 'security_policy_violation',
                    'source_ip': ip_address,
                    'description': f'IP address blocked: {reason}',
                    'action': 'ip_blocked',
                    'reason': reason,
                    'duration_minutes': duration_minutes
                })
                
                logger.warning(f"IP address blocked: {ip_address} (reason: {reason})")
                return True
                
        except Exception as e:
            logger.error(f"Failed to block IP: {str(e)}")
            return False
    
    def unblock_ip(self, ip_address: str, reason: str = "manual_unblock") -> bool:
        """
        Unblock IP address
        
        Args:
            ip_address: IP address to unblock
            reason: Reason for unblocking
            
        Returns:
            True if unblocked successfully
        """
        try:
            with self._lock:
                if ip_address in self._blocked_ips:
                    self._blocked_ips.remove(ip_address)
                    
                    # Log unblocking action
                    self.process_security_event({
                        'event_type': 'security_policy_violation',
                        'source_ip': ip_address,
                        'description': f'IP address unblocked: {reason}',
                        'action': 'ip_unblocked',
                        'reason': reason
                    })
                    
                    logger.info(f"IP address unblocked: {ip_address} (reason: {reason})")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Failed to unblock IP: {str(e)}")
            return False
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP address is blocked"""
        return ip_address in self._blocked_ips
    
    def add_threat_intelligence(self, indicator: str, indicator_type: str,
                              threat_type: str, confidence: float,
                              source: str, description: str) -> bool:
        """
        Add threat intelligence indicator
        
        Args:
            indicator: Threat indicator (IP, domain, hash, etc.)
            indicator_type: Type of indicator
            threat_type: Type of threat
            confidence: Confidence level (0.0-1.0)
            source: Intelligence source
            description: Description
            
        Returns:
            True if added successfully
        """
        try:
            with self._lock:
                threat_intel = ThreatIntelligence(
                    indicator=indicator,
                    indicator_type=indicator_type,
                    threat_type=threat_type,
                    confidence=confidence,
                    source=source,
                    first_seen=datetime.utcnow(),
                    last_seen=datetime.utcnow(),
                    description=description
                )
                
                self._threat_intelligence[indicator] = threat_intel
                
                logger.info(f"Threat intelligence added: {indicator} ({threat_type})")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add threat intelligence: {str(e)}")
            return False
    
    # Private helper methods
    
    def _classify_security_event(self, event_data: Dict[str, Any]) -> Tuple[AlertType, ThreatLevel]:
        """Classify security event type and threat level"""
        event_type_str = event_data.get('event_type', '').lower()
        
        # Map event types
        type_mapping = {
            'authentication_failure': AlertType.AUTHENTICATION_FAILURE,
            'brute_force_attack': AlertType.BRUTE_FORCE_ATTACK,
            'suspicious_login': AlertType.SUSPICIOUS_LOGIN,
            'privilege_escalation': AlertType.PRIVILEGE_ESCALATION,
            'data_exfiltration': AlertType.DATA_EXFILTRATION,
            'malicious_request': AlertType.MALICIOUS_REQUEST,
            'rate_limit_exceeded': AlertType.RATE_LIMIT_EXCEEDED,
            'unusual_activity': AlertType.UNUSUAL_ACTIVITY,
            'security_policy_violation': AlertType.SECURITY_POLICY_VIOLATION,
            'intrusion_attempt': AlertType.INTRUSION_ATTEMPT,
        }
        
        event_type = type_mapping.get(event_type_str, AlertType.UNUSUAL_ACTIVITY)
        
        # Determine threat level
        threat_level = ThreatLevel.MEDIUM  # Default
        
        if event_type in [AlertType.BRUTE_FORCE_ATTACK, AlertType.DATA_EXFILTRATION, AlertType.INTRUSION_ATTEMPT]:
            threat_level = ThreatLevel.HIGH
        elif event_type in [AlertType.PRIVILEGE_ESCALATION]:
            threat_level = ThreatLevel.CRITICAL
        elif event_type in [AlertType.AUTHENTICATION_FAILURE, AlertType.RATE_LIMIT_EXCEEDED]:
            threat_level = ThreatLevel.LOW
        
        # Adjust based on indicators
        if event_data.get('suspicion_score', 0) > 7.0:
            threat_level = ThreatLevel.CRITICAL
        elif event_data.get('risk_score', 0) > 5.0:
            threat_level = ThreatLevel.HIGH
        
        return event_type, threat_level
    
    def _extract_indicators(self, event_data: Dict[str, Any]) -> List[str]:
        """Extract indicators of compromise from event data"""
        indicators = []
        
        # Extract IP addresses
        if 'source_ip' in event_data:
            indicators.append(f"ip:{event_data['source_ip']}")
        
        # Extract user agents
        if 'user_agent' in event_data:
            indicators.append(f"user_agent:{event_data['user_agent']}")
        
        # Extract endpoints
        if 'endpoint' in event_data:
            indicators.append(f"endpoint:{event_data['endpoint']}")
        
        # Extract custom indicators
        if 'indicators' in event_data:
            indicators.extend(event_data['indicators'])
        
        return indicators
    
    def _map_to_mitre(self, event_type: AlertType) -> Tuple[List[str], List[str]]:
        """Map event type to MITRE ATT&CK tactics and techniques"""
        mapping = {
            AlertType.AUTHENTICATION_FAILURE: (['Initial Access'], ['T1078']),
            AlertType.BRUTE_FORCE_ATTACK: (['Credential Access'], ['T1110']),
            AlertType.SUSPICIOUS_LOGIN: (['Initial Access'], ['T1078']),
            AlertType.PRIVILEGE_ESCALATION: (['Privilege Escalation'], ['T1068']),
            AlertType.DATA_EXFILTRATION: (['Exfiltration'], ['T1041']),
            AlertType.MALICIOUS_REQUEST: (['Initial Access'], ['T1190']),
            AlertType.INTRUSION_ATTEMPT: (['Initial Access'], ['T1190']),
        }
        
        return mapping.get(event_type, ([], []))
    
    def _check_threat_intelligence(self, event: SecurityEvent):
        """Check event against threat intelligence"""
        # Check source IP
        if event.source_ip in self._threat_intelligence:
            threat_intel = self._threat_intelligence[event.source_ip]
            if threat_intel.confidence > 0.7:
                event.threat_level = ThreatLevel.HIGH
                event.indicators.append(f"threat_intel:{threat_intel.threat_type}")
    
    def _apply_security_rules(self, event: SecurityEvent):
        """Apply security rules to event"""
        for rule in self._alert_rules:
            if self._rule_matches(rule, event):
                self._execute_rule_action(rule, event)
    
    def _rule_matches(self, rule: Dict[str, Any], event: SecurityEvent) -> bool:
        """Check if security rule matches event"""
        # Simple rule matching - would be more sophisticated in production
        if 'event_type' in rule and rule['event_type'] != event.event_type.value:
            return False
        
        if 'threat_level' in rule and rule['threat_level'] != event.threat_level.value:
            return False
        
        if 'source_ip_pattern' in rule:
            pattern = rule['source_ip_pattern']
            if not re.match(pattern, event.source_ip):
                return False
        
        return True
    
    def _execute_rule_action(self, rule: Dict[str, Any], event: SecurityEvent):
        """Execute security rule action"""
        action = rule.get('action')
        
        if action == 'block_ip':
            self._block_ip(event.source_ip, f"Security rule: {rule.get('name', 'unknown')}")
        elif action == 'create_incident':
            self._auto_create_incident(event)
        elif action == 'send_alert':
            self._send_event_alert(event)
    
    def _update_real_time_tracking(self, event: SecurityEvent):
        """Update real-time tracking data"""
        # Track by IP
        if event.source_ip not in self._suspicious_ips:
            self._suspicious_ips[event.source_ip] = {
                'first_seen': event.timestamp,
                'event_count': 0,
                'threat_score': 0.0
            }
        
        ip_data = self._suspicious_ips[event.source_ip]
        ip_data['event_count'] += 1
        ip_data['last_seen'] = event.timestamp
        
        # Update threat score
        threat_scores = {
            ThreatLevel.INFO: 0.1,
            ThreatLevel.LOW: 0.5,
            ThreatLevel.MEDIUM: 1.0,
            ThreatLevel.HIGH: 2.0,
            ThreatLevel.CRITICAL: 5.0
        }
        ip_data['threat_score'] += threat_scores.get(event.threat_level, 1.0)
    
    def _auto_create_incident(self, event: SecurityEvent):
        """Auto-create incident for high-priority events"""
        incident_title = f"{event.event_type.value.replace('_', ' ').title()} - {event.source_ip}"
        incident_description = f"Automatic incident created for {event.event_type.value} event from {event.source_ip}"
        
        self.create_security_incident(
            title=incident_title,
            description=incident_description,
            threat_level=event.threat_level,
            event_ids=[event.event_id]
        )
    
    def _assign_response_actions(self, incident: SecurityIncident):
        """Assign response actions based on incident type and threat level"""
        if incident.threat_level == ThreatLevel.CRITICAL:
            incident.response_actions.extend([
                'Immediate investigation required',
                'Consider system isolation',
                'Notify security team',
                'Document all actions'
            ])
        elif incident.threat_level == ThreatLevel.HIGH:
            incident.response_actions.extend([
                'Investigate within 1 hour',
                'Monitor affected systems',
                'Notify security team'
            ])
        else:
            incident.response_actions.extend([
                'Investigate within 24 hours',
                'Monitor for escalation'
            ])
    
    def _send_security_alert(self, incident: SecurityIncident):
        """Send security alert for incident"""
        # In production, would integrate with alerting systems (email, Slack, etc.)
        logger.warning(f"SECURITY ALERT: {incident.title} (Level: {incident.threat_level.value})")
    
    def _send_event_alert(self, event: SecurityEvent):
        """Send alert for security event"""
        logger.warning(f"SECURITY EVENT: {event.event_type.value} from {event.source_ip}")
    
    def _block_ip(self, ip_address: str, reason: str):
        """Block IP address"""
        self.block_ip(ip_address, reason, self.config['auto_block_duration_minutes'])
    
    def _is_malicious_ip(self, ip_address: str) -> bool:
        """Check if IP is known malicious"""
        threat_intel = self._threat_intelligence.get(ip_address)
        return threat_intel is not None and threat_intel.confidence > 0.7
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """Check if user agent is suspicious"""
        suspicious_patterns = [
            r'bot', r'crawler', r'scanner', r'automated', r'script',
            r'curl', r'wget', r'python', r'java', r'go-http'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(re.search(pattern, user_agent_lower) for pattern in suspicious_patterns)
    
    def _is_impossible_travel(self, last_event: SecurityEvent, 
                            current_location: Dict[str, Any]) -> bool:
        """Check for impossible travel between locations"""
        # Simplified implementation - would need proper geolocation in production
        if not hasattr(last_event, 'location_data') or not current_location:
            return False
        
        # Check time difference
        time_diff = (datetime.utcnow() - last_event.timestamp).total_seconds() / 3600  # hours
        
        # If less than 1 hour and different countries, likely impossible
        if time_diff < 1 and current_location.get('country') != last_event.raw_data.get('location_data', {}).get('country'):
            return True
        
        return False
    
    def _initialize_alert_rules(self):
        """Initialize default alert rules"""
        self._alert_rules = [
            {
                'name': 'High threat level auto-block',
                'event_type': 'brute_force_attack',
                'action': 'block_ip',
                'enabled': True
            },
            {
                'name': 'Critical event incident creation',
                'threat_level': 'critical',
                'action': 'create_incident',
                'enabled': True
            },
            {
                'name': 'Suspicious IP pattern',
                'source_ip_pattern': r'^10\.0\.0\.',  # Example internal IP
                'action': 'send_alert',
                'enabled': False
            }
        ]
    
    def _initialize_threat_intelligence(self):
        """Initialize threat intelligence data"""
        # Add some example threat intelligence
        known_bad_ips = [
            '*********',  # Example IP
            '************',  # Example IP
        ]
        
        for ip in known_bad_ips:
            self.add_threat_intelligence(
                indicator=ip,
                indicator_type='ip',
                threat_type='malicious_ip',
                confidence=0.9,
                source='internal_blacklist',
                description='Known malicious IP address'
            )
    
    def _start_monitoring_thread(self):
        """Start background monitoring thread"""
        def monitoring_loop():
            while True:
                try:
                    # Perform periodic security checks
                    self._periodic_security_check()
                    time.sleep(60)  # Check every minute
                except Exception as e:
                    logger.error(f"Monitoring thread error: {str(e)}")
                    time.sleep(60)
        
        import threading
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def _periodic_security_check(self):
        """Perform periodic security checks"""
        try:
            # Check for patterns in recent events
            recent_events = [
                e for e in self._security_events
                if e.timestamp >= datetime.utcnow() - timedelta(minutes=5)
            ]
            
            # Look for coordinated attacks
            if len(recent_events) > 50:  # High volume of events
                unique_ips = set(e.source_ip for e in recent_events)
                if len(unique_ips) < 5:  # From few IPs
                    self.process_security_event({
                        'event_type': 'intrusion_attempt',
                        'description': f'Coordinated attack detected: {len(recent_events)} events from {len(unique_ips)} IPs',
                        'event_count': len(recent_events),
                        'unique_ips': len(unique_ips)
                    })
            
            # Clean up old data
            self._cleanup_old_data()
            
        except Exception as e:
            logger.error(f"Periodic security check failed: {str(e)}")
    
    def _cleanup_old_data(self):
        """Clean up old security data"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=30)
            
            # Clean suspicious IPs
            expired_ips = [
                ip for ip, data in self._suspicious_ips.items()
                if data.get('last_seen', datetime.min) < cutoff_time
            ]
            
            for ip in expired_ips:
                del self._suspicious_ips[ip]
            
        except Exception as e:
            logger.error(f"Data cleanup failed: {str(e)}")


# Global service instance
_security_monitoring_service = None


def get_security_monitoring_service() -> SecurityMonitoringService:
    """Get the global security monitoring service instance"""
    global _security_monitoring_service
    if _security_monitoring_service is None:
        _security_monitoring_service = SecurityMonitoringService()
    return _security_monitoring_service