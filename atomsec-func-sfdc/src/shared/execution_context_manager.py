"""
Execution Context Manager Module

This module provides centralized management of execution contexts for tracking
parent-child task relationships and determining overall execution status.

Features:
- Create and manage execution contexts
- Track parent-child task relationships
- Determine overall execution status
- Generate execution summaries
- Support for task lifecycle management

Best practices implemented:
- Single responsibility principle
- Comprehensive error handling
- Structured logging with correlation IDs
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional, List

# Import shared modules
from src.shared.azure_services import is_local_dev
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.task_status_service import get_task_status_service

# Configure module-level logger
logger = logging.getLogger(__name__)

# Global repository instances
_execution_context_table_repo = None
_execution_context_sql_repo = None


class ExecutionContextManager:
    """
    Manager class for handling execution contexts and task relationships
    
    This class provides functionality to:
    - Create and manage execution contexts
    - Track parent-child task relationships
    - Determine overall execution status
    - Generate execution summaries
    """
    
    def __init__(self):
        """Initialize the execution context manager"""
        self.table_repo = None
        self.sql_repo = None
        self.task_status_service = get_task_status_service()
        self._initialize_repositories()
        
        logger.info(f"Execution context manager initialized for {'local' if is_local_dev() else 'production'} environment")
    
    def _initialize_repositories(self):
        """Initialize the appropriate repository based on environment"""
        try:
            if is_local_dev():
                # Use Table Storage for local development
                self.table_repo = self._get_table_repository()
                logger.info("Initialized execution context manager with Table Storage")
            else:
                # Use SQL Database for production
                self.sql_repo = self._get_sql_repository()
                logger.info("Initialized execution context manager with SQL Database")
        except Exception as e:
            logger.error(f"Error initializing execution context repositories: {str(e)}")
    
    def _get_table_repository(self) -> Optional[TableStorageRepository]:
        """Get Table Storage repository for execution contexts"""
        global _execution_context_table_repo
        if _execution_context_table_repo is None:
            try:
                _execution_context_table_repo = TableStorageRepository(table_name="ExecutionContext")
                logger.debug("Created Table Storage repository for execution contexts")
            except Exception as e:
                logger.error(f"Failed to create Table Storage repository: {str(e)}")
                _execution_context_table_repo = None
        return _execution_context_table_repo
    
    def _get_sql_repository(self) -> Optional[SqlDatabaseRepository]:
        """Get SQL Database repository for execution contexts"""
        global _execution_context_sql_repo
        if _execution_context_sql_repo is None and not is_local_dev():
            try:
                _execution_context_sql_repo = SqlDatabaseRepository(table_name="ExecutionContext")
                logger.debug("Created SQL Database repository for execution contexts")
            except Exception as e:
                logger.error(f"Failed to create SQL Database repository: {str(e)}")
                _execution_context_sql_repo = None
        return _execution_context_sql_repo
    
    def create_execution_context(
        self,
        org_id: str,
        user_id: str,
        task_type: str,
        parent_execution_log_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Create a new execution context
        
        Args:
            org_id: Organization ID
            user_id: User ID who initiated the task
            task_type: Type of task being executed
            parent_execution_log_id: Optional parent execution log ID
            metadata: Optional metadata for the execution
            
        Returns:
            str: Execution log ID if successful, None otherwise
        """
        try:
            execution_log_id = str(uuid.uuid4())
            
            logger.info(f"Creating execution context: {execution_log_id} for org={org_id}, type={task_type}")
            
            execution_context = {
                "ExecutionLogId": execution_log_id,
                "OrgId": org_id,
                "UserId": user_id,
                "TaskType": task_type,
                "Status": "pending",
                "ParentExecutionLogId": parent_execution_log_id,
                "ChildTaskIds": json.dumps([]),
                "CreatedAt": datetime.now(),
                "UpdatedAt": datetime.now(),
                "TotalChildTasks": 0,
                "CompletedChildTasks": 0,
                "FailedChildTasks": 0,
                "Metadata": json.dumps(metadata or {})
            }
            
            if is_local_dev():
                return self._create_execution_context_table(execution_context)
            else:
                return self._create_execution_context_sql(execution_context)
                
        except Exception as e:
            logger.error(f"Error creating execution context: {str(e)}")
            return None
    
    def _create_execution_context_table(self, execution_context: Dict[str, Any]) -> Optional[str]:
        """Create execution context in Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None
            
            # Create entity for Table Storage
            entity = {
                "PartitionKey": f"execution_{execution_context['OrgId']}",
                "RowKey": execution_context["ExecutionLogId"],
                "ExecutionLogId": execution_context["ExecutionLogId"],
                "OrgId": execution_context["OrgId"],
                "UserId": execution_context["UserId"],
                "TaskType": execution_context["TaskType"],
                "Status": execution_context["Status"],
                "ParentExecutionLogId": execution_context["ParentExecutionLogId"],
                "ChildTaskIds": execution_context["ChildTaskIds"],
                "CreatedAt": execution_context["CreatedAt"].isoformat(),
                "UpdatedAt": execution_context["UpdatedAt"].isoformat(),
                "CompletedAt": None,
                "TotalChildTasks": execution_context["TotalChildTasks"],
                "CompletedChildTasks": execution_context["CompletedChildTasks"],
                "FailedChildTasks": execution_context["FailedChildTasks"],
                "Metadata": execution_context["Metadata"]
            }
            
            success = self.table_repo.insert_entity(entity)
            if success:
                logger.info(f"Created execution context in Table Storage: {execution_context['ExecutionLogId']}")
                return execution_context["ExecutionLogId"]
            else:
                logger.error(f"Failed to create execution context in Table Storage")
                return None
                
        except Exception as e:
            logger.error(f"Error creating execution context in Table Storage: {str(e)}")
            return None
    
    def _create_execution_context_sql(self, execution_context: Dict[str, Any]) -> Optional[str]:
        """Create execution context in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None
            
            query = """
            INSERT INTO ExecutionContext (
                ExecutionLogId, OrgId, UserId, TaskType, Status, ParentExecutionLogId,
                ChildTaskIds, CreatedAt, UpdatedAt, TotalChildTasks, CompletedChildTasks,
                FailedChildTasks, Metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                execution_context["ExecutionLogId"],
                execution_context["OrgId"],
                execution_context["UserId"],
                execution_context["TaskType"],
                execution_context["Status"],
                execution_context["ParentExecutionLogId"],
                execution_context["ChildTaskIds"],
                execution_context["CreatedAt"].isoformat(),
                execution_context["UpdatedAt"].isoformat(),
                execution_context["TotalChildTasks"],
                execution_context["CompletedChildTasks"],
                execution_context["FailedChildTasks"],
                execution_context["Metadata"]
            )
            
            success = self.sql_repo.execute_non_query(query, params)
            if success:
                logger.info(f"Created execution context in SQL Database: {execution_context['ExecutionLogId']}")
                return execution_context["ExecutionLogId"]
            else:
                logger.error(f"Failed to create execution context in SQL Database")
                return None
                
        except Exception as e:
            logger.error(f"Error creating execution context in SQL Database: {str(e)}")
            return None
    
    def add_child_task(
        self,
        execution_log_id: str,
        child_task_id: str
    ) -> bool:
        """
        Add a child task to an execution context
        
        Args:
            execution_log_id: Parent execution log ID
            child_task_id: Child task ID to add
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Adding child task {child_task_id} to execution context {execution_log_id}")
            
            # Get current execution context
            context = self.get_execution_context(execution_log_id)
            if not context:
                logger.error(f"Execution context not found: {execution_log_id}")
                return False
            
            # Update child task IDs
            child_task_ids = json.loads(context.get("ChildTaskIds", "[]"))
            if child_task_id not in child_task_ids:
                child_task_ids.append(child_task_id)
            
            # Update execution context
            return self._update_execution_context(
                execution_log_id,
                {
                    "ChildTaskIds": json.dumps(child_task_ids),
                    "TotalChildTasks": len(child_task_ids),
                    "UpdatedAt": datetime.now()
                }
            )
            
        except Exception as e:
            logger.error(f"Error adding child task: {str(e)}")
            return False

    def record_child_task(
        self,
        execution_log_id: str,
        child_task_id: str
    ) -> bool:
        """
        Record a child task for an execution context (alias for add_child_task)
        
        Args:
            execution_log_id: Parent execution log ID
            child_task_id: Child task ID to record
            
        Returns:
            bool: True if successful, False otherwise
        """
        return self.add_child_task(execution_log_id, child_task_id)

    def get_child_tasks(
        self,
        execution_log_id: str
    ) -> List[str]:
        """
        Get all child task IDs for an execution context
        
        Args:
            execution_log_id: Parent execution log ID
            
        Returns:
            List[str]: List of child task IDs
        """
        try:
            context = self.get_execution_context(execution_log_id)
            if not context:
                return []
            
            child_task_ids = json.loads(context.get("ChildTaskIds", "[]"))
            return child_task_ids
            
        except Exception as e:
            logger.error(f"Error getting child tasks: {str(e)}")
            return []
    
    def check_completion_status(self, execution_log_id: str) -> Optional[str]:
        """
        Check the completion status of an execution context
        
        Args:
            execution_log_id: Execution log ID to check
            
        Returns:
            str: Overall status (pending, running, completed, failed)
        """
        try:
            logger.debug(f"Checking completion status for execution context: {execution_log_id}")
            
            # Get all tasks for this execution log
            tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,  # Get all tasks regardless of org
                include_completed=True
            )
            
            # Filter tasks by execution log ID
            execution_tasks = [
                task for task in tasks 
                if task.get("ExecutionLogId") == execution_log_id
            ]
            
            if not execution_tasks:
                logger.warning(f"No tasks found for execution log: {execution_log_id}")
                return "pending"
            
            # Check parent task status
            parent_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_parent_task")]
            if not parent_tasks:
                logger.warning(f"No parent task found for execution log: {execution_log_id}")
                return "pending"
            
            parent_task = parent_tasks[0]
            parent_status = parent_task.get("Status", "pending")
            
            # If parent is already completed/failed, return that status
            if parent_status in ["completed", "failed"]:
                return parent_status
            
            # Check child tasks
            child_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_child_task")]
            if not child_tasks:
                return "running"
            
            # Count child task statuses
            completed_count = sum(1 for task in child_tasks if task.get("Status") == "completed")
            failed_count = sum(1 for task in child_tasks if task.get("Status") == "failed")
            total_children = len(child_tasks)
            
            # Update execution context with counts
            self._update_execution_context(
                execution_log_id,
                {
                    "CompletedChildTasks": completed_count,
                    "FailedChildTasks": failed_count,
                    "UpdatedAt": datetime.now()
                }
            )
            
            # Determine overall status
            if failed_count > 0:
                return "failed"
            elif completed_count == total_children:
                return "completed"
            else:
                return "running"
                
        except Exception as e:
            logger.error(f"Error checking completion status: {str(e)}")
            return None
    
    def get_execution_summary(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a comprehensive summary of an execution context
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict: Execution summary with task details
        """
        try:
            logger.debug(f"Getting execution summary for: {execution_log_id}")
            
            # Get execution context
            context = self.get_execution_context(execution_log_id)
            if not context:
                logger.error(f"Execution context not found: {execution_log_id}")
                return None
            
            # Get all tasks for this execution log
            tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True
            )
            
            execution_tasks = [
                task for task in tasks 
                if task.get("ExecutionLogId") == execution_log_id
            ]
            
            # Separate parent and child tasks
            parent_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_parent_task")]
            child_tasks = [task for task in execution_tasks if task.get("Params", {}).get("is_child_task")]
            
            # Build summary
            summary = {
                "execution_log_id": execution_log_id,
                "org_id": context.get("OrgId"),
                "user_id": context.get("UserId"),
                "task_type": context.get("TaskType"),
                "status": context.get("Status"),
                "created_at": context.get("CreatedAt"),
                "updated_at": context.get("UpdatedAt"),
                "completed_at": context.get("CompletedAt"),
                "parent_task": parent_tasks[0] if parent_tasks else None,
                "child_tasks": child_tasks,
                "total_child_tasks": len(child_tasks),
                "completed_child_tasks": sum(1 for task in child_tasks if task.get("Status") == "completed"),
                "failed_child_tasks": sum(1 for task in child_tasks if task.get("Status") == "failed"),
                "running_child_tasks": sum(1 for task in child_tasks if task.get("Status") == "running"),
                "pending_child_tasks": sum(1 for task in child_tasks if task.get("Status") == "pending")
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting execution summary: {str(e)}")
            return None
    
    def get_execution_context(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get execution context by ID"""
        try:
            if is_local_dev():
                return self._get_execution_context_table(execution_log_id)
            else:
                return self._get_execution_context_sql(execution_log_id)
                
        except Exception as e:
            logger.error(f"Error getting execution context: {str(e)}")
            return None
    
    def _get_execution_context_table(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get execution context from Table Storage"""
        try:
            if not self.table_repo:
                return None
            
            # Query all entities and filter by RowKey
            all_entities = list(self.table_repo.query_entities())
            
            for entity in all_entities:
                if entity.get('RowKey') == execution_log_id:
                    return dict(entity)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting execution context from Table Storage: {str(e)}")
            return None
    
    def _get_execution_context_sql(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get execution context from SQL Database"""
        try:
            if not self.sql_repo:
                return None
            
            query = """
            SELECT ExecutionLogId, OrgId, UserId, TaskType, Status, ParentExecutionLogId,
                   ChildTaskIds, CreatedAt, UpdatedAt, CompletedAt, TotalChildTasks,
                   CompletedChildTasks, FailedChildTasks, Metadata
            FROM ExecutionContext
            WHERE ExecutionLogId = ?
            """
            
            results = self.sql_repo.execute_query(query, (execution_log_id,))
            
            if results and len(results) > 0:
                row = results[0]
                return {
                    "ExecutionLogId": row[0],
                    "OrgId": row[1],
                    "UserId": row[2],
                    "TaskType": row[3],
                    "Status": row[4],
                    "ParentExecutionLogId": row[5],
                    "ChildTaskIds": row[6],
                    "CreatedAt": row[7],
                    "UpdatedAt": row[8],
                    "CompletedAt": row[9],
                    "TotalChildTasks": row[10],
                    "CompletedChildTasks": row[11],
                    "FailedChildTasks": row[12],
                    "Metadata": row[13]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting execution context from SQL Database: {str(e)}")
            return None
    
    def _update_execution_context(
        self,
        execution_log_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update execution context with given fields"""
        try:
            if is_local_dev():
                return self._update_execution_context_table(execution_log_id, updates)
            else:
                return self._update_execution_context_sql(execution_log_id, updates)
                
        except Exception as e:
            logger.error(f"Error updating execution context: {str(e)}")
            return False
    
    def _update_execution_context_table(
        self,
        execution_log_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update execution context in Table Storage"""
        try:
            if not self.table_repo:
                return False
            
            # Find the entity
            all_entities = list(self.table_repo.query_entities())
            
            for entity in all_entities:
                if entity.get('RowKey') == execution_log_id:
                    # Update fields
                    for key, value in updates.items():
                        if key == "UpdatedAt" and isinstance(value, datetime):
                            entity[key] = value.isoformat()
                        else:
                            entity[key] = value
                    
                    return self.table_repo.update_entity(entity)
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating execution context in Table Storage: {str(e)}")
            return False
    
    def _update_execution_context_sql(
        self,
        execution_log_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update execution context in SQL Database"""
        try:
            if not self.sql_repo:
                return False
            
            # Build dynamic update query
            update_fields = ["UpdatedAt = ?"]
            params = [datetime.now().isoformat()]
            
            for key, value in updates.items():
                if key != "UpdatedAt":
                    update_fields.append(f"{key} = ?")
                    if isinstance(value, datetime):
                        params.append(value.isoformat())
                    else:
                        params.append(value)
            
            params.append(execution_log_id)
            query = f"UPDATE ExecutionContext SET {', '.join(update_fields)} WHERE ExecutionLogId = ?"
            
            return self.sql_repo.execute_non_query(query, tuple(params))
            
        except Exception as e:
            logger.error(f"Error updating execution context in SQL Database: {str(e)}")
            return False


# Global manager instance
_execution_context_manager = None


def get_execution_context_manager() -> ExecutionContextManager:
    """
    Get the global execution context manager instance
    
    Returns:
        ExecutionContextManager: The manager instance
    """
    global _execution_context_manager
    
    if _execution_context_manager is None:
        _execution_context_manager = ExecutionContextManager()
        logger.debug("Created global execution context manager instance")
    
    return _execution_context_manager