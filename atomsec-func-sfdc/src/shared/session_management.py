"""
Session Management Module

This module provides session management capabilities including:
- Session lifecycle management
- Token refresh automation
- Session security monitoring
- Device and location tracking

Requirements addressed: 1.1
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import azure.functions as func
from fastapi import Request, HTTPException, status

from src.shared.advanced_auth_service import get_advanced_auth_service, AuthenticationError
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


@dataclass
class SessionContext:
    """Session context for request processing"""
    user_id: str
    session_id: str
    access_token: str
    mfa_verified: bool
    expires_at: datetime
    needs_refresh: bool
    ip_address: str
    user_agent: str
    endpoint: str
    risk_score: float = 0.0


class SessionManager:
    """
    Session management service for handling user sessions,
    token refresh, and security monitoring
    """
    
    def __init__(self):
        self.auth_service = get_advanced_auth_service()
        self.monitoring = get_monitoring_service()
        
        # Configuration
        self.config = {
            'auto_refresh_enabled': True,
            'refresh_threshold_minutes': 60,
            'session_extension_minutes': 480,  # 8 hours
            'max_session_extensions': 3,
            'require_mfa_for_sensitive_endpoints': [
                '/api/integrations/*/scan',
                '/api/security/*',
                '/api/policies/*',
                '/api/key-vault/*'
            ]
        }
    
    def extract_session_context(self, request: func.HttpRequest) -> SessionContext:
        """
        Extract session context from HTTP request
        
        Args:
            request: HTTP request object
            
        Returns:
            SessionContext: Session context information
            
        Raises:
            AuthenticationError: If session extraction fails
        """
        try:
            # Extract access token from Authorization header
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                raise AuthenticationError("Missing or invalid Authorization header")
            
            access_token = auth_header[7:]  # Remove 'Bearer ' prefix
            
            # Extract request context
            ip_address = self._extract_client_ip(request)
            user_agent = request.headers.get('User-Agent', 'unknown')
            endpoint = request.url
            
            request_context = {
                'ip_address': ip_address,
                'user_agent': user_agent,
                'endpoint': endpoint
            }
            
            # Validate session
            session_info = self.auth_service.validate_session(access_token, request_context)
            
            return SessionContext(
                user_id=session_info['user_id'],
                session_id=session_info['session_id'],
                access_token=access_token,
                mfa_verified=session_info['mfa_verified'],
                expires_at=datetime.fromisoformat(session_info['expires_at'].replace('Z', '+00:00')),
                needs_refresh=session_info['needs_refresh'],
                ip_address=ip_address,
                user_agent=user_agent,
                endpoint=endpoint
            )
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Error extracting session context: {str(e)}")
            raise AuthenticationError(f"Session context extraction failed: {str(e)}")
    
    def validate_session_for_endpoint(self, session_context: SessionContext,
                                    endpoint: str) -> Dict[str, Any]:
        """
        Validate session for specific endpoint access
        
        Args:
            session_context: Session context
            endpoint: Endpoint being accessed
            
        Returns:
            Dict containing validation result and any required actions
            
        Raises:
            AuthenticationError: If validation fails
        """
        try:
            validation_result = {
                'valid': True,
                'requires_mfa': False,
                'requires_refresh': False,
                'actions': []
            }
            
            # Check if endpoint requires MFA
            if self._endpoint_requires_mfa(endpoint):
                if not session_context.mfa_verified:
                    validation_result['requires_mfa'] = True
                    validation_result['actions'].append('mfa_verification_required')
                    
                    # Log security event
                    self.monitoring.track_custom_metric(
                        'mfa_required_endpoint_access',
                        1.0,
                        {
                            'user_id': session_context.user_id,
                            'endpoint': endpoint,
                            'mfa_verified': session_context.mfa_verified
                        }
                    )
            
            # Check if token needs refresh
            if session_context.needs_refresh and self.config['auto_refresh_enabled']:
                validation_result['requires_refresh'] = True
                validation_result['actions'].append('token_refresh_recommended')
            
            # Check session expiration
            time_until_expiry = (session_context.expires_at - datetime.utcnow()).total_seconds()
            if time_until_expiry <= 0:
                raise AuthenticationError("Session expired")
            elif time_until_expiry < 300:  # Less than 5 minutes
                validation_result['actions'].append('session_expiring_soon')
            
            # Track endpoint access
            self.monitoring.track_custom_metric(
                'endpoint_access',
                1.0,
                {
                    'user_id': session_context.user_id,
                    'endpoint': endpoint,
                    'mfa_verified': session_context.mfa_verified,
                    'session_age_minutes': (datetime.utcnow() - 
                                          (session_context.expires_at - timedelta(minutes=480))).total_seconds() / 60
                }
            )
            
            return validation_result
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Error validating session for endpoint: {str(e)}")
            raise AuthenticationError(f"Session validation failed: {str(e)}")
    
    def refresh_session_if_needed(self, session_context: SessionContext,
                                request: func.HttpRequest) -> Optional[Dict[str, Any]]:
        """
        Refresh session token if needed
        
        Args:
            session_context: Current session context
            request: HTTP request for context
            
        Returns:
            Dict containing new token information if refreshed, None otherwise
        """
        try:
            if not session_context.needs_refresh or not self.config['auto_refresh_enabled']:
                return None
            
            # Extract refresh token from request (could be in header or body)
            refresh_token = self._extract_refresh_token(request)
            if not refresh_token:
                logger.warning("Token refresh needed but no refresh token provided")
                return None
            
            # Prepare request context
            request_context = {
                'ip_address': session_context.ip_address,
                'user_agent': session_context.user_agent,
                'endpoint': session_context.endpoint
            }
            
            # Refresh token
            refresh_result = self.auth_service.refresh_session_token(refresh_token, request_context)
            
            logger.info(f"Session token refreshed for user {session_context.user_id}")
            
            return {
                'access_token': refresh_result['access_token'],
                'refresh_token': refresh_result['refresh_token'],
                'expires_at': refresh_result['expires_at'],
                'refreshed_at': datetime.utcnow().isoformat()
            }
            
        except AuthenticationError as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error during token refresh: {str(e)}")
            return None
    
    def extend_session(self, session_context: SessionContext,
                      extension_minutes: int = None) -> Dict[str, Any]:
        """
        Extend session duration
        
        Args:
            session_context: Current session context
            extension_minutes: Minutes to extend (default from config)
            
        Returns:
            Dict containing extended session information
            
        Raises:
            AuthenticationError: If extension fails
        """
        try:
            if extension_minutes is None:
                extension_minutes = self.config['session_extension_minutes']
            
            # Check if extension is allowed (implement extension limits)
            # This would require tracking extension count in session data
            
            # For now, create a new session with extended time
            # In a real implementation, this would update the existing session
            new_expires_at = datetime.utcnow() + timedelta(minutes=extension_minutes)
            
            logger.info(f"Session extended for user {session_context.user_id} until {new_expires_at}")
            
            self.monitoring.track_custom_metric(
                'session_extended',
                1.0,
                {
                    'user_id': session_context.user_id,
                    'extension_minutes': extension_minutes
                }
            )
            
            return {
                'session_id': session_context.session_id,
                'expires_at': new_expires_at.isoformat(),
                'extended_by_minutes': extension_minutes
            }
            
        except Exception as e:
            logger.error(f"Error extending session: {str(e)}")
            raise AuthenticationError(f"Session extension failed: {str(e)}")
    
    def revoke_session(self, session_context: SessionContext,
                      reason: str = "user_logout") -> bool:
        """
        Revoke user session
        
        Args:
            session_context: Session context to revoke
            reason: Reason for revocation
            
        Returns:
            bool: True if session was revoked successfully
        """
        try:
            success = self.auth_service.revoke_session(session_context.session_id, reason)
            
            if success:
                logger.info(f"Session revoked for user {session_context.user_id}, reason: {reason}")
                
                self.monitoring.track_custom_metric(
                    'session_revoked',
                    1.0,
                    {
                        'user_id': session_context.user_id,
                        'reason': reason
                    }
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Error revoking session: {str(e)}")
            return False
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all active sessions for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            List of active sessions
        """
        try:
            # This would query the auth service for user sessions
            # For now, return empty list as the auth service doesn't expose this method
            return []
            
        except Exception as e:
            logger.error(f"Error retrieving user sessions: {str(e)}")
            return []
    
    def monitor_session_security(self, session_context: SessionContext) -> Dict[str, Any]:
        """
        Monitor session for security issues
        
        Args:
            session_context: Session context to monitor
            
        Returns:
            Dict containing security monitoring results
        """
        try:
            security_status = {
                'secure': True,
                'warnings': [],
                'threats': [],
                'recommendations': []
            }
            
            # Check for suspicious activity
            suspicious_behavior = self.auth_service.detect_suspicious_behavior(session_context.user_id)
            
            for indicator in suspicious_behavior:
                if indicator['severity'] == 'high':
                    security_status['threats'].append(indicator)
                    security_status['secure'] = False
                elif indicator['severity'] == 'medium':
                    security_status['warnings'].append(indicator)
                else:
                    security_status['recommendations'].append(indicator)
            
            # Check session age
            session_age_hours = (datetime.utcnow() - 
                               (session_context.expires_at - timedelta(minutes=480))).total_seconds() / 3600
            
            if session_age_hours > 12:  # Session older than 12 hours
                security_status['recommendations'].append({
                    'type': 'long_session',
                    'description': f'Session has been active for {session_age_hours:.1f} hours',
                    'recommendation': 'Consider re-authentication for security'
                })
            
            # Check MFA status for sensitive operations
            if not session_context.mfa_verified:
                security_status['recommendations'].append({
                    'type': 'mfa_not_verified',
                    'description': 'Multi-factor authentication not verified',
                    'recommendation': 'Enable MFA for enhanced security'
                })
            
            return security_status
            
        except Exception as e:
            logger.error(f"Error monitoring session security: {str(e)}")
            return {
                'secure': False,
                'warnings': [{'type': 'monitoring_error', 'description': str(e)}],
                'threats': [],
                'recommendations': []
            }
    
    # Private helper methods
    
    def _extract_client_ip(self, request: func.HttpRequest) -> str:
        """Extract client IP address from request"""
        # Check for forwarded IP headers
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback to remote address (may not be available in Azure Functions)
        return request.headers.get('X-Client-IP', 'unknown')
    
    def _extract_refresh_token(self, request: func.HttpRequest) -> Optional[str]:
        """Extract refresh token from request"""
        # Check for refresh token in X-Refresh-Token header
        refresh_token = request.headers.get('X-Refresh-Token')
        if refresh_token:
            return refresh_token
        
        # Check in request body for POST requests
        if request.method == 'POST':
            try:
                body = request.get_json()
                if body and 'refresh_token' in body:
                    return body['refresh_token']
            except:
                pass
        
        return None
    
    def _endpoint_requires_mfa(self, endpoint: str) -> bool:
        """Check if endpoint requires MFA verification"""
        for pattern in self.config['require_mfa_for_sensitive_endpoints']:
            # Simple pattern matching (could be enhanced with regex)
            if '*' in pattern:
                pattern_parts = pattern.split('*')
                if len(pattern_parts) == 2:
                    if endpoint.startswith(pattern_parts[0]) and endpoint.endswith(pattern_parts[1]):
                        return True
            else:
                if endpoint == pattern:
                    return True
        
        return False


# Session management middleware decorator
def require_session(mfa_required: bool = False):
    """
    Decorator to require valid session for endpoint access
    
    Args:
        mfa_required: Whether MFA verification is required
        
    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(req: func.HttpRequest):
            try:
                session_manager = SessionManager()
                
                # Extract session context
                session_context = session_manager.extract_session_context(req)
                
                # Validate session for endpoint
                validation_result = session_manager.validate_session_for_endpoint(
                    session_context, req.url
                )
                
                # Check MFA requirement
                if mfa_required and not session_context.mfa_verified:
                    return func.HttpResponse(
                        json.dumps({
                            'success': False,
                            'error': 'MFA verification required',
                            'requires_mfa': True
                        }),
                        mimetype='application/json',
                        status_code=401
                    )
                
                # Add session context to request
                setattr(req, 'session_context', session_context)
                setattr(req, 'session_validation', validation_result)
                
                # Call original function
                response = func(req)
                
                # Add session information to response headers if needed
                if hasattr(response, 'headers') and validation_result.get('requires_refresh'):
                    response.headers['X-Token-Refresh-Recommended'] = 'true'
                
                return response
                
            except AuthenticationError as e:
                logger.warning(f"Authentication failed: {str(e)}")
                return func.HttpResponse(
                    json.dumps({
                        'success': False,
                        'error': str(e),
                        'requires_authentication': True
                    }),
                    mimetype='application/json',
                    status_code=401
                )
            except Exception as e:
                logger.error(f"Session middleware error: {str(e)}")
                return func.HttpResponse(
                    json.dumps({
                        'success': False,
                        'error': 'Internal authentication error'
                    }),
                    mimetype='application/json',
                    status_code=500
                )
        
        # Copy function attributes
        import functools
        functools.update_wrapper(wrapper, func)
        return wrapper
    
    return decorator


# Global session manager instance
_session_manager = None


def get_session_manager() -> SessionManager:
    """Get the global session manager instance"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager