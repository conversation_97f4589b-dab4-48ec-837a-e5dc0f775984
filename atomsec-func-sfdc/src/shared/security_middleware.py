"""
Security Middleware Module

This module provides security middleware for SFDC service requests,
including request validation, security headers, and monitoring.
"""

import logging
import json
import time
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from functools import wraps
import azure.functions as func

# Import enhanced security modules
from src.shared.enhanced_parameter_validator import (
    get_enhanced_parameter_validator,
    ParameterValidationError,
    SecurityValidationError
)
from src.shared.enhanced_auth_service import (
    get_enhanced_auth_service,
    AuthenticationError,
    RateLimitExceededError,
    SalesforceAuthenticationError
)

logger = logging.getLogger(__name__)


class SecurityMiddleware:
    """
    Security middleware for SFDC service requests
    
    This middleware provides:
    - Request parameter validation
    - Security header validation
    - Rate limiting
    - Security monitoring
    - Error handling
    """
    
    def __init__(self):
        self.validator = get_enhanced_parameter_validator()
        self.auth_service = get_enhanced_auth_service()
        self.security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'",
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
    
    def validate_request_security(self, req: func.HttpRequest,
                                required_fields: List[str] = None,
                                validate_auth: bool = True,
                                validate_execution_log: bool = True) -> Dict[str, Any]:
        """
        Validate request security including parameters and authentication
        
        Args:
            req: HTTP request
            required_fields: Required parameter fields
            validate_auth: Whether to validate authentication
            validate_execution_log: Whether to validate execution_log_id
            
        Returns:
            Dict[str, Any]: Validated request data
            
        Raises:
            SecurityValidationError: If security validation fails
        """
        try:
            start_time = time.time()
            
            # Get request data
            request_data = self._extract_request_data(req)
            
            # Validate parameters
            if request_data:
                validated_params = self.validator.validate_sfdc_request_parameters(
                    request_data,
                    required_fields=required_fields,
                    validate_execution_log=validate_execution_log
                )
            else:
                validated_params = {}
            
            # Validate authentication if required
            user_info = None
            if validate_auth:
                user_info = self._validate_request_authentication(req)
            
            # Log security validation success
            validation_time = time.time() - start_time
            self._log_security_event('request_validation_success', {
                'method': req.method,
                'url': req.url,
                'user_id': user_info.get('id') if user_info else None,
                'param_count': len(validated_params),
                'validation_time_ms': round(validation_time * 1000, 2)
            })
            
            return {
                'params': validated_params,
                'user': user_info,
                'validation_time': validation_time
            }
            
        except (ParameterValidationError, SecurityValidationError, AuthenticationError) as e:
            self._log_security_event('request_validation_failed', {
                'method': req.method,
                'url': req.url,
                'error': str(e),
                'error_type': type(e).__name__
            })
            raise
        except Exception as e:
            logger.error(f"Unexpected error in request validation: {str(e)}")
            raise SecurityValidationError(f"Request validation failed: {str(e)}")
    
    def _extract_request_data(self, req: func.HttpRequest) -> Dict[str, Any]:
        """
        Extract and parse request data
        
        Args:
            req: HTTP request
            
        Returns:
            Dict[str, Any]: Parsed request data
        """
        try:
            # Try to get JSON data
            if req.get_body():
                return req.get_json() or {}
            
            # Fall back to query parameters
            params = {}
            for key in req.params:
                params[key] = req.params[key]
            
            return params
            
        except Exception as e:
            logger.warning(f"Failed to extract request data: {str(e)}")
            return {}
    
    def _validate_request_authentication(self, req: func.HttpRequest) -> Dict[str, Any]:
        """
        Validate request authentication
        
        Args:
            req: HTTP request
            
        Returns:
            Dict[str, Any]: User information
            
        Raises:
            AuthenticationError: If authentication fails
        """
        # Extract token from Authorization header
        auth_header = req.headers.get('Authorization')
        if not auth_header:
            raise AuthenticationError("Missing Authorization header")
        
        if not auth_header.startswith('Bearer '):
            raise AuthenticationError("Invalid Authorization header format")
        
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Validate token using enhanced auth service
        try:
            payload = self.auth_service.validate_jwt_token_enhanced(token)
            
            # Extract user information
            user_info = {
                'id': payload.get('sub'),
                'email': payload.get('email'),
                'scopes': payload.get('scopes', []),
                'exp': payload.get('exp')
            }
            
            return user_info
            
        except Exception as e:
            raise AuthenticationError(f"Token validation failed: {str(e)}")
    
    def add_security_headers(self, response: func.HttpResponse) -> func.HttpResponse:
        """
        Add security headers to response
        
        Args:
            response: HTTP response
            
        Returns:
            func.HttpResponse: Response with security headers
        """
        # Add security headers
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        # Add correlation ID if not present
        if 'X-Correlation-ID' not in response.headers:
            import uuid
            response.headers['X-Correlation-ID'] = str(uuid.uuid4())
        
        return response
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log security events
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details
        }
        
        logger.info(f"Security middleware event: {event_type}", extra=details)


# Global security middleware instance
_security_middleware = None


def get_security_middleware() -> SecurityMiddleware:
    """
    Get the global security middleware instance
    
    Returns:
        SecurityMiddleware: The security middleware instance
    """
    global _security_middleware
    if _security_middleware is None:
        _security_middleware = SecurityMiddleware()
    return _security_middleware


def secure_endpoint(required_fields: List[str] = None,
                   validate_auth: bool = True,
                   validate_execution_log: bool = True):
    """
    Decorator to secure SFDC endpoints with validation and security checks
    
    Args:
        required_fields: Required parameter fields
        validate_auth: Whether to validate authentication
        validate_execution_log: Whether to validate execution_log_id
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            middleware = get_security_middleware()
            
            try:
                # Validate request security
                validation_result = middleware.validate_request_security(
                    req,
                    required_fields=required_fields,
                    validate_auth=validate_auth,
                    validate_execution_log=validate_execution_log
                )
                
                # Add validated data to request
                req.validated_params = validation_result['params']
                req.user_info = validation_result['user']
                
                # Call the original function
                response = func(req)
                
                # Add security headers to response
                if isinstance(response, func.HttpResponse):
                    response = middleware.add_security_headers(response)
                
                return response
                
            except ParameterValidationError as e:
                logger.warning(f"Parameter validation failed: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Parameter validation failed: {str(e)}",
                        "error_type": "parameter_validation"
                    }),
                    mimetype="application/json",
                    status_code=400
                )
                return middleware.add_security_headers(response)
                
            except SecurityValidationError as e:
                logger.warning(f"Security validation failed: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Security validation failed: {str(e)}",
                        "error_type": "security_validation"
                    }),
                    mimetype="application/json",
                    status_code=400
                )
                return middleware.add_security_headers(response)
                
            except AuthenticationError as e:
                logger.warning(f"Authentication failed: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Authentication failed: {str(e)}",
                        "error_type": "authentication"
                    }),
                    mimetype="application/json",
                    status_code=401
                )
                return middleware.add_security_headers(response)
                
            except RateLimitExceededError as e:
                logger.warning(f"Rate limit exceeded: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Rate limit exceeded: {str(e)}",
                        "error_type": "rate_limit"
                    }),
                    mimetype="application/json",
                    status_code=429
                )
                return middleware.add_security_headers(response)
                
            except Exception as e:
                logger.error(f"Unexpected error in secure endpoint: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Internal server error",
                        "error_type": "internal_error"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
                return middleware.add_security_headers(response)
        
        return wrapper
    return decorator


def validate_sequential_task_request(task_type: str, execution_log_id: str):
    """
    Decorator to validate sequential task requests
    
    Args:
        task_type: Type of task being processed
        execution_log_id: Execution log ID for task sequence tracking
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            middleware = get_security_middleware()
            
            try:
                # Extract request data
                request_data = middleware._extract_request_data(req)
                
                # Validate sequential task parameters
                validated_params = middleware.validator.validate_sequential_task_parameters(
                    request_data,
                    task_type,
                    execution_log_id
                )
                
                # Add validated data to request
                req.validated_params = validated_params
                
                # Call the original function
                response = func(req)
                
                # Add security headers to response
                if isinstance(response, func.HttpResponse):
                    response = middleware.add_security_headers(response)
                
                return response
                
            except (ParameterValidationError, SecurityValidationError) as e:
                logger.warning(f"Sequential task validation failed: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Task validation failed: {str(e)}",
                        "error_type": "task_validation"
                    }),
                    mimetype="application/json",
                    status_code=400
                )
                return middleware.add_security_headers(response)
                
            except Exception as e:
                logger.error(f"Unexpected error in task validation: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Internal server error",
                        "error_type": "internal_error"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
                return middleware.add_security_headers(response)
        
        return wrapper
    return decorator


def internal_service_endpoint():
    """
    Decorator to secure internal service endpoints
    
    This decorator validates that requests are coming from trusted internal services
    and adds appropriate security headers to responses.
    
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            from src.shared.internal_service_security import (
                validate_internal_service_call,
                add_internal_response_headers,
                InternalServiceSecurityError
            )
            
            try:
                # Validate internal service request
                is_valid, message, service_info = validate_internal_service_call(req)
                
                if not is_valid:
                    logger.warning(f"Internal service validation failed: {message}")
                    response = func.HttpResponse(
                        json.dumps({
                            "success": False,
                            "error": f"Internal service validation failed: {message}",
                            "error_type": "internal_service_auth"
                        }),
                        mimetype="application/json",
                        status_code=403
                    )
                    return add_internal_response_headers(response, {})
                
                # Add service info to request
                req.internal_service_info = service_info
                
                # Call the original function
                response = func(req)
                
                # Add internal service security headers
                if isinstance(response, func.HttpResponse):
                    response = add_internal_response_headers(response, service_info)
                
                return response
                
            except InternalServiceSecurityError as e:
                logger.error(f"Internal service security error: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Internal service security error: {str(e)}",
                        "error_type": "internal_service_security"
                    }),
                    mimetype="application/json",
                    status_code=403
                )
                return add_internal_response_headers(response, {})
                
            except Exception as e:
                logger.error(f"Unexpected error in internal service endpoint: {str(e)}")
                response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Internal server error",
                        "error_type": "internal_error"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
                return add_internal_response_headers(response, {})