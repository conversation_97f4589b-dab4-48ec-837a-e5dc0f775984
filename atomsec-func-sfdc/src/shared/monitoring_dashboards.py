"""
Advanced Monitoring Dashboards and Alerting

This module provides advanced monitoring dashboards, KPI tracking, and real-time alerting
for the SFDC service. It integrates with Application Insights to create comprehensive
monitoring solutions.

Features:
- Application Insights workbooks and dashboards
- Business metrics and KPI tracking
- Real-time alerting and notification systems
- Custom dashboard creation and management
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

from .monitoring import get_performance_monitor, get_task_sequence_monitor, get_metrics_collector

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    ERROR = "error"


class AlertStatus(Enum):
    """Alert status"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    description: str
    metric_name: str
    condition: str  # 'greater_than', 'less_than', 'equals', 'not_equals'
    threshold: float
    severity: AlertSeverity
    evaluation_window_minutes: int = 5
    cooldown_minutes: int = 15
    enabled: bool = True
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class Alert:
    """Active alert instance"""
    rule_name: str
    severity: AlertSeverity
    status: AlertStatus
    message: str
    metric_value: float
    threshold: float
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    tags: Dict[str, str] = None
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}
        if self.context is None:
            self.context = {}


class DashboardConfig:
    """Configuration for Application Insights dashboards"""
    
    @staticmethod
    def get_system_performance_dashboard() -> Dict[str, Any]:
        """Get system performance dashboard configuration"""
        return {
            "name": "SFDC Service - System Performance",
            "description": "System-level performance metrics and health indicators",
            "tiles": [
                {
                    "title": "CPU Usage",
                    "query": """
                    customMetrics
                    | where name == "system.cpu_percent"
                    | summarize avg(value) by bin(timestamp, 5m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "medium"
                },
                {
                    "title": "Memory Usage",
                    "query": """
                    customMetrics
                    | where name == "system.memory_percent"
                    | summarize avg(value) by bin(timestamp, 5m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "medium"
                },
                {
                    "title": "Active Traces",
                    "query": """
                    customMetrics
                    | where name == "traces.active_count"
                    | summarize avg(value) by bin(timestamp, 1m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "small"
                },
                {
                    "title": "Health Score",
                    "query": """
                    customMetrics
                    | where name == "system.health_score"
                    | summarize avg(value) by bin(timestamp, 5m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "small"
                }
            ]
        }
    
    @staticmethod
    def get_task_sequence_dashboard() -> Dict[str, Any]:
        """Get task sequence monitoring dashboard configuration"""
        return {
            "name": "SFDC Service - Task Sequence Monitoring",
            "description": "Task sequence execution monitoring and analytics",
            "tiles": [
                {
                    "title": "Task Sequence Success Rate",
                    "query": """
                    customMetrics
                    | where name == "task_sequence.completed"
                    | summarize 
                        Total = count(),
                        Successful = countif(tostring(customDimensions.status) == "completed"),
                        Failed = countif(tostring(customDimensions.status) == "failed")
                    | extend SuccessRate = (Successful * 100.0) / Total
                    | project SuccessRate, Total, Successful, Failed
                    """,
                    "visualization": "card",
                    "size": "medium"
                },
                {
                    "title": "Task Sequence Duration Trends",
                    "query": """
                    customMetrics
                    | where name == "task_sequence.total_duration_seconds"
                    | summarize avg(value), percentile(value, 95) by bin(timestamp, 10m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "large"
                },
                {
                    "title": "Task Type Performance",
                    "query": """
                    customMetrics
                    | where name == "tasks.duration_seconds"
                    | summarize 
                        AvgDuration = avg(value),
                        P95Duration = percentile(value, 95),
                        Count = count()
                    by tostring(customDimensions.task_type)
                    | order by AvgDuration desc
                    """,
                    "visualization": "table",
                    "size": "medium"
                },
                {
                    "title": "Active Task Sequences",
                    "query": """
                    customMetrics
                    | where name == "task_sequence.progress_percent"
                    | summarize arg_max(timestamp, value) by tostring(customDimensions.execution_log_id)
                    | where value < 100
                    | summarize count()
                    """,
                    "visualization": "card",
                    "size": "small"
                }
            ]
        }
    
    @staticmethod
    def get_business_metrics_dashboard() -> Dict[str, Any]:
        """Get business metrics and KPI dashboard configuration"""
        return {
            "name": "SFDC Service - Business Metrics",
            "description": "Business KPIs and operational metrics",
            "tiles": [
                {
                    "title": "Daily Task Completions",
                    "query": """
                    customMetrics
                    | where name == "tasks.completed"
                    | where timestamp > ago(7d)
                    | summarize count() by bin(timestamp, 1d), tostring(customDimensions.success)
                    | render columnchart
                    """,
                    "visualization": "columnchart",
                    "size": "large"
                },
                {
                    "title": "Error Rate by Task Type",
                    "query": """
                    customMetrics
                    | where name == "tasks.completed"
                    | where timestamp > ago(24h)
                    | summarize 
                        Total = count(),
                        Errors = countif(tostring(customDimensions.success) == "false")
                    by TaskType = tostring(customDimensions.task_type)
                    | extend ErrorRate = (Errors * 100.0) / Total
                    | order by ErrorRate desc
                    """,
                    "visualization": "table",
                    "size": "medium"
                },
                {
                    "title": "User Activity",
                    "query": """
                    customEvents
                    | where name == "user_activity"
                    | summarize count() by bin(timestamp, 1h), tostring(customDimensions.user_id)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "medium"
                },
                {
                    "title": "API Response Times",
                    "query": """
                    requests
                    | where timestamp > ago(24h)
                    | summarize 
                        AvgDuration = avg(duration),
                        P95Duration = percentile(duration, 95)
                    by bin(timestamp, 10m)
                    | render timechart
                    """,
                    "visualization": "timechart",
                    "size": "medium"
                }
            ]
        }


class AlertManager:
    """Enhanced alert manager with real-time monitoring and notifications"""
    
    def __init__(self):
        """Initialize alert manager"""
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = []
        self.notification_handlers = []
        self.lock = threading.Lock()
        self.monitoring_thread = None
        self.is_monitoring = False
        
        # Load default alert rules
        self._load_default_alert_rules()
    
    def _load_default_alert_rules(self):
        """Load default alert rules"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU usage is above 80%",
                metric_name="system.cpu_percent",
                condition="greater_than",
                threshold=80.0,
                severity=AlertSeverity.WARNING,
                evaluation_window_minutes=5,
                cooldown_minutes=15
            ),
            AlertRule(
                name="critical_cpu_usage",
                description="CPU usage is above 95%",
                metric_name="system.cpu_percent",
                condition="greater_than",
                threshold=95.0,
                severity=AlertSeverity.CRITICAL,
                evaluation_window_minutes=2,
                cooldown_minutes=10
            ),
            AlertRule(
                name="high_memory_usage",
                description="Memory usage is above 85%",
                metric_name="system.memory_percent",
                condition="greater_than",
                threshold=85.0,
                severity=AlertSeverity.WARNING,
                evaluation_window_minutes=5,
                cooldown_minutes=15
            ),
            AlertRule(
                name="task_sequence_failure_rate",
                description="Task sequence failure rate is above 10%",
                metric_name="task_sequence.failure_rate",
                condition="greater_than",
                threshold=10.0,
                severity=AlertSeverity.WARNING,
                evaluation_window_minutes=10,
                cooldown_minutes=30
            ),
            AlertRule(
                name="high_task_duration",
                description="Task duration is above 300 seconds",
                metric_name="tasks.duration_seconds",
                condition="greater_than",
                threshold=300.0,
                severity=AlertSeverity.WARNING,
                evaluation_window_minutes=5,
                cooldown_minutes=20
            ),
            AlertRule(
                name="low_health_score",
                description="System health score is below 50",
                metric_name="system.health_score",
                condition="less_than",
                threshold=50.0,
                severity=AlertSeverity.CRITICAL,
                evaluation_window_minutes=3,
                cooldown_minutes=10
            )
        ]
        
        for rule in default_rules:
            self.add_alert_rule(rule)
    
    def add_alert_rule(self, rule: AlertRule):
        """Add an alert rule"""
        with self.lock:
            self.alert_rules[rule.name] = rule
    
    def remove_alert_rule(self, rule_name: str):
        """Remove an alert rule"""
        with self.lock:
            self.alert_rules.pop(rule_name, None)
    
    def add_notification_handler(self, handler: Callable[[Alert], None]):
        """Add a notification handler"""
        self.notification_handlers.append(handler)
    
    def start_monitoring(self):
        """Start real-time alert monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logger.info("Alert monitoring started")
    
    def stop_monitoring(self):
        """Stop alert monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("Alert monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                self._evaluate_alert_rules()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _evaluate_alert_rules(self):
        """Evaluate all alert rules"""
        metrics_collector = get_metrics_collector()
        
        with self.lock:
            rules_to_evaluate = list(self.alert_rules.values())
        
        for rule in rules_to_evaluate:
            if not rule.enabled:
                continue
            
            try:
                self._evaluate_single_rule(rule, metrics_collector)
            except Exception as e:
                logger.error(f"Error evaluating alert rule {rule.name}: {e}")
    
    def _evaluate_single_rule(self, rule: AlertRule, metrics_collector):
        """Evaluate a single alert rule"""
        # Get recent metrics
        time_range_hours = rule.evaluation_window_minutes / 60.0
        summary = metrics_collector.get_summary(rule.metric_name, time_range_hours)
        
        if summary['count'] == 0:
            return
        
        # Get the value to evaluate
        metric_value = summary.get('last_value', 0)
        if metric_value is None:
            return
        
        # Check if alert condition is met
        alert_triggered = self._check_condition(metric_value, rule.condition, rule.threshold)
        
        alert_key = f"{rule.name}_{rule.metric_name}"
        
        if alert_triggered:
            # Check if alert is already active or in cooldown
            if alert_key in self.active_alerts:
                existing_alert = self.active_alerts[alert_key]
                if existing_alert.status == AlertStatus.ACTIVE:
                    return  # Alert already active
            
            # Check cooldown period
            if self._is_in_cooldown(rule):
                return
            
            # Create new alert
            alert = Alert(
                rule_name=rule.name,
                severity=rule.severity,
                status=AlertStatus.ACTIVE,
                message=f"{rule.description}. Current value: {metric_value}, Threshold: {rule.threshold}",
                metric_value=metric_value,
                threshold=rule.threshold,
                triggered_at=datetime.now(),
                tags=rule.tags.copy(),
                context={
                    'metric_name': rule.metric_name,
                    'condition': rule.condition,
                    'evaluation_window_minutes': rule.evaluation_window_minutes,
                    'metric_summary': summary
                }
            )
            
            with self.lock:
                self.active_alerts[alert_key] = alert
                self.alert_history.append(alert)
            
            # Send notifications
            self._send_notifications(alert)
            
            logger.warning(f"Alert triggered: {alert.message}")
        
        else:
            # Check if we need to resolve an active alert
            if alert_key in self.active_alerts:
                alert = self.active_alerts[alert_key]
                if alert.status == AlertStatus.ACTIVE:
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    logger.info(f"Alert resolved: {alert.rule_name}")
                    
                    # Send resolution notification
                    self._send_notifications(alert)
    
    def _check_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Check if alert condition is met"""
        if condition == "greater_than":
            return value > threshold
        elif condition == "less_than":
            return value < threshold
        elif condition == "equals":
            return abs(value - threshold) < 0.001
        elif condition == "not_equals":
            return abs(value - threshold) >= 0.001
        else:
            logger.warning(f"Unknown alert condition: {condition}")
            return False
    
    def _is_in_cooldown(self, rule: AlertRule) -> bool:
        """Check if rule is in cooldown period"""
        cooldown_cutoff = datetime.now() - timedelta(minutes=rule.cooldown_minutes)
        
        # Check recent alerts for this rule
        for alert in reversed(self.alert_history[-100:]):  # Check last 100 alerts
            if (alert.rule_name == rule.name and 
                alert.triggered_at > cooldown_cutoff):
                return True
        
        return False
    
    def _send_notifications(self, alert: Alert):
        """Send alert notifications"""
        for handler in self.notification_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Error sending alert notification: {e}")
    
    def acknowledge_alert(self, alert_key: str, acknowledged_by: str):
        """Acknowledge an active alert"""
        with self.lock:
            if alert_key in self.active_alerts:
                alert = self.active_alerts[alert_key]
                alert.status = AlertStatus.ACKNOWLEDGED
                alert.acknowledged_at = datetime.now()
                alert.acknowledged_by = acknowledged_by
                
                logger.info(f"Alert acknowledged by {acknowledged_by}: {alert.rule_name}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        with self.lock:
            return [alert for alert in self.active_alerts.values() 
                   if alert.status == AlertStatus.ACTIVE]
    
    def get_alert_summary(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get alert summary for time range"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        recent_alerts = [
            alert for alert in self.alert_history
            if alert.triggered_at > cutoff_time
        ]
        
        severity_counts = {severity.value: 0 for severity in AlertSeverity}
        for alert in recent_alerts:
            severity_counts[alert.severity.value] += 1
        
        return {
            'total_alerts': len(recent_alerts),
            'active_alerts': len(self.get_active_alerts()),
            'severity_breakdown': severity_counts,
            'time_range_hours': time_range_hours,
            'most_frequent_alerts': self._get_most_frequent_alerts(recent_alerts)
        }
    
    def _get_most_frequent_alerts(self, alerts: List[Alert]) -> List[Dict[str, Any]]:
        """Get most frequently triggered alerts"""
        rule_counts = {}
        for alert in alerts:
            rule_counts[alert.rule_name] = rule_counts.get(alert.rule_name, 0) + 1
        
        return [
            {'rule_name': rule, 'count': count}
            for rule, count in sorted(rule_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        ]


class KPITracker:
    """Track business KPIs and operational metrics"""
    
    def __init__(self):
        """Initialize KPI tracker"""
        self.metrics_collector = get_metrics_collector()
        self.task_monitor = get_task_sequence_monitor()
        self.performance_monitor = get_performance_monitor()
    
    def track_user_activity(self, user_id: str, activity_type: str, 
                           correlation_id: str = None, context: Dict[str, Any] = None):
        """Track user activity for business metrics"""
        properties = {
            'user_id': user_id,
            'activity_type': activity_type
        }
        if context:
            properties.update(context)
        
        self.metrics_collector.record_custom_event(
            'user_activity', 
            properties=properties,
            correlation_id=correlation_id
        )
    
    def track_business_event(self, event_name: str, properties: Dict[str, Any] = None,
                           measurements: Dict[str, float] = None, correlation_id: str = None):
        """Track custom business events"""
        self.metrics_collector.record_custom_event(
            f'business.{event_name}',
            properties=properties,
            measurements=measurements,
            correlation_id=correlation_id
        )
    
    def get_operational_kpis(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get operational KPIs"""
        task_summary = self.task_monitor.get_task_summary(time_range_hours)
        sequence_summary = self.task_monitor.get_sequence_summary(time_range_hours)
        health_status = self.performance_monitor.get_health_status()
        
        return {
            'task_success_rate': task_summary['success_rate'],
            'sequence_success_rate': sequence_summary['success_rate'],
            'average_task_duration': task_summary['average_duration_seconds'],
            'average_sequence_duration': sequence_summary['average_duration_seconds'],
            'system_health_score': health_status.get('system', {}).get('cpu_percent', 0),
            'active_sequences': sequence_summary['running_sequences'],
            'total_tasks_processed': task_summary['total_tasks'],
            'total_sequences_processed': sequence_summary['total_sequences'],
            'time_range_hours': time_range_hours
        }
    
    def get_business_kpis(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get business-focused KPIs"""
        # This would integrate with actual business metrics
        # For now, providing structure for future implementation
        return {
            'user_engagement_score': 85.0,  # Placeholder
            'feature_adoption_rate': 72.0,  # Placeholder
            'customer_satisfaction_score': 4.2,  # Placeholder
            'time_to_completion': 180.0,  # Average seconds
            'error_impact_score': 15.0,  # Percentage
            'time_range_hours': time_range_hours
        }


# Global instances
_alert_manager = None
_kpi_tracker = None

def get_alert_manager() -> AlertManager:
    """Get global alert manager instance"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager()
    return _alert_manager

def get_kpi_tracker() -> KPITracker:
    """Get global KPI tracker instance"""
    global _kpi_tracker
    if _kpi_tracker is None:
        _kpi_tracker = KPITracker()
    return _kpi_tracker

def setup_monitoring_dashboards():
    """Set up monitoring dashboards and start alert monitoring"""
    try:
        # Start alert monitoring
        alert_manager = get_alert_manager()
        alert_manager.start_monitoring()
        
        # Add default notification handlers
        alert_manager.add_notification_handler(_log_alert_notification)
        
        logger.info("Monitoring dashboards and alerting configured successfully")
        
    except Exception as e:
        logger.error(f"Error setting up monitoring dashboards: {e}")

def _log_alert_notification(alert: Alert):
    """Default alert notification handler that logs alerts"""
    severity_emoji = {
        AlertSeverity.INFO: "ℹ️",
        AlertSeverity.WARNING: "⚠️",
        AlertSeverity.CRITICAL: "🚨",
        AlertSeverity.ERROR: "❌"
    }
    
    emoji = severity_emoji.get(alert.severity, "📊")
    
    if alert.status == AlertStatus.ACTIVE:
        logger.warning(f"{emoji} ALERT TRIGGERED: {alert.message}")
    elif alert.status == AlertStatus.RESOLVED:
        logger.info(f"✅ ALERT RESOLVED: {alert.rule_name}")
    elif alert.status == AlertStatus.ACKNOWLEDGED:
        logger.info(f"👍 ALERT ACKNOWLEDGED: {alert.rule_name} by {alert.acknowledged_by}")

def cleanup_monitoring():
    """Cleanup monitoring resources"""
    try:
        alert_manager = get_alert_manager()
        alert_manager.stop_monitoring()
        logger.info("Monitoring cleanup completed")
    except Exception as e:
        logger.error(f"Error during monitoring cleanup: {e}")