"""
Task Coordination Service Module

This module provides centralized task coordination for the queue-based task processing system.
It orchestrates task creation, execution context management, and queue enqueueing.

Features:
- Create and manage execution contexts
- Coordinate parent-child task relationships
- Handle task enqueueing with proper context
- Support for priority-based task routing
- Integration with Azure Storage Queues

Best practices implemented:
- Single responsibility principle
- Comprehensive error handling
- Structured logging with correlation IDs
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional, List

# Import shared modules
from src.shared.azure_services import get_queue_client
# Import shared modules
from src.shared.azure_services import get_queue_client
from src.shared.config import get_storage_connection_string
from src.shared.common import is_local_dev
from src.shared.monitoring import get_task_monitor, get_structured_logger
from src.shared.config import get_storage_connection_string
from src.shared.common import is_local_dev
from src.shared.monitoring import get_task_monitor, get_structured_logger

# Configure module-level logger
logger = logging.getLogger(__name__)

# Queue configuration constants
PRIORITY_QUEUES = {
    "high": "task-queue-high",
    "medium": "task-queue-medium",
    "low": "task-queue-low"
}

# Message processing constants
MAX_MESSAGE_SIZE = 64 * 1024  # 64KB limit for Azure Storage Queue messages
MESSAGE_TTL = 604800  # 7 days in seconds


class TaskCoordinationService:
    """
    Service class for coordinating task creation and execution
    
    This service provides a centralized interface for:
    - Creating execution contexts
    - Managing parent-child task relationships
    - Enqueueing tasks to appropriate queues
    - Tracking task lifecycle
    """
    
    def __init__(self, task_status_service=None, queue_client=None):
        """
        Initialize the task coordination service
        
        Args:
            task_status_service: Optional TaskStatusService instance
            queue_client: Optional queue client for testing
        """
        self._task_status_service = task_status_service
        self.queue_clients = {}
        self.connection_string = get_storage_connection_string()
        self.is_local = is_local_dev()
        self.task_monitor = get_task_monitor()
        self.logger = get_structured_logger("TaskCoordinationService")
        
        # Initialize queue clients
        self._initialize_queue_clients()
        
        self.logger.info("Task coordination service initialized", {
            "environment": "local" if self.is_local else "production"
        })
    
    @property
    def task_status_service(self):
        """Lazy-loaded task status service to avoid circular imports"""
        if self._task_status_service is None:
            from src.shared.task_status_service import get_task_status_service
            self._task_status_service = get_task_status_service()
        return self._task_status_service
    
    def _initialize_queue_clients(self):
        """Initialize queue clients for all priority levels"""
        try:
            from azure.storage.queue import QueueClient
            
            for priority, queue_name in PRIORITY_QUEUES.items():
                try:
                    client = QueueClient.from_connection_string(
                        conn_str=self.connection_string,
                        queue_name=queue_name
                    )
                    # Verify queue exists
                    client.get_queue_properties()
                    self.queue_clients[priority] = client
                    logger.debug(f"Initialized queue client for {priority} priority: {queue_name}")
                except Exception as e:
                    logger.warning(f"Could not initialize queue client for {priority}: {str(e)}")
                    
        except ImportError:
            logger.error("Azure Storage Queue package not available")
            self.queue_clients = {}
    
    def initiate_scan_task(
        self,
        org_id: str,
        user_id: str,
        task_type: str,
        params: Dict[str, Any],
        priority: str = "medium"
    ) -> Optional[str]:
        """
        Initiate a new scan task with full coordination
        
        Args:
            org_id: Organization ID
            user_id: User ID who initiated the task
            task_type: Type of scan task
            params: Task parameters
            priority: Task priority (high, medium, low)
            
        Returns:
            str: Execution log ID if successful, None otherwise
        """
        try:
            self.logger.info("Initiating scan task", {
                "org_id": org_id,
                "user_id": user_id,
                "task_type": task_type,
                "priority": priority
            })
            
            # Create execution context
            execution_log_id = self.create_execution_context(org_id, user_id, task_type)
            if not execution_log_id:
                self.logger.error("Failed to create execution context", {
                    "org_id": org_id,
                    "task_type": task_type
                })
                return None
            
            # Start monitoring
            self.task_monitor.start_task(execution_log_id, task_type, execution_log_id)
            
            # Create parent task with explicit task_id
            parent_task_id = self.task_status_service.create_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                priority=priority,
                execution_log_id=execution_log_id,
                params={
                    **params,
                    "is_parent_task": True,
                    "parent_execution_log_id": execution_log_id,
                    "initiated_at": datetime.now().isoformat()
                }
            )
            
            if not parent_task_id:
                self.logger.error("Failed to create parent task", {
                    "execution_log_id": execution_log_id,
                    "task_type": task_type
                })
                self.task_monitor.complete_task(execution_log_id, False)
                return None
            
            # Prepare task data for queue with explicit task_id
            task_data = {
                "task_id": parent_task_id,
                "execution_log_id": execution_log_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "priority": priority,
                "params": params,
                "created_at": datetime.now().isoformat(),
                "is_parent_task": True
            }
            
            # Log the task data being enqueued
            self.logger.info(f"Enqueuing task with data: {json.dumps(task_data, default=str)}")
            
            # Enqueue task
            if self.enqueue_with_context(execution_log_id, task_data):
                self.logger.info("Successfully initiated scan task", {
                    "execution_log_id": execution_log_id,
                    "parent_task_id": parent_task_id,
                    "priority": priority
                })
                self.task_monitor.complete_task(execution_log_id, True, {
                    "parent_task_id": parent_task_id,
                    "priority": priority
                })
                
                # Verify task was created by checking task status
                try:
                    task_status = self.task_status_service.get_task(parent_task_id)
                    if task_status and task_status.get("TaskId") == parent_task_id:
                        self.logger.info(f"Task verification successful: {parent_task_id}")
                    else:
                        self.logger.warning(f"Task created but verification failed: {parent_task_id}")
                except Exception as verify_error:
                    self.logger.warning(f"Error verifying task: {verify_error}")
                
                return execution_log_id
            else:
                self.logger.error("Failed to enqueue task", {
                    "execution_log_id": execution_log_id,
                    "parent_task_id": parent_task_id
                })
                self.task_monitor.complete_task(execution_log_id, False)
                return None
                
        except Exception as e:
            self.logger.error("Error initiating scan task", {
                "error": str(e),
                "org_id": org_id,
                "task_type": task_type
            })
            if 'execution_log_id' in locals():
                self.task_monitor.complete_task(execution_log_id, False, {"error": str(e)})
            return None
    
    def create_execution_context(
        self,
        org_id: str,
        user_id: str,
        task_type: str,
        parent_execution_log_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a new execution context
        
        Args:
            org_id: Organization ID
            user_id: User ID
            task_type: Type of task
            parent_execution_log_id: Optional parent execution log ID
            
        Returns:
            str: Execution log ID if successful, None otherwise
        """
        try:
            execution_log_id = str(uuid.uuid4())
            
            logger.info(f"Creating execution context: {execution_log_id} for org={org_id}, type={task_type}")
            
            # For now, we'll just return the execution log ID
            # In a full implementation, this would create a record in ExecutionContext table
            return execution_log_id
            
        except Exception as e:
            logger.error(f"Error creating execution context: {str(e)}")
            return None
    
    def enqueue_with_context(
        self,
        execution_log_id: str,
        task_data: Dict[str, Any]
    ) -> bool:
        """
        Enqueue a task with execution context
        
        Args:
            execution_log_id: Execution log ID for context
            task_data: Task data to enqueue
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            priority = task_data.get("priority", "medium")
            
            if priority not in self.queue_clients:
                logger.error(f"Invalid priority: {priority}")
                return False
            
            queue_client = self.queue_clients[priority]
            if not queue_client:
                logger.error(f"Queue client not available for priority: {priority}")
                return False
            
            # Ensure execution_log_id is in task data
            task_data["execution_log_id"] = execution_log_id
            
            # Serialize task data
            message_content = json.dumps(task_data)
            
            # Check message size
            if len(message_content.encode('utf-8')) > MAX_MESSAGE_SIZE:
                logger.error(f"Task message too large: {len(message_content.encode('utf-8'))} bytes")
                return False
            
            # Send message to queue
            queue_client.send_message(
                content=message_content,
                time_to_live=MESSAGE_TTL
            )
            
            logger.info(f"Enqueued task: execution_log_id={execution_log_id}, priority={priority}")
            return True
            
        except Exception as e:
            logger.error(f"Error enqueuing task: {str(e)}")
            return False
    
    def create_child_tasks(
        self,
        parent_execution_log_id: str,
        parent_task_id: str,
        child_tasks: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Create child tasks with proper execution context
        
        Args:
            parent_execution_log_id: Parent execution log ID
            parent_task_id: Parent task ID
            child_tasks: List of child task data
            
        Returns:
            List[str]: List of created child task IDs
        """
        try:
            created_child_task_ids = []
            
            for child_task in child_tasks:
                try:
                    # Ensure required fields
                    child_task_type = child_task.get("task_type")
                    child_org_id = child_task.get("org_id")
                    child_user_id = child_task.get("user_id")
                    
                    if not all([child_task_type, child_org_id, child_user_id]):
                        logger.error(f"Missing required fields in child task: {child_task}")
                        continue
                    
                    # Create child task
                    child_task_id = self.task_status_service.create_task(
                        task_type=child_task_type,
                        org_id=child_org_id,
                        user_id=child_user_id,
                        priority=child_task.get("priority", "medium"),
                        execution_log_id=parent_execution_log_id,
                        params={
                            **child_task.get("params", {}),
                            "parent_execution_log_id": parent_execution_log_id,
                            "parent_task_id": parent_task_id,
                            "is_child_task": True
                        }
                    )
                    
                    if child_task_id:
                        created_child_task_ids.append(child_task_id)
                        logger.info(f"Created child task: {child_task_id} for parent={parent_task_id}")
                    else:
                        logger.error(f"Failed to create child task for parent={parent_task_id}")
                        
                except Exception as e:
                    logger.error(f"Error creating child task: {str(e)}")
                    continue
            
            logger.info(f"Created {len(created_child_task_ids)} child tasks for parent={parent_task_id}")
            return created_child_task_ids
            
        except Exception as e:
            logger.error(f"Error creating child tasks: {str(e)}")
            return []
    
    def get_task_status(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of all tasks for an execution log
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict: Task status summary
        """
        try:
            # This would query the database for all tasks with this execution_log_id
            # For now, return a basic structure
            return {
                "execution_log_id": execution_log_id,
                "total_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "pending_tasks": 0,
                "status": "unknown"
            }
            
        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return None


# Global service instance
_task_coordination_service = None


def get_task_coordination_service() -> TaskCoordinationService:
    """
    Get the global task coordination service instance
    
    Returns:
        TaskCoordinationService: The service instance
    """
    global _task_coordination_service
    
    if _task_coordination_service is None:
        _task_coordination_service = TaskCoordinationService()
        logger.debug("Created global task coordination service instance")
    
    return _task_coordination_service