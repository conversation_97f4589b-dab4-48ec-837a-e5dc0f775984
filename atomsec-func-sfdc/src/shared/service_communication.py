"""
Service Communication Utilities

This module provides utilities for inter-service communication in the microservices architecture.
It handles communication between the SFDC service and other services (DB service, etc.).
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import aiohttp
import azure.functions as func

logger = logging.getLogger(__name__)

class ServiceType(Enum):
    """Enumeration of service types"""
    DB_SERVICE = "db_service"
    SFDC_SERVICE = "sfdc_service"
    AUTH_SERVICE = "auth_service"

@dataclass
class ServiceEndpoint:
    """Service endpoint configuration"""
    service_type: ServiceType
    base_url: str
    health_endpoint: str = "/health"
    timeout: int = 30
    retry_count: int = 3

@dataclass
class ServiceRequest:
    """Service request configuration"""
    method: str
    endpoint: str
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict[str, Any]] = None
    params: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

@dataclass
class ServiceResponse:
    """Service response wrapper"""
    status_code: int
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    success: bool = False

class ServiceCommunicationManager:
    """Manages communication between microservices"""
    
    def __init__(self):
        self.endpoints = self._load_service_endpoints()
        self.session: Optional[aiohttp.ClientSession] = None
    
    def _load_service_endpoints(self) -> Dict[ServiceType, ServiceEndpoint]:
        """Load service endpoint configurations"""
        from src.shared.azure_services import is_local_dev, get_secret
        
        endpoints = {}
        
        if is_local_dev():
            # Local development endpoints
            endpoints[ServiceType.DB_SERVICE] = ServiceEndpoint(
                service_type=ServiceType.DB_SERVICE,
                base_url="http://localhost:7072/api/db"
            )
            endpoints[ServiceType.SFDC_SERVICE] = ServiceEndpoint(
                service_type=ServiceType.SFDC_SERVICE,
                base_url="http://localhost:7071/api"
            )
            endpoints[ServiceType.AUTH_SERVICE] = ServiceEndpoint(
                service_type=ServiceType.AUTH_SERVICE,
                base_url="http://localhost:7073/api/auth"
            )
        else:
            # Production endpoints from Key Vault
            try:
                db_service_url = get_secret("db-service-url")
                sfdc_service_url = get_secret("sfdc-service-url")
                auth_service_url = get_secret("auth-service-url")
                
                if db_service_url:
                    endpoints[ServiceType.DB_SERVICE] = ServiceEndpoint(
                        service_type=ServiceType.DB_SERVICE,
                        base_url=f"{db_service_url}/api/db"
                    )
                
                if sfdc_service_url:
                    endpoints[ServiceType.SFDC_SERVICE] = ServiceEndpoint(
                        service_type=ServiceType.SFDC_SERVICE,
                        base_url=f"{sfdc_service_url}/api"
                    )
                
                if auth_service_url:
                    endpoints[ServiceType.AUTH_SERVICE] = ServiceEndpoint(
                        service_type=ServiceType.AUTH_SERVICE,
                        base_url=f"{auth_service_url}/api/auth"
                    )
                    
            except Exception as e:
                logger.error(f"Error loading service endpoints from Key Vault: {str(e)}")
        
        return endpoints
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def _close_session(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def send_request(
        self, 
        service_type: ServiceType, 
        request: ServiceRequest
    ) -> ServiceResponse:
        """Send request to a service"""
        
        if service_type not in self.endpoints:
            return ServiceResponse(
                status_code=503,
                error=f"Service endpoint not configured: {service_type.value}",
                success=False
            )
        
        endpoint_config = self.endpoints[service_type]
        url = f"{endpoint_config.base_url}{request.endpoint}"
        
        # Prepare headers
        headers = request.headers or {}
        headers.setdefault("Content-Type", "application/json")
        headers.setdefault("User-Agent", "AtomSec-ServiceCommunication/1.0")
        
        # Add internal service authentication header
        headers["X-Internal-Service"] = "sfdc-service"
        
        session = await self._get_session()
        
        try:
            timeout = request.timeout or endpoint_config.timeout
            
            async with session.request(
                method=request.method,
                url=url,
                headers=headers,
                json=request.data,
                params=request.params,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                
                response_headers = dict(response.headers)
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"message": await response.text()}
                
                return ServiceResponse(
                    status_code=response.status,
                    data=response_data,
                    headers=response_headers,
                    success=200 <= response.status < 300
                )
                
        except asyncio.TimeoutError:
            logger.error(f"Timeout calling {service_type.value} at {url}")
            return ServiceResponse(
                status_code=408,
                error="Request timeout",
                success=False
            )
        except Exception as e:
            logger.error(f"Error calling {service_type.value} at {url}: {str(e)}")
            return ServiceResponse(
                status_code=500,
                error=str(e),
                success=False
            )
    
    async def check_service_health(self, service_type: ServiceType) -> ServiceResponse:
        """Check health of a service"""
        endpoint_config = self.endpoints.get(service_type)
        if not endpoint_config:
            return ServiceResponse(
                status_code=503,
                error=f"Service not configured: {service_type.value}",
                success=False
            )
        
        request = ServiceRequest(
            method="GET",
            endpoint=endpoint_config.health_endpoint,
            timeout=10
        )
        
        return await self.send_request(service_type, request)
    
    async def proxy_request_to_db_service(
        self, 
        endpoint: str, 
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> ServiceResponse:
        """Proxy request to DB service"""
        
        request = ServiceRequest(
            method=method,
            endpoint=endpoint,
            data=data,
            headers=headers,
            params=params
        )
        
        return await self.send_request(ServiceType.DB_SERVICE, request)
    
    async def notify_task_completion(
        self, 
        task_id: str, 
        status: str, 
        result: Optional[Dict[str, Any]] = None
    ) -> ServiceResponse:
        """Notify DB service of task completion"""
        
        data = {
            "task_id": task_id,
            "status": status,
            "result": result
        }
        
        request = ServiceRequest(
            method="PUT",
            endpoint=f"/tasks/{task_id}/status",
            data=data
        )
        
        return await self.send_request(ServiceType.DB_SERVICE, request)
    
    async def get_integration_details(self, integration_id: str) -> ServiceResponse:
        """Get integration details from DB service"""
        
        request = ServiceRequest(
            method="GET",
            endpoint=f"/integrations/{integration_id}"
        )
        
        return await self.send_request(ServiceType.DB_SERVICE, request)
    
    async def update_integration_health(
        self, 
        integration_id: str, 
        health_score: int,
        last_scan: str
    ) -> ServiceResponse:
        """Update integration health in DB service"""
        
        data = {
            "health_score": health_score,
            "last_scan": last_scan
        }
        
        request = ServiceRequest(
            method="PUT",
            endpoint=f"/integrations/{integration_id}/health",
            data=data
        )
        
        return await self.send_request(ServiceType.DB_SERVICE, request)
    
    def __del__(self):
        """Cleanup on destruction"""
        if self.session and not self.session.closed:
            try:
                asyncio.create_task(self._close_session())
            except:
                pass

# Global service communication manager instance
_service_comm_manager: Optional[ServiceCommunicationManager] = None

def get_service_communication_manager() -> ServiceCommunicationManager:
    """Get global service communication manager instance"""
    global _service_comm_manager
    if _service_comm_manager is None:
        _service_comm_manager = ServiceCommunicationManager()
    return _service_comm_manager

# Convenience functions for common operations
async def call_db_service(
    endpoint: str,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None
) -> ServiceResponse:
    """Convenience function to call DB service"""
    manager = get_service_communication_manager()
    return await manager.proxy_request_to_db_service(endpoint, method, data, headers)

async def check_all_services_health() -> Dict[ServiceType, ServiceResponse]:
    """Check health of all configured services"""
    manager = get_service_communication_manager()
    results = {}
    
    for service_type in manager.endpoints.keys():
        results[service_type] = await manager.check_service_health(service_type)
    
    return results

def create_internal_request_headers(source_service: str = "sfdc-service") -> Dict[str, str]:
    """Create headers for internal service requests"""
    return {
        "X-Internal-Service": source_service,
        "Content-Type": "application/json",
        "User-Agent": f"AtomSec-{source_service}/1.0"
    }

def convert_azure_request_to_service_request(
    req: func.HttpRequest,
    endpoint: str
) -> ServiceRequest:
    """Convert Azure Function request to service request"""
    
    # Extract headers (excluding Azure-specific ones)
    headers = {}
    for key, value in req.headers.items():
        if not key.startswith("X-Azure-") and key.lower() not in ["host", "content-length"]:
            headers[key] = value
    
    # Add internal service header
    headers.update(create_internal_request_headers())
    
    # Extract request data
    data = None
    if req.method in ["POST", "PUT", "PATCH"]:
        try:
            data = req.get_json()
        except:
            pass
    
    # Extract query parameters
    params = dict(req.params) if req.params else None
    
    return ServiceRequest(
        method=req.method,
        endpoint=endpoint,
        headers=headers,
        data=data,
        params=params
    )

def convert_service_response_to_azure_response(
    service_response: ServiceResponse
) -> func.HttpResponse:
    """Convert service response to Azure Function response"""
    
    headers = service_response.headers or {}
    
    # Add CORS headers for browser requests
    headers.update({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"
    })
    
    body = ""
    if service_response.data:
        body = json.dumps(service_response.data)
    elif service_response.error:
        body = json.dumps({"error": service_response.error, "success": False})
    
    return func.HttpResponse(
        body=body,
        status_code=service_response.status_code,
        headers=headers,
        mimetype="application/json"
    )