"""
Error Handler Module

This module provides comprehensive error handling and retry mechanisms for the queue-based task processing system.
It includes retry strategies, error categorization, and recovery mechanisms.

Features:
- Comprehensive error categorization
- Retry strategies with exponential backoff
- Circuit breaker pattern
- Error logging and monitoring
- Recovery mechanisms
"""

import logging
import time
import json
import os
import sys
import threading
import uuid
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from functools import wraps
import traceback

# Configure module-level logger
logger = logging.getLogger(__name__)

class ErrorCategory:
    """Error categories for proper handling"""
    TRANSIENT = "transient"  # Temporary issues (network, service unavailable)
    PERMANENT = "permanent"  # Permanent issues (invalid data, auth failures)
    RETRYABLE = "retryable"  # Issues that can be retried
    NON_RETRYABLE = "non_retryable"  # Issues that should not be retried
    TIMEOUT = "timeout"  # Timeout-related errors
    RATE_LIMIT = "rate_limit"  # Rate limiting errors
    RESOURCE_EXHAUSTED = "resource_exhausted"  # Resource exhaustion errors

class RetryStrategy:
    """Retry strategy types"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    FIBONACCI_BACKOFF = "fibonacci_backoff"

class RetryConfig:
    """Enhanced configuration for retry mechanisms"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_multiplier: float = 2.0,
                 jitter: bool = True, strategy: str = RetryStrategy.EXPONENTIAL_BACKOFF,
                 timeout_per_attempt: float = None, retryable_exceptions: List[type] = None,
                 non_retryable_exceptions: List[type] = None):
        """
        Initialize enhanced retry configuration
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds
            max_delay: Maximum delay between retries in seconds
            backoff_multiplier: Multiplier for exponential backoff
            jitter: Whether to add random jitter to delays
            strategy: Retry strategy to use
            timeout_per_attempt: Timeout for each attempt
            retryable_exceptions: List of exception types that should be retried
            non_retryable_exceptions: List of exception types that should not be retried
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_multiplier = backoff_multiplier
        self.jitter = jitter
        self.strategy = strategy
        self.timeout_per_attempt = timeout_per_attempt
        self.retryable_exceptions = retryable_exceptions or []
        self.non_retryable_exceptions = non_retryable_exceptions or []

class RetryMetrics:
    """Metrics for retry operations"""
    
    def __init__(self):
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        self.total_attempts = 0
        self.retry_attempts = 0
        self.operation_history = []
        self.success_rates_by_attempt = {}
    
    def record_operation_start(self, operation_name: str):
        """Record start of operation"""
        self.total_operations += 1
        return {
            'operation_name': operation_name,
            'start_time': datetime.now(),
            'attempts': 0
        }
    
    def record_attempt(self, operation_context: Dict, success: bool, attempt_number: int):
        """Record attempt result"""
        self.total_attempts += 1
        operation_context['attempts'] = attempt_number
        
        if attempt_number > 1:
            self.retry_attempts += 1
        
        if success:
            self.successful_operations += 1
            operation_context['success'] = True
            operation_context['end_time'] = datetime.now()
            
            # Track success rate by attempt number
            if attempt_number not in self.success_rates_by_attempt:
                self.success_rates_by_attempt[attempt_number] = {'successes': 0, 'total': 0}
            self.success_rates_by_attempt[attempt_number]['successes'] += 1
            self.success_rates_by_attempt[attempt_number]['total'] += 1
            
            self.operation_history.append(operation_context)
        else:
            if attempt_number not in self.success_rates_by_attempt:
                self.success_rates_by_attempt[attempt_number] = {'successes': 0, 'total': 0}
            self.success_rates_by_attempt[attempt_number]['total'] += 1
    
    def record_operation_failure(self, operation_context: Dict):
        """Record final operation failure"""
        self.failed_operations += 1
        operation_context['success'] = False
        operation_context['end_time'] = datetime.now()
        self.operation_history.append(operation_context)
    
    def get_success_rate(self) -> float:
        """Get overall success rate"""
        if self.total_operations == 0:
            return 0.0
        return self.successful_operations / self.total_operations
    
    def get_retry_effectiveness(self) -> Dict[str, Any]:
        """Get retry effectiveness metrics"""
        effectiveness = {}
        for attempt, data in self.success_rates_by_attempt.items():
            if data['total'] > 0:
                effectiveness[f"attempt_{attempt}"] = {
                    'success_rate': data['successes'] / data['total'],
                    'total_attempts': data['total']
                }
        return effectiveness
    
    def get_summary(self) -> Dict[str, Any]:
        """Get metrics summary"""
        return {
            'total_operations': self.total_operations,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'success_rate': self.get_success_rate(),
            'total_attempts': self.total_attempts,
            'retry_attempts': self.retry_attempts,
            'avg_attempts_per_operation': self.total_attempts / max(self.total_operations, 1),
            'retry_effectiveness': self.get_retry_effectiveness()
        }

class EnhancedRetryPolicy:
    """Enhanced retry policy with intelligent backoff and monitoring"""
    
    def __init__(self, config: RetryConfig):
        """Initialize enhanced retry policy"""
        self.config = config
        self.metrics = RetryMetrics()
        self._fibonacci_cache = [1, 1]
    
    def should_retry(self, error: Exception, attempt: int) -> bool:
        """Enhanced retry decision logic"""
        if attempt >= self.config.max_attempts:
            return False
        
        # Check non-retryable exceptions first
        if self.config.non_retryable_exceptions:
            if any(isinstance(error, exc_type) for exc_type in self.config.non_retryable_exceptions):
                return False
        
        # Check retryable exceptions
        if self.config.retryable_exceptions:
            if any(isinstance(error, exc_type) for exc_type in self.config.retryable_exceptions):
                return True
        
        # Fall back to error categorization
        error_str = str(error).lower()
        
        # Non-retryable conditions
        non_retryable_indicators = [
            'authentication', 'authorization', 'forbidden', '401', '403',
            'not found', '404', 'bad request', '400', 'malformed',
            'invalid parameter', 'validation error'
        ]
        
        if any(indicator in error_str for indicator in non_retryable_indicators):
            return False
        
        # Retryable conditions
        retryable_indicators = [
            'timeout', 'connection', 'network', 'service unavailable',
            'temporary', 'transient', '503', '504', '502', '429',
            'rate limit', 'throttle', 'resource exhausted'
        ]
        
        return any(indicator in error_str for indicator in retryable_indicators)
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay using configured strategy"""
        if self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * attempt
        elif self.config.strategy == RetryStrategy.FIBONACCI_BACKOFF:
            delay = self.config.base_delay * self._get_fibonacci(attempt)
        else:  # Default to exponential backoff
            delay = self.config.base_delay * (self.config.backoff_multiplier ** (attempt - 1))
        
        # Apply maximum delay limit
        delay = min(delay, self.config.max_delay)
        
        # Add jitter if enabled
        if self.config.jitter:
            import random
            jitter_factor = 0.1 + random.random() * 0.1  # 10-20% jitter
            delay = delay * (1 + jitter_factor)
        
        return delay
    
    def _get_fibonacci(self, n: int) -> int:
        """Get nth Fibonacci number (cached)"""
        while len(self._fibonacci_cache) <= n:
            self._fibonacci_cache.append(
                self._fibonacci_cache[-1] + self._fibonacci_cache[-2]
            )
        return self._fibonacci_cache[n]
    
    def execute_with_retry(self, operation: Callable, operation_name: str, 
                          *args, **kwargs) -> Any:
        """Execute operation with enhanced retry logic"""
        operation_context = self.metrics.record_operation_start(operation_name)
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                # Apply per-attempt timeout if configured
                if self.config.timeout_per_attempt:
                    import signal
                    
                    def timeout_handler(signum, frame):
                        raise TimeoutError(f"Operation timed out after {self.config.timeout_per_attempt} seconds")
                    
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(int(self.config.timeout_per_attempt))
                
                result = operation(*args, **kwargs)
                
                if self.config.timeout_per_attempt:
                    signal.alarm(0)  # Cancel timeout
                
                self.metrics.record_attempt(operation_context, True, attempt)
                logger.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                return result
                
            except Exception as e:
                if self.config.timeout_per_attempt:
                    signal.alarm(0)  # Cancel timeout
                
                last_exception = e
                self.metrics.record_attempt(operation_context, False, attempt)
                
                logger.warning(f"Operation {operation_name} failed on attempt {attempt}: {e}")
                
                if not self.should_retry(e, attempt):
                    logger.info(f"Not retrying {operation_name} due to error type: {type(e).__name__}")
                    break
                
                if attempt < self.config.max_attempts:
                    delay = self.calculate_delay(attempt)
                    logger.info(f"Retrying {operation_name} in {delay:.2f} seconds (attempt {attempt + 1}/{self.config.max_attempts})")
                    time.sleep(delay)
        
        self.metrics.record_operation_failure(operation_context)
        raise last_exception
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get retry policy metrics"""
        return self.metrics.get_summary()
    
    def optimize_config(self) -> RetryConfig:
        """Suggest optimized configuration based on metrics"""
        metrics = self.metrics.get_summary()
        effectiveness = self.metrics.get_retry_effectiveness()
        
        # Suggest optimizations based on success patterns
        optimized_config = RetryConfig(
            max_attempts=self.config.max_attempts,
            base_delay=self.config.base_delay,
            max_delay=self.config.max_delay,
            backoff_multiplier=self.config.backoff_multiplier,
            jitter=self.config.jitter,
            strategy=self.config.strategy
        )
        
        # Adjust max attempts based on effectiveness
        if metrics['success_rate'] > 0.9 and metrics['avg_attempts_per_operation'] < 1.5:
            # High success rate with few retries - can reduce max attempts
            optimized_config.max_attempts = max(2, self.config.max_attempts - 1)
        elif metrics['success_rate'] < 0.7 and 'attempt_3' in effectiveness:
            # Low success rate but attempt 3 shows improvement - increase max attempts
            optimized_config.max_attempts = min(5, self.config.max_attempts + 1)
        
        return optimized_config

class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60,
                 success_threshold: int = 3, monitoring_window: int = 300,
                 auto_recovery: bool = True, health_check_interval: int = 30):
        """
        Initialize circuit breaker configuration
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time in seconds before attempting recovery
            success_threshold: Number of successes needed to close circuit from half-open
            monitoring_window: Time window in seconds for failure rate calculation
            auto_recovery: Whether to enable automatic recovery attempts
            health_check_interval: Interval in seconds for health checks
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        self.monitoring_window = monitoring_window
        self.auto_recovery = auto_recovery
        self.health_check_interval = health_check_interval

class CircuitBreakerState:
    """Circuit breaker state tracking"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half-open"

class CircuitBreakerMetrics:
    """Circuit breaker metrics for monitoring"""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.state_changes = []
        self.last_failure_time = None
        self.last_success_time = None
        self.recovery_attempts = 0
        self.health_check_results = []
    
    def record_request(self, success: bool):
        """Record a request result"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            self.last_success_time = datetime.now()
        else:
            self.failed_requests += 1
            self.last_failure_time = datetime.now()
    
    def record_state_change(self, old_state: str, new_state: str):
        """Record state change"""
        self.state_changes.append({
            'timestamp': datetime.now(),
            'from_state': old_state,
            'to_state': new_state
        })
    
    def record_health_check(self, success: bool, response_time: float = None):
        """Record health check result"""
        self.health_check_results.append({
            'timestamp': datetime.now(),
            'success': success,
            'response_time': response_time
        })
        
        # Keep only last 100 health check results
        if len(self.health_check_results) > 100:
            self.health_check_results = self.health_check_results[-100:]
    
    def get_failure_rate(self, window_seconds: int = 300) -> float:
        """Calculate failure rate within time window"""
        if self.total_requests == 0:
            return 0.0
        
        cutoff_time = datetime.now() - timedelta(seconds=window_seconds)
        
        # For simplicity, return overall failure rate
        # In production, you'd track time-windowed metrics
        return self.failed_requests / self.total_requests
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary"""
        recent_checks = [
            check for check in self.health_check_results
            if (datetime.now() - check['timestamp']).seconds < 300
        ]
        
        if not recent_checks:
            return {'status': 'unknown', 'recent_checks': 0}
        
        successful_checks = sum(1 for check in recent_checks if check['success'])
        health_rate = successful_checks / len(recent_checks)
        
        return {
            'status': 'healthy' if health_rate > 0.8 else 'unhealthy',
            'health_rate': health_rate,
            'recent_checks': len(recent_checks),
            'avg_response_time': sum(
                check.get('response_time', 0) for check in recent_checks
                if check.get('response_time')
            ) / len([c for c in recent_checks if c.get('response_time')])
        }

class EnhancedCircuitBreaker:
    """Enhanced circuit breaker with service-specific configurations and monitoring"""
    
    def __init__(self, service_name: str, config: CircuitBreakerConfig = None):
        """
        Initialize enhanced circuit breaker
        
        Args:
            service_name: Name of the service this circuit breaker protects
            config: Circuit breaker configuration
        """
        self.service_name = service_name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_state_change = datetime.now()
        self.metrics = CircuitBreakerMetrics()
        self.health_check_function = None
        self.last_health_check = None
        
        logger.info(f"Enhanced circuit breaker initialized for service: {service_name}")
    
    def set_health_check_function(self, health_check_func: Callable[[], bool]):
        """Set health check function for auto-recovery"""
        self.health_check_function = health_check_func
    
    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        current_time = datetime.now()
        
        # Perform auto-recovery check if enabled
        if self.config.auto_recovery and self.state == CircuitBreakerState.OPEN:
            self._attempt_auto_recovery(current_time)
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        if self.state == CircuitBreakerState.OPEN:
            if self.last_failure_time and \
               (current_time - self.last_failure_time).seconds >= self.config.recovery_timeout:
                self._transition_to_half_open()
                return True
            return False
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self):
        """Record successful operation"""
        old_state = self.state
        self.metrics.record_request(True)
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._transition_to_closed()
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0  # Reset failure count on success
        
        if old_state != self.state:
            self.metrics.record_state_change(old_state, self.state)
            logger.info(f"Circuit breaker for {self.service_name} transitioned from {old_state} to {self.state}")
    
    def record_failure(self):
        """Record failed operation"""
        old_state = self.state
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        self.metrics.record_request(False)
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            # Immediately open on failure in half-open state
            self._transition_to_open()
        elif self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self._transition_to_open()
        
        if old_state != self.state:
            self.metrics.record_state_change(old_state, self.state)
            logger.warning(f"Circuit breaker for {self.service_name} transitioned from {old_state} to {self.state} after {self.failure_count} failures")
    
    def _transition_to_open(self):
        """Transition to open state"""
        self.state = CircuitBreakerState.OPEN
        self.last_state_change = datetime.now()
        self.success_count = 0
        logger.warning(f"Circuit breaker OPENED for service: {self.service_name}")
    
    def _transition_to_half_open(self):
        """Transition to half-open state"""
        self.state = CircuitBreakerState.HALF_OPEN
        self.last_state_change = datetime.now()
        self.success_count = 0
        self.failure_count = 0
        logger.info(f"Circuit breaker HALF-OPEN for service: {self.service_name}")
    
    def _transition_to_closed(self):
        """Transition to closed state"""
        self.state = CircuitBreakerState.CLOSED
        self.last_state_change = datetime.now()
        self.failure_count = 0
        self.success_count = 0
        logger.info(f"Circuit breaker CLOSED for service: {self.service_name}")
    
    def _attempt_auto_recovery(self, current_time: datetime):
        """Attempt automatic recovery through health checks"""
        if not self.health_check_function:
            return
        
        # Check if enough time has passed since last health check
        if (self.last_health_check and 
            (current_time - self.last_health_check).seconds < self.config.health_check_interval):
            return
        
        self.last_health_check = current_time
        
        try:
            start_time = time.time()
            is_healthy = self.health_check_function()
            response_time = time.time() - start_time
            
            self.metrics.record_health_check(is_healthy, response_time)
            self.metrics.recovery_attempts += 1
            
            if is_healthy:
                logger.info(f"Health check passed for {self.service_name}, attempting recovery")
                self._transition_to_half_open()
            else:
                logger.debug(f"Health check failed for {self.service_name}, remaining open")
                
        except Exception as e:
            logger.error(f"Health check error for {self.service_name}: {e}")
            self.metrics.record_health_check(False)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status"""
        return {
            'service_name': self.service_name,
            'state': self.state,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None,
            'last_state_change': self.last_state_change.isoformat(),
            'failure_rate': self.metrics.get_failure_rate(),
            'total_requests': self.metrics.total_requests,
            'health_summary': self.metrics.get_health_summary(),
            'config': {
                'failure_threshold': self.config.failure_threshold,
                'recovery_timeout': self.config.recovery_timeout,
                'success_threshold': self.config.success_threshold,
                'auto_recovery': self.config.auto_recovery
            }
        }
    
    def reset(self):
        """Reset circuit breaker to closed state"""
        old_state = self.state
        self._transition_to_closed()
        self.metrics.record_state_change(old_state, self.state)
        logger.info(f"Circuit breaker for {self.service_name} manually reset")

# Legacy CircuitBreaker class for backward compatibility
class CircuitBreaker(EnhancedCircuitBreaker):
    """Legacy circuit breaker for backward compatibility"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        config = CircuitBreakerConfig(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout
        )
        super().__init__("legacy", config)

class ErrorHandler:
    """Comprehensive error handler with retry and recovery"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        """Initialize error handler"""
        self.retry_config = retry_config or RetryConfig()
        self.circuit_breakers: Dict[str, EnhancedCircuitBreaker] = {}
        self.error_history: List[Dict[str, Any]] = []
        self.service_configs: Dict[str, CircuitBreakerConfig] = {}
    
    def configure_service_circuit_breaker(self, service_name: str, config: CircuitBreakerConfig):
        """Configure circuit breaker for specific service"""
        self.service_configs[service_name] = config
        if service_name in self.circuit_breakers:
            # Update existing circuit breaker
            self.circuit_breakers[service_name].config = config
    
    def get_circuit_breaker(self, operation_name: str) -> EnhancedCircuitBreaker:
        """Get or create enhanced circuit breaker for operation"""
        if operation_name not in self.circuit_breakers:
            config = self.service_configs.get(operation_name, CircuitBreakerConfig())
            self.circuit_breakers[operation_name] = EnhancedCircuitBreaker(operation_name, config)
        return self.circuit_breakers[operation_name]
    
    def set_health_check_function(self, service_name: str, health_check_func: Callable[[], bool]):
        """Set health check function for service circuit breaker"""
        circuit_breaker = self.get_circuit_breaker(service_name)
        circuit_breaker.set_health_check_function(health_check_func)
    
    def get_all_circuit_breaker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all circuit breakers"""
        return {
            name: cb.get_status() 
            for name, cb in self.circuit_breakers.items()
        }
    
    def categorize_error(self, error: Exception) -> str:
        """Enhanced error categorization for appropriate handling"""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Authentication and authorization errors
        auth_indicators = [
            'authentication', 'authorization', 'unauthorized', 'forbidden',
            '401', '403', 'invalid token', 'expired token', 'access denied',
            'invalid credentials', 'login failed'
        ]
        
        # Validation and parameter errors
        validation_indicators = [
            'validation', 'parameter', 'malformed', 'invalid', 'serialization',
            'decode', 'parse', 'format', 'schema', 'required field',
            'bad request', '400', 'missing parameter'
        ]
        
        # Transient errors
        transient_indicators = [
            'timeout', 'connection', 'network', 'service unavailable',
            'temporary', 'transient', 'retry', '503', '504', '502',
            'connection reset', 'connection refused', 'dns'
        ]
        
        # Rate limit errors
        rate_limit_indicators = [
            'rate limit', 'too many requests', '429', 'throttle',
            'quota exceeded', 'api limit', 'request limit'
        ]
        
        # Resource exhaustion
        resource_indicators = [
            'resource exhausted', 'out of memory', 'disk full',
            'storage full', 'capacity exceeded', 'memory error'
        ]
        
        # Business logic errors
        business_indicators = [
            'business rule', 'workflow', 'approval', 'duplicate',
            'conflict', '409', 'already exists', 'not found', '404'
        ]
        
        # System errors
        system_indicators = [
            'internal server error', '500', 'database error',
            'configuration error', 'service error'
        ]
        
        # Categorize based on error type first
        if error_type in ['valueerror', 'typeerror', 'keyerror']:
            return ErrorCategory.NON_RETRYABLE
        elif error_type in ['connectionerror', 'timeouterror']:
            return ErrorCategory.TRANSIENT
        elif error_type in ['permissionerror', 'authenticationerror']:
            return ErrorCategory.PERMANENT
        
        # Categorize based on error message
        if any(indicator in error_str for indicator in auth_indicators):
            return ErrorCategory.PERMANENT
        elif any(indicator in error_str for indicator in validation_indicators):
            return ErrorCategory.NON_RETRYABLE
        elif any(indicator in error_str for indicator in transient_indicators):
            return ErrorCategory.TRANSIENT
        elif any(indicator in error_str for indicator in rate_limit_indicators):
            return ErrorCategory.RATE_LIMIT
        elif any(indicator in error_str for indicator in resource_indicators):
            return ErrorCategory.RESOURCE_EXHAUSTED
        elif any(indicator in error_str for indicator in business_indicators):
            return ErrorCategory.NON_RETRYABLE
        elif any(indicator in error_str for indicator in system_indicators):
            return ErrorCategory.RETRYABLE
        elif 'timeout' in error_str:
            return ErrorCategory.TIMEOUT
        else:
            return ErrorCategory.PERMANENT
    
    def should_retry(self, error: Exception, attempt: int) -> bool:
        """Determine if operation should be retried"""
        if attempt >= self.retry_config.max_attempts:
            return False
        
        category = self.categorize_error(error)
        return category in [ErrorCategory.TRANSIENT, ErrorCategory.TIMEOUT, 
                           ErrorCategory.RATE_LIMIT, ErrorCategory.RETRYABLE]
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt (legacy method)"""
        delay = self.retry_config.base_delay * (self.retry_config.backoff_multiplier ** attempt)
        delay = min(delay, self.retry_config.max_delay)
        
        if self.retry_config.jitter:
            import random
            delay = delay * (0.5 + random.random() * 0.5)  # Add 50% jitter
        
        return delay
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Enhanced error logging with context enrichment"""
        correlation_id = context.get('correlation_id') if context else str(uuid.uuid4())
        
        error_info = {
            'correlation_id': correlation_id,
            'timestamp': datetime.now().isoformat(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'error_category': self.categorize_error(error),
            'stack_trace': traceback.format_exc(),
            'context': self._enrich_error_context(context or {}),
            'severity': self._determine_error_severity(error),
            'user_impact': self._assess_user_impact(error, context or {}),
            'suggested_action': self._get_suggested_action(error)
        }
        
        self.error_history.append(error_info)
        
        # Keep only last 1000 errors
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-1000:]
        
        # Log with appropriate level based on severity
        if error_info['severity'] == 'critical':
            logger.critical(f"Critical error: {error_info}")
        elif error_info['severity'] == 'high':
            logger.error(f"High severity error: {error_info}")
        elif error_info['severity'] == 'medium':
            logger.warning(f"Medium severity error: {error_info}")
        else:
            logger.info(f"Low severity error: {error_info}")
    
    def _enrich_error_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich error context with additional information"""
        enriched_context = context.copy()
        
        # Add system information
        enriched_context.update({
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'process_id': os.getpid(),
            'thread_id': threading.current_thread().ident,
            'memory_usage': self._get_memory_usage(),
            'environment': os.environ.get('ENVIRONMENT', 'unknown')
        })
        
        # Add request information if available
        if 'request' in context:
            request = context['request']
            enriched_context['request_info'] = {
                'method': getattr(request, 'method', 'unknown'),
                'url': getattr(request, 'url', 'unknown'),
                'headers': dict(getattr(request, 'headers', {})),
                'user_agent': getattr(request, 'headers', {}).get('User-Agent', 'unknown')
            }
        
        return enriched_context
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage information"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss': memory_info.rss,
                'vms': memory_info.vms,
                'percent': process.memory_percent()
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception:
            return {'error': 'unable to get memory info'}
    
    def _determine_error_severity(self, error: Exception) -> str:
        """Determine error severity level"""
        error_type = type(error).__name__.lower()
        error_str = str(error).lower()
        
        # Critical errors
        critical_indicators = [
            'out of memory', 'disk full', 'database connection',
            'authentication service', 'key vault', 'critical service'
        ]
        
        # High severity errors
        high_indicators = [
            'internal server error', '500', 'database error',
            'service unavailable', 'timeout', 'connection'
        ]
        
        # Medium severity errors
        medium_indicators = [
            'validation', 'parameter', 'bad request', '400',
            'not found', '404', 'conflict', '409'
        ]
        
        if error_type in ['memoryerror', 'systemerror']:
            return 'critical'
        elif any(indicator in error_str for indicator in critical_indicators):
            return 'critical'
        elif any(indicator in error_str for indicator in high_indicators):
            return 'high'
        elif any(indicator in error_str for indicator in medium_indicators):
            return 'medium'
        else:
            return 'low'
    
    def _assess_user_impact(self, error: Exception, context: Dict[str, Any]) -> str:
        """Assess the impact on users"""
        error_category = self.categorize_error(error)
        severity = self._determine_error_severity(error)
        
        if severity == 'critical':
            return 'high'
        elif error_category in [ErrorCategory.PERMANENT, ErrorCategory.NON_RETRYABLE]:
            return 'medium'
        elif error_category in [ErrorCategory.TRANSIENT, ErrorCategory.TIMEOUT]:
            return 'low'
        else:
            return 'medium'
    
    def _get_suggested_action(self, error: Exception) -> str:
        """Get suggested action for error resolution"""
        error_category = self.categorize_error(error)
        error_str = str(error).lower()
        
        if error_category == ErrorCategory.TRANSIENT:
            return "Retry the operation after a brief delay"
        elif error_category == ErrorCategory.RATE_LIMIT:
            return "Reduce request rate and retry with exponential backoff"
        elif error_category == ErrorCategory.TIMEOUT:
            return "Check network connectivity and increase timeout if necessary"
        elif error_category == ErrorCategory.RESOURCE_EXHAUSTED:
            return "Check system resources and scale if necessary"
        elif 'authentication' in error_str or 'authorization' in error_str:
            return "Verify credentials and permissions"
        elif 'validation' in error_str or 'parameter' in error_str:
            return "Check input parameters and format"
        elif 'not found' in error_str:
            return "Verify resource exists and path is correct"
        else:
            return "Review error details and contact support if needed"
    
    def retry_with_backoff(self, operation: Callable, operation_name: str, 
                          *args, **kwargs) -> Any:
        """
        Execute operation with retry and backoff
        
        Args:
            operation: Function to execute
            operation_name: Name for circuit breaker
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Operation result
        """
        circuit_breaker = self.get_circuit_breaker(operation_name)
        
        if not circuit_breaker.can_execute():
            raise Exception(f"Circuit breaker is open for {operation_name}")
        
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts + 1):
            try:
                result = operation(*args, **kwargs)
                circuit_breaker.record_success()
                return result
                
            except Exception as e:
                last_exception = e
                self.log_error(e, {'operation': operation_name, 'attempt': attempt})
                
                if not self.should_retry(e, attempt):
                    circuit_breaker.record_failure()
                    break
                
                if attempt < self.retry_config.max_attempts:
                    delay = self.calculate_delay(attempt)
                    logger.info(f"Retrying {operation_name} in {delay:.2f} seconds (attempt {attempt + 1})")
                    time.sleep(delay)
                else:
                    circuit_breaker.record_failure()
        
        raise last_exception
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for monitoring"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_errors = [
            error for error in self.error_history
            if datetime.fromisoformat(error['timestamp']) >= cutoff_time
        ]
        
        error_types = {}
        for error in recent_errors:
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            'total_errors': len(recent_errors),
            'error_types': error_types,
            'circuit_breaker_states': {
                name: cb.state for name, cb in self.circuit_breakers.items()
            },
            'time_range_hours': hours
        }

def retry_with_error_handler(operation_name: str, max_attempts: int = 3, 
                           base_delay: float = 1.0):
    """
    Decorator for retry with error handling
    
    Args:
        operation_name: Name of the operation for logging
        max_attempts: Maximum retry attempts
        base_delay: Base delay between retries
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = ErrorHandler(
                RetryConfig(max_attempts=max_attempts, base_delay=base_delay)
            )
            return error_handler.retry_with_backoff(func, operation_name, *args, **kwargs)
        return wrapper
    return decorator

class TaskErrorHandler(ErrorHandler):
    """Specialized error handler for task processing"""
    
    def __init__(self):
        super().__init__(RetryConfig(max_attempts=3, base_delay=2.0))
    
    def handle_task_error(self, task_id: str, error: Exception, 
                         task_type: str = None, execution_log_id: str = None) -> Dict[str, Any]:
        """Handle task-specific errors"""
        context = {
            'task_id': task_id,
            'task_type': task_type,
            'execution_log_id': execution_log_id
        }
        
        self.log_error(error, context)
        
        category = self.categorize_error(error)
        
        return {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'category': category,
            'retryable': self.should_retry(error, 0),
            'timestamp': datetime.now().isoformat()
        }

class QueueErrorHandler(ErrorHandler):
    """Specialized error handler for queue operations"""
    
    def __init__(self):
        super().__init__(RetryConfig(max_attempts=5, base_delay=1.0))
    
    def handle_queue_error(self, queue_name: str, error: Exception, 
                          message_id: str = None) -> Dict[str, Any]:
        """Handle queue-specific errors"""
        context = {
            'queue_name': queue_name,
            'message_id': message_id
        }
        
        self.log_error(error, context)
        
        return {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'category': self.categorize_error(error),
            'retryable': self.should_retry(error, 0),
            'timestamp': datetime.now().isoformat()
        }

class ServiceCircuitBreakerConfigurations:
    """Predefined circuit breaker configurations for different services"""
    
    @staticmethod
    def get_salesforce_config() -> CircuitBreakerConfig:
        """Configuration for Salesforce API calls"""
        return CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=120,
            success_threshold=2,
            monitoring_window=300,
            auto_recovery=True,
            health_check_interval=60
        )
    
    @staticmethod
    def get_database_config() -> CircuitBreakerConfig:
        """Configuration for database operations"""
        return CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=60,
            success_threshold=3,
            monitoring_window=180,
            auto_recovery=True,
            health_check_interval=30
        )
    
    @staticmethod
    def get_azure_service_config() -> CircuitBreakerConfig:
        """Configuration for Azure service calls"""
        return CircuitBreakerConfig(
            failure_threshold=4,
            recovery_timeout=90,
            success_threshold=2,
            monitoring_window=240,
            auto_recovery=True,
            health_check_interval=45
        )
    
    @staticmethod
    def get_queue_config() -> CircuitBreakerConfig:
        """Configuration for queue operations"""
        return CircuitBreakerConfig(
            failure_threshold=6,
            recovery_timeout=30,
            success_threshold=3,
            monitoring_window=120,
            auto_recovery=True,
            health_check_interval=20
        )
    
    @staticmethod
    def get_external_api_config() -> CircuitBreakerConfig:
        """Configuration for external API calls"""
        return CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=180,
            success_threshold=2,
            monitoring_window=360,
            auto_recovery=True,
            health_check_interval=90
        )

class OperationRetryConfigurations:
    """Predefined retry configurations for different operations"""
    
    @staticmethod
    def get_salesforce_api_config() -> RetryConfig:
        """Configuration for Salesforce API calls"""
        return RetryConfig(
            max_attempts=4,
            base_delay=2.0,
            max_delay=120.0,
            backoff_multiplier=2.0,
            jitter=True,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            timeout_per_attempt=30.0,
            retryable_exceptions=[ConnectionError, TimeoutError],
            non_retryable_exceptions=[ValueError, TypeError]
        )
    
    @staticmethod
    def get_database_config() -> RetryConfig:
        """Configuration for database operations"""
        return RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            backoff_multiplier=2.0,
            jitter=True,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            timeout_per_attempt=15.0
        )
    
    @staticmethod
    def get_azure_service_config() -> RetryConfig:
        """Configuration for Azure service calls"""
        return RetryConfig(
            max_attempts=3,
            base_delay=1.5,
            max_delay=60.0,
            backoff_multiplier=2.0,
            jitter=True,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            timeout_per_attempt=20.0
        )
    
    @staticmethod
    def get_queue_operation_config() -> RetryConfig:
        """Configuration for queue operations"""
        return RetryConfig(
            max_attempts=5,
            base_delay=0.5,
            max_delay=15.0,
            backoff_multiplier=1.5,
            jitter=True,
            strategy=RetryStrategy.LINEAR_BACKOFF,
            timeout_per_attempt=10.0
        )
    
    @staticmethod
    def get_file_operation_config() -> RetryConfig:
        """Configuration for file operations"""
        return RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=20.0,
            backoff_multiplier=2.0,
            jitter=False,
            strategy=RetryStrategy.FIXED_DELAY,
            timeout_per_attempt=30.0
        )
    
    @staticmethod
    def get_network_request_config() -> RetryConfig:
        """Configuration for network requests"""
        return RetryConfig(
            max_attempts=4,
            base_delay=1.0,
            max_delay=45.0,
            backoff_multiplier=2.0,
            jitter=True,
            strategy=RetryStrategy.FIBONACCI_BACKOFF,
            timeout_per_attempt=25.0
        )

class EnhancedErrorHandler(ErrorHandler):
    """Enhanced error handler with service-specific configurations and retry policies"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        """Initialize enhanced error handler with service configurations"""
        super().__init__(retry_config)
        self.retry_policies: Dict[str, EnhancedRetryPolicy] = {}
        self._configure_service_circuit_breakers()
        self._configure_operation_retry_policies()
    
    def _configure_service_circuit_breakers(self):
        """Configure circuit breakers for known services"""
        configs = ServiceCircuitBreakerConfigurations()
        
        self.configure_service_circuit_breaker('salesforce', configs.get_salesforce_config())
        self.configure_service_circuit_breaker('database', configs.get_database_config())
        self.configure_service_circuit_breaker('azure_keyvault', configs.get_azure_service_config())
        self.configure_service_circuit_breaker('azure_storage', configs.get_azure_service_config())
        self.configure_service_circuit_breaker('service_bus', configs.get_queue_config())
        self.configure_service_circuit_breaker('external_api', configs.get_external_api_config())
    
    def _configure_operation_retry_policies(self):
        """Configure retry policies for different operations"""
        retry_configs = OperationRetryConfigurations()
        
        self.retry_policies['salesforce_api'] = EnhancedRetryPolicy(retry_configs.get_salesforce_api_config())
        self.retry_policies['database'] = EnhancedRetryPolicy(retry_configs.get_database_config())
        self.retry_policies['azure_service'] = EnhancedRetryPolicy(retry_configs.get_azure_service_config())
        self.retry_policies['queue_operation'] = EnhancedRetryPolicy(retry_configs.get_queue_operation_config())
        self.retry_policies['file_operation'] = EnhancedRetryPolicy(retry_configs.get_file_operation_config())
        self.retry_policies['network_request'] = EnhancedRetryPolicy(retry_configs.get_network_request_config())
    
    def get_retry_policy(self, operation_type: str) -> EnhancedRetryPolicy:
        """Get retry policy for operation type"""
        return self.retry_policies.get(operation_type, 
                                     EnhancedRetryPolicy(RetryConfig()))
    
    def execute_with_circuit_breaker(self, operation: Callable, service_name: str, 
                                   *args, **kwargs) -> Any:
        """Execute operation with service-specific circuit breaker"""
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        if not circuit_breaker.can_execute():
            raise Exception(f"Circuit breaker is open for service: {service_name}")
        
        try:
            result = operation(*args, **kwargs)
            circuit_breaker.record_success()
            return result
        except Exception as e:
            circuit_breaker.record_failure()
            raise e
    
    def execute_with_retry_and_circuit_breaker(self, operation: Callable, 
                                             operation_name: str, service_name: str,
                                             operation_type: str = None,
                                             *args, **kwargs) -> Any:
        """Execute operation with both retry policy and circuit breaker"""
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        if not circuit_breaker.can_execute():
            raise Exception(f"Circuit breaker is open for service: {service_name}")
        
        # Get appropriate retry policy
        retry_policy = self.get_retry_policy(operation_type or service_name)
        
        def circuit_breaker_wrapper():
            try:
                result = operation(*args, **kwargs)
                circuit_breaker.record_success()
                return result
            except Exception as e:
                circuit_breaker.record_failure()
                raise e
        
        return retry_policy.execute_with_retry(circuit_breaker_wrapper, operation_name)
    
    def get_retry_metrics_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get retry metrics for all policies"""
        return {
            operation_type: policy.get_metrics()
            for operation_type, policy in self.retry_policies.items()
        }
    
    def optimize_retry_policies(self) -> Dict[str, RetryConfig]:
        """Get optimized retry configurations based on metrics"""
        return {
            operation_type: policy.optimize_config()
            for operation_type, policy in self.retry_policies.items()
        }

# Global error handler instances
_task_error_handler = None
_queue_error_handler = None
_enhanced_error_handler = None

def get_task_error_handler() -> TaskErrorHandler:
    """Get global task error handler instance"""
    global _task_error_handler
    if _task_error_handler is None:
        _task_error_handler = TaskErrorHandler()
    return _task_error_handler

def get_queue_error_handler() -> QueueErrorHandler:
    """Get global queue error handler instance"""
    global _queue_error_handler
    if _queue_error_handler is None:
        _queue_error_handler = QueueErrorHandler()
    return _queue_error_handler

class ErrorResponse:
    """Standardized error response structure"""
    
    def __init__(self, error: Exception, context: Dict[str, Any] = None, 
                 correlation_id: str = None):
        """Initialize error response"""
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.timestamp = datetime.now().isoformat()
        self.error = error
        self.context = context or {}
        
        # Get enhanced error handler for categorization
        error_handler = get_enhanced_error_handler()
        
        self.error_type = type(error).__name__
        self.error_message = str(error)
        self.error_category = error_handler.categorize_error(error)
        self.severity = error_handler._determine_error_severity(error)
        self.user_impact = error_handler._assess_user_impact(error, self.context)
        self.suggested_action = error_handler._get_suggested_action(error)
        self.retryable = error_handler.should_retry(error, 0)
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert to dictionary format"""
        response = {
            'success': False,
            'error': {
                'correlation_id': self.correlation_id,
                'timestamp': self.timestamp,
                'type': self.error_type,
                'message': self.error_message,
                'category': self.error_category,
                'severity': self.severity,
                'user_impact': self.user_impact,
                'suggested_action': self.suggested_action,
                'retryable': self.retryable
            }
        }
        
        # Include sensitive information only if requested (for internal use)
        if include_sensitive:
            response['error'].update({
                'stack_trace': traceback.format_exc(),
                'context': self.context
            })
        
        return response
    
    def to_json(self, include_sensitive: bool = False) -> str:
        """Convert to JSON format"""
        return json.dumps(self.to_dict(include_sensitive), default=str)
    
    def to_user_friendly_message(self) -> str:
        """Generate user-friendly error message"""
        if self.error_category == ErrorCategory.TRANSIENT:
            return "A temporary issue occurred. Please try again in a moment."
        elif self.error_category == ErrorCategory.RATE_LIMIT:
            return "Too many requests. Please wait a moment before trying again."
        elif self.error_category == ErrorCategory.TIMEOUT:
            return "The request took too long to complete. Please try again."
        elif self.error_category == ErrorCategory.NON_RETRYABLE:
            return "There was an issue with your request. Please check your input and try again."
        elif 'authentication' in self.error_message.lower():
            return "Authentication failed. Please check your credentials."
        elif 'not found' in self.error_message.lower():
            return "The requested resource was not found."
        else:
            return "An unexpected error occurred. Please try again or contact support."

class ErrorResponseBuilder:
    """Builder for creating standardized error responses"""
    
    def __init__(self):
        """Initialize error response builder"""
        self.error_handler = get_enhanced_error_handler()
    
    def build_error_response(self, error: Exception, context: Dict[str, Any] = None,
                           correlation_id: str = None) -> ErrorResponse:
        """Build comprehensive error response"""
        
        # Log the error
        self.error_handler.log_error(error, context)
        
        # Create error response
        return ErrorResponse(error, context, correlation_id)
    
    def build_validation_error_response(self, validation_errors: List[str],
                                      context: Dict[str, Any] = None) -> ErrorResponse:
        """Build validation error response"""
        
        error_message = "Validation failed: " + "; ".join(validation_errors)
        validation_error = ValueError(error_message)
        
        return self.build_error_response(validation_error, context)
    
    def build_authentication_error_response(self, message: str = "Authentication failed",
                                          context: Dict[str, Any] = None) -> ErrorResponse:
        """Build authentication error response"""
        
        auth_error = PermissionError(message)
        return self.build_error_response(auth_error, context)
    
    def build_timeout_error_response(self, operation: str, timeout_seconds: int,
                                   context: Dict[str, Any] = None) -> ErrorResponse:
        """Build timeout error response"""
        
        timeout_error = TimeoutError(f"Operation '{operation}' timed out after {timeout_seconds} seconds")
        return self.build_error_response(timeout_error, context)
    
    def build_resource_error_response(self, resource: str, message: str,
                                    context: Dict[str, Any] = None) -> ErrorResponse:
        """Build resource-related error response"""
        
        resource_error = RuntimeError(f"Resource '{resource}': {message}")
        return self.build_error_response(resource_error, context)
    
    def build_business_logic_error_response(self, rule: str, message: str,
                                          context: Dict[str, Any] = None) -> ErrorResponse:
        """Build business logic error response"""
        
        business_error = ValueError(f"Business rule '{rule}' violation: {message}")
        return self.build_error_response(business_error, context)

class ErrorContextManager:
    """Manager for error context and correlation tracking"""
    
    def __init__(self):
        """Initialize error context manager"""
        self.active_contexts: Dict[str, Dict[str, Any]] = {}
    
    def create_context(self, correlation_id: str = None, **kwargs) -> str:
        """Create new error context"""
        correlation_id = correlation_id or str(uuid.uuid4())
        
        self.active_contexts[correlation_id] = {
            'correlation_id': correlation_id,
            'created_at': datetime.now().isoformat(),
            'context_data': kwargs
        }
        
        return correlation_id
    
    def update_context(self, correlation_id: str, **kwargs):
        """Update existing error context"""
        if correlation_id in self.active_contexts:
            self.active_contexts[correlation_id]['context_data'].update(kwargs)
            self.active_contexts[correlation_id]['updated_at'] = datetime.now().isoformat()
    
    def get_context(self, correlation_id: str) -> Optional[Dict[str, Any]]:
        """Get error context"""
        return self.active_contexts.get(correlation_id)
    
    def clear_context(self, correlation_id: str):
        """Clear error context"""
        self.active_contexts.pop(correlation_id, None)
    
    def cleanup_old_contexts(self, max_age_hours: int = 24):
        """Clean up old contexts"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        to_remove = []
        for correlation_id, context in self.active_contexts.items():
            created_at = datetime.fromisoformat(context['created_at'])
            if created_at < cutoff_time:
                to_remove.append(correlation_id)
        
        for correlation_id in to_remove:
            self.clear_context(correlation_id)

# Global instances
_error_response_builder = None
_error_context_manager = None

def get_error_response_builder() -> ErrorResponseBuilder:
    """Get global error response builder instance"""
    global _error_response_builder
    if _error_response_builder is None:
        _error_response_builder = ErrorResponseBuilder()
    return _error_response_builder

def get_error_context_manager() -> ErrorContextManager:
    """Get global error context manager instance"""
    global _error_context_manager
    if _error_context_manager is None:
        _error_context_manager = ErrorContextManager()
    return _error_context_manager

def get_enhanced_error_handler() -> EnhancedErrorHandler:
    """Get global enhanced error handler instance"""
    global _enhanced_error_handler
    if _enhanced_error_handler is None:
        _enhanced_error_handler = EnhancedErrorHandler()
    return _enhanced_error_handler