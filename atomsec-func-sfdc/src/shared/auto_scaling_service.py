"""
Auto-Scaling Service

This module provides advanced auto-scaling configuration and monitoring for Azure Function Apps,
including intelligent scaling rules, load balancing strategies, and performance-based scaling.

Features:
- Dynamic scaling rules based on performance metrics
- Load balancing configuration
- Scaling metrics monitoring
- Predictive scaling based on patterns
- Cost-optimized scaling strategies
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import defaultdict, deque
import statistics

# Configure module-level logger
logger = logging.getLogger(__name__)

class ScalingTrigger(Enum):
    """Scaling trigger types"""
    CPU_UTILIZATION = "cpu_utilization"
    MEMORY_UTILIZATION = "memory_utilization"
    REQUEST_RATE = "request_rate"
    RESPONSE_TIME = "response_time"
    QUEUE_LENGTH = "queue_length"
    ERROR_RATE = "error_rate"
    CUSTOM_METRIC = "custom_metric"

class ScalingDirection(Enum):
    """Scaling direction"""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    MAINTAIN = "maintain"

@dataclass
class ScalingRule:
    """Auto-scaling rule configuration"""
    name: str
    trigger: ScalingTrigger
    threshold_up: float
    threshold_down: float
    evaluation_window: int  # seconds
    cooldown_period: int   # seconds
    min_instances: int
    max_instances: int
    scale_increment: int
    enabled: bool = True
    priority: int = 1

@dataclass
class ScalingEvent:
    """Scaling event record"""
    timestamp: float
    trigger: ScalingTrigger
    direction: ScalingDirection
    current_instances: int
    target_instances: int
    metric_value: float
    threshold: float
    reason: str

@dataclass
class LoadBalancingConfig:
    """Load balancing configuration"""
    strategy: str  # 'round_robin', 'least_connections', 'weighted', 'ip_hash'
    health_check_enabled: bool
    health_check_interval: int
    health_check_timeout: int
    sticky_sessions: bool
    session_affinity_timeout: int
    distribution_weights: Dict[str, float]

class AutoScalingService:
    """Advanced auto-scaling service for Azure Function Apps"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize auto-scaling service"""
        self.config = config
        self.scaling_rules = []
        self.scaling_events = deque(maxlen=1000)
        self.current_instances = config.get('initial_instances', 1)
        self.metrics_history = defaultdict(deque)
        self.lock = threading.RLock()
        
        # Scaling configuration
        self.evaluation_interval = config.get('evaluation_interval', 60)  # seconds
        self.metrics_retention = config.get('metrics_retention', 3600)    # seconds
        self.predictive_scaling = config.get('predictive_scaling', False)
        self.cost_optimization = config.get('cost_optimization', True)
        
        # Load balancing
        self.load_balancing_config = LoadBalancingConfig(
            strategy=config.get('load_balancing_strategy', 'round_robin'),
            health_check_enabled=config.get('health_check_enabled', True),
            health_check_interval=config.get('health_check_interval', 30),
            health_check_timeout=config.get('health_check_timeout', 5),
            sticky_sessions=config.get('sticky_sessions', False),
            session_affinity_timeout=config.get('session_affinity_timeout', 1800),
            distribution_weights=config.get('distribution_weights', {})
        )
        
        # Performance tracking
        self.scaling_effectiveness = defaultdict(list)
        self.cost_metrics = defaultdict(list)
        
        # Initialize default scaling rules
        self._initialize_default_rules()
        
        # Start scaling monitor
        self._start_scaling_monitor()
    
    def _initialize_default_rules(self):
        """Initialize default scaling rules"""
        default_rules = [
            ScalingRule(
                name="CPU_Based_Scaling",
                trigger=ScalingTrigger.CPU_UTILIZATION,
                threshold_up=70.0,
                threshold_down=30.0,
                evaluation_window=300,  # 5 minutes
                cooldown_period=600,    # 10 minutes
                min_instances=1,
                max_instances=20,
                scale_increment=2,
                priority=1
            ),
            ScalingRule(
                name="Memory_Based_Scaling",
                trigger=ScalingTrigger.MEMORY_UTILIZATION,
                threshold_up=80.0,
                threshold_down=40.0,
                evaluation_window=300,
                cooldown_period=600,
                min_instances=1,
                max_instances=15,
                scale_increment=1,
                priority=2
            ),
            ScalingRule(
                name="Request_Rate_Scaling",
                trigger=ScalingTrigger.REQUEST_RATE,
                threshold_up=100.0,  # requests per second
                threshold_down=20.0,
                evaluation_window=180,  # 3 minutes
                cooldown_period=300,    # 5 minutes
                min_instances=1,
                max_instances=25,
                scale_increment=3,
                priority=1
            ),
            ScalingRule(
                name="Response_Time_Scaling",
                trigger=ScalingTrigger.RESPONSE_TIME,
                threshold_up=2.0,    # seconds
                threshold_down=0.5,
                evaluation_window=240,  # 4 minutes
                cooldown_period=480,    # 8 minutes
                min_instances=1,
                max_instances=20,
                scale_increment=2,
                priority=2
            ),
            ScalingRule(
                name="Queue_Length_Scaling",
                trigger=ScalingTrigger.QUEUE_LENGTH,
                threshold_up=50.0,   # messages
                threshold_down=10.0,
                evaluation_window=120,  # 2 minutes
                cooldown_period=240,    # 4 minutes
                min_instances=1,
                max_instances=30,
                scale_increment=5,
                priority=1
            ),
            ScalingRule(
                name="Error_Rate_Scaling",
                trigger=ScalingTrigger.ERROR_RATE,
                threshold_up=5.0,    # percentage
                threshold_down=1.0,
                evaluation_window=300,
                cooldown_period=600,
                min_instances=2,     # Higher minimum for error scenarios
                max_instances=15,
                scale_increment=2,
                priority=3
            )
        ]
        
        self.scaling_rules.extend(default_rules)
        logger.info(f"Initialized {len(default_rules)} default scaling rules")
    
    def add_scaling_rule(self, rule: ScalingRule):
        """Add custom scaling rule"""
        with self.lock:
            self.scaling_rules.append(rule)
            # Sort by priority
            self.scaling_rules.sort(key=lambda r: r.priority)
        
        logger.info(f"Added scaling rule: {rule.name}")
    
    def remove_scaling_rule(self, rule_name: str) -> bool:
        """Remove scaling rule by name"""
        with self.lock:
            for i, rule in enumerate(self.scaling_rules):
                if rule.name == rule_name:
                    del self.scaling_rules[i]
                    logger.info(f"Removed scaling rule: {rule_name}")
                    return True
        
        logger.warning(f"Scaling rule not found: {rule_name}")
        return False
    
    def update_scaling_rule(self, rule_name: str, updates: Dict[str, Any]) -> bool:
        """Update scaling rule configuration"""
        with self.lock:
            for rule in self.scaling_rules:
                if rule.name == rule_name:
                    for key, value in updates.items():
                        if hasattr(rule, key):
                            setattr(rule, key, value)
                    logger.info(f"Updated scaling rule: {rule_name}")
                    return True
        
        logger.warning(f"Scaling rule not found for update: {rule_name}")
        return False
    
    def record_metric(self, trigger: ScalingTrigger, value: float, timestamp: Optional[float] = None):
        """Record metric value for scaling evaluation"""
        timestamp = timestamp or time.time()
        
        with self.lock:
            self.metrics_history[trigger].append({
                'timestamp': timestamp,
                'value': value
            })
            
            # Clean old metrics
            cutoff = timestamp - self.metrics_retention
            while (self.metrics_history[trigger] and 
                   self.metrics_history[trigger][0]['timestamp'] < cutoff):
                self.metrics_history[trigger].popleft()
    
    def evaluate_scaling(self) -> Optional[ScalingEvent]:
        """Evaluate scaling rules and determine scaling action"""
        try:
            current_time = time.time()
            scaling_decisions = []
            
            with self.lock:
                for rule in self.scaling_rules:
                    if not rule.enabled:
                        continue
                    
                    # Check cooldown period
                    if self._is_in_cooldown(rule, current_time):
                        continue
                    
                    # Get recent metrics for this trigger
                    recent_metrics = self._get_recent_metrics(rule.trigger, rule.evaluation_window)
                    
                    if not recent_metrics:
                        continue
                    
                    # Calculate average metric value
                    avg_value = statistics.mean(recent_metrics)
                    
                    # Determine scaling direction
                    scaling_direction = self._determine_scaling_direction(rule, avg_value)
                    
                    if scaling_direction != ScalingDirection.MAINTAIN:
                        scaling_decisions.append({
                            'rule': rule,
                            'direction': scaling_direction,
                            'metric_value': avg_value,
                            'priority': rule.priority
                        })
            
            # Select best scaling decision
            if scaling_decisions:
                # Sort by priority and select the highest priority decision
                scaling_decisions.sort(key=lambda d: d['priority'])
                best_decision = scaling_decisions[0]
                
                # Calculate target instances
                target_instances = self._calculate_target_instances(
                    best_decision['rule'], 
                    best_decision['direction']
                )
                
                # Create scaling event
                scaling_event = ScalingEvent(
                    timestamp=current_time,
                    trigger=best_decision['rule'].trigger,
                    direction=best_decision['direction'],
                    current_instances=self.current_instances,
                    target_instances=target_instances,
                    metric_value=best_decision['metric_value'],
                    threshold=best_decision['rule'].threshold_up if best_decision['direction'] == ScalingDirection.SCALE_UP else best_decision['rule'].threshold_down,
                    reason=f"Rule: {best_decision['rule'].name}, Metric: {best_decision['metric_value']:.2f}"
                )
                
                # Record scaling event
                self.scaling_events.append(scaling_event)
                
                # Update current instances
                self.current_instances = target_instances
                
                logger.info(f"Scaling decision: {scaling_event.direction.value} from {scaling_event.current_instances} to {scaling_event.target_instances} instances")
                
                return scaling_event
            
            return None
            
        except Exception as e:
            logger.error(f"Error evaluating scaling: {e}")
            return None
    
    def _is_in_cooldown(self, rule: ScalingRule, current_time: float) -> bool:
        """Check if rule is in cooldown period"""
        for event in reversed(self.scaling_events):
            if event.trigger == rule.trigger:
                if current_time - event.timestamp < rule.cooldown_period:
                    return True
                break
        return False
    
    def _get_recent_metrics(self, trigger: ScalingTrigger, window_seconds: int) -> List[float]:
        """Get recent metric values within time window"""
        cutoff = time.time() - window_seconds
        metrics = self.metrics_history.get(trigger, deque())
        
        return [
            m['value'] for m in metrics
            if m['timestamp'] > cutoff
        ]
    
    def _determine_scaling_direction(self, rule: ScalingRule, metric_value: float) -> ScalingDirection:
        """Determine scaling direction based on rule and metric value"""
        if metric_value >= rule.threshold_up and self.current_instances < rule.max_instances:
            return ScalingDirection.SCALE_UP
        elif metric_value <= rule.threshold_down and self.current_instances > rule.min_instances:
            return ScalingDirection.SCALE_DOWN
        else:
            return ScalingDirection.MAINTAIN
    
    def _calculate_target_instances(self, rule: ScalingRule, direction: ScalingDirection) -> int:
        """Calculate target number of instances"""
        if direction == ScalingDirection.SCALE_UP:
            target = min(
                self.current_instances + rule.scale_increment,
                rule.max_instances
            )
        elif direction == ScalingDirection.SCALE_DOWN:
            target = max(
                self.current_instances - rule.scale_increment,
                rule.min_instances
            )
        else:
            target = self.current_instances
        
        # Apply cost optimization
        if self.cost_optimization:
            target = self._apply_cost_optimization(target, rule)
        
        return target
    
    def _apply_cost_optimization(self, target_instances: int, rule: ScalingRule) -> int:
        """Apply cost optimization to scaling decisions"""
        try:
            # Get current time and check if it's off-peak hours
            current_hour = datetime.now().hour
            is_off_peak = current_hour < 6 or current_hour > 22
            
            # Reduce scaling during off-peak hours
            if is_off_peak and rule.trigger != ScalingTrigger.ERROR_RATE:
                target_instances = max(
                    int(target_instances * 0.7),  # Reduce by 30%
                    rule.min_instances
                )
            
            # Consider recent scaling effectiveness
            effectiveness = self._get_scaling_effectiveness(rule.trigger)
            if effectiveness < 0.5:  # Low effectiveness
                # Be more conservative with scaling
                if target_instances > self.current_instances:
                    target_instances = min(
                        target_instances,
                        self.current_instances + 1  # Scale more gradually
                    )
            
            return target_instances
            
        except Exception as e:
            logger.error(f"Error applying cost optimization: {e}")
            return target_instances
    
    def _get_scaling_effectiveness(self, trigger: ScalingTrigger) -> float:
        """Calculate scaling effectiveness for a trigger"""
        try:
            recent_events = [
                e for e in self.scaling_events
                if e.trigger == trigger and time.time() - e.timestamp < 3600
            ]
            
            if len(recent_events) < 2:
                return 1.0  # Assume effective if not enough data
            
            # Calculate effectiveness based on whether scaling achieved desired results
            effective_events = 0
            for i in range(len(recent_events) - 1):
                current_event = recent_events[i]
                next_event = recent_events[i + 1]
                
                # Check if scaling in one direction was followed by scaling in opposite direction
                if (current_event.direction == ScalingDirection.SCALE_UP and 
                    next_event.direction == ScalingDirection.SCALE_DOWN):
                    continue  # Not effective
                elif (current_event.direction == ScalingDirection.SCALE_DOWN and 
                      next_event.direction == ScalingDirection.SCALE_UP):
                    continue  # Not effective
                else:
                    effective_events += 1
            
            return effective_events / len(recent_events) if recent_events else 1.0
            
        except Exception as e:
            logger.error(f"Error calculating scaling effectiveness: {e}")
            return 1.0
    
    def get_scaling_metrics(self, time_window: int = 3600) -> Dict[str, Any]:
        """Get scaling metrics and statistics"""
        try:
            cutoff = time.time() - time_window
            recent_events = [e for e in self.scaling_events if e.timestamp > cutoff]
            
            if not recent_events:
                return {
                    'time_window': time_window,
                    'total_events': 0,
                    'current_instances': self.current_instances
                }
            
            # Calculate metrics
            scale_up_events = [e for e in recent_events if e.direction == ScalingDirection.SCALE_UP]
            scale_down_events = [e for e in recent_events if e.direction == ScalingDirection.SCALE_DOWN]
            
            # Group events by trigger
            events_by_trigger = defaultdict(list)
            for event in recent_events:
                events_by_trigger[event.trigger.value].append(event)
            
            # Calculate instance usage statistics
            instance_changes = [e.target_instances - e.current_instances for e in recent_events]
            
            metrics = {
                'time_window': time_window,
                'current_instances': self.current_instances,
                'total_events': len(recent_events),
                'scale_up_events': len(scale_up_events),
                'scale_down_events': len(scale_down_events),
                'events_by_trigger': {k: len(v) for k, v in events_by_trigger.items()},
                'instance_statistics': {
                    'min_instances': min(e.current_instances for e in recent_events),
                    'max_instances': max(e.target_instances for e in recent_events),
                    'avg_instances': statistics.mean(e.current_instances for e in recent_events),
                    'total_instance_changes': sum(abs(change) for change in instance_changes)
                },
                'scaling_effectiveness': {}
            }
            
            # Calculate effectiveness by trigger
            for trigger in ScalingTrigger:
                effectiveness = self._get_scaling_effectiveness(trigger)
                metrics['scaling_effectiveness'][trigger.value] = effectiveness
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting scaling metrics: {e}")
            return {'error': str(e)}
    
    def get_load_balancing_config(self) -> Dict[str, Any]:
        """Get current load balancing configuration"""
        return asdict(self.load_balancing_config)
    
    def update_load_balancing_config(self, updates: Dict[str, Any]):
        """Update load balancing configuration"""
        try:
            for key, value in updates.items():
                if hasattr(self.load_balancing_config, key):
                    setattr(self.load_balancing_config, key, value)
            
            logger.info(f"Updated load balancing configuration: {updates}")
            
        except Exception as e:
            logger.error(f"Error updating load balancing config: {e}")
    
    def generate_host_json_config(self) -> Dict[str, Any]:
        """Generate optimized host.json configuration for scaling"""
        try:
            # Base configuration
            config = {
                "version": "2.0",
                "functionTimeout": "00:15:00",
                "concurrency": {
                    "dynamicConcurrencyEnabled": True,
                    "snapshotPersistenceEnabled": True
                },
                "healthMonitor": {
                    "enabled": True,
                    "healthCheckInterval": "00:00:10",
                    "healthCheckWindow": "00:02:00",
                    "healthCheckThreshold": 6,
                    "counterThreshold": 0.80
                },
                "extensions": {
                    "http": {
                        "routePrefix": "api",
                        "maxOutstandingRequests": min(1000, self.current_instances * 50),
                        "maxConcurrentRequests": min(400, self.current_instances * 20),
                        "dynamicThrottlesEnabled": True
                    }
                }
            }
            
            # Add scaling-specific configuration
            if self.current_instances > 5:
                # High-scale configuration
                config["extensions"]["http"]["maxOutstandingRequests"] = 2000
                config["extensions"]["http"]["maxConcurrentRequests"] = 800
                config["extensions"]["queues"] = {
                    "batchSize": 64,
                    "maxDequeueCount": 5,
                    "newBatchThreshold": 32
                }
            elif self.current_instances <= 2:
                # Low-scale configuration
                config["extensions"]["http"]["maxOutstandingRequests"] = 200
                config["extensions"]["http"]["maxConcurrentRequests"] = 100
                config["extensions"]["queues"] = {
                    "batchSize": 16,
                    "maxDequeueCount": 3,
                    "newBatchThreshold": 8
                }
            
            return config
            
        except Exception as e:
            logger.error(f"Error generating host.json config: {e}")
            return {}
    
    def _start_scaling_monitor(self):
        """Start background scaling monitor"""
        def scaling_monitor():
            while True:
                try:
                    # Evaluate scaling rules
                    scaling_event = self.evaluate_scaling()
                    
                    if scaling_event:
                        # Log scaling decision
                        logger.info(f"Scaling event: {asdict(scaling_event)}")
                        
                        # Here you would integrate with Azure APIs to actually scale
                        # For now, we just log the decision
                        self._simulate_scaling_action(scaling_event)
                    
                    time.sleep(self.evaluation_interval)
                    
                except Exception as e:
                    logger.error(f"Scaling monitor error: {e}")
                    time.sleep(self.evaluation_interval)
        
        monitor_thread = threading.Thread(target=scaling_monitor, daemon=True)
        monitor_thread.start()
        logger.info("Started scaling monitor")
    
    def _simulate_scaling_action(self, scaling_event: ScalingEvent):
        """Simulate scaling action (placeholder for actual Azure integration)"""
        try:
            # In a real implementation, this would call Azure APIs to scale the function app
            logger.info(f"Simulating scaling action: {scaling_event.direction.value} to {scaling_event.target_instances} instances")
            
            # Record cost metrics
            cost_per_instance_hour = 0.20  # Example cost
            current_cost = self.current_instances * cost_per_instance_hour
            new_cost = scaling_event.target_instances * cost_per_instance_hour
            cost_change = new_cost - current_cost
            
            self.cost_metrics['hourly_cost'].append({
                'timestamp': scaling_event.timestamp,
                'cost': new_cost,
                'change': cost_change,
                'instances': scaling_event.target_instances
            })
            
            # Track scaling effectiveness
            self.scaling_effectiveness[scaling_event.trigger].append({
                'timestamp': scaling_event.timestamp,
                'direction': scaling_event.direction.value,
                'instances_change': scaling_event.target_instances - scaling_event.current_instances
            })
            
        except Exception as e:
            logger.error(f"Error simulating scaling action: {e}")
    
    def get_cost_analysis(self, time_window: int = 86400) -> Dict[str, Any]:
        """Get cost analysis for scaling decisions"""
        try:
            cutoff = time.time() - time_window
            recent_costs = [
                c for c in self.cost_metrics['hourly_cost']
                if c['timestamp'] > cutoff
            ]
            
            if not recent_costs:
                return {'status': 'no_data', 'time_window': time_window}
            
            total_cost = sum(c['cost'] for c in recent_costs)
            cost_changes = [c['change'] for c in recent_costs]
            
            analysis = {
                'time_window': time_window,
                'total_cost': total_cost,
                'average_hourly_cost': total_cost / (time_window / 3600),
                'cost_savings': -sum(c for c in cost_changes if c < 0),
                'cost_increases': sum(c for c in cost_changes if c > 0),
                'net_cost_change': sum(cost_changes),
                'scaling_efficiency': len([c for c in cost_changes if c < 0]) / len(cost_changes) if cost_changes else 0
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error generating cost analysis: {e}")
            return {'error': str(e)}
    
    def export_scaling_configuration(self) -> Dict[str, Any]:
        """Export complete scaling configuration"""
        try:
            return {
                'service_config': self.config,
                'scaling_rules': [asdict(rule) for rule in self.scaling_rules],
                'load_balancing_config': asdict(self.load_balancing_config),
                'current_instances': self.current_instances,
                'recent_events': [asdict(event) for event in list(self.scaling_events)[-10:]],
                'host_json_config': self.generate_host_json_config(),
                'export_timestamp': time.time()
            }
        except Exception as e:
            logger.error(f"Error exporting scaling configuration: {e}")
            return {'error': str(e)}

# Global auto-scaling service instance
_auto_scaling_service = None

def get_auto_scaling_service(config: Optional[Dict[str, Any]] = None) -> AutoScalingService:
    """Get global auto-scaling service instance"""
    global _auto_scaling_service
    
    if _auto_scaling_service is None:
        default_config = {
            'initial_instances': 1,
            'evaluation_interval': 60,
            'metrics_retention': 3600,
            'predictive_scaling': False,
            'cost_optimization': True,
            'load_balancing_strategy': 'round_robin',
            'health_check_enabled': True,
            'health_check_interval': 30,
            'health_check_timeout': 5
        }
        
        if config:
            default_config.update(config)
        
        _auto_scaling_service = AutoScalingService(default_config)
    
    return _auto_scaling_service