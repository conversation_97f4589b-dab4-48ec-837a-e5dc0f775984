"""
Monitoring Module

This module provides comprehensive monitoring and logging capabilities for the queue-based task processing system.
It includes metrics collection, performance monitoring, health checks, and alerting.

Features:
- Comprehensive metrics collection
- Performance monitoring
- Health check endpoints
- Alerting system
- Structured logging
- Distributed tracing support
"""

import logging
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
import threading
from collections import defaultdict, deque
import psutil
import os

# Configure module-level logger
logger = logging.getLogger(__name__)

class MetricsCollector:
    """Enhanced metrics collection with Application Insights integration"""
    
    def __init__(self, max_history: int = 10000):
        """Initialize enhanced metrics collector"""
        self.max_history = max_history
        self.metrics = defaultdict(deque)
        self.lock = threading.Lock()
        self.app_insights_client = self._initialize_app_insights()
        self.custom_dimensions = self._get_default_dimensions()
        
    def _initialize_app_insights(self):
        """Initialize Application Insights client"""
        try:
            from applicationinsights import TelemetryClient
            instrumentation_key = os.getenv('APPINSIGHTS_INSTRUMENTATIONKEY')
            if instrumentation_key:
                client = TelemetryClient(instrumentation_key)
                client.context.application.ver = os.getenv('APP_VERSION', '1.0.0')
                client.context.device.type = 'Azure Function'
                return client
        except ImportError:
            logger.warning("Application Insights SDK not available")
        except Exception as e:
            logger.error(f"Failed to initialize Application Insights: {e}")
        return None
    
    def _get_default_dimensions(self) -> Dict[str, str]:
        """Get default custom dimensions for all metrics"""
        return {
            'environment': os.getenv('ENVIRONMENT', 'development'),
            'function_app': os.getenv('WEBSITE_SITE_NAME', 'local'),
            'region': os.getenv('WEBSITE_RESOURCE_GROUP', 'unknown'),
            'version': os.getenv('APP_VERSION', '1.0.0')
        }
        
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None, 
                     correlation_id: str = None, execution_log_id: str = None):
        """Enhanced metric recording with Application Insights integration"""
        with self.lock:
            # Combine tags with default dimensions
            all_tags = {**self.custom_dimensions}
            if tags:
                all_tags.update(tags)
            if correlation_id:
                all_tags['correlation_id'] = correlation_id
            if execution_log_id:
                all_tags['execution_log_id'] = execution_log_id
                
            metric_data = {
                'timestamp': datetime.now().isoformat(),
                'name': name,
                'value': value,
                'tags': all_tags
            }
            self.metrics[name].append(metric_data)
            
            # Keep only recent metrics
            if len(self.metrics[name]) > self.max_history:
                self.metrics[name].popleft()
        
        # Send to Application Insights
        self._send_to_app_insights(name, value, all_tags)
    
    def _send_to_app_insights(self, name: str, value: float, properties: Dict[str, str]):
        """Send metric to Application Insights"""
        if self.app_insights_client:
            try:
                self.app_insights_client.track_metric(name, value, properties=properties)
                self.app_insights_client.flush()
            except Exception as e:
                logger.error(f"Failed to send metric to Application Insights: {e}")
    
    def record_custom_event(self, event_name: str, properties: Dict[str, Any] = None, 
                           measurements: Dict[str, float] = None, correlation_id: str = None):
        """Record custom event with Application Insights"""
        all_properties = {**self.custom_dimensions}
        if properties:
            all_properties.update({k: str(v) for k, v in properties.items()})
        if correlation_id:
            all_properties['correlation_id'] = correlation_id
            
        if self.app_insights_client:
            try:
                self.app_insights_client.track_event(
                    event_name, 
                    properties=all_properties, 
                    measurements=measurements
                )
                self.app_insights_client.flush()
            except Exception as e:
                logger.error(f"Failed to send event to Application Insights: {e}")
    
    def track_dependency(self, name: str, dependency_type: str, target: str, 
                        duration_ms: float, success: bool, result_code: str = None,
                        correlation_id: str = None):
        """Track dependency call with Application Insights"""
        properties = {**self.custom_dimensions}
        if correlation_id:
            properties['correlation_id'] = correlation_id
            
        if self.app_insights_client:
            try:
                self.app_insights_client.track_dependency(
                    name=name,
                    data=target,
                    type=dependency_type,
                    duration=duration_ms,
                    success=success,
                    result_code=result_code,
                    properties=properties
                )
                self.app_insights_client.flush()
            except Exception as e:
                logger.error(f"Failed to track dependency: {e}")
    
    def track_exception(self, exception: Exception, properties: Dict[str, Any] = None,
                       correlation_id: str = None, execution_log_id: str = None):
        """Track exception with Application Insights"""
        all_properties = {**self.custom_dimensions}
        if properties:
            all_properties.update({k: str(v) for k, v in properties.items()})
        if correlation_id:
            all_properties['correlation_id'] = correlation_id
        if execution_log_id:
            all_properties['execution_log_id'] = execution_log_id
            
        if self.app_insights_client:
            try:
                self.app_insights_client.track_exception(
                    type(exception),
                    exception,
                    exception.__traceback__,
                    properties=all_properties
                )
                self.app_insights_client.flush()
            except Exception as e:
                logger.error(f"Failed to track exception: {e}")
    
    def get_metrics(self, name: str, time_range_hours: int = 1) -> List[Dict[str, Any]]:
        """Get metrics for a specific name within time range"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        with self.lock:
            if name not in self.metrics:
                return []
            
            return [
                metric for metric in self.metrics[name]
                if datetime.fromisoformat(metric['timestamp']) >= cutoff_time
            ]
    
    def get_summary(self, name: str, time_range_hours: int = 1) -> Dict[str, Any]:
        """Get enhanced summary statistics for a metric"""
        metrics = self.get_metrics(name, time_range_hours)
        if not metrics:
            return {'count': 0}
        
        values = [m['value'] for m in metrics]
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': sum(values) / len(values),
            'median': sorted(values)[len(values) // 2] if values else 0,
            'p95': sorted(values)[int(len(values) * 0.95)] if len(values) > 20 else max(values) if values else 0,
            'p99': sorted(values)[int(len(values) * 0.99)] if len(values) > 100 else max(values) if values else 0,
            'last_value': values[-1] if values else None,
            'trend': self._calculate_trend(values)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for values"""
        if len(values) < 2:
            return 'stable'
        
        # Compare first and last quartiles
        quarter_size = len(values) // 4
        if quarter_size < 1:
            return 'stable'
            
        first_quarter_avg = sum(values[:quarter_size]) / quarter_size
        last_quarter_avg = sum(values[-quarter_size:]) / quarter_size
        
        change_percent = ((last_quarter_avg - first_quarter_avg) / first_quarter_avg) * 100
        
        if change_percent > 10:
            return 'increasing'
        elif change_percent < -10:
            return 'decreasing'
        else:
            return 'stable'

class DistributedTraceContext:
    """Context for distributed tracing across service boundaries"""
    
    def __init__(self, trace_id: str = None, span_id: str = None, parent_span_id: str = None):
        """Initialize trace context"""
        self.trace_id = trace_id or str(uuid.uuid4())
        self.span_id = span_id or str(uuid.uuid4())
        self.parent_span_id = parent_span_id
        self.start_time = datetime.now()
        self.tags = {}
        self.logs = []
    
    def add_tag(self, key: str, value: str):
        """Add tag to trace context"""
        self.tags[key] = value
    
    def add_log(self, message: str, level: str = 'info'):
        """Add log to trace context"""
        self.logs.append({
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'level': level
        })
    
    def create_child_span(self, operation_name: str) -> 'DistributedTraceContext':
        """Create child span for nested operations"""
        child = DistributedTraceContext(
            trace_id=self.trace_id,
            parent_span_id=self.span_id
        )
        child.add_tag('operation_name', operation_name)
        return child
    
    def to_headers(self) -> Dict[str, str]:
        """Convert trace context to HTTP headers for service calls"""
        return {
            'X-Trace-Id': self.trace_id,
            'X-Span-Id': self.span_id,
            'X-Parent-Span-Id': self.parent_span_id or ''
        }
    
    @classmethod
    def from_headers(cls, headers: Dict[str, str]) -> 'DistributedTraceContext':
        """Create trace context from HTTP headers"""
        return cls(
            trace_id=headers.get('X-Trace-Id'),
            span_id=headers.get('X-Span-Id'),
            parent_span_id=headers.get('X-Parent-Span-Id') or None
        )


class PerformanceMonitor:
    """Enhanced performance monitoring with distributed tracing"""
    
    def __init__(self):
        """Initialize enhanced performance monitor"""
        self.metrics_collector = MetricsCollector()
        self.start_time = datetime.now()
        self.active_traces = {}
        self.trace_lock = threading.Lock()
        
    def start_trace(self, operation_name: str, correlation_id: str = None, 
                   parent_context: DistributedTraceContext = None) -> DistributedTraceContext:
        """Start distributed trace for an operation"""
        if parent_context:
            trace_context = parent_context.create_child_span(operation_name)
        else:
            trace_context = DistributedTraceContext()
            trace_context.add_tag('operation_name', operation_name)
        
        if correlation_id:
            trace_context.add_tag('correlation_id', correlation_id)
        
        with self.trace_lock:
            self.active_traces[trace_context.span_id] = trace_context
        
        # Record trace start metric
        self.metrics_collector.record_metric(
            'trace.started', 1, 
            {'operation': operation_name}, 
            correlation_id
        )
        
        return trace_context
    
    def end_trace(self, trace_context: DistributedTraceContext, success: bool = True, 
                 error: Exception = None):
        """End distributed trace"""
        duration = (datetime.now() - trace_context.start_time).total_seconds()
        
        # Record trace metrics
        operation_name = trace_context.tags.get('operation_name', 'unknown')
        correlation_id = trace_context.tags.get('correlation_id')
        
        self.metrics_collector.record_metric(
            'trace.duration', duration * 1000,  # Convert to milliseconds
            {'operation': operation_name, 'success': str(success)},
            correlation_id
        )
        
        if not success and error:
            self.metrics_collector.track_exception(error, {
                'trace_id': trace_context.trace_id,
                'span_id': trace_context.span_id,
                'operation': operation_name
            }, correlation_id)
        
        # Send to Application Insights
        if self.metrics_collector.app_insights_client:
            try:
                self.metrics_collector.app_insights_client.track_dependency(
                    name=operation_name,
                    data=trace_context.trace_id,
                    type='Internal',
                    duration=duration * 1000,
                    success=success,
                    properties={
                        'trace_id': trace_context.trace_id,
                        'span_id': trace_context.span_id,
                        **trace_context.tags
                    }
                )
            except Exception as e:
                logger.error(f"Failed to send trace to Application Insights: {e}")
        
        # Remove from active traces
        with self.trace_lock:
            self.active_traces.pop(trace_context.span_id, None)
    
    def collect_system_metrics(self, correlation_id: str = None):
        """Enhanced system metrics collection"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_collector.record_metric('system.cpu_percent', cpu_percent, 
                                                correlation_id=correlation_id)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics_collector.record_metric('system.memory_percent', memory.percent,
                                                correlation_id=correlation_id)
            self.metrics_collector.record_metric('system.memory_used_mb', memory.used / 1024 / 1024,
                                                correlation_id=correlation_id)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self.metrics_collector.record_metric('system.disk_percent', (disk.used / disk.total) * 100,
                                                correlation_id=correlation_id)
            
            # Process metrics
            process = psutil.Process(os.getpid())
            self.metrics_collector.record_metric('process.memory_mb', process.memory_info().rss / 1024 / 1024,
                                                correlation_id=correlation_id)
            self.metrics_collector.record_metric('process.cpu_percent', process.cpu_percent(),
                                                correlation_id=correlation_id)
            
            # Thread metrics
            self.metrics_collector.record_metric('process.thread_count', threading.active_count(),
                                                correlation_id=correlation_id)
            
            # Active traces metric
            with self.trace_lock:
                self.metrics_collector.record_metric('traces.active_count', len(self.active_traces),
                                                    correlation_id=correlation_id)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            self.metrics_collector.track_exception(e, {'operation': 'collect_system_metrics'}, correlation_id)
    
    def collect_queue_metrics(self, queue_manager, correlation_id: str = None):
        """Enhanced queue metrics collection"""
        try:
            metrics = queue_manager.get_queue_metrics()
            
            for queue_name, queue_data in metrics.items():
                tags = {
                    'queue_name': queue_name,
                    'priority': queue_data.get('priority', 'unknown'),
                    'type': queue_data.get('type', 'unknown')
                }
                
                message_count = queue_data.get('message_count', 0)
                self.metrics_collector.record_metric('queue.message_count', message_count, tags, correlation_id)
                
                # Track queue processing rate
                processed_count = queue_data.get('processed_count', 0)
                self.metrics_collector.record_metric('queue.processed_count', processed_count, tags, correlation_id)
                
                # Track queue errors
                if 'error' in queue_data:
                    self.metrics_collector.record_metric('queue.error_count', 1, tags, correlation_id)
                    
                # Track queue latency
                avg_latency = queue_data.get('avg_latency_ms', 0)
                if avg_latency > 0:
                    self.metrics_collector.record_metric('queue.avg_latency_ms', avg_latency, tags, correlation_id)
                    
        except Exception as e:
            logger.error(f"Error collecting queue metrics: {e}")
            self.metrics_collector.track_exception(e, {'operation': 'collect_queue_metrics'}, correlation_id)
    
    def collect_task_metrics(self, task_status_service, correlation_id: str = None):
        """Enhanced task metrics collection"""
        try:
            # Get task statistics from task status service
            if hasattr(task_status_service, 'get_task_statistics'):
                stats = task_status_service.get_task_statistics()
                
                for task_type, task_stats in stats.items():
                    tags = {'task_type': task_type}
                    
                    self.metrics_collector.record_metric('tasks.total_processed', 
                                                       task_stats.get('total', 0), tags, correlation_id)
                    self.metrics_collector.record_metric('tasks.success_count', 
                                                       task_stats.get('success', 0), tags, correlation_id)
                    self.metrics_collector.record_metric('tasks.failure_count', 
                                                       task_stats.get('failure', 0), tags, correlation_id)
                    self.metrics_collector.record_metric('tasks.avg_duration_ms', 
                                                       task_stats.get('avg_duration_ms', 0), tags, correlation_id)
            else:
                # Fallback metrics
                self.metrics_collector.record_metric('tasks.total_processed', 1, correlation_id=correlation_id)
            
        except Exception as e:
            logger.error(f"Error collecting task metrics: {e}")
            self.metrics_collector.track_exception(e, {'operation': 'collect_task_metrics'}, correlation_id)
    
    def get_health_status(self, correlation_id: str = None) -> Dict[str, Any]:
        """Enhanced health status with detailed metrics"""
        try:
            # System health
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Determine health status
            health_status = "healthy"
            issues = []
            warnings = []
            
            # CPU checks
            if cpu_percent > 95:
                health_status = "critical"
                issues.append(f"Critical CPU usage: {cpu_percent}%")
            elif cpu_percent > 80:
                if health_status == "healthy":
                    health_status = "warning"
                warnings.append(f"High CPU usage: {cpu_percent}%")
            
            # Memory checks
            if memory.percent > 95:
                health_status = "critical"
                issues.append(f"Critical memory usage: {memory.percent}%")
            elif memory.percent > 85:
                if health_status == "healthy":
                    health_status = "warning"
                warnings.append(f"High memory usage: {memory.percent}%")
            
            # Disk checks
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 95:
                health_status = "critical"
                issues.append(f"Critical disk usage: {disk_percent:.1f}%")
            elif disk_percent > 90:
                if health_status == "healthy":
                    health_status = "warning"
                warnings.append(f"High disk usage: {disk_percent:.1f}%")
            
            # Active traces check
            with self.trace_lock:
                active_trace_count = len(self.active_traces)
                if active_trace_count > 100:
                    if health_status == "healthy":
                        health_status = "warning"
                    warnings.append(f"High number of active traces: {active_trace_count}")
            
            health_data = {
                'status': health_status,
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': (datetime.now() - self.start_time).total_seconds(),
                'system': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_mb': memory.used / 1024 / 1024,
                    'memory_available_mb': memory.available / 1024 / 1024,
                    'disk_percent': disk_percent,
                    'disk_used_gb': disk.used / 1024 / 1024 / 1024,
                    'disk_free_gb': disk.free / 1024 / 1024 / 1024
                },
                'process': {
                    'pid': os.getpid(),
                    'thread_count': threading.active_count(),
                    'active_traces': active_trace_count
                },
                'issues': issues,
                'warnings': warnings
            }
            
            # Record health status metric
            self.metrics_collector.record_metric('system.health_score', 
                                                self._calculate_health_score(health_status),
                                                correlation_id=correlation_id)
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            self.metrics_collector.track_exception(e, {'operation': 'get_health_status'}, correlation_id)
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _calculate_health_score(self, health_status: str) -> float:
        """Calculate numeric health score"""
        scores = {
            'healthy': 100.0,
            'warning': 75.0,
            'critical': 25.0,
            'error': 0.0
        }
        return scores.get(health_status, 0.0)
    
    def get_active_traces(self) -> List[Dict[str, Any]]:
        """Get information about active traces"""
        with self.trace_lock:
            return [
                {
                    'trace_id': trace.trace_id,
                    'span_id': trace.span_id,
                    'operation': trace.tags.get('operation_name', 'unknown'),
                    'start_time': trace.start_time.isoformat(),
                    'duration_seconds': (datetime.now() - trace.start_time).total_seconds(),
                    'tags': trace.tags
                }
                for trace in self.active_traces.values()
            ]

class StructuredLogger:
    """Enhanced structured logging with correlation IDs and centralized configuration"""
    
    def __init__(self, component_name: str, correlation_id: str = None):
        """Initialize structured logger with correlation support"""
        self.component_name = component_name
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.logger = logging.getLogger(f"{__name__}.{component_name}")
        self._configure_logger()
        
    def _configure_logger(self):
        """Configure logger with centralized settings"""
        # Set log level from environment or default to INFO
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.logger.setLevel(getattr(logging, log_level))
        
        # Configure formatter for structured logging
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def set_correlation_id(self, correlation_id: str):
        """Set correlation ID for request tracking"""
        self.correlation_id = correlation_id
    
    def log(self, level: str, message: str, context: Dict[str, Any] = None, 
            execution_log_id: str = None, user_id: str = None):
        """Enhanced log with correlation IDs and additional context"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level.upper(),
            'component': self.component_name,
            'message': message,
            'correlation_id': self.correlation_id,
            'context': context or {}
        }
        
        # Add execution log ID for task sequence tracking
        if execution_log_id:
            log_entry['execution_log_id'] = execution_log_id
            
        # Add user context if available
        if user_id:
            log_entry['user_id'] = user_id
            
        # Add system context
        log_entry['system'] = {
            'process_id': os.getpid(),
            'thread_id': threading.current_thread().ident,
            'function_name': self._get_caller_function()
        }
        
        # Filter sensitive data
        log_entry = self._filter_sensitive_data(log_entry)
        
        log_method = getattr(self.logger, level.lower())
        log_method(json.dumps(log_entry, default=str))
    
    def _get_caller_function(self) -> str:
        """Get the name of the calling function"""
        import inspect
        try:
            frame = inspect.currentframe()
            # Go up the stack to find the actual caller (skip this method and log method)
            for _ in range(3):
                frame = frame.f_back
                if frame is None:
                    return "unknown"
            return frame.f_code.co_name
        except:
            return "unknown"
    
    def _filter_sensitive_data(self, log_entry: Dict[str, Any]) -> Dict[str, Any]:
        """Filter sensitive data from log entries"""
        sensitive_keys = {
            'password', 'token', 'secret', 'key', 'credential', 
            'authorization', 'auth', 'jwt', 'access_token'
        }
        
        def filter_dict(obj):
            if isinstance(obj, dict):
                return {
                    k: '[REDACTED]' if any(sensitive in k.lower() for sensitive in sensitive_keys)
                    else filter_dict(v) for k, v in obj.items()
                }
            elif isinstance(obj, list):
                return [filter_dict(item) for item in obj]
            return obj
        
        return filter_dict(log_entry)
    
    def info(self, message: str, context: Dict[str, Any] = None, 
             execution_log_id: str = None, user_id: str = None):
        """Enhanced info level logging"""
        self.log('info', message, context, execution_log_id, user_id)
    
    def error(self, message: str, context: Dict[str, Any] = None, 
              execution_log_id: str = None, user_id: str = None, exception: Exception = None):
        """Enhanced error level logging with exception details"""
        if exception:
            if context is None:
                context = {}
            context.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'stack_trace': self._get_stack_trace(exception)
            })
        self.log('error', message, context, execution_log_id, user_id)
    
    def warning(self, message: str, context: Dict[str, Any] = None, 
                execution_log_id: str = None, user_id: str = None):
        """Enhanced warning level logging"""
        self.log('warning', message, context, execution_log_id, user_id)
    
    def debug(self, message: str, context: Dict[str, Any] = None, 
              execution_log_id: str = None, user_id: str = None):
        """Enhanced debug level logging"""
        self.log('debug', message, context, execution_log_id, user_id)
    
    def audit(self, action: str, resource: str, user_id: str = None, 
              execution_log_id: str = None, context: Dict[str, Any] = None):
        """Audit logging for security and compliance"""
        audit_context = {
            'audit_action': action,
            'audit_resource': resource,
            'audit_timestamp': datetime.now().isoformat()
        }
        if context:
            audit_context.update(context)
            
        self.info(f"AUDIT: {action} on {resource}", audit_context, execution_log_id, user_id)
    
    def _get_stack_trace(self, exception: Exception) -> str:
        """Get formatted stack trace from exception"""
        import traceback
        return traceback.format_exception(type(exception), exception, exception.__traceback__)


class LogAggregator:
    """Centralized log aggregation and management"""
    
    def __init__(self, max_logs: int = 10000):
        """Initialize log aggregator"""
        self.max_logs = max_logs
        self.logs = deque(maxlen=max_logs)
        self.lock = threading.Lock()
        self.retention_hours = int(os.getenv('LOG_RETENTION_HOURS', '24'))
        
    def add_log(self, log_entry: Dict[str, Any]):
        """Add log entry to aggregation"""
        with self.lock:
            self.logs.append(log_entry)
    
    def get_logs(self, level: str = None, component: str = None, 
                 correlation_id: str = None, execution_log_id: str = None,
                 time_range_hours: int = None) -> List[Dict[str, Any]]:
        """Get filtered logs"""
        if time_range_hours is None:
            time_range_hours = self.retention_hours
            
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        with self.lock:
            filtered_logs = []
            for log in self.logs:
                # Time filter
                try:
                    log_time = datetime.fromisoformat(log.get('timestamp', ''))
                    if log_time < cutoff_time:
                        continue
                except:
                    continue
                
                # Level filter
                if level and log.get('level', '').lower() != level.lower():
                    continue
                    
                # Component filter
                if component and log.get('component', '') != component:
                    continue
                    
                # Correlation ID filter
                if correlation_id and log.get('correlation_id', '') != correlation_id:
                    continue
                    
                # Execution log ID filter
                if execution_log_id and log.get('execution_log_id', '') != execution_log_id:
                    continue
                
                filtered_logs.append(log)
            
            return filtered_logs
    
    def get_log_summary(self, time_range_hours: int = 1) -> Dict[str, Any]:
        """Get log summary statistics"""
        logs = self.get_logs(time_range_hours=time_range_hours)
        
        level_counts = defaultdict(int)
        component_counts = defaultdict(int)
        
        for log in logs:
            level_counts[log.get('level', 'unknown')] += 1
            component_counts[log.get('component', 'unknown')] += 1
        
        return {
            'total_logs': len(logs),
            'level_distribution': dict(level_counts),
            'component_distribution': dict(component_counts),
            'time_range_hours': time_range_hours
        }
    
    def cleanup_old_logs(self):
        """Remove logs older than retention period"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        with self.lock:
            # Convert to list to avoid modifying deque during iteration
            logs_to_keep = []
            for log in self.logs:
                try:
                    log_time = datetime.fromisoformat(log.get('timestamp', ''))
                    if log_time >= cutoff_time:
                        logs_to_keep.append(log)
                except:
                    # Keep logs with invalid timestamps for safety
                    logs_to_keep.append(log)
            
            self.logs.clear()
            self.logs.extend(logs_to_keep)

class TaskSequenceMonitor:
    """Enhanced monitoring for sequential task execution with execution_log_id tracking"""
    
    def __init__(self):
        """Initialize task sequence monitor"""
        self.metrics_collector = MetricsCollector()
        self.active_sequences = {}  # execution_log_id -> sequence info
        self.active_tasks = {}      # task_id -> task info
        self.task_history = deque(maxlen=1000)
        self.sequence_history = deque(maxlen=500)
        self.lock = threading.Lock()
        
        # Define standard SFDC task sequence
        self.standard_sequence = [
            'sfdc_authenticate',
            'health_check', 
            'metadata_extraction',
            'profiles_analysis',
            'permission_sets_analysis',
            'security_analysis',
            'pmd_apex_security'
        ]
    
    def start_task_sequence(self, execution_log_id: str, sequence_type: str = 'standard_sfdc',
                           correlation_id: str = None, user_id: str = None) -> Dict[str, Any]:
        """Start monitoring a task sequence"""
        with self.lock:
            sequence_info = {
                'execution_log_id': execution_log_id,
                'sequence_type': sequence_type,
                'correlation_id': correlation_id,
                'user_id': user_id,
                'start_time': datetime.now(),
                'expected_tasks': self.standard_sequence.copy(),
                'completed_tasks': [],
                'failed_tasks': [],
                'current_task': None,
                'status': 'running',
                'total_duration': 0,
                'task_durations': {}
            }
            
            self.active_sequences[execution_log_id] = sequence_info
        
        # Record sequence start metric
        tags = {
            'sequence_type': sequence_type,
            'execution_log_id': execution_log_id
        }
        self.metrics_collector.record_metric('task_sequence.started', 1, tags, 
                                           correlation_id, execution_log_id)
        
        return sequence_info
    
    def start_task(self, task_id: str, task_type: str, execution_log_id: str, 
                  correlation_id: str = None, user_id: str = None):
        """Start monitoring a task within a sequence"""
        start_time = datetime.now()
        
        with self.lock:
            task_info = {
                'task_id': task_id,
                'task_type': task_type,
                'execution_log_id': execution_log_id,
                'correlation_id': correlation_id,
                'user_id': user_id,
                'start_time': start_time,
                'status': 'running'
            }
            
            self.active_tasks[task_id] = task_info
            
            # Update sequence info
            if execution_log_id in self.active_sequences:
                self.active_sequences[execution_log_id]['current_task'] = task_type
        
        # Record task start metrics
        tags = {
            'task_id': task_id,
            'task_type': task_type,
            'execution_log_id': execution_log_id,
            'sequence_position': self._get_task_position(task_type)
        }
        
        self.metrics_collector.record_metric('tasks.started', 1, tags, 
                                           correlation_id, execution_log_id)
        
        # Track sequence progress
        self._update_sequence_progress(execution_log_id, task_type, 'started', correlation_id)
    
    def complete_task(self, task_id: str, success: bool, result: Dict[str, Any] = None,
                     error_message: str = None):
        """Complete a task within a sequence"""
        with self.lock:
            if task_id not in self.active_tasks:
                return
            
            task_info = self.active_tasks.pop(task_id)
            
        end_time = datetime.now()
        duration = (end_time - task_info['start_time']).total_seconds()
        
        # Update task info
        task_info.update({
            'end_time': end_time,
            'duration_seconds': duration,
            'success': success,
            'result': result,
            'error_message': error_message,
            'status': 'completed' if success else 'failed'
        })
        
        # Add to history
        self.task_history.append(task_info)
        
        # Record completion metrics
        tags = {
            'task_id': task_id,
            'task_type': task_info['task_type'],
            'execution_log_id': task_info['execution_log_id'],
            'success': str(success),
            'sequence_position': self._get_task_position(task_info['task_type'])
        }
        
        self.metrics_collector.record_metric('tasks.completed', 1, tags, 
                                           task_info['correlation_id'], task_info['execution_log_id'])
        self.metrics_collector.record_metric('tasks.duration_seconds', duration, tags,
                                           task_info['correlation_id'], task_info['execution_log_id'])
        
        # Update sequence tracking
        self._update_sequence_progress(task_info['execution_log_id'], task_info['task_type'], 
                                     'completed' if success else 'failed', 
                                     task_info['correlation_id'], duration, error_message)
        
        # Check if sequence is complete
        self._check_sequence_completion(task_info['execution_log_id'])
    
    def _get_task_position(self, task_type: str) -> str:
        """Get the position of task in standard sequence"""
        try:
            position = self.standard_sequence.index(task_type) + 1
            return f"{position}/{len(self.standard_sequence)}"
        except ValueError:
            return "unknown"
    
    def _update_sequence_progress(self, execution_log_id: str, task_type: str, status: str,
                                correlation_id: str = None, duration: float = None, 
                                error_message: str = None):
        """Update sequence progress tracking"""
        with self.lock:
            if execution_log_id not in self.active_sequences:
                return
            
            sequence = self.active_sequences[execution_log_id]
            
            if status == 'started':
                sequence['current_task'] = task_type
            elif status == 'completed':
                if task_type not in sequence['completed_tasks']:
                    sequence['completed_tasks'].append(task_type)
                if duration:
                    sequence['task_durations'][task_type] = duration
                    sequence['total_duration'] += duration
                sequence['current_task'] = None
            elif status == 'failed':
                if task_type not in sequence['failed_tasks']:
                    sequence['failed_tasks'].append(task_type)
                if duration:
                    sequence['task_durations'][task_type] = duration
                    sequence['total_duration'] += duration
                sequence['current_task'] = None
                sequence['status'] = 'failed'
                sequence['error_message'] = error_message
        
        # Record sequence progress metrics
        progress_percent = self._calculate_sequence_progress(execution_log_id)
        tags = {
            'execution_log_id': execution_log_id,
            'task_type': task_type,
            'status': status
        }
        
        self.metrics_collector.record_metric('task_sequence.progress_percent', progress_percent, 
                                           tags, correlation_id, execution_log_id)
    
    def _calculate_sequence_progress(self, execution_log_id: str) -> float:
        """Calculate sequence completion percentage"""
        with self.lock:
            if execution_log_id not in self.active_sequences:
                return 0.0
            
            sequence = self.active_sequences[execution_log_id]
            total_tasks = len(sequence['expected_tasks'])
            completed_tasks = len(sequence['completed_tasks'])
            
            return (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0.0
    
    def _check_sequence_completion(self, execution_log_id: str):
        """Check if sequence is complete and finalize it"""
        with self.lock:
            if execution_log_id not in self.active_sequences:
                return
            
            sequence = self.active_sequences[execution_log_id]
            
            # Check if all expected tasks are completed or if there was a failure
            all_completed = set(sequence['completed_tasks']) >= set(sequence['expected_tasks'])
            has_failures = len(sequence['failed_tasks']) > 0
            
            if all_completed or has_failures:
                # Finalize sequence
                sequence['end_time'] = datetime.now()
                sequence['total_duration'] = (sequence['end_time'] - sequence['start_time']).total_seconds()
                
                if all_completed and not has_failures:
                    sequence['status'] = 'completed'
                elif has_failures:
                    sequence['status'] = 'failed'
                else:
                    sequence['status'] = 'partial'
                
                # Move to history
                self.sequence_history.append(sequence.copy())
                del self.active_sequences[execution_log_id]
                
                # Record sequence completion metrics
                tags = {
                    'execution_log_id': execution_log_id,
                    'sequence_type': sequence['sequence_type'],
                    'status': sequence['status'],
                    'total_tasks': str(len(sequence['expected_tasks'])),
                    'completed_tasks': str(len(sequence['completed_tasks'])),
                    'failed_tasks': str(len(sequence['failed_tasks']))
                }
                
                self.metrics_collector.record_metric('task_sequence.completed', 1, tags,
                                                   sequence['correlation_id'], execution_log_id)
                self.metrics_collector.record_metric('task_sequence.total_duration_seconds', 
                                                   sequence['total_duration'], tags,
                                                   sequence['correlation_id'], execution_log_id)
    
    def get_sequence_status(self, execution_log_id: str) -> Dict[str, Any]:
        """Get current status of a task sequence"""
        with self.lock:
            if execution_log_id in self.active_sequences:
                sequence = self.active_sequences[execution_log_id].copy()
                sequence['is_active'] = True
                return sequence
            
            # Check history
            for sequence in reversed(self.sequence_history):
                if sequence['execution_log_id'] == execution_log_id:
                    sequence_copy = sequence.copy()
                    sequence_copy['is_active'] = False
                    return sequence_copy
            
            return None
    
    def get_sequence_health(self, execution_log_id: str) -> Dict[str, Any]:
        """Get health status of a task sequence"""
        sequence_status = self.get_sequence_status(execution_log_id)
        if not sequence_status:
            return {'status': 'not_found'}
        
        health_info = {
            'execution_log_id': execution_log_id,
            'overall_status': sequence_status['status'],
            'progress_percent': self._calculate_sequence_progress(execution_log_id),
            'current_task': sequence_status.get('current_task'),
            'completed_tasks': len(sequence_status['completed_tasks']),
            'failed_tasks': len(sequence_status['failed_tasks']),
            'total_expected_tasks': len(sequence_status['expected_tasks']),
            'is_healthy': sequence_status['status'] in ['running', 'completed'],
            'issues': []
        }
        
        # Detect issues
        if sequence_status['status'] == 'failed':
            health_info['is_healthy'] = False
            health_info['issues'].append(f"Sequence failed with {len(sequence_status['failed_tasks'])} failed tasks")
        
        # Check for stuck sequences
        if sequence_status.get('is_active') and sequence_status.get('current_task'):
            current_time = datetime.now()
            start_time = sequence_status['start_time']
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time)
            
            duration = (current_time - start_time).total_seconds()
            if duration > 1800:  # 30 minutes
                health_info['is_healthy'] = False
                health_info['issues'].append(f"Sequence running for {duration/60:.1f} minutes, may be stuck")
        
        return health_info
    
    def get_task_summary(self, time_range_hours: int = 1, execution_log_id: str = None) -> Dict[str, Any]:
        """Get enhanced task execution summary"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        # Filter tasks
        recent_tasks = []
        for task in self.task_history:
            task_time = task['start_time']
            if isinstance(task_time, str):
                task_time = datetime.fromisoformat(task_time)
            
            if task_time >= cutoff_time:
                if execution_log_id is None or task.get('execution_log_id') == execution_log_id:
                    recent_tasks.append(task)
        
        # Calculate statistics
        total_tasks = len(recent_tasks)
        successful_tasks = len([t for t in recent_tasks if t.get('success', False)])
        failed_tasks = total_tasks - successful_tasks
        
        avg_duration = 0
        if recent_tasks:
            durations = [t.get('duration_seconds', 0) for t in recent_tasks]
            avg_duration = sum(durations) / len(durations)
        
        # Task type breakdown
        task_type_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'failed': 0, 'avg_duration': 0})
        for task in recent_tasks:
            task_type = task.get('task_type', 'unknown')
            task_type_stats[task_type]['total'] += 1
            if task.get('success', False):
                task_type_stats[task_type]['success'] += 1
            else:
                task_type_stats[task_type]['failed'] += 1
        
        # Calculate average durations per task type
        for task_type, stats in task_type_stats.items():
            type_tasks = [t for t in recent_tasks if t.get('task_type') == task_type]
            if type_tasks:
                durations = [t.get('duration_seconds', 0) for t in type_tasks]
                stats['avg_duration'] = sum(durations) / len(durations)
        
        return {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'success_rate': (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            'average_duration_seconds': avg_duration,
            'active_tasks': len(self.active_tasks),
            'active_sequences': len(self.active_sequences),
            'task_type_breakdown': dict(task_type_stats),
            'time_range_hours': time_range_hours
        }
    
    def get_sequence_summary(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get summary of task sequences"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        # Get recent sequences from history
        recent_sequences = []
        for sequence in self.sequence_history:
            seq_time = sequence['start_time']
            if isinstance(seq_time, str):
                seq_time = datetime.fromisoformat(seq_time)
            
            if seq_time >= cutoff_time:
                recent_sequences.append(sequence)
        
        # Add active sequences
        with self.lock:
            recent_sequences.extend(self.active_sequences.values())
        
        # Calculate statistics
        total_sequences = len(recent_sequences)
        completed_sequences = len([s for s in recent_sequences if s.get('status') == 'completed'])
        failed_sequences = len([s for s in recent_sequences if s.get('status') == 'failed'])
        running_sequences = len([s for s in recent_sequences if s.get('status') == 'running'])
        
        avg_duration = 0
        if recent_sequences:
            durations = [s.get('total_duration', 0) for s in recent_sequences if s.get('total_duration')]
            avg_duration = sum(durations) / len(durations) if durations else 0
        
        return {
            'total_sequences': total_sequences,
            'completed_sequences': completed_sequences,
            'failed_sequences': failed_sequences,
            'running_sequences': running_sequences,
            'success_rate': (completed_sequences / total_sequences * 100) if total_sequences > 0 else 0,
            'average_duration_seconds': avg_duration,
            'time_range_hours': time_range_hours
        }


# Maintain backward compatibility
class TaskMonitor(TaskSequenceMonitor):
    """Backward compatible TaskMonitor that extends TaskSequenceMonitor"""
    pass

class AlertManager:
    """Manage alerts and notifications"""
    
    def __init__(self):
        """Initialize alert manager"""
        self.alerts = []
        self.alert_thresholds = {
            'queue.message_count': {'warning': 1000, 'critical': 5000},
            'system.cpu_percent': {'warning': 80, 'critical': 95},
            'system.memory_percent': {'warning': 85, 'critical': 95},
            'system.disk_percent': {'warning': 85, 'critical': 95},
            'tasks.failed': {'warning': 10, 'critical': 50}
        }
    
    def check_alerts(self, metrics_collector: MetricsCollector) -> List[Dict[str, Any]]:
        """Check for alert conditions"""
        alerts = []
        
        for metric_name, thresholds in self.alert_thresholds.items():
            summary = metrics_collector.get_summary(metric_name)
            if summary['count'] > 0:
                last_value = summary['last_value']
                
                if last_value is not None:
                    if last_value >= thresholds.get('critical', float('inf')):
                        alerts.append({
                            'severity': 'critical',
                            'metric': metric_name,
                            'value': last_value,
                            'threshold': thresholds['critical'],
                            'timestamp': datetime.now().isoformat()
                        })
                    elif last_value >= thresholds.get('warning', float('inf')):
                        alerts.append({
                            'severity': 'warning',
                            'metric': metric_name,
                            'value': last_value,
                            'threshold': thresholds['warning'],
                            'timestamp': datetime.now().isoformat()
                        })
        
        return alerts

# Global monitoring instances
_performance_monitor = None
_task_monitor = None
_alert_manager = None
_log_aggregator = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def get_task_monitor() -> TaskSequenceMonitor:
    """Get global task sequence monitor instance"""
    global _task_monitor
    if _task_monitor is None:
        _task_monitor = TaskSequenceMonitor()
    return _task_monitor

def get_task_sequence_monitor() -> TaskSequenceMonitor:
    """Get global task sequence monitor instance (alias for get_task_monitor)"""
    return get_task_monitor()

def get_alert_manager() -> AlertManager:
    """Get global alert manager instance"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager()
    return _alert_manager

def get_log_aggregator() -> LogAggregator:
    """Get global log aggregator instance"""
    global _log_aggregator
    if _log_aggregator is None:
        _log_aggregator = LogAggregator()
    return _log_aggregator

def get_structured_logger(component_name: str, correlation_id: str = None) -> StructuredLogger:
    """Get enhanced structured logger for a component"""
    return StructuredLogger(component_name, correlation_id)

def configure_centralized_logging():
    """Configure centralized logging settings"""
    # Set up log aggregation
    aggregator = get_log_aggregator()
    
    # Configure log levels based on environment
    environment = os.getenv('ENVIRONMENT', 'development').lower()
    if environment == 'production':
        logging.getLogger().setLevel(logging.INFO)
    elif environment == 'development':
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.WARNING)
    
    # Set up log retention
    retention_hours = int(os.getenv('LOG_RETENTION_HOURS', '24'))
    logger.info(f"Configured centralized logging with {retention_hours}h retention")

def cleanup_logs():
    """Cleanup old logs based on retention policy"""
    try:
        aggregator = get_log_aggregator()
        aggregator.cleanup_old_logs()
        logger.debug("Log cleanup completed")
    except Exception as e:
        logger.error(f"Error during log cleanup: {e}")

def initialize_monitoring():
    """Initialize comprehensive monitoring system"""
    try:
        # Configure centralized logging
        configure_centralized_logging()
        
        # Initialize monitoring components
        performance_monitor = get_performance_monitor()
        task_monitor = get_task_sequence_monitor()
        
        # Set up dashboard and alerting (imported here to avoid circular imports)
        try:
            from .monitoring_dashboards import setup_monitoring_dashboards
            setup_monitoring_dashboards()
        except ImportError:
            logger.warning("Monitoring dashboards module not available")
        
        logger.info("Comprehensive monitoring system initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing monitoring system: {e}")

def shutdown_monitoring():
    """Shutdown monitoring system gracefully"""
    try:
        # Cleanup logs
        cleanup_logs()
        
        # Shutdown dashboard and alerting
        try:
            from .monitoring_dashboards import cleanup_monitoring
            cleanup_monitoring()
        except ImportError:
            pass
        
        logger.info("Monitoring system shutdown completed")
        
    except Exception as e:
        logger.error(f"Error during monitoring shutdown: {e}")

# Decorator for performance monitoring
def monitor_performance(operation_name: str):
    """Decorator to monitor operation performance"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            task_monitor = get_task_monitor()
            
            start_time = time.time()
            operation_id = str(uuid.uuid4())
            
            # Start monitoring
            monitor.metrics_collector.record_metric(
                f'operation.{operation_name}.started', 1
            )
            
            try:
                result = func(*args, **kwargs)
                
                duration = time.time() - start_time
                monitor.metrics_collector.record_metric(
                    f'operation.{operation_name}.duration', duration
                )
                monitor.metrics_collector.record_metric(
                    f'operation.{operation_name}.success', 1
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                monitor.metrics_collector.record_metric(
                    f'operation.{operation_name}.duration', duration
                )
                monitor.metrics_collector.record_metric(
                    f'operation.{operation_name}.error', 1
                )
                
                # Log error with context
                logger.error(f"Operation {operation_name} failed", extra={
                    'operation_id': operation_id,
                    'duration': duration,
                    'error': str(e)
                })
                
                raise
        
        return wrapper
    return decorator