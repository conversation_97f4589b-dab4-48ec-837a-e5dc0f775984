"""
Task Lookup Service Module

This module provides comprehensive task lookup functionality to resolve
"task not found" errors by implementing multiple search strategies and
fallback mechanisms.

Features:
- Multi-strategy task lookup
- Fallback search mechanisms
- Task existence verification
- Partial match support
- Comprehensive logging
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

# Import shared modules
from src.shared.task_status_service import get_task_status_service, TaskStatusService

# Configure module-level logger
logger = logging.getLogger(__name__)


class TaskLookupService:
    """
    Comprehensive service for resolving task not found errors
    """
    
    def __init__(self, task_status_service: Optional[TaskStatusService] = None):
        """Initialize the task lookup service"""
        self.task_status_service = task_status_service or get_task_status_service()
    
    def find_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Comprehensive task finding with multiple strategies
        
        Args:
            task_id: Task ID to find
            
        Returns:
            Dict[str, Any]: Task data if found, None otherwise
        """
        if not task_id:
            logger.error("Task ID is required")
            return None
        
        task_id = str(task_id).strip()
        
        # Strategy 1: Direct lookup
        task = self._direct_lookup(task_id)
        if task:
            return task
        
        # Strategy 2: Case-insensitive lookup
        task = self._case_insensitive_lookup(task_id)
        if task:
            return task
        
        # Strategy 3: Partial match lookup
        task = self._partial_match_lookup(task_id)
        if task:
            return task
        
        # Strategy 4: Recent task lookup
        task = self._recent_task_lookup(task_id)
        if task:
            return task
        
        logger.warning(f"Task not found using all strategies: {task_id}")
        return None
    
    def _direct_lookup(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Direct task lookup"""
        try:
            task = self.task_status_service.get_task(task_id)
            if task:
                logger.debug(f"Found task via direct lookup: {task_id}")
                return task
            return None
        except Exception as e:
            logger.error(f"Error in direct lookup: {str(e)}")
            return None
    
    def _case_insensitive_lookup(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Case-insensitive task lookup"""
        try:
            # Get all tasks and search case-insensitively
            all_tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True,
                limit=1000
            )
            
            for task in all_tasks:
                if task.get('TaskId', '').lower() == task_id.lower():
                    logger.debug(f"Found task via case-insensitive lookup: {task_id}")
                    return task
            
            return None
        except Exception as e:
            logger.error(f"Error in case-insensitive lookup: {str(e)}")
            return None
    
    def _partial_match_lookup(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Partial match task lookup"""
        try:
            # Get recent tasks and search for partial matches
            recent_tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True,
                limit=100
            )
            
            # Look for tasks containing the task_id
            for task in recent_tasks:
                task_id_full = task.get('TaskId', '')
                if task_id in task_id_full or task_id_full in task_id:
                    logger.debug(f"Found task via partial match: {task_id} -> {task_id_full}")
                    return task
            
            return None
        except Exception as e:
            logger.error(f"Error in partial match lookup: {str(e)}")
            return None
    
    def _recent_task_lookup(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Lookup recent tasks for potential matches"""
        try:
            # Get tasks from the last 24 hours
            recent_tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True,
                limit=50
            )
            
            # Check if any recent tasks might be related
            for task in recent_tasks:
                task_id_full = task.get('TaskId', '')
                
                # Check for similar patterns
                if len(task_id) >= 8 and len(task_id_full) >= 8:
                    # Compare first 8 characters
                    if task_id[:8].lower() == task_id_full[:8].lower():
                        logger.info(f"Found potential task match: {task_id} -> {task_id_full}")
                        return task
            
            return None
        except Exception as e:
            logger.error(f"Error in recent task lookup: {str(e)}")
            return None
    
    def verify_task_exists(self, task_id: str) -> bool:
        """
        Verify if a task exists
        
        Args:
            task_id: Task ID to verify
            
        Returns:
            bool: True if task exists, False otherwise
        """
        task = self.find_task(task_id)
        return task is not None
    
    def get_task_suggestions(self, partial_task_id: str) -> List[Dict[str, Any]]:
        """
        Get task suggestions for partial task IDs
        
        Args:
            partial_task_id: Partial task ID
            
        Returns:
            List[Dict[str, Any]]: List of potential matching tasks
        """
        if not partial_task_id:
            return []
        
        try:
            all_tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True,
                limit=100
            )
            
            suggestions = []
            partial = str(partial_task_id).lower()
            
            for task in all_tasks:
                task_id = task.get('TaskId', '').lower()
                
                # Match by prefix
                if task_id.startswith(partial):
                    suggestions.append(task)
                # Match by contains
                elif partial in task_id:
                    suggestions.append(task)
            
            # Sort by creation date (most recent first)
            suggestions.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
            
            logger.debug(f"Found {len(suggestions)} task suggestions for: {partial_task_id}")
            return suggestions[:10]  # Return top 10 matches
            
        except Exception as e:
            logger.error(f"Error getting task suggestions: {str(e)}")
            return []
    
    def diagnose_task_not_found(self, task_id: str) -> Dict[str, Any]:
        """
        Diagnose why a task might not be found
        
        Args:
            task_id: Task ID that wasn't found
            
        Returns:
            Dict[str, Any]: Diagnostic information
        """
        diagnosis = {
            "task_id": task_id,
            "found": False,
            "suggestions": [],
            "recent_tasks": [],
            "error_details": None
        }
        
        try:
            # Check if task exists
            task = self.find_task(task_id)
            if task:
                diagnosis["found"] = True
                diagnosis["task"] = task
                return diagnosis
            
            # Get suggestions
            diagnosis["suggestions"] = self.get_task_suggestions(task_id)
            
            # Get recent tasks
            recent_tasks = self.task_status_service.get_tasks_by_org(
                org_id=None,
                include_completed=True,
                limit=20
            )
            diagnosis["recent_tasks"] = [
                {
                    "TaskId": task.get("TaskId"),
                    "TaskType": task.get("TaskType"),
                    "Status": task.get("Status"),
                    "CreatedAt": task.get("CreatedAt")
                }
                for task in recent_tasks
            ]
            
            # Analyze potential issues
            issues = []
            if not task_id:
                issues.append("Empty task ID provided")
            elif len(task_id) < 8:
                issues.append("Task ID too short")
            elif not all(c in '0123456789abcdefABCDEF-' for c in task_id):
                issues.append("Invalid task ID format")
            
            diagnosis["potential_issues"] = issues
            
        except Exception as e:
            diagnosis["error_details"] = str(e)
            logger.error(f"Error diagnosing task not found: {str(e)}")
        
        return diagnosis


# Global service instance
_task_lookup_service = None


def get_task_lookup_service() -> TaskLookupService:
    """
    Get the global task lookup service instance
    
    Returns:
        TaskLookupService: The service instance
    """
    global _task_lookup_service
    
    if _task_lookup_service is None:
        _task_lookup_service = TaskLookupService()
    
    return _task_lookup_service