"""
Queue Manager Module

This module provides centralized queue management for Azure Storage Queues
with support for priority-based routing, retry mechanisms, and monitoring.

Features:
- Manage Azure Storage Queue connections
- Handle priority-based task routing
- Implement retry mechanisms with exponential backoff
- Manage poison message handling
- Collect queue metrics for monitoring
- Support for both local development and production environments

Best practices implemented:
- Single responsibility principle
- Comprehensive error handling
- Structured logging with correlation IDs
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union

# Import Azure Storage Queue
try:
    from azure.storage.queue import QueueClient, QueueServiceClient
    from azure.core.exceptions import ResourceNotFoundError, HttpResponseError
except ImportError:
    logging.warning("Azure Storage Queue package not available. Install with: pip install azure-storage-queue")
    # Create dummy classes for development
    class QueueClient:
        def __init__(self, *args, **kwargs):
            pass
    class QueueServiceClient:
        def __init__(self, *args, **kwargs):
            pass

# Import shared modules
from src.shared.azure_services import is_local_dev
from src.shared.config import get_storage_connection_string
from src.shared.common import is_local_dev
from src.shared.error_handler import get_queue_error_handler, retry_with_error_handler

# Configure module-level logger
logger = logging.getLogger(__name__)

# Queue configuration constants
PRIORITY_QUEUES = {
    "high": "task-queue-high",
    "medium": "task-queue-medium",
    "low": "task-queue-low"
}

POISON_QUEUES = {
    "high": "task-queue-high-poison",
    "medium": "task-queue-medium-poison",
    "low": "task-queue-low-poison"
}

# Message processing constants
MAX_RETRY_COUNT = 3
EXPONENTIAL_BACKOFF_BASE = 2
MAX_VISIBILITY_TIMEOUT = 300  # 5 minutes
DEFAULT_VISIBILITY_TIMEOUT = 30  # 30 seconds
MESSAGE_PROCESSING_TIMEOUT = 600  # 10 minutes
MAX_MESSAGE_SIZE = 64 * 1024  # 64KB limit for Azure Storage Queue messages

# Metrics collection
METRICS_COLLECTION_INTERVAL = 60  # seconds


class QueueManager:
    """
    Queue manager for handling Azure Storage Queue operations
    
    This class provides functionality to:
    - Manage queue connections and operations
    - Handle priority-based task routing
    - Implement retry mechanisms
    - Manage poison message handling
    - Collect and report queue metrics
    """
    
    def __init__(self, connection_string: Optional[str] = None):
        """
        Initialize the queue manager
        
        Args:
            connection_string: Optional connection string override
        """
        self.connection_string = connection_string or get_storage_connection_string()
        self.queue_clients = {}
        self.poison_queue_clients = {}
        self.queue_service_client = None
        self.is_local = is_local_dev()
        self.error_handler = get_queue_error_handler()
        
        # Initialize queue clients
        self._initialize_queue_clients()
        
        logger.info(f"Queue manager initialized for {'local' if self.is_local else 'production'} environment")
    
    def _initialize_queue_clients(self):
        """Initialize queue clients for all priority levels"""
        try:
            if not self.connection_string:
                logger.error("Storage connection string not available")
                return
            
            # Initialize queue service client
            self.queue_service_client = QueueServiceClient.from_connection_string(
                conn_str=self.connection_string
            )
            
            # Create queues if they don't exist
            self._ensure_queues_exist()
            
            # Initialize priority queues
            for priority, queue_name in PRIORITY_QUEUES.items():
                self.queue_clients[queue_name] = self.queue_service_client.get_queue_client(queue_name)
                logger.debug(f"Initialized queue client for {priority} priority: {queue_name}")
            
            # Initialize poison queues
            for priority, poison_queue_name in POISON_QUEUES.items():
                self.poison_queue_clients[poison_queue_name] = self.queue_service_client.get_queue_client(poison_queue_name)
                logger.debug(f"Initialized poison queue client for {priority} priority: {poison_queue_name}")
            
            logger.info("All queue clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing queue clients: {str(e)}")
    
    def _ensure_queues_exist(self):
        """Ensure all required queues exist"""
        try:
            all_queues = list(PRIORITY_QUEUES.values()) + list(POISON_QUEUES.values())
            
            for queue_name in all_queues:
                try:
                    self.queue_service_client.create_queue(queue_name)
                    logger.info(f"Created queue: {queue_name}")
                except HttpResponseError as e:
                    if "QueueAlreadyExists" in str(e):
                        logger.debug(f"Queue already exists: {queue_name}")
                    else:
                        logger.warning(f"Error creating queue {queue_name}: {str(e)}")
                        
        except Exception as e:
            logger.error(f"Error ensuring queues exist: {str(e)}")
    
    def enqueue_scan_task(
        self,
        execution_log_id: str,
        task_data: Dict[str, Any],
        priority: str = "medium"
    ) -> bool:
        """
        Enqueue a scan task to the appropriate queue
        
        Args:
            execution_log_id: Execution log ID for context
            task_data: Task data to enqueue
            priority: Task priority (high, medium, low)
            
        Returns:
            bool: True if successful, False otherwise
        """
        @retry_with_error_handler("enqueue_scan_task", max_attempts=3, base_delay=1.0)
        def _enqueue_task():
            if priority not in PRIORITY_QUEUES:
                raise ValueError(f"Invalid priority: {priority}")
            
            queue_name = PRIORITY_QUEUES[priority]
            queue_client = self.queue_clients.get(queue_name)
            
            if not queue_client:
                raise RuntimeError(f"Queue client not found for {queue_name}")
            
            # Ensure execution_log_id is in task data
            task_data["execution_log_id"] = execution_log_id
            
            # Generate task ID if not provided
            if "task_id" not in task_data:
                task_data["task_id"] = str(uuid.uuid4())
            
            # Add metadata
            task_data["created_at"] = datetime.now().isoformat()
            task_data["retry_count"] = 0
            
            # Serialize task data
            message_content = json.dumps(task_data)
            
            # Check message size
            if len(message_content.encode('utf-8')) > MAX_MESSAGE_SIZE:
                raise ValueError(f"Task message too large: {len(message_content.encode('utf-8'))} bytes")
            
            # Send message to queue
            queue_client.send_message(
                content=message_content,
                time_to_live=MESSAGE_PROCESSING_TIMEOUT
            )
            
            logger.info(f"Enqueued task: execution_log_id={execution_log_id}, priority={priority}, queue={queue_name}")
            return True
        
        try:
            return _enqueue_task()
        except Exception as e:
            error_result = self.error_handler.handle_queue_error(
                PRIORITY_QUEUES.get(priority, "unknown"), e
            )
            logger.error(f"Failed to enqueue task after retries: {error_result}")
            return False
    
    def receive_messages(
        self,
        queue_name: str,
        max_messages: int = 1,
        visibility_timeout: int = DEFAULT_VISIBILITY_TIMEOUT
    ) -> List[Dict[str, Any]]:
        """
        Receive messages from a queue
        
        Args:
            queue_name: Name of the queue
            max_messages: Maximum number of messages to receive
            visibility_timeout: Visibility timeout in seconds
            
        Returns:
            List[Dict[str, Any]]: List of received messages with metadata
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return []
            
            # Receive messages from queue
            messages = queue_client.receive_messages(
                max_messages=max_messages,
                visibility_timeout=visibility_timeout
            )
            
            decoded_messages = []
            for message in messages:
                try:
                    # Parse message content
                    message_data = json.loads(message.content)
                    
                    # Add queue metadata
                    message_data["_queue_metadata"] = {
                        "message_id": message.id,
                        "pop_receipt": message.pop_receipt,
                        "dequeue_count": message.dequeue_count,
                        "next_visible_on": message.next_visible_on.isoformat() if message.next_visible_on else None,
                        "queue_name": queue_name
                    }
                    
                    decoded_messages.append(message_data)
                    
                except json.JSONDecodeError:
                    logger.error(f"Error decoding message from {queue_name}: {message.content}")
                    continue
            
            logger.debug(f"Received {len(decoded_messages)} messages from {queue_name}")
            return decoded_messages
            
        except Exception as e:
            logger.error(f"Error receiving messages from {queue_name}: {str(e)}")
            return []
    
    def acknowledge_message(
        self,
        queue_name: str,
        message_id: str,
        pop_receipt: str
    ) -> bool:
        """
        Acknowledge (delete) a message from the queue
        
        Args:
            queue_name: Name of the queue
            message_id: Message ID
            pop_receipt: Pop receipt from the message
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            queue_client.delete_message(message_id, pop_receipt)
            logger.debug(f"Message acknowledged: message_id={message_id}, queue={queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error acknowledging message: {str(e)}")
            return False
    
    def move_to_poison_queue(
        self,
        original_queue_name: str,
        message_data: Dict[str, Any],
        error_details: str
    ) -> bool:
        """
        Move a message to the corresponding poison queue
        
        Args:
            original_queue_name: Name of the original queue
            message_data: Message data to move
            error_details: Details about why the message is being moved
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine poison queue name
            poison_queue_name = None
            for priority, queue_name in PRIORITY_QUEUES.items():
                if queue_name == original_queue_name:
                    poison_queue_name = POISON_QUEUES[priority]
                    break
            
            if not poison_queue_name:
                logger.error(f"No poison queue mapping found for {original_queue_name}")
                return False
            
            poison_queue_client = self.poison_queue_clients.get(poison_queue_name)
            if not poison_queue_client:
                logger.error(f"Poison queue client not found for {poison_queue_name}")
                return False
            
            # Add poison queue metadata
            poison_message_data = {
                **message_data,
                "poison_metadata": {
                    "original_queue": original_queue_name,
                    "moved_to_poison_at": datetime.now().isoformat(),
                    "error_details": error_details,
                    "final_retry_count": message_data.get("retry_count", 0),
                    "final_dequeue_count": message_data.get("_queue_metadata", {}).get("dequeue_count", 0)
                }
            }
            
            # Remove original queue metadata
            poison_message_data.pop("_queue_metadata", None)
            
            # Send to poison queue
            message_content = json.dumps(poison_message_data)
            poison_queue_client.send_message(
                content=message_content,
                time_to_live=-1  # Never expire poison messages
            )
            
            # Log detailed information
            task_id = message_data.get("task_id")
            execution_log_id = message_data.get("execution_log_id")
            
            logger.error(f"Message moved to poison queue: {poison_queue_name}")
            logger.error(f"  Task ID: {task_id}")
            logger.error(f"  Execution Log ID: {execution_log_id}")
            logger.error(f"  Original Queue: {original_queue_name}")
            logger.error(f"  Error Details: {error_details}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error moving message to poison queue: {str(e)}")
            return False
    
    def requeue_message_with_delay(
        self,
        queue_name: str,
        message_data: Dict[str, Any],
        delay_seconds: int
    ) -> bool:
        """
        Re-enqueue a message with a visibility delay
        
        Args:
            queue_name: Name of the queue
            message_data: Message data to enqueue
            delay_seconds: Delay in seconds before message becomes visible
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            # Update retry count
            retry_count = message_data.get("retry_count", 0) + 1
            message_data["retry_count"] = retry_count
            
            # Add retry metadata
            message_data["retry_metadata"] = {
                "last_retry_at": datetime.now().isoformat(),
                "retry_delay_seconds": delay_seconds,
                "retry_attempt": retry_count
            }
            
            # Remove queue metadata to avoid conflicts
            message_data.pop("_queue_metadata", None)
            
            # Serialize and send
            message_content = json.dumps(message_data)
            queue_client.send_message(
                content=message_content,
                visibility_timeout=delay_seconds,
                time_to_live=MESSAGE_PROCESSING_TIMEOUT
            )
            
            logger.debug(f"Message requeued with {delay_seconds}s delay to {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error requeuing message: {str(e)}")
            return False
    
    def get_queue_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get metrics for all queues
        
        Returns:
            Dict[str, Dict[str, Any]]: Metrics for each queue
        """
        try:
            metrics = {}
            
            # Get metrics for priority queues
            for priority, queue_name in PRIORITY_QUEUES.items():
                try:
                    queue_client = self.queue_clients.get(queue_name)
                    if queue_client:
                        properties = queue_client.get_queue_properties()
                        metrics[queue_name] = {
                            "priority": priority,
                            "message_count": properties.approximate_message_count,
                            "metadata": dict(properties.metadata),
                            "type": "priority"
                        }
                    else:
                        metrics[queue_name] = {
                            "priority": priority,
                            "message_count": 0,
                            "error": "Queue client not available",
                            "type": "priority"
                        }
                        
                except Exception as e:
                    metrics[queue_name] = {
                        "priority": priority,
                        "message_count": 0,
                        "error": str(e),
                        "type": "priority"
                    }
            
            # Get metrics for poison queues
            for priority, poison_queue_name in POISON_QUEUES.items():
                try:
                    poison_queue_client = self.poison_queue_clients.get(poison_queue_name)
                    if poison_queue_client:
                        properties = poison_queue_client.get_queue_properties()
                        metrics[poison_queue_name] = {
                            "priority": priority,
                            "message_count": properties.approximate_message_count,
                            "metadata": dict(properties.metadata),
                            "type": "poison"
                        }
                    else:
                        metrics[poison_queue_name] = {
                            "priority": priority,
                            "message_count": 0,
                            "error": "Queue client not available",
                            "type": "poison"
                        }
                        
                except Exception as e:
                    metrics[poison_queue_name] = {
                        "priority": priority,
                        "message_count": 0,
                        "error": str(e),
                        "type": "poison"
                    }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting queue metrics: {str(e)}")
            return {}
    
    def peek_messages(
        self,
        queue_name: str,
        max_messages: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Peek at messages in a queue without removing them
        
        Args:
            queue_name: Name of the queue
            max_messages: Maximum number of messages to peek
            
        Returns:
            List[Dict[str, Any]]: List of messages with metadata
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return []
            
            messages = queue_client.peek_messages(max_messages=max_messages)
            peeked_messages = []
            
            for message in messages:
                try:
                    message_data = json.loads(message.content)
                    peeked_messages.append({
                        "message_id": message.id,
                        "inserted_on": message.inserted_on.isoformat() if message.inserted_on else None,
                        "expires_on": message.expires_on.isoformat() if message.expires_on else None,
                        "content": message_data
                    })
                    
                except json.JSONDecodeError:
                    peeked_messages.append({
                        "message_id": message.id,
                        "error": "Failed to parse message content",
                        "raw_content": message.content[:200]
                    })
            
            return peeked_messages
            
        except Exception as e:
            logger.error(f"Error peeking messages: {str(e)}")
            return []
    
    def clear_queue(self, queue_name: str) -> bool:
        """
        Clear all messages from a queue
        
        Args:
            queue_name: Name of the queue
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            # Clear the queue
            queue_client.clear_messages()
            logger.info(f"Cleared queue: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing queue {queue_name}: {str(e)}")
            return False


# Global queue manager instance
_queue_manager = None


def get_queue_manager() -> QueueManager:
    """
    Get the global queue manager instance
    
    Returns:
        QueueManager: The manager instance
    """
    global _queue_manager
    
    if _queue_manager is None:
        _queue_manager = QueueManager()
        logger.debug("Created global queue manager instance")
    
    return _queue_manager