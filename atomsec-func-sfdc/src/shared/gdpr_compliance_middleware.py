"""
GDPR Compliance Middleware

This module provides middleware for GDPR compliance including:
- Automatic consent tracking
- Data processing logging
- Privacy-by-design enforcement
- Data subject rights handling

Requirements addressed: 1.2, 1.4
"""

import logging
import json
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import azure.functions as func
from functools import wraps

from src.shared.data_protection_service import (
    get_data_protection_service, 
    DataCategory, 
    ProcessingPurpose,
    DataClassification
)
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


@dataclass
class ConsentRecord:
    """Consent record for GDPR compliance"""
    user_id: str
    consent_type: str
    consent_given: bool
    consent_timestamp: datetime
    consent_version: str
    processing_purposes: List[ProcessingPurpose]
    data_categories: List[DataCategory]
    legal_basis: str
    withdrawal_timestamp: Optional[datetime] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class GDPRComplianceMiddleware:
    """
    GDPR compliance middleware for automatic compliance enforcement
    """
    
    def __init__(self):
        self.data_protection = get_data_protection_service()
        self.monitoring = get_monitoring_service()
        
        # Consent tracking
        self._consent_records: Dict[str, List[ConsentRecord]] = {}
        
        # Configuration
        self.config = {
            'require_explicit_consent': True,
            'consent_version': '1.0',
            'data_retention_warning_days': 30,
            'auto_classify_data': True,
            'log_all_processing': True,
            'privacy_by_design': True,
        }
        
        # Endpoint-specific processing purposes
        self.endpoint_purposes = {
            '/api/auth/login': [ProcessingPurpose.AUTHENTICATION],
            '/api/auth/logout': [ProcessingPurpose.AUTHENTICATION],
            '/api/users': [ProcessingPurpose.AUTHORIZATION, ProcessingPurpose.BUSINESS_ANALYTICS],
            '/api/integrations': [ProcessingPurpose.BUSINESS_ANALYTICS, ProcessingPurpose.AUDIT_LOGGING],
            '/api/security': [ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.AUDIT_LOGGING],
            '/api/policies': [ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.LEGAL_COMPLIANCE],
            '/api/key-vault': [ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.LEGAL_COMPLIANCE],
            '/api/pmd': [ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.BUSINESS_ANALYTICS],
        }
    
    def process_request(self, req: func.HttpRequest, 
                       endpoint: str,
                       user_id: str = None) -> Dict[str, Any]:
        """
        Process request for GDPR compliance
        
        Args:
            req: HTTP request
            endpoint: Endpoint being accessed
            user_id: User identifier
            
        Returns:
            Dict containing compliance status and any required actions
        """
        try:
            compliance_result = {
                'compliant': True,
                'consent_required': False,
                'data_processing_logged': False,
                'warnings': [],
                'actions_required': []
            }
            
            # Determine processing purposes for endpoint
            processing_purposes = self._get_processing_purposes(endpoint)
            
            # Check consent requirements
            if user_id and self.config['require_explicit_consent']:
                consent_status = self._check_consent_status(user_id, processing_purposes)
                if not consent_status['valid']:
                    compliance_result['consent_required'] = True
                    compliance_result['compliant'] = False
                    compliance_result['actions_required'].append({
                        'action': 'obtain_consent',
                        'purposes': [p.value for p in processing_purposes],
                        'legal_basis': 'consent'
                    })
            
            # Log data processing activity
            if user_id and self.config['log_all_processing']:
                self._log_data_processing(user_id, endpoint, processing_purposes, req)
                compliance_result['data_processing_logged'] = True
            
            # Check for sensitive data in request
            if req.method in ['POST', 'PUT', 'PATCH']:
                sensitive_data_check = self._check_request_for_sensitive_data(req)
                if sensitive_data_check['contains_sensitive']:
                    compliance_result['warnings'].append({
                        'type': 'sensitive_data_detected',
                        'fields': sensitive_data_check['sensitive_fields'],
                        'recommendation': 'Ensure proper encryption and consent'
                    })
            
            # Privacy by design checks
            if self.config['privacy_by_design']:
                privacy_check = self._privacy_by_design_check(req, endpoint)
                if privacy_check['violations']:
                    compliance_result['warnings'].extend(privacy_check['violations'])
            
            return compliance_result
            
        except Exception as e:
            logger.error(f"GDPR compliance processing failed: {str(e)}")
            return {
                'compliant': False,
                'error': str(e),
                'actions_required': [{'action': 'manual_review', 'reason': 'compliance_check_failed'}]
            }
    
    def process_response(self, response_data: Dict[str, Any],
                        user_id: str = None,
                        endpoint: str = None) -> Dict[str, Any]:
        """
        Process response for GDPR compliance
        
        Args:
            response_data: Response data to process
            user_id: User identifier
            endpoint: Endpoint that generated response
            
        Returns:
            Processed response data with privacy protections applied
        """
        try:
            if not response_data or not isinstance(response_data, dict):
                return response_data
            
            # Classify response data
            if self.config['auto_classify_data']:
                classifications = self.data_protection.classify_data(response_data)
                
                # Apply data minimization
                minimized_data = self._apply_data_minimization(
                    response_data, classifications, endpoint
                )
                
                # Encrypt sensitive fields if needed
                protected_data = self._apply_response_protection(
                    minimized_data, classifications
                )
                
                return protected_data
            
            return response_data
            
        except Exception as e:
            logger.error(f"Response processing failed: {str(e)}")
            return response_data
    
    def record_consent(self, user_id: str, 
                      consent_type: str,
                      consent_given: bool,
                      processing_purposes: List[ProcessingPurpose],
                      data_categories: List[DataCategory],
                      legal_basis: str = "consent",
                      request_context: Dict[str, Any] = None) -> str:
        """
        Record user consent for GDPR compliance
        
        Args:
            user_id: User identifier
            consent_type: Type of consent (e.g., 'analytics', 'marketing')
            consent_given: Whether consent was given
            processing_purposes: Purposes for data processing
            data_categories: Categories of data
            legal_basis: Legal basis for processing
            request_context: Request context (IP, user agent, etc.)
            
        Returns:
            Consent record ID
        """
        try:
            consent_record = ConsentRecord(
                user_id=user_id,
                consent_type=consent_type,
                consent_given=consent_given,
                consent_timestamp=datetime.utcnow(),
                consent_version=self.config['consent_version'],
                processing_purposes=processing_purposes,
                data_categories=data_categories,
                legal_basis=legal_basis,
                ip_address=request_context.get('ip_address') if request_context else None,
                user_agent=request_context.get('user_agent') if request_context else None
            )
            
            # Store consent record
            if user_id not in self._consent_records:
                self._consent_records[user_id] = []
            
            self._consent_records[user_id].append(consent_record)
            
            # Create data processing record
            if consent_given:
                processing_record_id = self.data_protection.create_processing_record(
                    data_subject_id=user_id,
                    data_categories=data_categories,
                    processing_purposes=processing_purposes,
                    legal_basis=legal_basis,
                    consent_given=True
                )
            
            # Track in monitoring
            self.monitoring.track_custom_metric(
                'gdpr_consent_recorded',
                1.0,
                {
                    'user_id': user_id,
                    'consent_type': consent_type,
                    'consent_given': consent_given,
                    'legal_basis': legal_basis
                }
            )
            
            logger.info(f"Consent recorded: {consent_type} for user {user_id} (given: {consent_given})")
            
            return f"consent_{user_id}_{int(datetime.utcnow().timestamp())}"
            
        except Exception as e:
            logger.error(f"Failed to record consent: {str(e)}")
            return ""
    
    def withdraw_consent(self, user_id: str, consent_type: str) -> bool:
        """
        Withdraw user consent
        
        Args:
            user_id: User identifier
            consent_type: Type of consent to withdraw
            
        Returns:
            True if consent was withdrawn successfully
        """
        try:
            user_consents = self._consent_records.get(user_id, [])
            
            # Find and update consent record
            for consent in user_consents:
                if (consent.consent_type == consent_type and 
                    consent.consent_given and 
                    consent.withdrawal_timestamp is None):
                    
                    consent.withdrawal_timestamp = datetime.utcnow()
                    
                    # Track withdrawal
                    self.monitoring.track_custom_metric(
                        'gdpr_consent_withdrawn',
                        1.0,
                        {
                            'user_id': user_id,
                            'consent_type': consent_type
                        }
                    )
                    
                    logger.info(f"Consent withdrawn: {consent_type} for user {user_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to withdraw consent: {str(e)}")
            return False
    
    def get_user_consents(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all consent records for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            List of consent records
        """
        try:
            user_consents = self._consent_records.get(user_id, [])
            
            return [
                {
                    'consent_type': consent.consent_type,
                    'consent_given': consent.consent_given,
                    'consent_timestamp': consent.consent_timestamp.isoformat(),
                    'consent_version': consent.consent_version,
                    'processing_purposes': [p.value for p in consent.processing_purposes],
                    'data_categories': [c.value for c in consent.data_categories],
                    'legal_basis': consent.legal_basis,
                    'withdrawal_timestamp': consent.withdrawal_timestamp.isoformat() if consent.withdrawal_timestamp else None,
                    'active': consent.consent_given and consent.withdrawal_timestamp is None
                }
                for consent in user_consents
            ]
            
        except Exception as e:
            logger.error(f"Failed to get user consents: {str(e)}")
            return []
    
    def check_data_retention(self) -> Dict[str, Any]:
        """
        Check data retention compliance
        
        Returns:
            Dict containing retention compliance status
        """
        try:
            return self.data_protection.check_retention_compliance()
            
        except Exception as e:
            logger.error(f"Data retention check failed: {str(e)}")
            return {'error': str(e)}
    
    def handle_data_subject_request(self, request_type: str, 
                                  user_id: str,
                                  verification_data: Dict[str, Any] = None) -> str:
        """
        Handle GDPR data subject request
        
        Args:
            request_type: Type of request
            user_id: User identifier
            verification_data: Verification data
            
        Returns:
            Request ID
        """
        try:
            return self.data_protection.handle_gdpr_request(
                request_type, user_id, verification_data
            )
            
        except Exception as e:
            logger.error(f"Failed to handle data subject request: {str(e)}")
            return ""
    
    # Private helper methods
    
    def _get_processing_purposes(self, endpoint: str) -> List[ProcessingPurpose]:
        """Get processing purposes for endpoint"""
        # Check exact match first
        if endpoint in self.endpoint_purposes:
            return self.endpoint_purposes[endpoint]
        
        # Check pattern matches
        for pattern, purposes in self.endpoint_purposes.items():
            if '*' in pattern:
                pattern_parts = pattern.split('*')
                if len(pattern_parts) == 2:
                    if endpoint.startswith(pattern_parts[0]) and endpoint.endswith(pattern_parts[1]):
                        return purposes
        
        # Default purposes
        return [ProcessingPurpose.BUSINESS_ANALYTICS]
    
    def _check_consent_status(self, user_id: str, 
                            processing_purposes: List[ProcessingPurpose]) -> Dict[str, Any]:
        """Check if user has valid consent for processing purposes"""
        user_consents = self._consent_records.get(user_id, [])
        
        # Check if we have valid consent for all required purposes
        valid_consents = []
        for consent in user_consents:
            if (consent.consent_given and 
                consent.withdrawal_timestamp is None and
                any(purpose in consent.processing_purposes for purpose in processing_purposes)):
                valid_consents.append(consent)
        
        # Check if all purposes are covered
        covered_purposes = set()
        for consent in valid_consents:
            covered_purposes.update(consent.processing_purposes)
        
        missing_purposes = [p for p in processing_purposes if p not in covered_purposes]
        
        return {
            'valid': len(missing_purposes) == 0,
            'missing_purposes': missing_purposes,
            'valid_consents': len(valid_consents)
        }
    
    def _log_data_processing(self, user_id: str, endpoint: str,
                           processing_purposes: List[ProcessingPurpose],
                           request: func.HttpRequest):
        """Log data processing activity"""
        try:
            # Determine data categories being processed
            data_categories = self._infer_data_categories(endpoint, request)
            
            # Create processing record
            self.data_protection.create_processing_record(
                data_subject_id=user_id,
                data_categories=data_categories,
                processing_purposes=processing_purposes,
                legal_basis="legitimate_interest",  # Default, should be determined properly
                data_sources=[f"endpoint:{endpoint}"]
            )
            
        except Exception as e:
            logger.error(f"Failed to log data processing: {str(e)}")
    
    def _infer_data_categories(self, endpoint: str, 
                             request: func.HttpRequest) -> List[DataCategory]:
        """Infer data categories from endpoint and request"""
        categories = [DataCategory.TECHNICAL_DATA]  # Always includes technical data
        
        # Check endpoint patterns
        if '/users' in endpoint or '/profile' in endpoint:
            categories.append(DataCategory.PERSONAL_DATA)
        
        if '/auth' in endpoint:
            categories.append(DataCategory.PERSONAL_DATA)
        
        if '/security' in endpoint or '/policies' in endpoint:
            categories.append(DataCategory.SENSITIVE_PERSONAL_DATA)
        
        # Check request data
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                request_data = request.get_json()
                if request_data:
                    classifications = self.data_protection.classify_data(request_data)
                    for field, rule in classifications.items():
                        if rule.category not in categories:
                            categories.append(rule.category)
            except:
                pass  # Ignore JSON parsing errors
        
        return categories
    
    def _check_request_for_sensitive_data(self, request: func.HttpRequest) -> Dict[str, Any]:
        """Check request for sensitive data"""
        result = {
            'contains_sensitive': False,
            'sensitive_fields': [],
            'classifications': {}
        }
        
        try:
            if request.method in ['POST', 'PUT', 'PATCH']:
                request_data = request.get_json()
                if request_data:
                    classifications = self.data_protection.classify_data(request_data)
                    
                    for field, rule in classifications.items():
                        if rule.classification in [DataClassification.CONFIDENTIAL, 
                                                 DataClassification.RESTRICTED,
                                                 DataClassification.TOP_SECRET]:
                            result['contains_sensitive'] = True
                            result['sensitive_fields'].append(field)
                            result['classifications'][field] = rule.classification.value
        
        except Exception as e:
            logger.debug(f"Could not check request for sensitive data: {str(e)}")
        
        return result
    
    def _privacy_by_design_check(self, request: func.HttpRequest, 
                               endpoint: str) -> Dict[str, Any]:
        """Perform privacy by design checks"""
        violations = []
        
        # Check for data minimization
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                request_data = request.get_json()
                if request_data and len(request_data) > 20:  # Arbitrary threshold
                    violations.append({
                        'type': 'data_minimization',
                        'severity': 'medium',
                        'description': f'Request contains {len(request_data)} fields - consider data minimization',
                        'recommendation': 'Only collect necessary data'
                    })
            except:
                pass
        
        # Check for encryption requirements
        if not request.url.startswith('https://'):
            violations.append({
                'type': 'encryption_in_transit',
                'severity': 'high',
                'description': 'Request not using HTTPS',
                'recommendation': 'Use HTTPS for all requests'
            })
        
        # Check for sensitive endpoints without proper protection
        sensitive_patterns = ['/password', '/secret', '/key', '/token']
        if any(pattern in endpoint.lower() for pattern in sensitive_patterns):
            # Should have additional security measures
            violations.append({
                'type': 'sensitive_endpoint_access',
                'severity': 'medium',
                'description': 'Access to sensitive endpoint detected',
                'recommendation': 'Ensure additional security measures are in place'
            })
        
        return {'violations': violations}
    
    def _apply_data_minimization(self, data: Dict[str, Any],
                               classifications: Dict[str, Any],
                               endpoint: str) -> Dict[str, Any]:
        """Apply data minimization principles"""
        # Determine which fields are necessary for the endpoint
        necessary_fields = self._get_necessary_fields(endpoint)
        
        if necessary_fields:
            # Only include necessary fields
            minimized_data = {
                field: value for field, value in data.items()
                if field in necessary_fields or field in ['id', 'timestamp', 'status']  # Always include these
            }
            return minimized_data
        
        return data
    
    def _get_necessary_fields(self, endpoint: str) -> Optional[Set[str]]:
        """Get necessary fields for endpoint"""
        # Define necessary fields per endpoint type
        field_requirements = {
            '/api/users': {'id', 'email', 'name', 'status'},
            '/api/auth': {'email', 'token', 'expires_at'},
            '/api/integrations': {'id', 'name', 'status', 'type'},
            '/api/security': {'id', 'risk_score', 'status', 'timestamp'},
        }
        
        for pattern, fields in field_requirements.items():
            if pattern in endpoint:
                return fields
        
        return None
    
    def _apply_response_protection(self, data: Dict[str, Any],
                                 classifications: Dict[str, Any]) -> Dict[str, Any]:
        """Apply protection to response data"""
        protected_data = data.copy()
        
        for field, rule in classifications.items():
            if field in protected_data:
                # Mask sensitive fields in responses
                if rule.classification in [DataClassification.CONFIDENTIAL,
                                         DataClassification.RESTRICTED,
                                         DataClassification.TOP_SECRET]:
                    
                    if 'email' in field.lower():
                        # Mask email
                        email = str(protected_data[field])
                        if '@' in email:
                            parts = email.split('@')
                            protected_data[field] = f"{parts[0][:2]}***@{parts[1]}"
                    elif 'phone' in field.lower():
                        # Mask phone
                        phone = str(protected_data[field])
                        protected_data[field] = f"***-***-{phone[-4:]}" if len(phone) >= 4 else "***"
                    elif any(sensitive in field.lower() for sensitive in ['password', 'secret', 'key']):
                        # Never return these fields
                        del protected_data[field]
        
        return protected_data


# Decorator for GDPR compliance
def gdpr_compliant(require_consent: bool = False,
                  processing_purposes: List[ProcessingPurpose] = None):
    """
    Decorator to enforce GDPR compliance on endpoints
    
    Args:
        require_consent: Whether explicit consent is required
        processing_purposes: Data processing purposes
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(req: func.HttpRequest):
            try:
                middleware = GDPRComplianceMiddleware()
                
                # Extract user ID from request
                user_id = getattr(req, 'user', {}).get('id') if hasattr(req, 'user') else None
                endpoint = req.url
                
                # Process request for compliance
                compliance_result = middleware.process_request(req, endpoint, user_id)
                
                # Check if consent is required
                if require_consent and compliance_result.get('consent_required'):
                    return func.HttpResponse(
                        json.dumps({
                            'success': False,
                            'error': 'Consent required',
                            'consent_required': True,
                            'processing_purposes': [p.value for p in (processing_purposes or [])],
                            'actions_required': compliance_result.get('actions_required', [])
                        }),
                        mimetype='application/json',
                        status_code=403
                    )
                
                # Call original function
                response = func(req)
                
                # Process response for compliance
                if hasattr(response, 'get_body'):
                    try:
                        response_data = json.loads(response.get_body().decode('utf-8'))
                        protected_data = middleware.process_response(response_data, user_id, endpoint)
                        
                        # Update response with protected data
                        if protected_data != response_data:
                            response = func.HttpResponse(
                                json.dumps(protected_data),
                                mimetype='application/json',
                                status_code=response.status_code
                            )
                    except:
                        pass  # Ignore JSON processing errors
                
                # Add compliance headers
                if hasattr(response, 'headers'):
                    response.headers['X-GDPR-Compliant'] = 'true'
                    if compliance_result.get('warnings'):
                        response.headers['X-GDPR-Warnings'] = str(len(compliance_result['warnings']))
                
                return response
                
            except Exception as e:
                logger.error(f"GDPR compliance middleware error: {str(e)}")
                # Continue with original function if middleware fails
                return func(req)
        
        return wrapper
    return decorator


# Global middleware instance
_gdpr_middleware = None


def get_gdpr_compliance_middleware() -> GDPRComplianceMiddleware:
    """Get the global GDPR compliance middleware instance"""
    global _gdpr_middleware
    if _gdpr_middleware is None:
        _gdpr_middleware = GDPRComplianceMiddleware()
    return _gdpr_middleware