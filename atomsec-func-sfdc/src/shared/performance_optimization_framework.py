"""
Performance Optimization Framework

This module provides comprehensive performance optimization capabilities including
profiling, bottleneck detection, database query optimization, and resource usage optimization.

Features:
- Real-time performance profiling
- Bottleneck detection and analysis
- Database query optimization
- Memory and CPU usage optimization
- Resource usage monitoring
- Performance recommendations
"""

import logging
import time
import psutil
import threading
import functools
import traceback
import gc
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum
import statistics
import weakref
import sys
import os

# Configure module-level logger
logger = logging.getLogger(__name__)

class PerformanceMetricType(Enum):
    """Performance metric types"""
    EXECUTION_TIME = "execution_time"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    DATABASE_QUERY_TIME = "database_query_time"
    API_RESPONSE_TIME = "api_response_time"
    CACHE_HIT_RATE = "cache_hit_rate"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"

class BottleneckType(Enum):
    """Bottleneck types"""
    CPU_BOUND = "cpu_bound"
    MEMORY_BOUND = "memory_bound"
    IO_BOUND = "io_bound"
    DATABASE_BOUND = "database_bound"
    NETWORK_BOUND = "network_bound"
    CACHE_MISS = "cache_miss"

@dataclass
class PerformanceMetric:
    """Performance metric data"""
    timestamp: float
    metric_type: PerformanceMetricType
    value: float
    context: Dict[str, Any]
    function_name: str
    duration: float

@dataclass
class BottleneckAnalysis:
    """Bottleneck analysis result"""
    bottleneck_type: BottleneckType
    severity: str  # 'low', 'medium', 'high', 'critical'
    affected_functions: List[str]
    impact_score: float
    description: str
    recommendations: List[str]
    detected_at: float

@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation"""
    category: str
    priority: str
    title: str
    description: str
    implementation_effort: str
    expected_improvement: str
    code_example: Optional[str] = None

class PerformanceProfiler:
    """Advanced performance profiler"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize performance profiler"""
        self.config = config
        self.metrics = deque(maxlen=config.get('max_metrics', 10000))
        self.function_stats = defaultdict(list)
        self.bottlenecks = deque(maxlen=100)
        self.lock = threading.RLock()
        
        # Profiling configuration
        self.enabled = config.get('enabled', True)
        self.sampling_rate = config.get('sampling_rate', 1.0)
        self.detailed_profiling = config.get('detailed_profiling', False)
        self.memory_profiling = config.get('memory_profiling', True)
        
        # Performance thresholds
        self.thresholds = config.get('thresholds', {
            'execution_time': 1.0,
            'memory_usage': 100 * 1024 * 1024,  # 100MB
            'cpu_usage': 80.0,
            'database_query_time': 0.5
        })
        
        # Start background monitoring
        self._start_monitoring()
    
    def profile_function(self, func: Callable) -> Callable:
        """Decorator to profile function performance"""
        if not self.enabled:
            return func
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check sampling rate
            if self.sampling_rate < 1.0 and time.time() % 1 > self.sampling_rate:
                return func(*args, **kwargs)
            
            start_time = time.time()
            start_memory = self._get_memory_usage() if self.memory_profiling else 0
            start_cpu = psutil.cpu_percent()
            
            try:
                result = func(*args, **kwargs)
                success = True
                error = None
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                end_time = time.time()
                end_memory = self._get_memory_usage() if self.memory_profiling else 0
                end_cpu = psutil.cpu_percent()
                
                duration = end_time - start_time
                memory_delta = end_memory - start_memory
                cpu_delta = end_cpu - start_cpu
                
                # Record metrics
                self._record_function_metrics(
                    func.__name__,
                    duration,
                    memory_delta,
                    cpu_delta,
                    success,
                    error,
                    args,
                    kwargs
                )
            
            return result
        
        return wrapper
    
    def _record_function_metrics(self, function_name: str, duration: float,
                                memory_delta: float, cpu_delta: float,
                                success: bool, error: Optional[str],
                                args: tuple, kwargs: dict):
        """Record function performance metrics"""
        try:
            timestamp = time.time()
            
            # Create context
            context = {
                'success': success,
                'error': error,
                'args_count': len(args),
                'kwargs_count': len(kwargs),
                'thread_id': threading.get_ident()
            }
            
            # Record execution time metric
            execution_metric = PerformanceMetric(
                timestamp=timestamp,
                metric_type=PerformanceMetricType.EXECUTION_TIME,
                value=duration,
                context=context,
                function_name=function_name,
                duration=duration
            )
            
            with self.lock:
                self.metrics.append(execution_metric)
                self.function_stats[function_name].append({
                    'timestamp': timestamp,
                    'duration': duration,
                    'memory_delta': memory_delta,
                    'cpu_delta': cpu_delta,
                    'success': success
                })
                
                # Keep only recent stats
                cutoff = timestamp - 3600  # 1 hour
                self.function_stats[function_name] = [
                    stat for stat in self.function_stats[function_name]
                    if stat['timestamp'] > cutoff
                ]
            
            # Record memory metric if significant change
            if self.memory_profiling and abs(memory_delta) > 1024 * 1024:  # 1MB
                memory_metric = PerformanceMetric(
                    timestamp=timestamp,
                    metric_type=PerformanceMetricType.MEMORY_USAGE,
                    value=memory_delta,
                    context=context,
                    function_name=function_name,
                    duration=duration
                )
                
                with self.lock:
                    self.metrics.append(memory_metric)
            
            # Check for performance issues
            self._check_performance_thresholds(function_name, duration, memory_delta, cpu_delta)
            
        except Exception as e:
            logger.error(f"Error recording function metrics: {e}")
    
    def _check_performance_thresholds(self, function_name: str, duration: float,
                                    memory_delta: float, cpu_delta: float):
        """Check if performance metrics exceed thresholds"""
        try:
            issues = []
            
            if duration > self.thresholds.get('execution_time', 1.0):
                issues.append(f"Slow execution: {duration:.3f}s")
            
            if abs(memory_delta) > self.thresholds.get('memory_usage', 100 * 1024 * 1024):
                issues.append(f"High memory usage: {memory_delta / 1024 / 1024:.1f}MB")
            
            if cpu_delta > self.thresholds.get('cpu_usage', 80.0):
                issues.append(f"High CPU usage: {cpu_delta:.1f}%")
            
            if issues:
                logger.warning(f"Performance issues in {function_name}: {', '.join(issues)}")
                
                # Trigger bottleneck analysis
                self._analyze_potential_bottleneck(function_name, duration, memory_delta, cpu_delta)
                
        except Exception as e:
            logger.error(f"Error checking performance thresholds: {e}")
    
    def _analyze_potential_bottleneck(self, function_name: str, duration: float,
                                    memory_delta: float, cpu_delta: float):
        """Analyze potential bottleneck"""
        try:
            bottleneck_type = None
            severity = 'medium'
            impact_score = 0.0
            
            # Determine bottleneck type
            if cpu_delta > 70:
                bottleneck_type = BottleneckType.CPU_BOUND
                impact_score = min(cpu_delta / 100, 1.0)
            elif abs(memory_delta) > 50 * 1024 * 1024:  # 50MB
                bottleneck_type = BottleneckType.MEMORY_BOUND
                impact_score = min(abs(memory_delta) / (100 * 1024 * 1024), 1.0)
            elif duration > 2.0:
                bottleneck_type = BottleneckType.IO_BOUND
                impact_score = min(duration / 5.0, 1.0)
            
            if bottleneck_type:
                # Determine severity
                if impact_score > 0.8:
                    severity = 'critical'
                elif impact_score > 0.6:
                    severity = 'high'
                elif impact_score > 0.3:
                    severity = 'medium'
                else:
                    severity = 'low'
                
                # Generate recommendations
                recommendations = self._generate_bottleneck_recommendations(bottleneck_type, function_name)
                
                bottleneck = BottleneckAnalysis(
                    bottleneck_type=bottleneck_type,
                    severity=severity,
                    affected_functions=[function_name],
                    impact_score=impact_score,
                    description=f"Performance bottleneck detected in {function_name}",
                    recommendations=recommendations,
                    detected_at=time.time()
                )
                
                with self.lock:
                    self.bottlenecks.append(bottleneck)
                
                logger.warning(f"Bottleneck detected: {asdict(bottleneck)}")
                
        except Exception as e:
            logger.error(f"Error analyzing potential bottleneck: {e}")
    
    def _generate_bottleneck_recommendations(self, bottleneck_type: BottleneckType,
                                           function_name: str) -> List[str]:
        """Generate recommendations for bottleneck type"""
        recommendations = []
        
        if bottleneck_type == BottleneckType.CPU_BOUND:
            recommendations.extend([
                "Consider optimizing algorithms for better time complexity",
                "Use caching to avoid repeated calculations",
                "Implement parallel processing where possible",
                "Profile code to identify CPU-intensive operations"
            ])
        elif bottleneck_type == BottleneckType.MEMORY_BOUND:
            recommendations.extend([
                "Optimize data structures to use less memory",
                "Implement streaming for large datasets",
                "Use generators instead of lists where possible",
                "Clear unused variables and call gc.collect()"
            ])
        elif bottleneck_type == BottleneckType.IO_BOUND:
            recommendations.extend([
                "Implement asynchronous I/O operations",
                "Use connection pooling for database operations",
                "Add caching for frequently accessed data",
                "Optimize database queries"
            ])
        elif bottleneck_type == BottleneckType.DATABASE_BOUND:
            recommendations.extend([
                "Add database indexes for frequently queried columns",
                "Optimize SQL queries and use EXPLAIN PLAN",
                "Implement query result caching",
                "Use batch operations for multiple database calls"
            ])
        elif bottleneck_type == BottleneckType.NETWORK_BOUND:
            recommendations.extend([
                "Implement request/response caching",
                "Use HTTP connection pooling",
                "Compress request/response data",
                "Implement retry logic with exponential backoff"
            ])
        
        return recommendations
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in bytes"""
        try:
            process = psutil.Process()
            return process.memory_info().rss
        except Exception:
            return 0.0
    
    def _start_monitoring(self):
        """Start background performance monitoring"""
        def monitor_worker():
            while True:
                try:
                    self._collect_system_metrics()
                    self._analyze_performance_trends()
                    time.sleep(60)  # Run every minute
                except Exception as e:
                    logger.error(f"Performance monitor error: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
    
    def _collect_system_metrics(self):
        """Collect system-wide performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_metric = PerformanceMetric(
                timestamp=time.time(),
                metric_type=PerformanceMetricType.CPU_USAGE,
                value=cpu_percent,
                context={'system_wide': True},
                function_name='system',
                duration=0.0
            )
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_metric = PerformanceMetric(
                timestamp=time.time(),
                metric_type=PerformanceMetricType.MEMORY_USAGE,
                value=memory.percent,
                context={'available_mb': memory.available / 1024 / 1024},
                function_name='system',
                duration=0.0
            )
            
            with self.lock:
                self.metrics.append(cpu_metric)
                self.metrics.append(memory_metric)
                
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _analyze_performance_trends(self):
        """Analyze performance trends and detect degradation"""
        try:
            current_time = time.time()
            cutoff = current_time - 1800  # 30 minutes
            
            with self.lock:
                recent_metrics = [m for m in self.metrics if m.timestamp > cutoff]
            
            # Group metrics by function and type
            function_metrics = defaultdict(lambda: defaultdict(list))
            for metric in recent_metrics:
                function_metrics[metric.function_name][metric.metric_type].append(metric.value)
            
            # Analyze trends for each function
            for function_name, metrics_by_type in function_metrics.items():
                for metric_type, values in metrics_by_type.items():
                    if len(values) >= 10:  # Need sufficient data points
                        self._check_performance_degradation(function_name, metric_type, values)
                        
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {e}")
    
    def _check_performance_degradation(self, function_name: str,
                                     metric_type: PerformanceMetricType,
                                     values: List[float]):
        """Check for performance degradation in function"""
        try:
            if len(values) < 10:
                return
            
            # Split into two halves for comparison
            mid_point = len(values) // 2
            first_half = values[:mid_point]
            second_half = values[mid_point:]
            
            first_avg = statistics.mean(first_half)
            second_avg = statistics.mean(second_half)
            
            # Check for significant degradation (>20% increase)
            if metric_type in [PerformanceMetricType.EXECUTION_TIME, PerformanceMetricType.MEMORY_USAGE]:
                if second_avg > first_avg * 1.2:
                    degradation_percent = ((second_avg - first_avg) / first_avg) * 100
                    logger.warning(f"Performance degradation detected in {function_name} "
                                 f"({metric_type.value}): {degradation_percent:.1f}% increase")
                    
        except Exception as e:
            logger.error(f"Error checking performance degradation: {e}")
    
    def get_performance_summary(self, time_window: int = 3600) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            cutoff = time.time() - time_window
            
            with self.lock:
                recent_metrics = [m for m in self.metrics if m.timestamp > cutoff]
                recent_bottlenecks = [b for b in self.bottlenecks if b.detected_at > cutoff]
            
            if not recent_metrics:
                return {'status': 'no_data', 'time_window': time_window}
            
            # Group metrics by function
            function_performance = defaultdict(lambda: defaultdict(list))
            for metric in recent_metrics:
                function_performance[metric.function_name][metric.metric_type.value].append(metric.value)
            
            # Calculate statistics
            summary = {
                'time_window': time_window,
                'total_metrics': len(recent_metrics),
                'functions_analyzed': len(function_performance),
                'bottlenecks_detected': len(recent_bottlenecks),
                'function_performance': {},
                'system_performance': {},
                'bottleneck_summary': {}
            }
            
            # Function performance statistics
            for function_name, metrics_by_type in function_performance.items():
                if function_name == 'system':
                    continue
                
                function_stats = {}
                for metric_type, values in metrics_by_type.items():
                    if values:
                        function_stats[metric_type] = {
                            'count': len(values),
                            'avg': statistics.mean(values),
                            'min': min(values),
                            'max': max(values),
                            'p95': sorted(values)[int(len(values) * 0.95)] if len(values) > 1 else values[0]
                        }
                
                summary['function_performance'][function_name] = function_stats
            
            # System performance
            system_metrics = function_performance.get('system', {})
            for metric_type, values in system_metrics.items():
                if values:
                    summary['system_performance'][metric_type] = {
                        'current': values[-1],
                        'avg': statistics.mean(values),
                        'max': max(values)
                    }
            
            # Bottleneck summary
            bottleneck_by_type = defaultdict(int)
            bottleneck_by_severity = defaultdict(int)
            for bottleneck in recent_bottlenecks:
                bottleneck_by_type[bottleneck.bottleneck_type.value] += 1
                bottleneck_by_severity[bottleneck.severity] += 1
            
            summary['bottleneck_summary'] = {
                'by_type': dict(bottleneck_by_type),
                'by_severity': dict(bottleneck_by_severity),
                'total': len(recent_bottlenecks)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {'error': str(e)}
    
    def get_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        try:
            summary = self.get_performance_summary()
            
            if summary.get('status') == 'no_data':
                return recommendations
            
            # Analyze function performance
            function_performance = summary.get('function_performance', {})
            for function_name, metrics in function_performance.items():
                execution_time = metrics.get('execution_time', {})
                memory_usage = metrics.get('memory_usage', {})
                
                # Slow function recommendation
                if execution_time.get('avg', 0) > 1.0:
                    recommendations.append(OptimizationRecommendation(
                        category='performance',
                        priority='high',
                        title=f'Optimize {function_name} execution time',
                        description=f'Function has average execution time of {execution_time["avg"]:.3f}s',
                        implementation_effort='medium',
                        expected_improvement='30-50% faster execution',
                        code_example='# Add caching, optimize algorithms, or use async operations'
                    ))
                
                # Memory usage recommendation
                if memory_usage.get('max', 0) > 50 * 1024 * 1024:  # 50MB
                    recommendations.append(OptimizationRecommendation(
                        category='memory',
                        priority='medium',
                        title=f'Optimize {function_name} memory usage',
                        description=f'Function uses up to {memory_usage["max"] / 1024 / 1024:.1f}MB memory',
                        implementation_effort='medium',
                        expected_improvement='20-40% memory reduction',
                        code_example='# Use generators, optimize data structures, clear unused variables'
                    ))
            
            # System performance recommendations
            system_performance = summary.get('system_performance', {})
            cpu_usage = system_performance.get('cpu_usage', {})
            memory_usage = system_performance.get('memory_usage', {})
            
            if cpu_usage.get('avg', 0) > 70:
                recommendations.append(OptimizationRecommendation(
                    category='system',
                    priority='high',
                    title='High CPU usage detected',
                    description=f'Average CPU usage is {cpu_usage["avg"]:.1f}%',
                    implementation_effort='high',
                    expected_improvement='Improved system stability and response times',
                    code_example='# Implement CPU-intensive task queuing or parallel processing'
                ))
            
            if memory_usage.get('avg', 0) > 80:
                recommendations.append(OptimizationRecommendation(
                    category='system',
                    priority='high',
                    title='High memory usage detected',
                    description=f'Average memory usage is {memory_usage["avg"]:.1f}%',
                    implementation_effort='medium',
                    expected_improvement='Reduced memory pressure and better stability',
                    code_example='# Implement memory cleanup, optimize data structures'
                ))
            
            # Bottleneck-based recommendations
            bottleneck_summary = summary.get('bottleneck_summary', {})
            bottlenecks_by_type = bottleneck_summary.get('by_type', {})
            
            for bottleneck_type, count in bottlenecks_by_type.items():
                if count > 0:
                    recommendations.extend(self._get_bottleneck_recommendations(bottleneck_type, count))
            
            # Sort by priority
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            recommendations.sort(key=lambda r: priority_order.get(r.priority, 3))
            
            return recommendations[:10]  # Return top 10 recommendations
            
        except Exception as e:
            logger.error(f"Error generating optimization recommendations: {e}")
            return []
    
    def _get_bottleneck_recommendations(self, bottleneck_type: str, count: int) -> List[OptimizationRecommendation]:
        """Get recommendations for specific bottleneck type"""
        recommendations = []
        
        if bottleneck_type == 'cpu_bound':
            recommendations.append(OptimizationRecommendation(
                category='cpu',
                priority='high',
                title='CPU bottleneck optimization',
                description=f'{count} CPU-bound bottlenecks detected',
                implementation_effort='medium',
                expected_improvement='20-40% CPU usage reduction',
                code_example='# Use caching, optimize algorithms, implement parallel processing'
            ))
        elif bottleneck_type == 'memory_bound':
            recommendations.append(OptimizationRecommendation(
                category='memory',
                priority='medium',
                title='Memory bottleneck optimization',
                description=f'{count} memory-bound bottlenecks detected',
                implementation_effort='medium',
                expected_improvement='30-50% memory usage reduction',
                code_example='# Use generators, optimize data structures, implement streaming'
            ))
        elif bottleneck_type == 'io_bound':
            recommendations.append(OptimizationRecommendation(
                category='io',
                priority='high',
                title='I/O bottleneck optimization',
                description=f'{count} I/O-bound bottlenecks detected',
                implementation_effort='high',
                expected_improvement='50-70% I/O performance improvement',
                code_example='# Implement async I/O, connection pooling, caching'
            ))
        
        return recommendations

class DatabaseQueryOptimizer:
    """Database query optimization service"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize database query optimizer"""
        self.config = config
        self.query_stats = defaultdict(list)
        self.slow_queries = deque(maxlen=100)
        self.lock = threading.RLock()
        
        # Optimization thresholds
        self.slow_query_threshold = config.get('slow_query_threshold', 1.0)
        self.optimization_enabled = config.get('optimization_enabled', True)
    
    def profile_query(self, query: str, params: Optional[Dict] = None) -> Callable:
        """Decorator to profile database queries"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if not self.optimization_enabled:
                    return func(*args, **kwargs)
                
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    duration = time.time() - start_time
                    self._record_query_stats(query, duration, success, error, params)
                
                return result
            return wrapper
        return decorator
    
    def _record_query_stats(self, query: str, duration: float, success: bool,
                           error: Optional[str], params: Optional[Dict]):
        """Record query performance statistics"""
        try:
            query_hash = hash(query)
            
            with self.lock:
                self.query_stats[query_hash].append({
                    'timestamp': time.time(),
                    'duration': duration,
                    'success': success,
                    'error': error,
                    'params': params
                })
                
                # Check for slow query
                if duration > self.slow_query_threshold:
                    self.slow_queries.append({
                        'query': query[:200] + '...' if len(query) > 200 else query,
                        'duration': duration,
                        'timestamp': time.time(),
                        'params': params
                    })
                    
                    logger.warning(f"Slow query detected: {duration:.3f}s - {query[:100]}...")
                    
        except Exception as e:
            logger.error(f"Error recording query stats: {e}")
    
    def get_query_optimization_recommendations(self) -> List[str]:
        """Get database query optimization recommendations"""
        recommendations = []
        
        try:
            with self.lock:
                # Analyze slow queries
                if self.slow_queries:
                    avg_slow_duration = statistics.mean(q['duration'] for q in self.slow_queries)
                    recommendations.append(f"Optimize {len(self.slow_queries)} slow queries (avg: {avg_slow_duration:.3f}s)")
                
                # Analyze query patterns
                for query_hash, stats in self.query_stats.items():
                    if len(stats) > 10:
                        durations = [s['duration'] for s in stats]
                        avg_duration = statistics.mean(durations)
                        
                        if avg_duration > 0.5:
                            recommendations.append(f"Optimize frequently executed slow query (avg: {avg_duration:.3f}s)")
                        
                        # Check for high variance (inconsistent performance)
                        if len(durations) > 1:
                            std_dev = statistics.stdev(durations)
                            if std_dev > avg_duration * 0.5:
                                recommendations.append("Investigate query performance inconsistency")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating query optimization recommendations: {e}")
            return []

class ResourceOptimizer:
    """System resource optimization service"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize resource optimizer"""
        self.config = config
        self.resource_history = defaultdict(deque)
        self.optimization_actions = deque(maxlen=100)
        self.lock = threading.RLock()
        
        # Start resource monitoring
        self._start_resource_monitoring()
    
    def _start_resource_monitoring(self):
        """Start background resource monitoring"""
        def monitor_worker():
            while True:
                try:
                    self._collect_resource_metrics()
                    self._check_resource_optimization()
                    time.sleep(30)  # Run every 30 seconds
                except Exception as e:
                    logger.error(f"Resource monitor error: {e}")
                    time.sleep(30)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
    
    def _collect_resource_metrics(self):
        """Collect system resource metrics"""
        try:
            timestamp = time.time()
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            
            with self.lock:
                self.resource_history['cpu'].append({
                    'timestamp': timestamp,
                    'value': cpu_percent
                })
                
                self.resource_history['memory'].append({
                    'timestamp': timestamp,
                    'value': memory.percent,
                    'available_mb': memory.available / 1024 / 1024
                })
                
                self.resource_history['disk'].append({
                    'timestamp': timestamp,
                    'value': (disk.used / disk.total) * 100,
                    'free_gb': disk.free / 1024 / 1024 / 1024
                })
                
                # Keep only recent data (1 hour)
                cutoff = timestamp - 3600
                for resource_type in self.resource_history:
                    while (self.resource_history[resource_type] and 
                           self.resource_history[resource_type][0]['timestamp'] < cutoff):
                        self.resource_history[resource_type].popleft()
                        
        except Exception as e:
            logger.error(f"Error collecting resource metrics: {e}")
    
    def _check_resource_optimization(self):
        """Check if resource optimization is needed"""
        try:
            with self.lock:
                # Check memory usage
                memory_data = list(self.resource_history['memory'])
                if memory_data:
                    recent_memory = [m['value'] for m in memory_data[-10:]]  # Last 10 readings
                    avg_memory = statistics.mean(recent_memory)
                    
                    if avg_memory > 85:
                        self._trigger_memory_optimization()
                
                # Check CPU usage
                cpu_data = list(self.resource_history['cpu'])
                if cpu_data:
                    recent_cpu = [c['value'] for c in cpu_data[-10:]]
                    avg_cpu = statistics.mean(recent_cpu)
                    
                    if avg_cpu > 80:
                        self._trigger_cpu_optimization()
                        
        except Exception as e:
            logger.error(f"Error checking resource optimization: {e}")
    
    def _trigger_memory_optimization(self):
        """Trigger memory optimization actions"""
        try:
            logger.info("Triggering memory optimization")
            
            # Force garbage collection
            collected = gc.collect()
            
            # Record optimization action
            action = {
                'timestamp': time.time(),
                'type': 'memory_optimization',
                'action': 'garbage_collection',
                'result': f'Collected {collected} objects'
            }
            
            with self.lock:
                self.optimization_actions.append(action)
            
            logger.info(f"Memory optimization completed: {action['result']}")
            
        except Exception as e:
            logger.error(f"Error in memory optimization: {e}")
    
    def _trigger_cpu_optimization(self):
        """Trigger CPU optimization actions"""
        try:
            logger.info("Triggering CPU optimization")
            
            # This is a placeholder for CPU optimization actions
            # In a real implementation, you might:
            # - Reduce thread pool sizes
            # - Implement request throttling
            # - Defer non-critical tasks
            
            action = {
                'timestamp': time.time(),
                'type': 'cpu_optimization',
                'action': 'throttling_enabled',
                'result': 'CPU optimization measures activated'
            }
            
            with self.lock:
                self.optimization_actions.append(action)
            
            logger.info(f"CPU optimization completed: {action['result']}")
            
        except Exception as e:
            logger.error(f"Error in CPU optimization: {e}")
    
    def get_resource_optimization_report(self) -> Dict[str, Any]:
        """Get resource optimization report"""
        try:
            with self.lock:
                recent_actions = list(self.optimization_actions)
                
                # Get current resource status
                current_memory = psutil.virtual_memory()
                current_cpu = psutil.cpu_percent()
                current_disk = psutil.disk_usage('/')
                
                report = {
                    'current_status': {
                        'memory_percent': current_memory.percent,
                        'memory_available_gb': current_memory.available / 1024 / 1024 / 1024,
                        'cpu_percent': current_cpu,
                        'disk_percent': (current_disk.used / current_disk.total) * 100,
                        'disk_free_gb': current_disk.free / 1024 / 1024 / 1024
                    },
                    'optimization_actions': recent_actions[-10:],  # Last 10 actions
                    'recommendations': self._generate_resource_recommendations()
                }
                
                return report
                
        except Exception as e:
            logger.error(f"Error generating resource optimization report: {e}")
            return {'error': str(e)}
    
    def _generate_resource_recommendations(self) -> List[str]:
        """Generate resource optimization recommendations"""
        recommendations = []
        
        try:
            # Analyze resource trends
            with self.lock:
                # Memory recommendations
                memory_data = list(self.resource_history['memory'])
                if memory_data:
                    recent_memory = [m['value'] for m in memory_data[-20:]]
                    avg_memory = statistics.mean(recent_memory)
                    
                    if avg_memory > 80:
                        recommendations.append("Consider increasing memory allocation or optimizing memory usage")
                    elif avg_memory < 30:
                        recommendations.append("Memory usage is low - consider reducing allocated memory")
                
                # CPU recommendations
                cpu_data = list(self.resource_history['cpu'])
                if cpu_data:
                    recent_cpu = [c['value'] for c in cpu_data[-20:]]
                    avg_cpu = statistics.mean(recent_cpu)
                    
                    if avg_cpu > 70:
                        recommendations.append("High CPU usage - consider optimizing algorithms or scaling horizontally")
                    elif avg_cpu < 20:
                        recommendations.append("Low CPU usage - consider reducing allocated resources")
                
                # Disk recommendations
                disk_data = list(self.resource_history['disk'])
                if disk_data:
                    recent_disk = [d['value'] for d in disk_data[-5:]]
                    avg_disk = statistics.mean(recent_disk)
                    
                    if avg_disk > 85:
                        recommendations.append("Disk usage is high - consider cleanup or increasing storage")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating resource recommendations: {e}")
            return []

# Global performance optimization framework instance
_performance_framework = None

def get_performance_framework(config: Optional[Dict[str, Any]] = None) -> Tuple[PerformanceProfiler, DatabaseQueryOptimizer, ResourceOptimizer]:
    """Get global performance optimization framework instance"""
    global _performance_framework
    
    if _performance_framework is None:
        default_config = {
            'profiler': {
                'enabled': True,
                'sampling_rate': 1.0,
                'detailed_profiling': False,
                'memory_profiling': True,
                'max_metrics': 10000,
                'thresholds': {
                    'execution_time': 1.0,
                    'memory_usage': 100 * 1024 * 1024,
                    'cpu_usage': 80.0,
                    'database_query_time': 0.5
                }
            },
            'query_optimizer': {
                'slow_query_threshold': 1.0,
                'optimization_enabled': True
            },
            'resource_optimizer': {
                'monitoring_enabled': True,
                'optimization_enabled': True
            }
        }
        
        if config:
            for key in default_config:
                if key in config:
                    default_config[key].update(config[key])
        
        profiler = PerformanceProfiler(default_config['profiler'])
        query_optimizer = DatabaseQueryOptimizer(default_config['query_optimizer'])
        resource_optimizer = ResourceOptimizer(default_config['resource_optimizer'])
        
        _performance_framework = (profiler, query_optimizer, resource_optimizer)
    
    return _performance_framework