"""
Enhanced Salesforce Credential Management

This module provides enhanced credential management specifically for Salesforce
credentials, including automated validation, refresh, and lifecycle management.
"""

import logging
import json
import hashlib
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import threading

# Import existing modules
from src.shared.azure_services import get_secret, set_secret, get_key_vault_client, is_local_dev
from src.shared.salesforce_client import SalesforceClient

logger = logging.getLogger(__name__)


class SalesforceCredentialError(Exception):
    """Custom exception for Salesforce credential management errors"""
    pass


class SalesforceCredentialManager:
    """
    Enhanced Salesforce credential management service
    
    This service provides:
    - Secure storage and retrieval of Salesforce credentials
    - Automated credential validation and refresh
    - Credential lifecycle management
    - Security monitoring for credential operations
    """
    
    def __init__(self):
        self._credential_cache = {}
        self._validation_cache = {}
        self._refresh_tokens = {}
        self._security_events = []
        self._lock = threading.Lock()
        
        # Credential validation settings
        self.validation_settings = {
            'cache_ttl_minutes': 30,
            'validation_retry_count': 3,
            'refresh_threshold_minutes': 60,
            'auto_refresh_enabled': True
        }
    
    def store_salesforce_credentials(self, org_id: str, credentials: Dict[str, Any],
                                   user_id: str = 'system') -> bool:
        """
        Store Salesforce credentials securely in Key Vault
        
        Args:
            org_id: Organization ID
            credentials: Salesforce credentials dictionary
            user_id: User ID storing the credentials
            
        Returns:
            bool: True if stored successfully
            
        Raises:
            SalesforceCredentialError: If storage fails
        """
        try:
            # Validate credential format
            self._validate_credential_format(credentials)
            
            # Generate credential key
            credential_key = f"sfdc-creds-{org_id}"
            
            # Prepare credential data for storage
            credential_data = {
                'client_id': credentials['client_id'],
                'client_secret': credentials['client_secret'],
                'instance_url': credentials['instance_url'],
                'is_sandbox': credentials.get('is_sandbox', False),
                'username': credentials.get('username'),
                'private_key': credentials.get('private_key'),
                'stored_at': datetime.utcnow().isoformat(),
                'stored_by': user_id,
                'org_id': org_id
            }
            
            # Store in Key Vault
            if is_local_dev():
                # For local development, store in memory cache
                with self._lock:
                    self._credential_cache[credential_key] = credential_data
                logger.info(f"Stored Salesforce credentials for org {org_id} in local cache")
            else:
                # Store in Azure Key Vault
                credential_json = json.dumps(credential_data)
                if set_secret(credential_key, credential_json):
                    logger.info(f"Stored Salesforce credentials for org {org_id} in Key Vault")
                else:
                    raise SalesforceCredentialError(f"Failed to store credentials in Key Vault for org {org_id}")
            
            # Log security event
            self._log_security_event('credentials_stored', {
                'org_id': org_id,
                'user_id': user_id,
                'client_id': credentials['client_id'][:10] + '...',
                'instance_url': credentials['instance_url']
            })
            
            # Clear validation cache for this org
            self._clear_validation_cache(org_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing Salesforce credentials for org {org_id}: {str(e)}")
            raise SalesforceCredentialError(f"Failed to store credentials: {str(e)}")
    
    def retrieve_salesforce_credentials(self, org_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve Salesforce credentials from secure storage
        
        Args:
            org_id: Organization ID
            
        Returns:
            Dict[str, Any]: Salesforce credentials or None if not found
        """
        try:
            credential_key = f"sfdc-creds-{org_id}"
            
            if is_local_dev():
                # Retrieve from local cache
                with self._lock:
                    credential_data = self._credential_cache.get(credential_key)
            else:
                # Retrieve from Azure Key Vault
                credential_json = get_secret(credential_key)
                if credential_json:
                    credential_data = json.loads(credential_json)
                else:
                    credential_data = None
            
            if credential_data:
                # Remove sensitive metadata before returning
                credentials = {
                    'client_id': credential_data['client_id'],
                    'client_secret': credential_data['client_secret'],
                    'instance_url': credential_data['instance_url'],
                    'is_sandbox': credential_data.get('is_sandbox', False),
                    'username': credential_data.get('username'),
                    'private_key': credential_data.get('private_key')
                }
                
                # Log security event
                self._log_security_event('credentials_retrieved', {
                    'org_id': org_id,
                    'client_id': credentials['client_id'][:10] + '...',
                    'instance_url': credentials['instance_url']
                })
                
                return credentials
            else:
                logger.warning(f"No Salesforce credentials found for org {org_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving Salesforce credentials for org {org_id}: {str(e)}")
            return None
    
    def validate_salesforce_credentials(self, org_id: str, 
                                      credentials: Optional[Dict[str, Any]] = None,
                                      force_validation: bool = False) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Validate Salesforce credentials by testing authentication
        
        Args:
            org_id: Organization ID
            credentials: Optional credentials to validate (if not provided, retrieves from storage)
            force_validation: Force validation even if cached result exists
            
        Returns:
            Tuple[bool, str, Dict]: (is_valid, message, connection_details)
        """
        try:
            # Check validation cache first
            if not force_validation:
                cached_result = self._get_cached_validation(org_id)
                if cached_result:
                    return cached_result
            
            # Get credentials if not provided
            if not credentials:
                credentials = self.retrieve_salesforce_credentials(org_id)
                if not credentials:
                    return False, f"No credentials found for org {org_id}", None
            
            # Validate credential format
            try:
                self._validate_credential_format(credentials)
            except SalesforceCredentialError as e:
                return False, f"Invalid credential format: {str(e)}", None
            
            # Test authentication with retry logic
            for attempt in range(self.validation_settings['validation_retry_count']):
                try:
                    # Create Salesforce client
                    sf_client = SalesforceClient(
                        client_id=credentials['client_id'],
                        client_secret=credentials['client_secret'],
                        instance_url=credentials['instance_url'],
                        is_sandbox=credentials.get('is_sandbox', False)
                    )
                    
                    # Test connection
                    success, error_message, connection_details = sf_client.test_client_credentials_flow()
                    
                    if success:
                        # Cache successful validation
                        self._cache_validation_result(org_id, True, "Credentials valid", connection_details)
                        
                        # Log security event
                        self._log_security_event('credentials_validated', {
                            'org_id': org_id,
                            'attempt': attempt + 1,
                            'instance_url': credentials['instance_url']
                        })
                        
                        return True, "Credentials valid", connection_details
                    else:
                        if attempt == self.validation_settings['validation_retry_count'] - 1:
                            # Last attempt failed
                            self._cache_validation_result(org_id, False, error_message, None)
                            
                            # Log security event
                            self._log_security_event('credentials_validation_failed', {
                                'org_id': org_id,
                                'attempts': attempt + 1,
                                'error': error_message
                            })
                            
                            return False, error_message, None
                        else:
                            # Retry
                            logger.warning(f"Credential validation attempt {attempt + 1} failed for org {org_id}: {error_message}")
                            continue
                            
                except Exception as e:
                    if attempt == self.validation_settings['validation_retry_count'] - 1:
                        error_msg = f"Validation error: {str(e)}"
                        self._cache_validation_result(org_id, False, error_msg, None)
                        return False, error_msg, None
                    else:
                        logger.warning(f"Credential validation attempt {attempt + 1} error for org {org_id}: {str(e)}")
                        continue
            
            return False, "Validation failed after all retry attempts", None
            
        except Exception as e:
            logger.error(f"Error validating Salesforce credentials for org {org_id}: {str(e)}")
            return False, f"Validation error: {str(e)}", None
    
    def refresh_salesforce_credentials(self, org_id: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Refresh Salesforce credentials (re-authenticate and update tokens)
        
        Args:
            org_id: Organization ID
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, new_connection_details)
        """
        try:
            # Get current credentials
            credentials = self.retrieve_salesforce_credentials(org_id)
            if not credentials:
                return False, f"No credentials found for org {org_id}", None
            
            # Force validation to get fresh tokens
            is_valid, message, connection_details = self.validate_salesforce_credentials(
                org_id, credentials, force_validation=True
            )
            
            if is_valid and connection_details:
                # Update refresh token if available
                if 'refresh_token' in connection_details:
                    with self._lock:
                        self._refresh_tokens[org_id] = {
                            'refresh_token': connection_details['refresh_token'],
                            'updated_at': datetime.utcnow()
                        }
                
                # Log security event
                self._log_security_event('credentials_refreshed', {
                    'org_id': org_id,
                    'instance_url': credentials['instance_url']
                })
                
                return True, "Credentials refreshed successfully", connection_details
            else:
                return False, f"Failed to refresh credentials: {message}", None
                
        except Exception as e:
            logger.error(f"Error refreshing Salesforce credentials for org {org_id}: {str(e)}")
            return False, f"Refresh error: {str(e)}", None
    
    def delete_salesforce_credentials(self, org_id: str, user_id: str = 'system') -> bool:
        """
        Delete Salesforce credentials from secure storage
        
        Args:
            org_id: Organization ID
            user_id: User ID deleting the credentials
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            credential_key = f"sfdc-creds-{org_id}"
            
            if is_local_dev():
                # Delete from local cache
                with self._lock:
                    if credential_key in self._credential_cache:
                        del self._credential_cache[credential_key]
                        logger.info(f"Deleted Salesforce credentials for org {org_id} from local cache")
                    else:
                        logger.warning(f"No credentials found to delete for org {org_id}")
            else:
                # Delete from Azure Key Vault
                try:
                    kv_client = get_key_vault_client()
                    if kv_client:
                        kv_client.begin_delete_secret(credential_key)
                        logger.info(f"Deleted Salesforce credentials for org {org_id} from Key Vault")
                    else:
                        logger.error("Key Vault client not available")
                        return False
                except Exception as e:
                    logger.error(f"Error deleting from Key Vault: {str(e)}")
                    return False
            
            # Clear caches
            self._clear_validation_cache(org_id)
            with self._lock:
                if org_id in self._refresh_tokens:
                    del self._refresh_tokens[org_id]
            
            # Log security event
            self._log_security_event('credentials_deleted', {
                'org_id': org_id,
                'user_id': user_id
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting Salesforce credentials for org {org_id}: {str(e)}")
            return False
    
    def list_stored_credentials(self) -> List[Dict[str, Any]]:
        """
        List all stored Salesforce credentials (metadata only)
        
        Returns:
            List[Dict[str, Any]]: List of credential metadata
        """
        try:
            credentials_list = []
            
            if is_local_dev():
                # List from local cache
                with self._lock:
                    for key, data in self._credential_cache.items():
                        if key.startswith('sfdc-creds-'):
                            credentials_list.append({
                                'org_id': data['org_id'],
                                'instance_url': data['instance_url'],
                                'is_sandbox': data.get('is_sandbox', False),
                                'stored_at': data['stored_at'],
                                'stored_by': data['stored_by']
                            })
            else:
                # List from Azure Key Vault
                try:
                    kv_client = get_key_vault_client()
                    if kv_client:
                        secrets = kv_client.list_properties_of_secrets()
                        for secret in secrets:
                            if secret.name.startswith('sfdc-creds-'):
                                org_id = secret.name.replace('sfdc-creds-', '')
                                credentials_list.append({
                                    'org_id': org_id,
                                    'secret_name': secret.name,
                                    'created_on': secret.created_on.isoformat() if secret.created_on else None,
                                    'updated_on': secret.updated_on.isoformat() if secret.updated_on else None
                                })
                except Exception as e:
                    logger.error(f"Error listing Key Vault secrets: {str(e)}")
            
            return credentials_list
            
        except Exception as e:
            logger.error(f"Error listing stored credentials: {str(e)}")
            return []
    
    def _validate_credential_format(self, credentials: Dict[str, Any]):
        """
        Validate credential format and required fields
        
        Args:
            credentials: Credentials to validate
            
        Raises:
            SalesforceCredentialError: If validation fails
        """
        required_fields = ['client_id', 'client_secret', 'instance_url']
        
        for field in required_fields:
            if field not in credentials or not credentials[field]:
                raise SalesforceCredentialError(f"Missing required field: {field}")
        
        # Validate field formats
        client_id = credentials['client_id']
        if not isinstance(client_id, str) or len(client_id) < 50:
            raise SalesforceCredentialError("client_id must be a string of at least 50 characters")
        
        client_secret = credentials['client_secret']
        if not isinstance(client_secret, str) or len(client_secret) < 20:
            raise SalesforceCredentialError("client_secret must be a string of at least 20 characters")
        
        instance_url = credentials['instance_url']
        if not isinstance(instance_url, str) or not instance_url.startswith('https://'):
            raise SalesforceCredentialError("instance_url must be a valid HTTPS URL")
        
        if 'salesforce.com' not in instance_url:
            raise SalesforceCredentialError("instance_url must be a valid Salesforce URL")
    
    def _get_cached_validation(self, org_id: str) -> Optional[Tuple[bool, str, Optional[Dict[str, Any]]]]:
        """
        Get cached validation result if still valid
        
        Args:
            org_id: Organization ID
            
        Returns:
            Cached validation result or None
        """
        with self._lock:
            if org_id in self._validation_cache:
                cached = self._validation_cache[org_id]
                cache_age = datetime.utcnow() - cached['cached_at']
                
                if cache_age.total_seconds() < (self.validation_settings['cache_ttl_minutes'] * 60):
                    logger.debug(f"Using cached validation result for org {org_id}")
                    return cached['is_valid'], cached['message'], cached['connection_details']
        
        return None
    
    def _cache_validation_result(self, org_id: str, is_valid: bool, 
                               message: str, connection_details: Optional[Dict[str, Any]]):
        """
        Cache validation result
        
        Args:
            org_id: Organization ID
            is_valid: Whether validation was successful
            message: Validation message
            connection_details: Connection details if successful
        """
        with self._lock:
            self._validation_cache[org_id] = {
                'is_valid': is_valid,
                'message': message,
                'connection_details': connection_details,
                'cached_at': datetime.utcnow()
            }
    
    def _clear_validation_cache(self, org_id: str):
        """
        Clear validation cache for an organization
        
        Args:
            org_id: Organization ID
        """
        with self._lock:
            if org_id in self._validation_cache:
                del self._validation_cache[org_id]
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log security events for credential operations
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details,
            'source': 'salesforce_credential_manager'
        }
        
        self._security_events.append(event)
        
        # Log to application logger
        logger.info(f"Salesforce credential event: {event_type}", extra=details)
        
        # Keep only recent events (last 500)
        if len(self._security_events) > 500:
            self._security_events = self._security_events[-500:]
    
    def get_security_events(self, event_type: str = None, 
                          since: datetime = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get security events for credential operations
        
        Args:
            event_type: Filter by event type
            since: Filter events since this timestamp
            limit: Maximum number of events to return
            
        Returns:
            List[Dict[str, Any]]: Filtered security events
        """
        events = self._security_events.copy()
        
        if event_type:
            events = [e for e in events if e['event_type'] == event_type]
        
        if since:
            since_str = since.isoformat()
            events = [e for e in events if e['timestamp'] >= since_str]
        
        return events[-limit:] if limit else events
    
    def cleanup_expired_cache(self):
        """
        Clean up expired cache entries
        """
        try:
            current_time = datetime.utcnow()
            cache_ttl = timedelta(minutes=self.validation_settings['cache_ttl_minutes'])
            
            with self._lock:
                expired_orgs = []
                for org_id, cached in self._validation_cache.items():
                    if current_time - cached['cached_at'] > cache_ttl:
                        expired_orgs.append(org_id)
                
                for org_id in expired_orgs:
                    del self._validation_cache[org_id]
                
                if expired_orgs:
                    logger.debug(f"Cleaned up {len(expired_orgs)} expired validation cache entries")
                    
        except Exception as e:
            logger.error(f"Error cleaning up expired cache: {str(e)}")


# Global credential manager instance
_salesforce_credential_manager = None


def get_salesforce_credential_manager() -> SalesforceCredentialManager:
    """
    Get the global Salesforce credential manager instance
    
    Returns:
        SalesforceCredentialManager: The credential manager instance
    """
    global _salesforce_credential_manager
    if _salesforce_credential_manager is None:
        _salesforce_credential_manager = SalesforceCredentialManager()
    return _salesforce_credential_manager