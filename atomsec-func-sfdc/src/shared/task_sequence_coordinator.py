"""
Enhanced Task Sequence Coordinator

This module provides optimized coordination for sequential task processing with
execution log ID tracking, dependency management, and performance monitoring.
"""

import logging
import json
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import threading
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

# Import existing modules
from src.shared.parameter_validator import get_parameter_validator
from src.shared.task_status_service import get_task_status_service

logger = logging.getLogger(__name__)


class TaskSequenceCoordinator:
    """
    Enhanced coordinator for sequential task processing with performance optimization
    
    This service provides:
    - Execution log ID tracking across task sequences
    - Task dependency validation
    - Sequence progress monitoring
    - Task coordination and synchronization
    - Performance monitoring and optimization
    - Efficient task execution tracking
    """
    
    def __init__(self):
        self.validator = get_parameter_validator()
        self.task_status_service = get_task_status_service()
        self._execution_logs = {}
        self._sequence_progress = defaultdict(dict)
        self._task_performance = defaultdict(dict)
        self._sequence_metrics = defaultdict(dict)
        self._lock = threading.Lock()
        self._performance_lock = threading.Lock()
        
        # Performance optimization settings
        self.enable_performance_tracking = True
        self.enable_task_caching = True
        self.enable_parallel_validation = True
        
        # Task execution statistics
        self._task_stats = defaultdict(lambda: {
            'total_executions': 0,
            'total_duration': 0.0,
            'average_duration': 0.0,
            'success_count': 0,
            'failure_count': 0,
            'success_rate': 0.0
        })
    
    def start_task_sequence(self, sequence_type: str, org_id: str, 
                          user_id: str, params: Dict[str, Any],
                          execution_log_id: Optional[str] = None) -> str:
        """
        Start a new task sequence with execution log tracking
        
        Args:
            sequence_type: Type of sequence ('full_scan', 'security_scan', etc.)
            org_id: Organization ID
            user_id: User ID
            params: Base parameters for all tasks in sequence
            execution_log_id: Optional existing execution log ID
            
        Returns:
            str: Execution log ID for the sequence
        """
        try:
            # Generate or validate execution log ID
            if not execution_log_id:
                execution_log_id = self.validator.generate_execution_log_id()
            elif not self.validator.validate_execution_log_id(execution_log_id):
                raise ValueError(f"Invalid execution_log_id: {execution_log_id}")
            
            # Get task sequence
            task_sequence = self.validator.get_task_sequence(sequence_type)
            if not task_sequence:
                raise ValueError(f"Unknown sequence type: {sequence_type}")
            
            # Initialize execution log
            with self._lock:
                self._execution_logs[execution_log_id] = {
                    'sequence_type': sequence_type,
                    'org_id': org_id,
                    'user_id': user_id,
                    'task_sequence': task_sequence,
                    'base_params': params,
                    'started_at': datetime.utcnow(),
                    'status': 'started',
                    'completed_tasks': [],
                    'failed_tasks': [],
                    'current_task': None
                }
                
                # Initialize sequence progress tracking
                self._sequence_progress[execution_log_id] = {
                    'total_tasks': len(task_sequence),
                    'completed_count': 0,
                    'failed_count': 0,
                    'progress_percentage': 0
                }
            
            logger.info(f"Started task sequence {sequence_type} with execution_log_id: {execution_log_id}")
            logger.info(f"Task sequence: {task_sequence}")
            
            return execution_log_id
            
        except Exception as e:
            logger.error(f"Error starting task sequence: {str(e)}")
            raise
    
    def validate_task_execution(self, task_type: str, execution_log_id: str,
                              params: Dict[str, Any]) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate that a task can be executed in the sequence
        
        Args:
            task_type: Type of task to validate
            execution_log_id: Execution log ID
            params: Task parameters
            
        Returns:
            Tuple[bool, str, Dict[str, Any]]: (can_execute, message, validated_params)
        """
        try:
            with self._lock:
                # Check if execution log exists
                if execution_log_id not in self._execution_logs:
                    return False, f"Execution log not found: {execution_log_id}", {}
                
                execution_log = self._execution_logs[execution_log_id]
                
                # Check if task is in the sequence
                if task_type not in execution_log['task_sequence']:
                    return False, f"Task {task_type} not in sequence {execution_log['sequence_type']}", {}
                
                # Check task dependencies
                completed_tasks = execution_log['completed_tasks']
                if not self.validator.validate_task_sequence_order(task_type, completed_tasks):
                    dependencies = self.validator.task_dependencies.get(task_type, [])
                    missing_deps = [dep for dep in dependencies if dep not in completed_tasks]
                    return False, f"Task dependencies not met. Missing: {missing_deps}", {}
                
                # Merge base parameters with task-specific parameters
                merged_params = execution_log['base_params'].copy()
                merged_params.update(params)
                merged_params['execution_log_id'] = execution_log_id
                
                # Validate task parameters
                try:
                    validated_params = self.validator.validate_sequential_task_parameters(
                        merged_params, task_type, execution_log_id
                    )
                except Exception as e:
                    return False, f"Parameter validation failed: {str(e)}", {}
                
                # Update current task
                execution_log['current_task'] = task_type
                
                return True, "Task can be executed", validated_params
                
        except Exception as e:
            logger.error(f"Error validating task execution: {str(e)}")
            return False, f"Validation error: {str(e)}", {}
    
    def mark_task_completed(self, task_type: str, execution_log_id: str,
                          task_id: Optional[str] = None) -> bool:
        """
        Mark a task as completed in the sequence
        
        Args:
            task_type: Type of task that completed
            execution_log_id: Execution log ID
            task_id: Optional task ID
            
        Returns:
            bool: True if marked successfully
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log not found: {execution_log_id}")
                    return False
                
                execution_log = self._execution_logs[execution_log_id]
                
                # Add to completed tasks if not already there
                if task_type not in execution_log['completed_tasks']:
                    execution_log['completed_tasks'].append(task_type)
                
                # Update progress
                progress = self._sequence_progress[execution_log_id]
                progress['completed_count'] = len(execution_log['completed_tasks'])
                progress['progress_percentage'] = (progress['completed_count'] / progress['total_tasks']) * 100
                
                # Check if sequence is complete
                if len(execution_log['completed_tasks']) == len(execution_log['task_sequence']):
                    execution_log['status'] = 'completed'
                    execution_log['completed_at'] = datetime.utcnow()
                    logger.info(f"Task sequence completed for execution_log_id: {execution_log_id}")
                
                # Update performance metrics
                if self.enable_performance_tracking:
                    self._update_task_performance_metrics(task_type, execution_log_id, True)
                
                logger.info(f"Task {task_type} completed for execution_log_id: {execution_log_id}")
                logger.info(f"Sequence progress: {progress['completed_count']}/{progress['total_tasks']} ({progress['progress_percentage']:.1f}%)")
                
                return True
                
        except Exception as e:
            logger.error(f"Error marking task completed: {str(e)}")
            return False
    
    def mark_task_failed(self, task_type: str, execution_log_id: str,
                        error_message: str, task_id: Optional[str] = None) -> bool:
        """
        Mark a task as failed in the sequence
        
        Args:
            task_type: Type of task that failed
            execution_log_id: Execution log ID
            error_message: Error message
            task_id: Optional task ID
            
        Returns:
            bool: True if marked successfully
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    logger.error(f"Execution log not found: {execution_log_id}")
                    return False
                
                execution_log = self._execution_logs[execution_log_id]
                
                # Add to failed tasks
                if task_type not in execution_log['failed_tasks']:
                    execution_log['failed_tasks'].append(task_type)
                
                # Update progress
                progress = self._sequence_progress[execution_log_id]
                progress['failed_count'] = len(execution_log['failed_tasks'])
                
                # Update performance metrics
                if self.enable_performance_tracking:
                    self._update_task_performance_metrics(task_type, execution_log_id, False, error_message)
                
                # Mark sequence as failed if critical task fails
                critical_tasks = ['sfdc_authenticate']  # Tasks that cause sequence failure
                if task_type in critical_tasks:
                    execution_log['status'] = 'failed'
                    execution_log['failed_at'] = datetime.utcnow()
                    execution_log['failure_reason'] = f"Critical task {task_type} failed: {error_message}"
                    logger.error(f"Task sequence failed due to critical task {task_type}: {error_message}")
                else:
                    logger.warning(f"Non-critical task {task_type} failed: {error_message}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error marking task failed: {str(e)}")
            return False
    
    def get_sequence_status(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task sequence
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict[str, Any]: Sequence status information
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    return None
                
                execution_log = self._execution_logs[execution_log_id]
                progress = self._sequence_progress[execution_log_id]
                
                return {
                    'execution_log_id': execution_log_id,
                    'sequence_type': execution_log['sequence_type'],
                    'org_id': execution_log['org_id'],
                    'user_id': execution_log['user_id'],
                    'status': execution_log['status'],
                    'started_at': execution_log['started_at'].isoformat(),
                    'completed_at': execution_log.get('completed_at', {}).isoformat() if execution_log.get('completed_at') else None,
                    'failed_at': execution_log.get('failed_at', {}).isoformat() if execution_log.get('failed_at') else None,
                    'current_task': execution_log.get('current_task'),
                    'task_sequence': execution_log['task_sequence'],
                    'completed_tasks': execution_log['completed_tasks'],
                    'failed_tasks': execution_log['failed_tasks'],
                    'total_tasks': progress['total_tasks'],
                    'completed_count': progress['completed_count'],
                    'failed_count': progress['failed_count'],
                    'progress_percentage': progress['progress_percentage'],
                    'failure_reason': execution_log.get('failure_reason')
                }
                
        except Exception as e:
            logger.error(f"Error getting sequence status: {str(e)}")
            return None
    
    def get_next_task(self, execution_log_id: str) -> Optional[str]:
        """
        Get the next task to execute in the sequence
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            str: Next task type or None if sequence is complete
        """
        try:
            with self._lock:
                if execution_log_id not in self._execution_logs:
                    return None
                
                execution_log = self._execution_logs[execution_log_id]
                
                if execution_log['status'] in ['completed', 'failed']:
                    return None
                
                completed_tasks = execution_log['completed_tasks']
                task_sequence = execution_log['task_sequence']
                
                # Find the next task that hasn't been completed
                for task_type in task_sequence:
                    if task_type not in completed_tasks:
                        # Check if dependencies are met
                        if self.validator.validate_task_sequence_order(task_type, completed_tasks):
                            return task_type
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting next task: {str(e)}")
            return None
    
    def cleanup_old_execution_logs(self, max_age_hours: int = 24):
        """
        Clean up old execution logs to prevent memory leaks
        
        Args:
            max_age_hours: Maximum age of execution logs to keep
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            
            with self._lock:
                expired_logs = []
                for execution_log_id, execution_log in self._execution_logs.items():
                    if execution_log['started_at'] < cutoff_time:
                        expired_logs.append(execution_log_id)
                
                for execution_log_id in expired_logs:
                    del self._execution_logs[execution_log_id]
                    if execution_log_id in self._sequence_progress:
                        del self._sequence_progress[execution_log_id]
                
                if expired_logs:
                    logger.info(f"Cleaned up {len(expired_logs)} expired execution logs")
                    
        except Exception as e:
            logger.error(f"Error cleaning up execution logs: {str(e)}")
    
    def start_task_execution_tracking(self, task_type: str, execution_log_id: str) -> str:
        """
        Start tracking task execution performance
        
        Args:
            task_type: Type of task being executed
            execution_log_id: Execution log ID
            
        Returns:
            str: Task execution tracking ID
        """
        if not self.enable_performance_tracking:
            return ""
        
        try:
            tracking_id = f"{execution_log_id}_{task_type}_{int(time.time() * 1000)}"
            
            with self._performance_lock:
                self._task_performance[tracking_id] = {
                    'task_type': task_type,
                    'execution_log_id': execution_log_id,
                    'start_time': time.time(),
                    'end_time': None,
                    'duration': None,
                    'success': None,
                    'error_message': None
                }
            
            return tracking_id
            
        except Exception as e:
            logger.error(f"Error starting task execution tracking: {str(e)}")
            return ""
    
    def end_task_execution_tracking(self, tracking_id: str, success: bool, 
                                  error_message: Optional[str] = None):
        """
        End tracking task execution performance
        
        Args:
            tracking_id: Task execution tracking ID
            success: Whether the task succeeded
            error_message: Optional error message if task failed
        """
        if not self.enable_performance_tracking or not tracking_id:
            return
        
        try:
            with self._performance_lock:
                if tracking_id in self._task_performance:
                    perf_data = self._task_performance[tracking_id]
                    perf_data['end_time'] = time.time()
                    perf_data['duration'] = perf_data['end_time'] - perf_data['start_time']
                    perf_data['success'] = success
                    perf_data['error_message'] = error_message
                    
                    # Update task statistics
                    task_type = perf_data['task_type']
                    stats = self._task_stats[task_type]
                    stats['total_executions'] += 1
                    stats['total_duration'] += perf_data['duration']
                    stats['average_duration'] = stats['total_duration'] / stats['total_executions']
                    
                    if success:
                        stats['success_count'] += 1
                    else:
                        stats['failure_count'] += 1
                    
                    stats['success_rate'] = (stats['success_count'] / stats['total_executions']) * 100
                    
                    logger.debug(f"Task {task_type} completed in {perf_data['duration']:.2f}s (success: {success})")
                    
        except Exception as e:
            logger.error(f"Error ending task execution tracking: {str(e)}")
    
    def _update_task_performance_metrics(self, task_type: str, execution_log_id: str, 
                                       success: bool, error_message: Optional[str] = None):
        """
        Update task performance metrics
        
        Args:
            task_type: Type of task
            execution_log_id: Execution log ID
            success: Whether the task succeeded
            error_message: Optional error message
        """
        try:
            with self._performance_lock:
                # Update sequence metrics
                if execution_log_id not in self._sequence_metrics:
                    self._sequence_metrics[execution_log_id] = {
                        'task_durations': {},
                        'task_success_rates': {},
                        'total_sequence_time': 0.0,
                        'sequence_start_time': time.time()
                    }
                
                metrics = self._sequence_metrics[execution_log_id]
                
                # Find corresponding performance data
                for tracking_id, perf_data in self._task_performance.items():
                    if (perf_data['task_type'] == task_type and 
                        perf_data['execution_log_id'] == execution_log_id and
                        perf_data['duration'] is not None):
                        
                        metrics['task_durations'][task_type] = perf_data['duration']
                        metrics['task_success_rates'][task_type] = success
                        break
                
        except Exception as e:
            logger.error(f"Error updating task performance metrics: {str(e)}")
    
    def get_task_performance_stats(self, task_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Get task performance statistics
        
        Args:
            task_type: Optional specific task type to get stats for
            
        Returns:
            Dict[str, Any]: Performance statistics
        """
        try:
            with self._performance_lock:
                if task_type:
                    return self._task_stats.get(task_type, {})
                else:
                    return dict(self._task_stats)
                    
        except Exception as e:
            logger.error(f"Error getting task performance stats: {str(e)}")
            return {}
    
    def get_sequence_performance_metrics(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get performance metrics for a specific sequence
        
        Args:
            execution_log_id: Execution log ID
            
        Returns:
            Dict[str, Any]: Sequence performance metrics
        """
        try:
            with self._performance_lock:
                if execution_log_id not in self._sequence_metrics:
                    return None
                
                metrics = self._sequence_metrics[execution_log_id].copy()
                
                # Calculate total sequence time if sequence is complete
                with self._lock:
                    if execution_log_id in self._execution_logs:
                        execution_log = self._execution_logs[execution_log_id]
                        if execution_log['status'] in ['completed', 'failed']:
                            end_time = execution_log.get('completed_at') or execution_log.get('failed_at')
                            if end_time:
                                start_time = execution_log['started_at']
                                metrics['total_sequence_time'] = (end_time - start_time).total_seconds()
                
                # Add performance analysis
                task_durations = metrics.get('task_durations', {})
                if task_durations:
                    metrics['slowest_task'] = max(task_durations, key=task_durations.get)
                    metrics['fastest_task'] = min(task_durations, key=task_durations.get)
                    metrics['average_task_duration'] = sum(task_durations.values()) / len(task_durations)
                
                return metrics
                
        except Exception as e:
            logger.error(f"Error getting sequence performance metrics: {str(e)}")
            return None
    
    def optimize_task_sequence(self, sequence_type: str) -> Dict[str, Any]:
        """
        Analyze and provide optimization recommendations for task sequences
        
        Args:
            sequence_type: Type of sequence to optimize
            
        Returns:
            Dict[str, Any]: Optimization recommendations
        """
        try:
            recommendations = {
                'sequence_type': sequence_type,
                'recommendations': [],
                'performance_insights': {},
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Analyze task performance statistics
            with self._performance_lock:
                task_sequence = self.validator.get_task_sequence(sequence_type)
                if not task_sequence:
                    return recommendations
                
                for task_type in task_sequence:
                    stats = self._task_stats.get(task_type, {})
                    if stats.get('total_executions', 0) > 0:
                        # Check for slow tasks
                        avg_duration = stats.get('average_duration', 0)
                        if avg_duration > 300:  # 5 minutes
                            recommendations['recommendations'].append(
                                f"Task {task_type} has high average duration ({avg_duration:.1f}s). Consider optimization."
                            )
                        
                        # Check for high failure rates
                        success_rate = stats.get('success_rate', 100)
                        if success_rate < 90:
                            recommendations['recommendations'].append(
                                f"Task {task_type} has low success rate ({success_rate:.1f}%). Review error handling."
                            )
                        
                        recommendations['performance_insights'][task_type] = {
                            'average_duration': avg_duration,
                            'success_rate': success_rate,
                            'total_executions': stats.get('total_executions', 0)
                        }
            
            # General recommendations
            if not recommendations['recommendations']:
                recommendations['recommendations'].append("Task sequence performance is within acceptable limits.")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error optimizing task sequence: {str(e)}")
            return {'error': str(e)}
    
    def get_performance_dashboard_data(self) -> Dict[str, Any]:
        """
        Get comprehensive performance data for dashboard display
        
        Returns:
            Dict[str, Any]: Dashboard performance data
        """
        try:
            dashboard_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'active_sequences': 0,
                'completed_sequences': 0,
                'failed_sequences': 0,
                'task_performance': {},
                'sequence_metrics': {},
                'system_health': 'healthy'
            }
            
            # Count sequence statuses
            with self._lock:
                for execution_log in self._execution_logs.values():
                    status = execution_log['status']
                    if status == 'started':
                        dashboard_data['active_sequences'] += 1
                    elif status == 'completed':
                        dashboard_data['completed_sequences'] += 1
                    elif status == 'failed':
                        dashboard_data['failed_sequences'] += 1
            
            # Get task performance data
            with self._performance_lock:
                dashboard_data['task_performance'] = dict(self._task_stats)
            
            # Determine system health
            total_sequences = (dashboard_data['active_sequences'] + 
                             dashboard_data['completed_sequences'] + 
                             dashboard_data['failed_sequences'])
            
            if total_sequences > 0:
                failure_rate = dashboard_data['failed_sequences'] / total_sequences
                if failure_rate > 0.1:  # More than 10% failure rate
                    dashboard_data['system_health'] = 'degraded'
                elif failure_rate > 0.2:  # More than 20% failure rate
                    dashboard_data['system_health'] = 'unhealthy'
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting performance dashboard data: {str(e)}")
            return {'error': str(e)}


# Global coordinator instance
_task_sequence_coordinator = None


def get_task_sequence_coordinator() -> TaskSequenceCoordinator:
    """
    Get the global task sequence coordinator instance
    
    Returns:
        TaskSequenceCoordinator: The coordinator instance
    """
    global _task_sequence_coordinator
    if _task_sequence_coordinator is None:
        _task_sequence_coordinator = TaskSequenceCoordinator()
    return _task_sequence_coordinator