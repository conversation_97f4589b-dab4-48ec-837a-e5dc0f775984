"""
Function Configuration Module

This module provides configuration management for Azure Function App optimization,
including auto-scaling rules, Application Insights integration, and performance monitoring.

Features:
- Auto-scaling configuration
- Application Insights telemetry
- Performance monitoring
- Resource optimization
- Environment-specific settings
"""

import logging
import os
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ScalingTrigger(Enum):
    """Scaling trigger types"""
    CPU_PERCENTAGE = "cpu_percentage"
    MEMORY_PERCENTAGE = "memory_percentage"
    REQUEST_COUNT = "request_count"
    QUEUE_LENGTH = "queue_length"
    RESPONSE_TIME = "response_time"


@dataclass
class ScalingRule:
    """Auto-scaling rule configuration"""
    trigger: ScalingTrigger
    threshold_upper: float
    threshold_lower: float
    scale_out_instances: int
    scale_in_instances: int
    cooldown_minutes: int
    evaluation_window_minutes: int
    enabled: bool = True


@dataclass
class PerformanceThresholds:
    """Performance monitoring thresholds"""
    max_response_time_ms: int = 5000
    max_cpu_percentage: float = 80.0
    max_memory_percentage: float = 85.0
    max_error_rate_percentage: float = 5.0
    min_success_rate_percentage: float = 95.0


@dataclass
class ApplicationInsightsConfig:
    """Application Insights configuration"""
    instrumentation_key: Optional[str] = None
    connection_string: Optional[str] = None
    sampling_percentage: float = 100.0
    enable_live_metrics: bool = True
    enable_dependency_tracking: bool = True
    enable_performance_counters: bool = True
    enable_heartbeat: bool = True
    enable_adaptive_sampling: bool = True
    max_telemetry_items_per_second: int = 20


class FunctionConfiguration:
    """Function App configuration manager"""
    
    def __init__(self, environment: str = None):
        """Initialize function configuration"""
        self.environment = environment or os.getenv('AZURE_FUNCTIONS_ENVIRONMENT', 'Development')
        self.config = self._load_configuration()
        self.scaling_rules = self._initialize_scaling_rules()
        self.performance_thresholds = self._initialize_performance_thresholds()
        self.app_insights_config = self._initialize_app_insights_config()
        
    def _load_configuration(self) -> Dict[str, Any]:
        """Load environment-specific configuration"""
        base_config = {
            'function_timeout_minutes': 15,
            'max_concurrent_requests': 200,
            'max_outstanding_requests': 500,
            'enable_dynamic_concurrency': True,
            'enable_health_monitoring': True,
            'enable_performance_optimization': True
        }
        
        # Environment-specific overrides
        env_overrides = {
            'Development': {
                'function_timeout_minutes': 10,
                'max_concurrent_requests': 50,
                'max_outstanding_requests': 100,
                'enable_dynamic_concurrency': False
            },
            'Staging': {
                'function_timeout_minutes': 12,
                'max_concurrent_requests': 100,
                'max_outstanding_requests': 200
            },
            'Production': {
                'function_timeout_minutes': 15,
                'max_concurrent_requests': 200,
                'max_outstanding_requests': 500,
                'enable_dynamic_concurrency': True
            }
        }
        
        if self.environment in env_overrides:
            base_config.update(env_overrides[self.environment])
        
        return base_config
    
    def _initialize_scaling_rules(self) -> List[ScalingRule]:
        """Initialize auto-scaling rules"""
        rules = []
        
        if self.environment == 'Production':
            # CPU-based scaling
            rules.append(ScalingRule(
                trigger=ScalingTrigger.CPU_PERCENTAGE,
                threshold_upper=70.0,
                threshold_lower=30.0,
                scale_out_instances=2,
                scale_in_instances=1,
                cooldown_minutes=5,
                evaluation_window_minutes=3
            ))
            
            # Memory-based scaling
            rules.append(ScalingRule(
                trigger=ScalingTrigger.MEMORY_PERCENTAGE,
                threshold_upper=80.0,
                threshold_lower=40.0,
                scale_out_instances=2,
                scale_in_instances=1,
                cooldown_minutes=5,
                evaluation_window_minutes=3
            ))
            
            # Request count-based scaling
            rules.append(ScalingRule(
                trigger=ScalingTrigger.REQUEST_COUNT,
                threshold_upper=1000.0,
                threshold_lower=100.0,
                scale_out_instances=3,
                scale_in_instances=1,
                cooldown_minutes=3,
                evaluation_window_minutes=2
            ))
            
            # Queue length-based scaling
            rules.append(ScalingRule(
                trigger=ScalingTrigger.QUEUE_LENGTH,
                threshold_upper=50.0,
                threshold_lower=5.0,
                scale_out_instances=2,
                scale_in_instances=1,
                cooldown_minutes=2,
                evaluation_window_minutes=1
            ))
            
        elif self.environment == 'Staging':
            # Reduced scaling for staging
            rules.append(ScalingRule(
                trigger=ScalingTrigger.CPU_PERCENTAGE,
                threshold_upper=80.0,
                threshold_lower=40.0,
                scale_out_instances=1,
                scale_in_instances=1,
                cooldown_minutes=10,
                evaluation_window_minutes=5
            ))
        
        return rules
    
    def _initialize_performance_thresholds(self) -> PerformanceThresholds:
        """Initialize performance thresholds"""
        if self.environment == 'Production':
            return PerformanceThresholds(
                max_response_time_ms=3000,
                max_cpu_percentage=75.0,
                max_memory_percentage=80.0,
                max_error_rate_percentage=2.0,
                min_success_rate_percentage=98.0
            )
        elif self.environment == 'Staging':
            return PerformanceThresholds(
                max_response_time_ms=5000,
                max_cpu_percentage=80.0,
                max_memory_percentage=85.0,
                max_error_rate_percentage=5.0,
                min_success_rate_percentage=95.0
            )
        else:  # Development
            return PerformanceThresholds(
                max_response_time_ms=10000,
                max_cpu_percentage=90.0,
                max_memory_percentage=90.0,
                max_error_rate_percentage=10.0,
                min_success_rate_percentage=90.0
            )
    
    def _initialize_app_insights_config(self) -> ApplicationInsightsConfig:
        """Initialize Application Insights configuration"""
        config = ApplicationInsightsConfig()
        
        # Get configuration from environment variables
        config.instrumentation_key = os.getenv('APPINSIGHTS_INSTRUMENTATIONKEY')
        config.connection_string = os.getenv('APPLICATIONINSIGHTS_CONNECTION_STRING')
        
        # Environment-specific settings
        if self.environment == 'Production':
            config.sampling_percentage = 10.0  # Sample 10% in production
            config.enable_adaptive_sampling = True
            config.max_telemetry_items_per_second = 50
        elif self.environment == 'Staging':
            config.sampling_percentage = 50.0  # Sample 50% in staging
            config.max_telemetry_items_per_second = 30
        else:  # Development
            config.sampling_percentage = 100.0  # Sample everything in development
            config.max_telemetry_items_per_second = 20
        
        return config
    
    def get_host_json_config(self) -> Dict[str, Any]:
        """Generate host.json configuration"""
        return {
            "version": "2.0",
            "functionTimeout": f"00:{self.config['function_timeout_minutes']:02d}:00",
            "concurrency": {
                "dynamicConcurrencyEnabled": self.config['enable_dynamic_concurrency'],
                "snapshotPersistenceEnabled": True
            },
            "healthMonitor": {
                "enabled": self.config['enable_health_monitoring'],
                "healthCheckInterval": "00:00:10",
                "healthCheckWindow": "00:02:00",
                "healthCheckThreshold": 6,
                "counterThreshold": 0.80
            },
            "extensions": {
                "http": {
                    "maxOutstandingRequests": self.config['max_outstanding_requests'],
                    "maxConcurrentRequests": self.config['max_concurrent_requests'],
                    "dynamicThrottlesEnabled": True
                }
            },
            "logging": {
                "applicationInsights": {
                    "samplingSettings": {
                        "isEnabled": True,
                        "maxTelemetryItemsPerSecond": self.app_insights_config.max_telemetry_items_per_second
                    },
                    "enableLiveMetrics": self.app_insights_config.enable_live_metrics,
                    "enableDependencyTracking": self.app_insights_config.enable_dependency_tracking
                }
            }
        }
    
    def get_scaling_configuration(self) -> Dict[str, Any]:
        """Get auto-scaling configuration"""
        return {
            "environment": self.environment,
            "scaling_rules": [asdict(rule) for rule in self.scaling_rules],
            "performance_thresholds": asdict(self.performance_thresholds),
            "last_updated": datetime.now().isoformat()
        }
    
    def should_scale_out(self, metrics: Dict[str, float]) -> tuple[bool, str]:
        """Determine if scaling out is needed"""
        for rule in self.scaling_rules:
            if not rule.enabled:
                continue
                
            metric_key = rule.trigger.value
            if metric_key in metrics:
                current_value = metrics[metric_key]
                if current_value > rule.threshold_upper:
                    return True, f"Scale out triggered by {metric_key}: {current_value} > {rule.threshold_upper}"
        
        return False, "No scale out triggers met"
    
    def should_scale_in(self, metrics: Dict[str, float]) -> tuple[bool, str]:
        """Determine if scaling in is needed"""
        for rule in self.scaling_rules:
            if not rule.enabled:
                continue
                
            metric_key = rule.trigger.value
            if metric_key in metrics:
                current_value = metrics[metric_key]
                if current_value < rule.threshold_lower:
                    return True, f"Scale in triggered by {metric_key}: {current_value} < {rule.threshold_lower}"
        
        return False, "No scale in triggers met"
    
    def validate_performance(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Validate performance against thresholds"""
        violations = []
        warnings = []
        
        thresholds = self.performance_thresholds
        
        # Check response time
        if 'response_time_ms' in metrics:
            if metrics['response_time_ms'] > thresholds.max_response_time_ms:
                violations.append(f"Response time {metrics['response_time_ms']}ms exceeds threshold {thresholds.max_response_time_ms}ms")
        
        # Check CPU usage
        if 'cpu_percentage' in metrics:
            if metrics['cpu_percentage'] > thresholds.max_cpu_percentage:
                violations.append(f"CPU usage {metrics['cpu_percentage']}% exceeds threshold {thresholds.max_cpu_percentage}%")
            elif metrics['cpu_percentage'] > thresholds.max_cpu_percentage * 0.8:
                warnings.append(f"CPU usage {metrics['cpu_percentage']}% approaching threshold")
        
        # Check memory usage
        if 'memory_percentage' in metrics:
            if metrics['memory_percentage'] > thresholds.max_memory_percentage:
                violations.append(f"Memory usage {metrics['memory_percentage']}% exceeds threshold {thresholds.max_memory_percentage}%")
            elif metrics['memory_percentage'] > thresholds.max_memory_percentage * 0.8:
                warnings.append(f"Memory usage {metrics['memory_percentage']}% approaching threshold")
        
        # Check error rate
        if 'error_rate_percentage' in metrics:
            if metrics['error_rate_percentage'] > thresholds.max_error_rate_percentage:
                violations.append(f"Error rate {metrics['error_rate_percentage']}% exceeds threshold {thresholds.max_error_rate_percentage}%")
        
        # Check success rate
        if 'success_rate_percentage' in metrics:
            if metrics['success_rate_percentage'] < thresholds.min_success_rate_percentage:
                violations.append(f"Success rate {metrics['success_rate_percentage']}% below threshold {thresholds.min_success_rate_percentage}%")
        
        return {
            "status": "healthy" if not violations else "unhealthy",
            "violations": violations,
            "warnings": warnings,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_telemetry_config(self) -> Dict[str, Any]:
        """Get telemetry configuration for Application Insights"""
        return {
            "instrumentation_key": self.app_insights_config.instrumentation_key,
            "connection_string": self.app_insights_config.connection_string,
            "sampling_percentage": self.app_insights_config.sampling_percentage,
            "enable_live_metrics": self.app_insights_config.enable_live_metrics,
            "enable_dependency_tracking": self.app_insights_config.enable_dependency_tracking,
            "enable_performance_counters": self.app_insights_config.enable_performance_counters,
            "enable_heartbeat": self.app_insights_config.enable_heartbeat,
            "max_telemetry_items_per_second": self.app_insights_config.max_telemetry_items_per_second
        }
    
    def update_scaling_rule(self, trigger: ScalingTrigger, **kwargs):
        """Update a specific scaling rule"""
        for rule in self.scaling_rules:
            if rule.trigger == trigger:
                for key, value in kwargs.items():
                    if hasattr(rule, key):
                        setattr(rule, key, value)
                logger.info(f"Updated scaling rule for {trigger.value}")
                return
        
        logger.warning(f"Scaling rule for {trigger.value} not found")
    
    def export_configuration(self) -> Dict[str, Any]:
        """Export complete configuration"""
        return {
            "environment": self.environment,
            "config": self.config,
            "scaling_rules": [asdict(rule) for rule in self.scaling_rules],
            "performance_thresholds": asdict(self.performance_thresholds),
            "app_insights_config": asdict(self.app_insights_config),
            "host_json_config": self.get_host_json_config(),
            "exported_at": datetime.now().isoformat()
        }


# Global configuration instance
_function_config = None

def get_function_configuration(environment: str = None) -> FunctionConfiguration:
    """Get global function configuration instance"""
    global _function_config
    if _function_config is None:
        _function_config = FunctionConfiguration(environment)
    return _function_config


def initialize_application_insights():
    """Initialize Application Insights telemetry"""
    try:
        config = get_function_configuration()
        telemetry_config = config.get_telemetry_config()
        
        if not telemetry_config.get('connection_string') and not telemetry_config.get('instrumentation_key'):
            logger.warning("Application Insights not configured - no connection string or instrumentation key")
            return None
        
        # Initialize Application Insights
        from opencensus.ext.azure.log_exporter import AzureLogHandler
        from opencensus.ext.azure.trace_exporter import AzureExporter
        from opencensus.trace.samplers import ProbabilitySampler
        from opencensus.trace import config_integration
        
        # Configure integrations
        config_integration.trace_integrations(['requests', 'logging'])
        
        # Configure sampling
        sampling_rate = telemetry_config['sampling_percentage'] / 100.0
        sampler = ProbabilitySampler(rate=sampling_rate)
        
        logger.info(f"Application Insights initialized with {telemetry_config['sampling_percentage']}% sampling")
        return True
        
    except ImportError:
        logger.warning("Application Insights dependencies not available")
        return None
    except Exception as e:
        logger.error(f"Failed to initialize Application Insights: {e}")
        return None