"""
Security Module

This module provides security and compliance features for the queue-based task processing system.
It includes encryption, access control, audit logging, and compliance monitoring.

Features:
- Data encryption at rest and in transit
- Access control and authorization
- Audit logging
- Compliance monitoring
- Secure configuration management
"""

import logging
import json
import hashlib
import hmac
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

# Configure module-level logger
logger = logging.getLogger(__name__)

class SecurityManager:
    """Central security manager for the system"""
    
    def __init__(self, encryption_key: Optional[str] = None):
        """Initialize security manager"""
        self.encryption_key = encryption_key or self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key.encode() if isinstance(self.encryption_key, str) else self.encryption_key)
        self.audit_logger = logging.getLogger('audit')
        
    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key"""
        key = os.getenv('ENCRYPTION_KEY')
        if key:
            return key.encode()
        
        # Generate a new key if none provided
        return Fernet.generate_key()
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            encrypted = self.cipher_suite.encrypt(data.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            raise
    
    def hash_data(self, data: str, salt: Optional[str] = None) -> str:
        """Hash sensitive data"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        combined = f"{data}{salt}"
        hashed = hashlib.sha256(combined.encode()).hexdigest()
        return f"{salt}${hashed}"
    
    def verify_hash(self, data: str, hashed: str) -> bool:
        """Verify hashed data"""
        try:
            salt, expected_hash = hashed.split('$')
            combined = f"{data}{salt}"
            actual_hash = hashlib.sha256(combined.encode()).hexdigest()
            return actual_hash == expected_hash
        except Exception:
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate secure random token"""
        return secrets.token_urlsafe(length)
    
    def sanitize_log_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize data for logging"""
        sensitive_fields = [
            'password', 'token', 'secret', 'key', 'credential',
            'auth', 'private', 'sensitive', 'confidential'
        ]
        
        sanitized = {}
        for key, value in data.items():
            if any(field in key.lower() for field in sensitive_fields):
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self.sanitize_log_data(value)
            else:
                sanitized[key] = value
        
        return sanitized
    
    def audit_log(self, action: str, user_id: str, details: Dict[str, Any], 
                  ip_address: Optional[str] = None):
        """Log audit events"""
        audit_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'user_id': user_id,
            'ip_address': ip_address,
            'details': self.sanitize_log_data(details)
        }
        
        self.audit_logger.info(json.dumps(audit_entry))
    
    def validate_access(self, user_id: str, resource: str, action: str) -> bool:
        """Validate user access to resource"""
        # Implement access control logic
        # This would typically check user permissions
        return True
    
    def mask_sensitive_data(self, data: str, mask_char: str = '*') -> str:
        """Mask sensitive data for display"""
        if len(data) <= 4:
            return mask_char * len(data)
        
        return data[:2] + mask_char * (len(data) - 4) + data[-2:]

class ComplianceMonitor:
    """Monitor compliance requirements"""
    
    def __init__(self):
        """Initialize compliance monitor"""
        self.compliance_rules = self._load_compliance_rules()
    
    def _load_compliance_rules(self) -> Dict[str, Any]:
        """Load compliance rules"""
        return {
            'data_retention_days': 90,
            'max_task_age_hours': 24,
            'encryption_required': True,
            'audit_logging_required': True,
            'access_control_required': True
        }
    
    def check_compliance(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check task compliance"""
        issues = []
        
        # Check data retention
        created_at = task_data.get('created_at')
        if created_at:
            created_date = datetime.fromisoformat(created_at)
            age = datetime.now() - created_date
            if age.days > self.compliance_rules['data_retention_days']:
                issues.append("Task exceeds data retention period")
        
        # Check encryption
        if self.compliance_rules['encryption_required']:
            # Verify encryption is applied
            pass
        
        # Check audit logging
        if self.compliance_rules['audit_logging_required']:
            # Verify audit logging is enabled
            pass
        
        return {
            'compliant': len(issues) == 0,
            'issues': issues,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate compliance report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'rules': self.compliance_rules,
            'status': 'compliant',
            'next_review': (datetime.now() + timedelta(days=30)).isoformat()
        }

class SecureQueueManager:
    """Secure wrapper for queue operations"""
    
    def __init__(self, security_manager: SecurityManager):
        """Initialize secure queue manager"""
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
    
    def secure_enqueue(self, queue_name: str, message: Dict[str, Any], 
                      user_id: str, ip_address: Optional[str] = None) -> bool:
        """Securely enqueue message"""
        try:
            # Validate access
            if not self.security_manager.validate_access(user_id, queue_name, 'enqueue'):
                self.security_manager.audit_log(
                    'enqueue_denied', user_id, {'queue': queue_name}, ip_address
                )
                return False
            
            # Encrypt sensitive data
            encrypted_message = self._encrypt_sensitive_fields(message)
            
            # Log audit event
            self.security_manager.audit_log(
                'enqueue', user_id, {'queue': queue_name, 'message_id': encrypted_message.get('task_id')}, ip_address
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Secure enqueue error: {e}")
            return False
    
    def secure_dequeue(self, queue_name: str, user_id: str, 
                      ip_address: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Securely dequeue message"""
        try:
            # Validate access
            if not self.security_manager.validate_access(user_id, queue_name, 'dequeue'):
                self.security_manager.audit_log(
                    'dequeue_denied', user_id, {'queue': queue_name}, ip_address
                )
                return None
            
            # Log audit event
            self.security_manager.audit_log(
                'dequeue', user_id, {'queue': queue_name}, ip_address
            )
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Secure dequeue error: {e}")
            return None
    
    def _encrypt_sensitive_fields(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive fields in message"""
        sensitive_fields = ['credentials', 'tokens', 'secrets']
        encrypted = message.copy()
        
        for field in sensitive_fields:
            if field in encrypted:
                encrypted[field] = self.security_manager.encrypt_data(str(encrypted[field]))
        
        return encrypted

class SecureTaskProcessor:
    """Secure task processor with compliance checks"""
    
    def __init__(self, security_manager: SecurityManager, compliance_monitor: ComplianceMonitor):
        """Initialize secure task processor"""
        self.security_manager = security_manager
        self.compliance_monitor = compliance_monitor
        self.logger = logging.getLogger(__name__)
    
    def process_task_securely(self, task_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Process task with security and compliance checks"""
        try:
            # Check compliance
            compliance_result = self.compliance_monitor.check_compliance(task_data)
            if not compliance_result['compliant']:
                return {
                    'success': False,
                    'error': 'Compliance check failed',
                    'issues': compliance_result['issues']
                }
            
            # Validate access
            if not self.security_manager.validate_access(user_id, 'task_processing', 'execute'):
                return {
                    'success': False,
                    'error': 'Access denied'
                }
            
            # Log audit event
            self.security_manager.audit_log(
                'task_processing', user_id, {'task_id': task_data.get('task_id')}
            )
            
            # Process task securely
            return {
                'success': True,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Secure task processing error: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# Global security instances
_security_manager = None
_compliance_monitor = None

def get_security_manager() -> SecurityManager:
    """Get global security manager instance"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager()
    return _security_manager

def get_compliance_monitor() -> ComplianceMonitor:
    """Get global compliance monitor instance"""
    global _compliance_monitor
    if _compliance_monitor is None:
        _compliance_monitor = ComplianceMonitor()
    return _compliance_monitor

def get_secure_queue_manager() -> SecureQueueManager:
    """Get secure queue manager"""
    return SecureQueueManager(get_security_manager())

def get_secure_task_processor() -> SecureTaskProcessor:
    """Get secure task processor"""
    return SecureTaskProcessor(
        get_security_manager(),
        get_compliance_monitor()
    )