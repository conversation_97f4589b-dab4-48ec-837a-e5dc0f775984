"""
Salesforce Client Module

This module provides a unified interface for Salesforce operations using simple-salesforce.
It replaces the custom Salesforce API calls with a more robust and maintained library.
"""

import logging
import urllib.parse
from typing import Dict, Any, Optional, List, Tuple, Union
import json
import datetime

# Import simple-salesforce
from simple_salesforce import Salesforce, SalesforceLogin, SFType
from simple_salesforce.exceptions import SalesforceAuthenticationFailed, SalesforceError

# Import shared modules
from src.shared.azure_services import get_secret

# Configure module-level logger
logger = logging.getLogger(__name__)


class SalesforceClient:
    """
    Salesforce client using simple-salesforce library

    This class provides a unified interface for all Salesforce operations,
    handling authentication, query execution, and error handling.
    """

    def __init__(self, instance_url: str = None, access_token: str = None,
                 client_id: str = None, client_secret: str = None,
                 is_sandbox: bool = False, use_jwt: bool = False,
                 username: str = None, private_key: str = None):
        """
        Initialize Salesforce client

        Args:
            instance_url: Salesforce instance URL (optional if using client credentials)
            access_token: Salesforce access token (optional if using client credentials)
            client_id: Salesforce client ID (optional if using access token)
            client_secret: Salesforce client secret (optional if using access token)
            is_sandbox: Whether the instance is a sandbox
            use_jwt: Whether to use JWT Bearer flow
            username: Salesforce username for JWT flow
            private_key: Private key for JWT flow
        """
        self.instance_url = instance_url
        self.access_token = access_token
        self.client_id = client_id
        self.client_secret = client_secret
        self.is_sandbox = is_sandbox
        self.use_jwt = use_jwt
        self.username = username
        self.private_key = private_key
        self.sf = None

        # Initialize client if we have enough information
        if access_token and instance_url:
            self._init_with_session_id()
        elif use_jwt and client_id and username and private_key and instance_url:
            self._init_with_jwt_bearer()
        elif client_id and client_secret and instance_url:
            self._init_with_client_credentials()

    def _init_with_session_id(self):
        """Initialize Salesforce client with session ID (access token)"""
        try:
            # Clean up instance URL if needed
            instance_url = self._normalize_instance_url(self.instance_url)

            # Extract domain from instance URL
            domain = instance_url.replace('https://', '').split('/')[0]

            # Initialize Salesforce client with session ID
            self.sf = Salesforce(
                instance_url=instance_url,
                session_id=self.access_token,
                domain=domain
            )
            logger.info(f"Initialized Salesforce client with session ID for {domain}")
            return True
        except Exception as e:
            logger.error(f"Error initializing Salesforce client with session ID: {str(e)}")
            return False

    def _init_with_jwt_bearer(self):
        """Initialize Salesforce client with JWT Bearer flow"""
        try:
            # Normalize instance URL
            instance_url = self._normalize_instance_url(self.instance_url)

            # Import the JWT test function
            from src.shared.salesforce_jwt_auth import test_jwt_bearer_flow

            # Use JWT Bearer flow to get access token
            success, error_message, connection_details = test_jwt_bearer_flow(
                client_id=self.client_id,
                tenant_url=self.instance_url,
                username=self.username,
                private_key=self.private_key,
                is_sandbox=self.is_sandbox
            )

            if not success:
                logger.error(f"Failed to authenticate with JWT Bearer flow: {error_message}")
                return False

            # Extract access token and instance URL
            self.access_token = connection_details.get('access_token')
            self.instance_url = connection_details.get('instance_url')

            # Initialize with session ID
            return self._init_with_session_id()
        except Exception as e:
            logger.error(f"Error initializing Salesforce client with JWT Bearer flow: {str(e)}")
            return False

    def _init_with_client_credentials(self):
        """Initialize Salesforce client with client credentials"""
        try:
            # Normalize instance URL
            instance_url = self._normalize_instance_url(self.instance_url)

            # Use client credentials flow to get access token
            # Note: simple-salesforce doesn't directly support client credentials flow,
            # so we'll use the token to initialize the client
            success, error_message, connection_details = self.test_client_credentials_flow()

            if not success:
                logger.error(f"Failed to authenticate with client credentials: {error_message}")
                return False

            # Extract access token and instance URL
            self.access_token = connection_details.get('access_token')
            self.instance_url = connection_details.get('instance_url')

            # Initialize with session ID
            return self._init_with_session_id()
        except Exception as e:
            logger.error(f"Error initializing Salesforce client with client credentials: {str(e)}")
            return False

    def _normalize_instance_url(self, url: str) -> str:
        """
        Normalize instance URL

        Args:
            url: Instance URL to normalize

        Returns:
            str: Normalized URL
        """
        if not url:
            return None

        # Ensure URL starts with https://
        if not url.startswith('http'):
            url = f"https://{url}"

        # Remove trailing slash if present
        if url.endswith('/'):
            url = url[:-1]

        return url

    def test_client_credentials_flow(self) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Test Salesforce connection using OAuth 2.0 Client Credentials flow

        Returns:
            Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
        """
        import requests

        try:
            logger.info(f"Testing Salesforce connection with Client Credentials flow")

            # Normalize instance URL
            normalized_instance_url = self._normalize_instance_url(self.instance_url)

            # Check if this is a Developer Edition org (contains -dev-ed in the URL)
            is_dev_edition = "-dev-ed" in normalized_instance_url.lower() if normalized_instance_url else False

            # For Developer Edition orgs, use the instance URL directly for authentication
            # For regular orgs, use the standard login endpoints
            if is_dev_edition and normalized_instance_url:
                logger.info(f"Detected Developer Edition org: {normalized_instance_url}")
                token_url = f"{normalized_instance_url}/services/oauth2/token"
            else:
                # Determine auth URL based on whether it's a sandbox
                auth_url = "https://test.salesforce.com" if self.is_sandbox else "https://login.salesforce.com"
                token_url = f"{auth_url}/services/oauth2/token"

            # Request access token
            token_data = {
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }

            logger.info(f"Requesting access token from {token_url}")
            response = requests.post(token_url, data=token_data)

            if response.status_code == 200:
                token_response = response.json()
                access_token = token_response.get('access_token')
                instance_url = token_response.get('instance_url')

                if not access_token or not instance_url:
                    error_msg = "Access token or instance URL not found in response"
                    logger.error(error_msg)
                    return False, error_msg, None

                logger.info(f"Successfully obtained access token for {instance_url}")

                # Verify token by making a simple API call
                api_url = f"{instance_url}/services/data/v62.0/sobjects"
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }

                api_response = requests.get(api_url, headers=headers)

                if api_response.status_code == 200:
                    logger.info("Successfully verified access token with API call")
                    return True, None, {
                        'access_token': access_token,
                        'instance_url': instance_url,
                        'token_type': token_response.get('token_type', 'Bearer'),
                        'issued_at': token_response.get('issued_at')
                    }
                else:
                    error_msg = f"API call failed: {api_response.status_code} - {api_response.text}"
                    logger.error(error_msg)
                    return False, error_msg, None
            else:
                # Try to parse error from response
                try:
                    response_json = response.json()
                    error_type = response_json.get('error')
                    error_description = response_json.get('error_description')

                    if error_type == 'invalid_client':
                        error_msg = f"Invalid client: {error_description}. Check your client ID and secret."
                        logger.error(error_msg)
                        return False, error_msg, None
                    elif error_type == 'invalid_grant':
                        error_msg = f"Invalid grant: {error_description}. Check your Connected App settings."
                        logger.error(error_msg)
                        return False, error_msg, None
                    elif error_type == 'invalid_scope':
                        error_msg = f"Invalid scope: {error_description}. Make sure your Connected App has the necessary OAuth scopes."
                        logger.error(error_msg)
                        return False, error_msg, None
                    else:
                        error_msg = f"Authentication error: {error_type} - {error_description}"
                        logger.error(error_msg)
                        return False, error_msg, None
                except Exception:
                    error_msg = f"Failed to authenticate: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    return False, error_msg, None

        except Exception as e:
            logger.error(f"Error in Client Credentials flow: {str(e)}")
            return False, f"Error in Client Credentials flow: {str(e)}", None

    def query(self, soql_query: str, include_deleted: bool = False) -> Optional[Dict[str, Any]]:
        """
        Execute a SOQL query

        Args:
            soql_query: SOQL query to execute
            include_deleted: Whether to include deleted records

        Returns:
            Dict: Query results or None if error
        """
        if not self.sf:
            logger.error("Salesforce client not initialized")
            return None

        try:
            logger.info(f"Executing SOQL query: {soql_query}")

            # Execute query
            if include_deleted:
                result = self.sf.query_all(soql_query)
            else:
                result = self.sf.query(soql_query)

            logger.debug(f"Raw Salesforce API Response: {result}")
            return result
        except Exception as e:
            logger.error(f"Error executing SOQL query: {str(e)}")
            return None

    def tooling_query(self, soql_query: str) -> Optional[Dict[str, Any]]:
        """
        Execute a SOQL query using the Tooling API

        Args:
            soql_query: SOQL query to execute

        Returns:
            Dict: Query results or None if error
        """
        if not self.sf:
            logger.error("Salesforce client not initialized")
            return None

        try:
            logger.info(f"Executing Tooling API query: {soql_query}")

            # Execute query using the Tooling API
            result = self.sf.toolingexecute(f"query/?q={urllib.parse.quote(soql_query)}", method='GET')

            logger.debug(f"Raw Salesforce Tooling API Response: {result}")
            return result
        except Exception as e:
            logger.error(f"Error executing Tooling API query: {str(e)}")

            # Special handling for SecurityHealthCheck Score query
            if "SELECT Score FROM SecurityHealthCheck" in soql_query:
                logger.warning(f"Error querying SecurityHealthCheck Score, trying regular API")
                try:
                    # Try using regular API instead of Tooling API
                    return self.query(soql_query)
                except Exception as inner_e:
                    logger.error(f"Regular API approach also failed: {str(inner_e)}")

            return None
