"""
Intrusion Detection and Prevention System (IDPS)

This module provides real-time intrusion detection and prevention including:
- Network-based intrusion detection
- Host-based intrusion detection
- Behavioral anomaly detection
- Automated response and prevention
- Attack pattern recognition

Requirements addressed: 1.7
"""

import logging
import json
import time
import re
from typing import Dict, Any, Optional, List, Set, Tu<PERSON>, <PERSON>tern
from datetime import datetime, timed<PERSON><PERSON>
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import hashlib
import ipaddress
from statistics import mean, stdev

from src.shared.security_monitoring_service import get_security_monitoring_service, ThreatLevel, AlertType
from src.shared.user_activity_monitor import get_user_activity_monitor
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


class AttackType(Enum):
    """Types of attacks detected"""
    SQL_INJECTION = "sql_injection"
    XSS = "cross_site_scripting"
    COMMAND_INJECTION = "command_injection"
    PATH_TRAVERSAL = "path_traversal"
    LDAP_INJECTION = "ldap_injection"
    XML_INJECTION = "xml_injection"
    NOSQL_INJECTION = "nosql_injection"
    SSRF = "server_side_request_forgery"
    XXE = "xml_external_entity"
    DESERIALIZATION = "insecure_deserialization"
    BUFFER_OVERFLOW = "buffer_overflow"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    BACKDOOR = "backdoor_access"
    MALWARE = "malware_detected"
    RECONNAISSANCE = "reconnaissance"
    DENIAL_OF_SERVICE = "denial_of_service"


class DetectionMethod(Enum):
    """Detection methods used"""
    SIGNATURE_BASED = "signature_based"
    ANOMALY_BASED = "anomaly_based"
    BEHAVIORAL_BASED = "behavioral_based"
    HEURISTIC_BASED = "heuristic_based"
    MACHINE_LEARNING = "machine_learning"


class ResponseAction(Enum):
    """Automated response actions"""
    BLOCK_IP = "block_ip"
    BLOCK_USER = "block_user"
    RATE_LIMIT = "rate_limit"
    QUARANTINE = "quarantine"
    ALERT_ONLY = "alert_only"
    LOG_ONLY = "log_only"
    TERMINATE_SESSION = "terminate_session"
    REQUIRE_MFA = "require_mfa"


@dataclass
class AttackSignature:
    """Attack signature definition"""
    signature_id: str
    name: str
    attack_type: AttackType
    pattern: str
    regex_flags: int
    severity: ThreatLevel
    description: str
    references: List[str]
    enabled: bool = True
    false_positive_rate: float = 0.0
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.utcnow()


@dataclass
class DetectionResult:
    """Intrusion detection result"""
    detection_id: str
    timestamp: datetime
    attack_type: AttackType
    detection_method: DetectionMethod
    confidence: float
    severity: ThreatLevel
    source_ip: str
    target_endpoint: str
    user_id: Optional[str]
    session_id: Optional[str]
    payload: str
    matched_signature: Optional[str]
    indicators: List[str]
    raw_request: Dict[str, Any]
    response_action: ResponseAction
    blocked: bool = False
    
    def __post_init__(self):
        if not self.indicators:
            self.indicators = []


@dataclass
class BehavioralBaseline:
    """Behavioral baseline for anomaly detection"""
    entity_id: str  # User ID, IP, etc.
    entity_type: str  # user, ip, endpoint
    request_rate_mean: float
    request_rate_stddev: float
    typical_endpoints: Set[str]
    typical_methods: Set[str]
    typical_user_agents: Set[str]
    typical_request_sizes: Tuple[float, float]  # mean, stddev
    typical_response_times: Tuple[float, float]  # mean, stddev
    last_updated: datetime
    confidence: float = 0.0
    
    def __post_init__(self):
        if isinstance(self.typical_endpoints, list):
            self.typical_endpoints = set(self.typical_endpoints)
        if isinstance(self.typical_methods, list):
            self.typical_methods = set(self.typical_methods)
        if isinstance(self.typical_user_agents, list):
            self.typical_user_agents = set(self.typical_user_agents)


class IntrusionDetectionSystem:
    """
    Comprehensive Intrusion Detection and Prevention System
    """
    
    def __init__(self):
        self.security_monitor = get_security_monitoring_service()
        self.activity_monitor = get_user_activity_monitor()
        self.monitoring = get_monitoring_service()
        
        # Detection data
        self._attack_signatures: Dict[str, AttackSignature] = {}
        self._detection_results: deque = deque(maxlen=50000)
        self._behavioral_baselines: Dict[str, BehavioralBaseline] = {}
        self._blocked_entities: Dict[str, Dict[str, Any]] = {}
        
        # Real-time tracking
        self._request_patterns: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._anomaly_scores: Dict[str, float] = {}
        self._attack_chains: Dict[str, List[str]] = defaultdict(list)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Configuration
        self.config = {
            'signature_detection_enabled': True,
            'anomaly_detection_enabled': True,
            'behavioral_detection_enabled': True,
            'auto_response_enabled': True,
            'learning_mode': False,  # If True, only log, don't block
            'confidence_threshold': 0.7,
            'anomaly_threshold': 3.0,  # Standard deviations
            'baseline_learning_days': 7,
            'max_false_positive_rate': 0.1,
            'response_delay_seconds': 0,  # Delay before taking action
        }
        
        # Initialize components
        self._initialize_attack_signatures()
        self._start_baseline_learning()
    
    def analyze_request(self, request_data: Dict[str, Any]) -> List[DetectionResult]:
        """
        Analyze incoming request for intrusions
        
        Args:
            request_data: Request data to analyze
            
        Returns:
            List of detection results
        """
        try:
            detections = []
            
            # Extract request components
            method = request_data.get('method', 'GET')
            endpoint = request_data.get('endpoint', '/')
            headers = request_data.get('headers', {})
            query_params = request_data.get('query_params', {})
            body = request_data.get('body', '')
            source_ip = request_data.get('source_ip', 'unknown')
            user_agent = headers.get('User-Agent', 'unknown')
            user_id = request_data.get('user_id')
            session_id = request_data.get('session_id')
            
            # Signature-based detection
            if self.config['signature_detection_enabled']:
                signature_detections = self._signature_based_detection(
                    request_data, method, endpoint, headers, query_params, body
                )
                detections.extend(signature_detections)
            
            # Anomaly-based detection
            if self.config['anomaly_detection_enabled']:
                anomaly_detections = self._anomaly_based_detection(
                    request_data, source_ip, user_id, endpoint
                )
                detections.extend(anomaly_detections)
            
            # Behavioral-based detection
            if self.config['behavioral_detection_enabled']:
                behavioral_detections = self._behavioral_based_detection(
                    request_data, source_ip, user_id, user_agent
                )
                detections.extend(behavioral_detections)
            
            # Process detections
            for detection in detections:
                self._process_detection(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"Request analysis failed: {str(e)}")
            return []
    
    def analyze_response(self, request_data: Dict[str, Any], 
                        response_data: Dict[str, Any]) -> List[DetectionResult]:
        """
        Analyze response for signs of successful attacks
        
        Args:
            request_data: Original request data
            response_data: Response data
            
        Returns:
            List of detection results
        """
        try:
            detections = []
            
            status_code = response_data.get('status_code', 200)
            response_body = response_data.get('body', '')
            response_headers = response_data.get('headers', {})
            response_time = response_data.get('response_time_ms', 0)
            
            # Check for error-based SQL injection
            if status_code >= 500:
                sql_errors = self._check_sql_error_patterns(response_body)
                if sql_errors:
                    detection = DetectionResult(
                        detection_id=self._generate_detection_id(),
                        timestamp=datetime.utcnow(),
                        attack_type=AttackType.SQL_INJECTION,
                        detection_method=DetectionMethod.SIGNATURE_BASED,
                        confidence=0.8,
                        severity=ThreatLevel.HIGH,
                        source_ip=request_data.get('source_ip', 'unknown'),
                        target_endpoint=request_data.get('endpoint', '/'),
                        user_id=request_data.get('user_id'),
                        session_id=request_data.get('session_id'),
                        payload=request_data.get('body', ''),
                        matched_signature='sql_error_response',
                        indicators=['sql_error_in_response', 'server_error'],
                        raw_request=request_data,
                        response_action=ResponseAction.BLOCK_IP
                    )
                    detections.append(detection)
            
            # Check for information disclosure
            if self._check_information_disclosure(response_body, response_headers):
                detection = DetectionResult(
                    detection_id=self._generate_detection_id(),
                    timestamp=datetime.utcnow(),
                    attack_type=AttackType.RECONNAISSANCE,
                    detection_method=DetectionMethod.HEURISTIC_BASED,
                    confidence=0.6,
                    severity=ThreatLevel.MEDIUM,
                    source_ip=request_data.get('source_ip', 'unknown'),
                    target_endpoint=request_data.get('endpoint', '/'),
                    user_id=request_data.get('user_id'),
                    session_id=request_data.get('session_id'),
                    payload='',
                    matched_signature='information_disclosure',
                    indicators=['sensitive_info_disclosure'],
                    raw_request=request_data,
                    response_action=ResponseAction.ALERT_ONLY
                )
                detections.append(detection)
            
            # Check for unusual response times (potential DoS)
            if response_time > 10000:  # 10 seconds
                detection = DetectionResult(
                    detection_id=self._generate_detection_id(),
                    timestamp=datetime.utcnow(),
                    attack_type=AttackType.DENIAL_OF_SERVICE,
                    detection_method=DetectionMethod.ANOMALY_BASED,
                    confidence=0.5,
                    severity=ThreatLevel.MEDIUM,
                    source_ip=request_data.get('source_ip', 'unknown'),
                    target_endpoint=request_data.get('endpoint', '/'),
                    user_id=request_data.get('user_id'),
                    session_id=request_data.get('session_id'),
                    payload=request_data.get('body', ''),
                    matched_signature='slow_response',
                    indicators=['slow_response_time'],
                    raw_request=request_data,
                    response_action=ResponseAction.RATE_LIMIT
                )
                detections.append(detection)
            
            # Process detections
            for detection in detections:
                self._process_detection(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"Response analysis failed: {str(e)}")
            return []
    
    def update_behavioral_baseline(self, entity_id: str, entity_type: str,
                                 activity_data: List[Dict[str, Any]]):
        """
        Update behavioral baseline for entity
        
        Args:
            entity_id: Entity identifier
            entity_type: Type of entity (user, ip, etc.)
            activity_data: Historical activity data
        """
        try:
            if len(activity_data) < 10:  # Need minimum data
                return
            
            # Calculate baseline metrics
            request_rates = [d.get('request_rate', 0) for d in activity_data]
            endpoints = set()
            methods = set()
            user_agents = set()
            request_sizes = []
            response_times = []
            
            for data in activity_data:
                endpoints.update(data.get('endpoints', []))
                methods.update(data.get('methods', []))
                user_agents.update(data.get('user_agents', []))
                request_sizes.extend(data.get('request_sizes', []))
                response_times.extend(data.get('response_times', []))
            
            # Create baseline
            baseline = BehavioralBaseline(
                entity_id=entity_id,
                entity_type=entity_type,
                request_rate_mean=mean(request_rates) if request_rates else 0,
                request_rate_stddev=stdev(request_rates) if len(request_rates) > 1 else 0,
                typical_endpoints=endpoints,
                typical_methods=methods,
                typical_user_agents=user_agents,
                typical_request_sizes=(mean(request_sizes) if request_sizes else 0,
                                     stdev(request_sizes) if len(request_sizes) > 1 else 0),
                typical_response_times=(mean(response_times) if response_times else 0,
                                      stdev(response_times) if len(response_times) > 1 else 0),
                last_updated=datetime.utcnow(),
                confidence=min(len(activity_data) / 100.0, 1.0)  # Max confidence at 100 data points
            )
            
            with self._lock:
                self._behavioral_baselines[f"{entity_type}:{entity_id}"] = baseline
            
            logger.debug(f"Behavioral baseline updated for {entity_type}:{entity_id}")
            
        except Exception as e:
            logger.error(f"Failed to update behavioral baseline: {str(e)}")
    
    def add_attack_signature(self, signature: AttackSignature) -> bool:
        """
        Add new attack signature
        
        Args:
            signature: Attack signature to add
            
        Returns:
            True if added successfully
        """
        try:
            with self._lock:
                # Validate regex pattern
                try:
                    re.compile(signature.pattern, signature.regex_flags)
                except re.error as e:
                    logger.error(f"Invalid regex pattern in signature {signature.signature_id}: {e}")
                    return False
                
                self._attack_signatures[signature.signature_id] = signature
                
                logger.info(f"Attack signature added: {signature.signature_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add attack signature: {str(e)}")
            return False
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """
        Get intrusion detection statistics
        
        Returns:
            Dict containing detection statistics
        """
        try:
            with self._lock:
                current_time = datetime.utcnow()
                last_24h = current_time - timedelta(hours=24)
                
                recent_detections = [
                    d for d in self._detection_results
                    if d.timestamp >= last_24h
                ]
                
                # Statistics by attack type
                attack_type_counts = defaultdict(int)
                detection_method_counts = defaultdict(int)
                severity_counts = defaultdict(int)
                blocked_count = 0
                
                for detection in recent_detections:
                    attack_type_counts[detection.attack_type.value] += 1
                    detection_method_counts[detection.detection_method.value] += 1
                    severity_counts[detection.severity.value] += 1
                    if detection.blocked:
                        blocked_count += 1
                
                # Top source IPs
                ip_counts = defaultdict(int)
                for detection in recent_detections:
                    ip_counts[detection.source_ip] += 1
                
                top_ips = sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                
                return {
                    'timestamp': current_time.isoformat(),
                    'total_detections_24h': len(recent_detections),
                    'blocked_attacks_24h': blocked_count,
                    'attack_types': dict(attack_type_counts),
                    'detection_methods': dict(detection_method_counts),
                    'severity_distribution': dict(severity_counts),
                    'top_source_ips': [{'ip': ip, 'count': count} for ip, count in top_ips],
                    'active_signatures': len([s for s in self._attack_signatures.values() if s.enabled]),
                    'behavioral_baselines': len(self._behavioral_baselines),
                    'blocked_entities': len(self._blocked_entities)
                }
                
        except Exception as e:
            logger.error(f"Failed to get detection statistics: {str(e)}")
            return {'error': str(e)}
    
    def is_blocked(self, entity_type: str, entity_id: str) -> bool:
        """
        Check if entity is blocked
        
        Args:
            entity_type: Type of entity (ip, user, etc.)
            entity_id: Entity identifier
            
        Returns:
            True if entity is blocked
        """
        key = f"{entity_type}:{entity_id}"
        blocked_info = self._blocked_entities.get(key)
        
        if not blocked_info:
            return False
        
        # Check if block has expired
        if blocked_info.get('expires_at'):
            if datetime.utcnow() > blocked_info['expires_at']:
                del self._blocked_entities[key]
                return False
        
        return True
    
    # Private helper methods
    
    def _signature_based_detection(self, request_data: Dict[str, Any],
                                 method: str, endpoint: str, headers: Dict[str, str],
                                 query_params: Dict[str, Any], body: str) -> List[DetectionResult]:
        """Perform signature-based detection"""
        detections = []
        
        # Combine all request data for pattern matching
        combined_data = f"{method} {endpoint} {json.dumps(query_params)} {body} {json.dumps(headers)}"
        
        for signature in self._attack_signatures.values():
            if not signature.enabled:
                continue
            
            try:
                pattern = re.compile(signature.pattern, signature.regex_flags)
                if pattern.search(combined_data):
                    # Check false positive rate
                    if signature.false_positive_rate > self.config['max_false_positive_rate']:
                        continue
                    
                    detection = DetectionResult(
                        detection_id=self._generate_detection_id(),
                        timestamp=datetime.utcnow(),
                        attack_type=signature.attack_type,
                        detection_method=DetectionMethod.SIGNATURE_BASED,
                        confidence=1.0 - signature.false_positive_rate,
                        severity=signature.severity,
                        source_ip=request_data.get('source_ip', 'unknown'),
                        target_endpoint=endpoint,
                        user_id=request_data.get('user_id'),
                        session_id=request_data.get('session_id'),
                        payload=body,
                        matched_signature=signature.signature_id,
                        indicators=[f"signature_match:{signature.name}"],
                        raw_request=request_data,
                        response_action=self._determine_response_action(signature.severity, signature.attack_type)
                    )
                    detections.append(detection)
                    
            except re.error as e:
                logger.error(f"Regex error in signature {signature.signature_id}: {e}")
        
        return detections
    
    def _anomaly_based_detection(self, request_data: Dict[str, Any],
                               source_ip: str, user_id: str, endpoint: str) -> List[DetectionResult]:
        """Perform anomaly-based detection"""
        detections = []
        
        # Check IP-based anomalies
        if source_ip != 'unknown':
            ip_baseline = self._behavioral_baselines.get(f"ip:{source_ip}")
            if ip_baseline and ip_baseline.confidence > 0.5:
                anomaly_score = self._calculate_anomaly_score(request_data, ip_baseline)
                
                if anomaly_score > self.config['anomaly_threshold']:
                    detection = DetectionResult(
                        detection_id=self._generate_detection_id(),
                        timestamp=datetime.utcnow(),
                        attack_type=AttackType.RECONNAISSANCE,  # Default for anomalies
                        detection_method=DetectionMethod.ANOMALY_BASED,
                        confidence=min(anomaly_score / 10.0, 1.0),
                        severity=ThreatLevel.MEDIUM if anomaly_score < 5.0 else ThreatLevel.HIGH,
                        source_ip=source_ip,
                        target_endpoint=endpoint,
                        user_id=user_id,
                        session_id=request_data.get('session_id'),
                        payload=request_data.get('body', ''),
                        matched_signature='anomaly_detection',
                        indicators=[f"anomaly_score:{anomaly_score:.2f}"],
                        raw_request=request_data,
                        response_action=ResponseAction.ALERT_ONLY if anomaly_score < 5.0 else ResponseAction.RATE_LIMIT
                    )
                    detections.append(detection)
        
        # Check user-based anomalies
        if user_id:
            user_baseline = self._behavioral_baselines.get(f"user:{user_id}")
            if user_baseline and user_baseline.confidence > 0.5:
                anomaly_score = self._calculate_anomaly_score(request_data, user_baseline)
                
                if anomaly_score > self.config['anomaly_threshold']:
                    detection = DetectionResult(
                        detection_id=self._generate_detection_id(),
                        timestamp=datetime.utcnow(),
                        attack_type=AttackType.PRIVILEGE_ESCALATION,
                        detection_method=DetectionMethod.ANOMALY_BASED,
                        confidence=min(anomaly_score / 10.0, 1.0),
                        severity=ThreatLevel.MEDIUM if anomaly_score < 5.0 else ThreatLevel.HIGH,
                        source_ip=source_ip,
                        target_endpoint=endpoint,
                        user_id=user_id,
                        session_id=request_data.get('session_id'),
                        payload=request_data.get('body', ''),
                        matched_signature='user_anomaly_detection',
                        indicators=[f"user_anomaly_score:{anomaly_score:.2f}"],
                        raw_request=request_data,
                        response_action=ResponseAction.REQUIRE_MFA if anomaly_score < 5.0 else ResponseAction.TERMINATE_SESSION
                    )
                    detections.append(detection)
        
        return detections
    
    def _behavioral_based_detection(self, request_data: Dict[str, Any],
                                  source_ip: str, user_id: str, user_agent: str) -> List[DetectionResult]:
        """Perform behavioral-based detection"""
        detections = []
        
        # Check for automated tools/bots
        if self._is_automated_tool(user_agent):
            detection = DetectionResult(
                detection_id=self._generate_detection_id(),
                timestamp=datetime.utcnow(),
                attack_type=AttackType.RECONNAISSANCE,
                detection_method=DetectionMethod.BEHAVIORAL_BASED,
                confidence=0.8,
                severity=ThreatLevel.MEDIUM,
                source_ip=source_ip,
                target_endpoint=request_data.get('endpoint', '/'),
                user_id=user_id,
                session_id=request_data.get('session_id'),
                payload=request_data.get('body', ''),
                matched_signature='automated_tool_detection',
                indicators=['automated_user_agent'],
                raw_request=request_data,
                response_action=ResponseAction.RATE_LIMIT
            )
            detections.append(detection)
        
        # Check for rapid-fire requests (potential DoS)
        current_time = time.time()
        request_times = self._request_patterns[source_ip]
        request_times.append(current_time)
        
        # Count requests in last minute
        recent_requests = [t for t in request_times if current_time - t < 60]
        if len(recent_requests) > 100:  # More than 100 requests per minute
            detection = DetectionResult(
                detection_id=self._generate_detection_id(),
                timestamp=datetime.utcnow(),
                attack_type=AttackType.DENIAL_OF_SERVICE,
                detection_method=DetectionMethod.BEHAVIORAL_BASED,
                confidence=0.9,
                severity=ThreatLevel.HIGH,
                source_ip=source_ip,
                target_endpoint=request_data.get('endpoint', '/'),
                user_id=user_id,
                session_id=request_data.get('session_id'),
                payload='',
                matched_signature='rapid_requests',
                indicators=[f'rapid_requests:{len(recent_requests)}'],
                raw_request=request_data,
                response_action=ResponseAction.BLOCK_IP
            )
            detections.append(detection)
        
        return detections
    
    def _process_detection(self, detection: DetectionResult):
        """Process detection result"""
        try:
            with self._lock:
                # Store detection
                self._detection_results.append(detection)
                
                # Execute response action if not in learning mode
                if not self.config['learning_mode'] and self.config['auto_response_enabled']:
                    if detection.confidence >= self.config['confidence_threshold']:
                        self._execute_response_action(detection)
                
                # Send to security monitoring
                self.security_monitor.process_security_event({
                    'event_type': detection.attack_type.value,
                    'source_ip': detection.source_ip,
                    'user_id': detection.user_id,
                    'session_id': detection.session_id,
                    'endpoint': detection.target_endpoint,
                    'description': f'{detection.attack_type.value} detected via {detection.detection_method.value}',
                    'confidence': detection.confidence,
                    'severity': detection.severity.value,
                    'matched_signature': detection.matched_signature,
                    'indicators': detection.indicators,
                    'payload': detection.payload
                })
                
                # Track in monitoring
                self.monitoring.track_custom_metric(
                    'intrusion_detected',
                    1.0,
                    {
                        'attack_type': detection.attack_type.value,
                        'detection_method': detection.detection_method.value,
                        'severity': detection.severity.value,
                        'confidence': detection.confidence,
                        'blocked': detection.blocked
                    }
                )
                
                logger.warning(f"Intrusion detected: {detection.attack_type.value} from {detection.source_ip}")
                
        except Exception as e:
            logger.error(f"Failed to process detection: {str(e)}")
    
    def _execute_response_action(self, detection: DetectionResult):
        """Execute automated response action"""
        try:
            action = detection.response_action
            
            if action == ResponseAction.BLOCK_IP:
                self._block_entity('ip', detection.source_ip, 'intrusion_detected', 3600)  # 1 hour
                detection.blocked = True
                
            elif action == ResponseAction.BLOCK_USER and detection.user_id:
                self._block_entity('user', detection.user_id, 'intrusion_detected', 1800)  # 30 minutes
                detection.blocked = True
                
            elif action == ResponseAction.RATE_LIMIT:
                self._apply_rate_limit(detection.source_ip, detection.user_id)
                
            elif action == ResponseAction.TERMINATE_SESSION and detection.session_id:
                # Would integrate with session management to terminate session
                logger.info(f"Session termination requested: {detection.session_id}")
                
            elif action == ResponseAction.QUARANTINE:
                # Would implement quarantine logic
                logger.info(f"Quarantine requested for: {detection.source_ip}")
                
            # Always log the action
            logger.info(f"Response action executed: {action.value} for detection {detection.detection_id}")
            
        except Exception as e:
            logger.error(f"Failed to execute response action: {str(e)}")
    
    def _block_entity(self, entity_type: str, entity_id: str, reason: str, duration_seconds: int):
        """Block entity for specified duration"""
        key = f"{entity_type}:{entity_id}"
        
        self._blocked_entities[key] = {
            'entity_type': entity_type,
            'entity_id': entity_id,
            'reason': reason,
            'blocked_at': datetime.utcnow(),
            'expires_at': datetime.utcnow() + timedelta(seconds=duration_seconds),
            'duration_seconds': duration_seconds
        }
        
        logger.warning(f"Entity blocked: {key} for {duration_seconds} seconds (reason: {reason})")
    
    def _apply_rate_limit(self, source_ip: str, user_id: str = None):
        """Apply rate limiting"""
        # Would integrate with rate limiting system
        logger.info(f"Rate limit applied to IP: {source_ip}, User: {user_id}")
    
    def _calculate_anomaly_score(self, request_data: Dict[str, Any], 
                               baseline: BehavioralBaseline) -> float:
        """Calculate anomaly score against baseline"""
        score = 0.0
        
        try:
            # Check endpoint anomaly
            endpoint = request_data.get('endpoint', '/')
            if endpoint not in baseline.typical_endpoints:
                score += 2.0
            
            # Check method anomaly
            method = request_data.get('method', 'GET')
            if method not in baseline.typical_methods:
                score += 1.0
            
            # Check user agent anomaly
            user_agent = request_data.get('headers', {}).get('User-Agent', '')
            if user_agent not in baseline.typical_user_agents:
                score += 1.5
            
            # Check request size anomaly
            request_size = len(request_data.get('body', ''))
            if baseline.typical_request_sizes[1] > 0:  # Has stddev
                z_score = abs(request_size - baseline.typical_request_sizes[0]) / baseline.typical_request_sizes[1]
                if z_score > 3:  # More than 3 standard deviations
                    score += z_score
            
        except Exception as e:
            logger.error(f"Error calculating anomaly score: {str(e)}")
            score = 1.0  # Default moderate anomaly
        
        return score
    
    def _determine_response_action(self, severity: ThreatLevel, attack_type: AttackType) -> ResponseAction:
        """Determine appropriate response action"""
        if severity == ThreatLevel.CRITICAL:
            if attack_type in [AttackType.SQL_INJECTION, AttackType.COMMAND_INJECTION]:
                return ResponseAction.BLOCK_IP
            else:
                return ResponseAction.QUARANTINE
        elif severity == ThreatLevel.HIGH:
            return ResponseAction.BLOCK_IP
        elif severity == ThreatLevel.MEDIUM:
            return ResponseAction.RATE_LIMIT
        else:
            return ResponseAction.ALERT_ONLY
    
    def _is_automated_tool(self, user_agent: str) -> bool:
        """Check if user agent indicates automated tool"""
        automated_patterns = [
            r'bot', r'crawler', r'spider', r'scraper', r'scanner',
            r'curl', r'wget', r'python', r'java', r'go-http',
            r'automated', r'script', r'tool', r'test'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(re.search(pattern, user_agent_lower) for pattern in automated_patterns)
    
    def _check_sql_error_patterns(self, response_body: str) -> List[str]:
        """Check for SQL error patterns in response"""
        sql_error_patterns = [
            r'SQL syntax.*MySQL',
            r'Warning.*mysql_.*',
            r'valid MySQL result',
            r'PostgreSQL.*ERROR',
            r'Warning.*pg_.*',
            r'valid PostgreSQL result',
            r'Microsoft.*ODBC.*SQL Server',
            r'SQLServer JDBC Driver',
            r'Oracle error',
            r'Oracle.*Driver',
            r'Microsoft Access Driver',
            r'JET Database Engine',
            r'SQLite.*error'
        ]
        
        found_patterns = []
        for pattern in sql_error_patterns:
            if re.search(pattern, response_body, re.IGNORECASE):
                found_patterns.append(pattern)
        
        return found_patterns
    
    def _check_information_disclosure(self, response_body: str, response_headers: Dict[str, str]) -> bool:
        """Check for information disclosure"""
        # Check for sensitive information in response
        sensitive_patterns = [
            r'password\s*[:=]\s*["\']?[^"\'\s]+',
            r'secret\s*[:=]\s*["\']?[^"\'\s]+',
            r'api[_-]?key\s*[:=]\s*["\']?[^"\'\s]+',
            r'token\s*[:=]\s*["\']?[^"\'\s]+',
            r'private[_-]?key',
            r'connection[_-]?string',
            r'database[_-]?url'
        ]
        
        for pattern in sensitive_patterns:
            if re.search(pattern, response_body, re.IGNORECASE):
                return True
        
        # Check headers for sensitive information
        sensitive_headers = ['x-powered-by', 'server', 'x-aspnet-version']
        for header in sensitive_headers:
            if header.lower() in [h.lower() for h in response_headers.keys()]:
                return True
        
        return False
    
    def _generate_detection_id(self) -> str:
        """Generate unique detection ID"""
        return f"det_{int(time.time() * 1000000)}"
    
    def _initialize_attack_signatures(self):
        """Initialize default attack signatures"""
        signatures = [
            # SQL Injection signatures
            AttackSignature(
                signature_id='sql_injection_union',
                name='SQL Injection - UNION Attack',
                attack_type=AttackType.SQL_INJECTION,
                pattern=r'(?i)(union\s+(all\s+)?select|select\s+.*\s+union)',
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.HIGH,
                description='Detects SQL injection attempts using UNION statements',
                references=['OWASP-A03-2021']
            ),
            AttackSignature(
                signature_id='sql_injection_boolean',
                name='SQL Injection - Boolean-based',
                attack_type=AttackType.SQL_INJECTION,
                pattern=r"(?i)((\s|^)(and|or)\s+\d+\s*[=<>]+\s*\d+|(\s|^)(and|or)\s+['\"]?\w+['\"]?\s*[=<>]+\s*['\"]?\w+['\"]?)",
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.HIGH,
                description='Detects boolean-based SQL injection attempts',
                references=['OWASP-A03-2021']
            ),
            
            # XSS signatures
            AttackSignature(
                signature_id='xss_script_tag',
                name='XSS - Script Tag Injection',
                attack_type=AttackType.XSS,
                pattern=r'(?i)<script[^>]*>.*?</script>|<script[^>]*>',
                regex_flags=re.IGNORECASE | re.DOTALL,
                severity=ThreatLevel.HIGH,
                description='Detects XSS attempts using script tags',
                references=['OWASP-A07-2021']
            ),
            AttackSignature(
                signature_id='xss_javascript_protocol',
                name='XSS - JavaScript Protocol',
                attack_type=AttackType.XSS,
                pattern=r'(?i)javascript\s*:',
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.MEDIUM,
                description='Detects XSS attempts using javascript: protocol',
                references=['OWASP-A07-2021']
            ),
            
            # Command Injection signatures
            AttackSignature(
                signature_id='command_injection_basic',
                name='Command Injection - Basic',
                attack_type=AttackType.COMMAND_INJECTION,
                pattern=r'(?i)(;|\||&|`|\$\(|\${).*?(ls|cat|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh)',
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.CRITICAL,
                description='Detects basic command injection attempts',
                references=['OWASP-A03-2021']
            ),
            
            # Path Traversal signatures
            AttackSignature(
                signature_id='path_traversal_basic',
                name='Path Traversal - Directory Traversal',
                attack_type=AttackType.PATH_TRAVERSAL,
                pattern=r'(?i)(\.\.[\\/]){2,}|[\\/]\.\.[\\/]|\.\.%2f|%2e%2e%2f|\.\.%5c|%2e%2e%5c',
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.HIGH,
                description='Detects directory traversal attempts',
                references=['OWASP-A01-2021']
            ),
            
            # LDAP Injection signatures
            AttackSignature(
                signature_id='ldap_injection_basic',
                name='LDAP Injection - Basic',
                attack_type=AttackType.LDAP_INJECTION,
                pattern=r'(?i)(\*\)|(\|\()|(\)\()|(\(\|)|(\*\(\|)|(\|\(\*))',
                regex_flags=re.IGNORECASE,
                severity=ThreatLevel.HIGH,
                description='Detects LDAP injection attempts',
                references=['OWASP-A03-2021']
            )
        ]
        
        for signature in signatures:
            self.add_attack_signature(signature)
    
    def _start_baseline_learning(self):
        """Start baseline learning process"""
        def learning_loop():
            while True:
                try:
                    # Update baselines periodically
                    self._update_baselines()
                    time.sleep(3600)  # Update every hour
                except Exception as e:
                    logger.error(f"Baseline learning error: {str(e)}")
                    time.sleep(3600)
        
        import threading
        learning_thread = threading.Thread(target=learning_loop, daemon=True)
        learning_thread.start()
    
    def _update_baselines(self):
        """Update behavioral baselines"""
        try:
            # Get activity data from activity monitor
            # This would integrate with the actual activity monitoring system
            logger.debug("Updating behavioral baselines")
            
        except Exception as e:
            logger.error(f"Failed to update baselines: {str(e)}")


# Global service instance
_intrusion_detection_system = None


def get_intrusion_detection_system() -> IntrusionDetectionSystem:
    """Get the global intrusion detection system instance"""
    global _intrusion_detection_system
    if _intrusion_detection_system is None:
        _intrusion_detection_system = IntrusionDetectionSystem()
    return _intrusion_detection_system