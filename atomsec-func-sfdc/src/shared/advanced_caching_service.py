"""
Advanced Caching Service

This module provides advanced caching strategies including distributed caching with Redis,
cache warming, preloading, and comprehensive cache performance analytics.

Features:
- Distributed Redis caching with fallback to memory
- Intelligent cache warming and preloading
- Cache performance analytics and optimization
- Multi-tier caching strategy
- Cache invalidation patterns
- Cache health monitoring
"""

import logging
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union, Set
from functools import wraps
from collections import defaultdict
import threading
import hashlib
import pickle
from dataclasses import dataclass, asdict
from enum import Enum

# Configure module-level logger
logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """Cache level enumeration"""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_PERSISTENT = "l3_persistent"

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: int
    size_bytes: int
    tags: Set[str]
    priority: int = 1

@dataclass
class CacheStats:
    """Cache statistics"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    errors: int = 0
    warming_operations: int = 0
    preload_operations: int = 0

class CacheWarmingStrategy(Enum):
    """Cache warming strategies"""
    EAGER = "eager"  # Warm cache immediately
    LAZY = "lazy"    # Warm cache on first access
    SCHEDULED = "scheduled"  # Warm cache on schedule
    PREDICTIVE = "predictive"  # Warm cache based on patterns

class AdvancedCacheManager:
    """Advanced multi-tier cache manager with Redis support"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize advanced cache manager"""
        self.config = config
        self.stats = CacheStats()
        self.lock = threading.RLock()
        
        # Initialize cache tiers
        self.l1_cache = {}  # Memory cache
        self.l1_metadata = {}  # Memory cache metadata
        self.l2_client = None  # Redis client
        self.l3_storage = {}  # Persistent storage
        
        # Cache configuration
        self.l1_max_size = config.get('l1_max_size', 1000)
        self.l1_ttl = config.get('l1_ttl', 300)  # 5 minutes
        self.l2_ttl = config.get('l2_ttl', 3600)  # 1 hour
        self.l3_ttl = config.get('l3_ttl', 86400)  # 24 hours
        
        # Warming configuration
        self.warming_enabled = config.get('warming_enabled', True)
        self.warming_strategy = CacheWarmingStrategy(config.get('warming_strategy', 'lazy'))
        self.warming_keys = set()
        self.warming_patterns = []
        
        # Performance tracking
        self.access_patterns = defaultdict(list)
        self.hot_keys = defaultdict(int)
        self.performance_metrics = defaultdict(list)
        
        # Initialize Redis if configured
        self._initialize_redis()
        
        # Start background tasks
        self._start_background_tasks()
    
    def _initialize_redis(self):
        """Initialize Redis client for L2 cache"""
        redis_config = self.config.get('redis', {})
        if not redis_config.get('enabled', False):
            logger.info("Redis caching disabled")
            return
        
        try:
            import redis
            self.l2_client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                decode_responses=False,  # Handle binary data
                socket_timeout=redis_config.get('timeout', 5),
                socket_connect_timeout=redis_config.get('connect_timeout', 5),
                retry_on_timeout=True,
                health_check_interval=30,
                max_connections=redis_config.get('max_connections', 20)
            )
            
            # Test connection
            self.l2_client.ping()
            logger.info("Redis L2 cache initialized successfully")
            
        except ImportError:
            logger.warning("Redis not available, L2 cache disabled")
        except Exception as e:
            logger.error(f"Failed to initialize Redis L2 cache: {e}")
            self.l2_client = None
    
    def _start_background_tasks(self):
        """Start background maintenance tasks"""
        if self.warming_enabled:
            # Start cache warming thread
            warming_thread = threading.Thread(target=self._cache_warming_worker, daemon=True)
            warming_thread.start()
            
            # Start performance monitoring thread
            monitor_thread = threading.Thread(target=self._performance_monitor_worker, daemon=True)
            monitor_thread.start()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value from multi-tier cache"""
        start_time = time.time()
        
        try:
            # Try L1 cache first
            value = self._get_from_l1(key)
            if value is not None:
                self._record_access(key, CacheLevel.L1_MEMORY, time.time() - start_time)
                self.stats.hits += 1
                return value
            
            # Try L2 cache (Redis)
            value = self._get_from_l2(key)
            if value is not None:
                # Promote to L1 cache
                self._set_in_l1(key, value, self.l1_ttl)
                self._record_access(key, CacheLevel.L2_REDIS, time.time() - start_time)
                self.stats.hits += 1
                return value
            
            # Try L3 cache (persistent)
            value = self._get_from_l3(key)
            if value is not None:
                # Promote to L1 and L2 caches
                self._set_in_l1(key, value, self.l1_ttl)
                self._set_in_l2(key, value, self.l2_ttl)
                self._record_access(key, CacheLevel.L3_PERSISTENT, time.time() - start_time)
                self.stats.hits += 1
                return value
            
            # Cache miss
            self.stats.misses += 1
            self._trigger_warming_if_needed(key)
            return default
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.stats.errors += 1
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
            tags: Optional[Set[str]] = None, priority: int = 1):
        """Set value in multi-tier cache"""
        try:
            tags = tags or set()
            
            # Set in L1 cache
            self._set_in_l1(key, value, ttl or self.l1_ttl, tags, priority)
            
            # Set in L2 cache if available
            if self.l2_client:
                self._set_in_l2(key, value, ttl or self.l2_ttl, tags)
            
            # Set in L3 cache for important data
            if priority >= 5:
                self._set_in_l3(key, value, ttl or self.l3_ttl, tags)
            
            self.stats.sets += 1
            self._update_hot_keys(key)
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.stats.errors += 1
    
    def _get_from_l1(self, key: str) -> Any:
        """Get value from L1 memory cache"""
        with self.lock:
            if key not in self.l1_cache:
                return None
            
            metadata = self.l1_metadata.get(key)
            if not metadata:
                return None
            
            # Check TTL
            if time.time() - metadata.created_at > metadata.ttl:
                self._evict_from_l1(key)
                return None
            
            # Update access metadata
            metadata.last_accessed = time.time()
            metadata.access_count += 1
            
            return self.l1_cache[key]
    
    def _set_in_l1(self, key: str, value: Any, ttl: int, 
                   tags: Optional[Set[str]] = None, priority: int = 1):
        """Set value in L1 memory cache"""
        with self.lock:
            # Check if eviction is needed
            if len(self.l1_cache) >= self.l1_max_size:
                self._evict_lru_from_l1()
            
            # Calculate size
            size_bytes = len(pickle.dumps(value))
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                ttl=ttl,
                size_bytes=size_bytes,
                tags=tags or set(),
                priority=priority
            )
            
            self.l1_cache[key] = value
            self.l1_metadata[key] = entry
    
    def _get_from_l2(self, key: str) -> Any:
        """Get value from L2 Redis cache"""
        if not self.l2_client:
            return None
        
        try:
            data = self.l2_client.get(key)
            if data:
                return pickle.loads(data)
            return None
        except Exception as e:
            logger.error(f"L2 cache get error: {e}")
            return None
    
    def _set_in_l2(self, key: str, value: Any, ttl: int, tags: Optional[Set[str]] = None):
        """Set value in L2 Redis cache"""
        if not self.l2_client:
            return
        
        try:
            data = pickle.dumps(value)
            self.l2_client.setex(key, ttl, data)
            
            # Store tags for invalidation
            if tags:
                for tag in tags:
                    self.l2_client.sadd(f"tag:{tag}", key)
                    self.l2_client.expire(f"tag:{tag}", ttl)
                    
        except Exception as e:
            logger.error(f"L2 cache set error: {e}")
    
    def _get_from_l3(self, key: str) -> Any:
        """Get value from L3 persistent cache"""
        # Placeholder for persistent storage implementation
        return self.l3_storage.get(key)
    
    def _set_in_l3(self, key: str, value: Any, ttl: int, tags: Optional[Set[str]] = None):
        """Set value in L3 persistent cache"""
        # Placeholder for persistent storage implementation
        self.l3_storage[key] = value
    
    def _evict_lru_from_l1(self):
        """Evict least recently used item from L1 cache"""
        if not self.l1_metadata:
            return
        
        # Find LRU item
        lru_key = min(self.l1_metadata.keys(), 
                     key=lambda k: self.l1_metadata[k].last_accessed)
        
        self._evict_from_l1(lru_key)
        self.stats.evictions += 1
    
    def _evict_from_l1(self, key: str):
        """Evict specific key from L1 cache"""
        with self.lock:
            self.l1_cache.pop(key, None)
            self.l1_metadata.pop(key, None)
    
    def delete(self, key: str) -> bool:
        """Delete key from all cache tiers"""
        try:
            deleted = False
            
            # Delete from L1
            with self.lock:
                if key in self.l1_cache:
                    del self.l1_cache[key]
                    del self.l1_metadata[key]
                    deleted = True
            
            # Delete from L2
            if self.l2_client:
                if self.l2_client.delete(key):
                    deleted = True
            
            # Delete from L3
            if key in self.l3_storage:
                del self.l3_storage[key]
                deleted = True
            
            if deleted:
                self.stats.deletes += 1
            
            return deleted
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            self.stats.errors += 1
            return False
    
    def invalidate_by_tags(self, tags: Set[str]):
        """Invalidate cache entries by tags"""
        try:
            keys_to_delete = set()
            
            # Find keys with matching tags in L1
            with self.lock:
                for key, metadata in self.l1_metadata.items():
                    if metadata.tags.intersection(tags):
                        keys_to_delete.add(key)
            
            # Find keys with matching tags in L2
            if self.l2_client:
                for tag in tags:
                    tag_keys = self.l2_client.smembers(f"tag:{tag}")
                    keys_to_delete.update(tag_keys)
            
            # Delete all matching keys
            for key in keys_to_delete:
                self.delete(key)
            
            logger.info(f"Invalidated {len(keys_to_delete)} cache entries by tags: {tags}")
            
        except Exception as e:
            logger.error(f"Cache invalidation error: {e}")
    
    def warm_cache(self, warming_data: Dict[str, Any], strategy: Optional[CacheWarmingStrategy] = None):
        """Warm cache with data"""
        if not self.warming_enabled:
            return
        
        strategy = strategy or self.warming_strategy
        
        try:
            for key, value in warming_data.items():
                if strategy == CacheWarmingStrategy.EAGER:
                    self.set(key, value, priority=3)
                elif strategy == CacheWarmingStrategy.LAZY:
                    self.warming_keys.add(key)
                elif strategy == CacheWarmingStrategy.SCHEDULED:
                    self._schedule_warming(key, value)
                elif strategy == CacheWarmingStrategy.PREDICTIVE:
                    self._predictive_warming(key, value)
            
            self.stats.warming_operations += len(warming_data)
            logger.info(f"Cache warmed with {len(warming_data)} items using {strategy.value} strategy")
            
        except Exception as e:
            logger.error(f"Cache warming error: {e}")
    
    def preload_cache(self, preload_func: Callable[[], Dict[str, Any]], 
                     keys: Optional[List[str]] = None):
        """Preload cache with data from function"""
        try:
            if keys:
                # Preload specific keys
                for key in keys:
                    if key not in self.l1_cache:
                        data = preload_func()
                        if key in data:
                            self.set(key, data[key], priority=4)
            else:
                # Preload all data
                data = preload_func()
                for key, value in data.items():
                    self.set(key, value, priority=4)
            
            self.stats.preload_operations += 1
            logger.info(f"Cache preloaded with data")
            
        except Exception as e:
            logger.error(f"Cache preload error: {e}")
    
    def _record_access(self, key: str, level: CacheLevel, response_time: float):
        """Record cache access for analytics"""
        self.access_patterns[key].append({
            'timestamp': time.time(),
            'level': level.value,
            'response_time': response_time
        })
        
        # Keep only recent access patterns
        cutoff = time.time() - 3600  # 1 hour
        self.access_patterns[key] = [
            access for access in self.access_patterns[key]
            if access['timestamp'] > cutoff
        ]
    
    def _update_hot_keys(self, key: str):
        """Update hot keys tracking"""
        self.hot_keys[key] += 1
        
        # Add to warming keys if frequently accessed
        if self.hot_keys[key] > 10:
            self.warming_keys.add(key)
    
    def _trigger_warming_if_needed(self, key: str):
        """Trigger cache warming for missed keys"""
        if self.warming_enabled and key in self.warming_keys:
            # Implement warming logic based on patterns
            pass
    
    def _cache_warming_worker(self):
        """Background cache warming worker"""
        while True:
            try:
                if self.warming_keys:
                    # Implement warming logic
                    logger.debug(f"Cache warming worker processing {len(self.warming_keys)} keys")
                
                time.sleep(60)  # Run every minute
                
            except Exception as e:
                logger.error(f"Cache warming worker error: {e}")
                time.sleep(60)
    
    def _performance_monitor_worker(self):
        """Background performance monitoring worker"""
        while True:
            try:
                # Collect performance metrics
                self._collect_performance_metrics()
                time.sleep(300)  # Run every 5 minutes
                
            except Exception as e:
                logger.error(f"Performance monitor worker error: {e}")
                time.sleep(300)
    
    def _collect_performance_metrics(self):
        """Collect cache performance metrics"""
        try:
            # Calculate hit rates
            total_requests = self.stats.hits + self.stats.misses
            hit_rate = self.stats.hits / total_requests if total_requests > 0 else 0
            
            # Record metrics
            self.performance_metrics['hit_rate'].append({
                'timestamp': time.time(),
                'value': hit_rate
            })
            
            # L1 cache metrics
            with self.lock:
                l1_size = len(self.l1_cache)
                l1_memory_usage = sum(
                    metadata.size_bytes for metadata in self.l1_metadata.values()
                )
            
            self.performance_metrics['l1_size'].append({
                'timestamp': time.time(),
                'value': l1_size
            })
            
            self.performance_metrics['l1_memory_usage'].append({
                'timestamp': time.time(),
                'value': l1_memory_usage
            })
            
            # L2 cache metrics (Redis)
            if self.l2_client:
                try:
                    info = self.l2_client.info()
                    self.performance_metrics['l2_memory_usage'].append({
                        'timestamp': time.time(),
                        'value': info.get('used_memory', 0)
                    })
                except Exception as e:
                    logger.warning(f"Could not collect L2 metrics: {e}")
            
        except Exception as e:
            logger.error(f"Performance metrics collection error: {e}")
    
    def get_performance_analytics(self, time_window: int = 3600) -> Dict[str, Any]:
        """Get comprehensive cache performance analytics"""
        cutoff = time.time() - time_window
        
        analytics = {
            'stats': asdict(self.stats),
            'hot_keys': dict(sorted(self.hot_keys.items(), key=lambda x: x[1], reverse=True)[:20]),
            'warming_keys_count': len(self.warming_keys),
            'cache_levels': {
                'l1_size': len(self.l1_cache),
                'l2_available': self.l2_client is not None,
                'l3_size': len(self.l3_storage)
            }
        }
        
        # Calculate hit rate
        total_requests = self.stats.hits + self.stats.misses
        analytics['hit_rate'] = self.stats.hits / total_requests if total_requests > 0 else 0
        
        # Access pattern analytics
        access_analytics = {}
        for key, accesses in self.access_patterns.items():
            recent_accesses = [a for a in accesses if a['timestamp'] > cutoff]
            if recent_accesses:
                access_analytics[key] = {
                    'access_count': len(recent_accesses),
                    'avg_response_time': sum(a['response_time'] for a in recent_accesses) / len(recent_accesses),
                    'cache_levels_used': list(set(a['level'] for a in recent_accesses))
                }
        
        analytics['access_patterns'] = access_analytics
        
        # Performance metrics
        performance_data = {}
        for metric_name, metrics in self.performance_metrics.items():
            recent_metrics = [m for m in metrics if m['timestamp'] > cutoff]
            if recent_metrics:
                values = [m['value'] for m in recent_metrics]
                performance_data[metric_name] = {
                    'current': values[-1] if values else 0,
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values)
                }
        
        analytics['performance_metrics'] = performance_data
        
        return analytics
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get cache optimization recommendations"""
        recommendations = []
        
        try:
            # Analyze hit rate
            total_requests = self.stats.hits + self.stats.misses
            hit_rate = self.stats.hits / total_requests if total_requests > 0 else 0
            
            if hit_rate < 0.7:
                recommendations.append("Low cache hit rate - consider increasing cache size or TTL")
            
            # Analyze L1 cache usage
            with self.lock:
                l1_usage = len(self.l1_cache) / self.l1_max_size
            
            if l1_usage > 0.9:
                recommendations.append("L1 cache nearly full - consider increasing size")
            
            # Analyze eviction rate
            if self.stats.evictions > self.stats.sets * 0.1:
                recommendations.append("High eviction rate - consider increasing cache size")
            
            # Analyze warming effectiveness
            if self.warming_enabled and self.stats.warming_operations > 0:
                warming_hit_rate = len(self.warming_keys) / self.stats.warming_operations
                if warming_hit_rate < 0.5:
                    recommendations.append("Cache warming not effective - review warming strategy")
            
            # Analyze hot keys
            if len(self.hot_keys) > 0:
                top_key_access = max(self.hot_keys.values())
                if top_key_access > 100:
                    recommendations.append("Consider dedicated caching for frequently accessed keys")
            
        except Exception as e:
            logger.error(f"Error generating optimization recommendations: {e}")
        
        return recommendations
    
    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive cache health check"""
        health = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'cache_levels': {}
        }
        
        try:
            # L1 cache health
            with self.lock:
                l1_health = {
                    'status': 'healthy',
                    'size': len(self.l1_cache),
                    'max_size': self.l1_max_size,
                    'usage_percent': (len(self.l1_cache) / self.l1_max_size) * 100
                }
            health['cache_levels']['l1'] = l1_health
            
            # L2 cache health (Redis)
            if self.l2_client:
                try:
                    self.l2_client.ping()
                    info = self.l2_client.info()
                    l2_health = {
                        'status': 'healthy',
                        'connected': True,
                        'memory_used': info.get('used_memory_human', 'N/A'),
                        'connected_clients': info.get('connected_clients', 0)
                    }
                except Exception as e:
                    l2_health = {
                        'status': 'unhealthy',
                        'connected': False,
                        'error': str(e)
                    }
                    health['status'] = 'degraded'
            else:
                l2_health = {
                    'status': 'disabled',
                    'connected': False
                }
            
            health['cache_levels']['l2'] = l2_health
            
            # L3 cache health
            l3_health = {
                'status': 'healthy',
                'size': len(self.l3_storage)
            }
            health['cache_levels']['l3'] = l3_health
            
            # Overall statistics
            health['statistics'] = asdict(self.stats)
            
        except Exception as e:
            health['status'] = 'unhealthy'
            health['error'] = str(e)
        
        return health

def cache_decorator(cache_manager: AdvancedCacheManager, ttl: int = 3600, 
                   tags: Optional[Set[str]] = None, priority: int = 1):
    """Decorator for caching function results"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_data = {
                'func': func.__name__,
                'args': args,
                'kwargs': sorted(kwargs.items())
            }
            cache_key = hashlib.md5(str(key_data).encode()).hexdigest()
            
            # Try to get from cache
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl=ttl, tags=tags, priority=priority)
            
            return result
        
        return wrapper
    return decorator

# Global cache manager instance
_cache_manager = None

def get_cache_manager(config: Optional[Dict[str, Any]] = None) -> AdvancedCacheManager:
    """Get global cache manager instance"""
    global _cache_manager
    
    if _cache_manager is None:
        default_config = {
            'l1_max_size': 1000,
            'l1_ttl': 300,
            'l2_ttl': 3600,
            'l3_ttl': 86400,
            'warming_enabled': True,
            'warming_strategy': 'lazy',
            'redis': {
                'enabled': False,
                'host': 'localhost',
                'port': 6379,
                'db': 0
            }
        }
        
        if config:
            default_config.update(config)
        
        _cache_manager = AdvancedCacheManager(default_config)
    
    return _cache_manager