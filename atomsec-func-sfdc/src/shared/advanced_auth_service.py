"""
Advanced Authentication Service

This module provides advanced authentication features including:
- Multi-factor authentication (MFA) support
- Session management and token refresh
- User activity monitoring and suspicious behavior detection
- Advanced security features for compliance

Requirements addressed: 1.1
"""

import logging
import time
import hashlib
import secrets
import json
from typing import Dict, Any, Optional, List, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import re
import ipaddress

# Import existing modules
from src.shared.enhanced_auth_service import get_enhanced_auth_service, AuthenticationError
from src.shared.azure_services import get_secret
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


class MFAMethod(Enum):
    """Multi-factor authentication methods"""
    TOTP = "totp"  # Time-based One-Time Password
    SMS = "sms"    # SMS verification
    EMAIL = "email"  # Email verification
    BACKUP_CODES = "backup_codes"  # Backup recovery codes


class SessionStatus(Enum):
    """Session status enumeration"""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    SUSPENDED = "suspended"


class SecurityThreatLevel(Enum):
    """Security threat levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class MFAChallenge:
    """MFA challenge data structure"""
    challenge_id: str
    user_id: str
    method: MFAMethod
    code: str
    expires_at: datetime
    attempts: int = 0
    max_attempts: int = 3
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()


@dataclass
class UserSession:
    """User session data structure"""
    session_id: str
    user_id: str
    access_token: str
    refresh_token: str
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    status: SessionStatus = SessionStatus.ACTIVE
    mfa_verified: bool = False
    device_fingerprint: Optional[str] = None
    location: Optional[Dict[str, Any]] = None


@dataclass
class UserActivity:
    """User activity tracking"""
    user_id: str
    session_id: str
    activity_type: str
    timestamp: datetime
    ip_address: str
    user_agent: str
    endpoint: str
    success: bool
    details: Dict[str, Any] = None
    risk_score: float = 0.0


@dataclass
class SecurityAlert:
    """Security alert data structure"""
    alert_id: str
    user_id: str
    threat_level: SecurityThreatLevel
    alert_type: str
    description: str
    timestamp: datetime
    details: Dict[str, Any]
    resolved: bool = False
    actions_taken: List[str] = None
    
    def __post_init__(self):
        if self.actions_taken is None:
            self.actions_taken = []


class AdvancedAuthService:
    """
    Advanced authentication service with MFA, session management,
    and security monitoring capabilities
    """
    
    def __init__(self):
        self.enhanced_auth = get_enhanced_auth_service()
        self.monitoring = get_monitoring_service()
        
        # Storage for authentication data
        self._mfa_challenges: Dict[str, MFAChallenge] = {}
        self._user_sessions: Dict[str, UserSession] = {}
        self._user_activities: List[UserActivity] = []
        self._security_alerts: List[SecurityAlert] = []
        self._failed_attempts: Dict[str, List[datetime]] = defaultdict(list)
        self._blocked_ips: Set[str] = set()
        self._trusted_devices: Dict[str, Set[str]] = defaultdict(set)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Configuration
        self.config = {
            'session_timeout_minutes': 480,  # 8 hours
            'refresh_token_timeout_days': 30,
            'mfa_code_timeout_minutes': 5,
            'max_failed_attempts': 5,
            'lockout_duration_minutes': 30,
            'suspicious_activity_threshold': 10,
            'max_concurrent_sessions': 5,
            'require_mfa_for_sensitive_ops': True,
            'token_refresh_threshold_minutes': 60,  # Refresh if expires within 1 hour
        }
        
        # Risk scoring patterns
        self.risk_patterns = {
            'new_device': 2.0,
            'new_location': 1.5,
            'unusual_time': 1.0,
            'multiple_failed_attempts': 3.0,
            'suspicious_user_agent': 2.0,
            'tor_network': 5.0,
            'known_malicious_ip': 10.0,
        }
    
    def initiate_mfa_challenge(self, user_id: str, method: MFAMethod,
                              contact_info: str = None) -> Dict[str, Any]:
        """
        Initiate MFA challenge for user
        
        Args:
            user_id: User identifier
            method: MFA method to use
            contact_info: Contact information (phone/email) if needed
            
        Returns:
            Dict containing challenge details
            
        Raises:
            AuthenticationError: If MFA challenge cannot be initiated
        """
        try:
            with self._lock:
                # Generate challenge
                challenge_id = self._generate_secure_id()
                code = self._generate_mfa_code(method)
                
                challenge = MFAChallenge(
                    challenge_id=challenge_id,
                    user_id=user_id,
                    method=method,
                    code=code,
                    expires_at=datetime.utcnow() + timedelta(
                        minutes=self.config['mfa_code_timeout_minutes']
                    )
                )
                
                self._mfa_challenges[challenge_id] = challenge
                
                # Send challenge based on method
                delivery_result = self._deliver_mfa_challenge(challenge, contact_info)
                
                if not delivery_result['success']:
                    raise AuthenticationError(f"Failed to deliver MFA challenge: {delivery_result['error']}")
                
                # Log security event
                self._log_security_event('mfa_challenge_initiated', {
                    'user_id': user_id,
                    'method': method.value,
                    'challenge_id': challenge_id
                })
                
                return {
                    'challenge_id': challenge_id,
                    'method': method.value,
                    'expires_at': challenge.expires_at.isoformat(),
                    'delivery_method': delivery_result.get('delivery_method'),
                    'masked_contact': delivery_result.get('masked_contact')
                }
                
        except Exception as e:
            logger.error(f"Failed to initiate MFA challenge: {str(e)}")
            raise AuthenticationError(f"MFA challenge initiation failed: {str(e)}")
    
    def verify_mfa_challenge(self, challenge_id: str, code: str,
                           user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Verify MFA challenge response
        
        Args:
            challenge_id: Challenge identifier
            code: User-provided verification code
            user_context: Additional user context for security
            
        Returns:
            Dict containing verification result
            
        Raises:
            AuthenticationError: If verification fails
        """
        try:
            with self._lock:
                challenge = self._mfa_challenges.get(challenge_id)
                if not challenge:
                    raise AuthenticationError("Invalid or expired MFA challenge")
                
                # Check expiration
                if datetime.utcnow() > challenge.expires_at:
                    del self._mfa_challenges[challenge_id]
                    raise AuthenticationError("MFA challenge expired")
                
                # Check attempt limit
                if challenge.attempts >= challenge.max_attempts:
                    del self._mfa_challenges[challenge_id]
                    self._log_security_event('mfa_max_attempts_exceeded', {
                        'user_id': challenge.user_id,
                        'challenge_id': challenge_id,
                        'attempts': challenge.attempts
                    })
                    raise AuthenticationError("Maximum MFA attempts exceeded")
                
                # Increment attempt counter
                challenge.attempts += 1
                
                # Verify code
                if not self._verify_mfa_code(challenge, code):
                    if challenge.attempts >= challenge.max_attempts:
                        del self._mfa_challenges[challenge_id]
                    
                    self._log_security_event('mfa_verification_failed', {
                        'user_id': challenge.user_id,
                        'challenge_id': challenge_id,
                        'attempts': challenge.attempts
                    })
                    raise AuthenticationError("Invalid MFA code")
                
                # Successful verification
                del self._mfa_challenges[challenge_id]
                
                # Generate MFA token
                mfa_token = self._generate_mfa_token(challenge.user_id)
                
                self._log_security_event('mfa_verification_success', {
                    'user_id': challenge.user_id,
                    'method': challenge.method.value,
                    'challenge_id': challenge_id
                })
                
                return {
                    'success': True,
                    'mfa_token': mfa_token,
                    'expires_at': (datetime.utcnow() + timedelta(minutes=10)).isoformat(),
                    'user_id': challenge.user_id
                }
                
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"MFA verification error: {str(e)}")
            raise AuthenticationError(f"MFA verification failed: {str(e)}")
    
    def create_authenticated_session(self, user_id: str, mfa_token: str = None,
                                   request_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create authenticated session with optional MFA verification
        
        Args:
            user_id: User identifier
            mfa_token: MFA verification token (if required)
            request_context: Request context (IP, user agent, etc.)
            
        Returns:
            Dict containing session details and tokens
            
        Raises:
            AuthenticationError: If session creation fails
        """
        try:
            with self._lock:
                # Validate MFA token if provided
                mfa_verified = False
                if mfa_token:
                    mfa_verified = self._validate_mfa_token(mfa_token, user_id)
                
                # Check if MFA is required
                if self.config['require_mfa_for_sensitive_ops'] and not mfa_verified:
                    raise AuthenticationError("MFA verification required")
                
                # Extract request context
                ip_address = request_context.get('ip_address', 'unknown') if request_context else 'unknown'
                user_agent = request_context.get('user_agent', 'unknown') if request_context else 'unknown'
                
                # Check for suspicious activity
                risk_score = self._calculate_risk_score(user_id, ip_address, user_agent)
                if risk_score >= 5.0:  # High risk threshold
                    self._create_security_alert(
                        user_id, SecurityThreatLevel.HIGH,
                        'high_risk_login_attempt',
                        f'High risk login attempt detected (score: {risk_score})',
                        {'ip_address': ip_address, 'user_agent': user_agent, 'risk_score': risk_score}
                    )
                    raise AuthenticationError("Login blocked due to suspicious activity")
                
                # Check concurrent session limit
                active_sessions = [s for s in self._user_sessions.values() 
                                 if s.user_id == user_id and s.status == SessionStatus.ACTIVE]
                
                if len(active_sessions) >= self.config['max_concurrent_sessions']:
                    # Revoke oldest session
                    oldest_session = min(active_sessions, key=lambda s: s.last_activity)
                    oldest_session.status = SessionStatus.REVOKED
                    
                    self._log_security_event('session_revoked_concurrent_limit', {
                        'user_id': user_id,
                        'revoked_session_id': oldest_session.session_id,
                        'active_sessions_count': len(active_sessions)
                    })
                
                # Generate session tokens
                session_id = self._generate_secure_id()
                access_token = self._generate_access_token(user_id, session_id)
                refresh_token = self._generate_refresh_token(user_id, session_id)
                
                # Create session
                session = UserSession(
                    session_id=session_id,
                    user_id=user_id,
                    access_token=access_token,
                    refresh_token=refresh_token,
                    created_at=datetime.utcnow(),
                    last_activity=datetime.utcnow(),
                    expires_at=datetime.utcnow() + timedelta(
                        minutes=self.config['session_timeout_minutes']
                    ),
                    ip_address=ip_address,
                    user_agent=user_agent,
                    mfa_verified=mfa_verified,
                    device_fingerprint=request_context.get('device_fingerprint') if request_context else None
                )
                
                self._user_sessions[session_id] = session
                
                # Track activity
                self._track_user_activity(
                    user_id, session_id, 'session_created',
                    ip_address, user_agent, '/auth/login', True,
                    {'mfa_verified': mfa_verified, 'risk_score': risk_score}
                )
                
                self._log_security_event('session_created', {
                    'user_id': user_id,
                    'session_id': session_id,
                    'mfa_verified': mfa_verified,
                    'ip_address': ip_address,
                    'risk_score': risk_score
                })
                
                return {
                    'session_id': session_id,
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'expires_at': session.expires_at.isoformat(),
                    'mfa_verified': mfa_verified,
                    'requires_mfa': not mfa_verified and self.config['require_mfa_for_sensitive_ops']
                }
                
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Session creation error: {str(e)}")
            raise AuthenticationError(f"Session creation failed: {str(e)}")
    
    def refresh_session_token(self, refresh_token: str,
                            request_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Refresh session access token using refresh token
        
        Args:
            refresh_token: Refresh token
            request_context: Request context for security validation
            
        Returns:
            Dict containing new access token and session info
            
        Raises:
            AuthenticationError: If token refresh fails
        """
        try:
            with self._lock:
                # Find session by refresh token
                session = None
                for s in self._user_sessions.values():
                    if s.refresh_token == refresh_token and s.status == SessionStatus.ACTIVE:
                        session = s
                        break
                
                if not session:
                    raise AuthenticationError("Invalid or expired refresh token")
                
                # Check session expiration
                if datetime.utcnow() > session.expires_at:
                    session.status = SessionStatus.EXPIRED
                    raise AuthenticationError("Session expired")
                
                # Validate request context for security
                if request_context:
                    ip_address = request_context.get('ip_address')
                    if ip_address and ip_address != session.ip_address:
                        # IP address changed - potential security risk
                        risk_score = self._calculate_risk_score(
                            session.user_id, ip_address, 
                            request_context.get('user_agent', session.user_agent)
                        )
                        
                        if risk_score >= 3.0:  # Medium-high risk
                            self._create_security_alert(
                                session.user_id, SecurityThreatLevel.MEDIUM,
                                'ip_address_change_during_refresh',
                                f'IP address changed during token refresh (risk: {risk_score})',
                                {
                                    'old_ip': session.ip_address,
                                    'new_ip': ip_address,
                                    'session_id': session.session_id,
                                    'risk_score': risk_score
                                }
                            )
                            
                            # Require re-authentication for high risk
                            if risk_score >= 5.0:
                                session.status = SessionStatus.SUSPENDED
                                raise AuthenticationError("Session suspended due to suspicious activity")
                
                # Generate new access token
                new_access_token = self._generate_access_token(session.user_id, session.session_id)
                session.access_token = new_access_token
                session.last_activity = datetime.utcnow()
                
                # Optionally rotate refresh token for enhanced security
                if self._should_rotate_refresh_token(session):
                    session.refresh_token = self._generate_refresh_token(session.user_id, session.session_id)
                
                # Track activity
                self._track_user_activity(
                    session.user_id, session.session_id, 'token_refreshed',
                    request_context.get('ip_address', session.ip_address) if request_context else session.ip_address,
                    request_context.get('user_agent', session.user_agent) if request_context else session.user_agent,
                    '/auth/refresh', True
                )
                
                self._log_security_event('token_refreshed', {
                    'user_id': session.user_id,
                    'session_id': session.session_id,
                    'ip_address': session.ip_address
                })
                
                return {
                    'access_token': new_access_token,
                    'refresh_token': session.refresh_token,
                    'expires_at': session.expires_at.isoformat(),
                    'session_id': session.session_id
                }
                
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            raise AuthenticationError(f"Token refresh failed: {str(e)}")
    
    def validate_session(self, access_token: str,
                        request_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate session and return user information
        
        Args:
            access_token: Access token to validate
            request_context: Request context for activity tracking
            
        Returns:
            Dict containing user and session information
            
        Raises:
            AuthenticationError: If session validation fails
        """
        try:
            with self._lock:
                # Find session by access token
                session = None
                for s in self._user_sessions.values():
                    if s.access_token == access_token and s.status == SessionStatus.ACTIVE:
                        session = s
                        break
                
                if not session:
                    raise AuthenticationError("Invalid or expired access token")
                
                # Check session expiration
                if datetime.utcnow() > session.expires_at:
                    session.status = SessionStatus.EXPIRED
                    raise AuthenticationError("Session expired")
                
                # Update last activity
                session.last_activity = datetime.utcnow()
                
                # Track activity if context provided
                if request_context:
                    self._track_user_activity(
                        session.user_id, session.session_id, 'api_access',
                        request_context.get('ip_address', session.ip_address),
                        request_context.get('user_agent', session.user_agent),
                        request_context.get('endpoint', 'unknown'), True
                    )
                
                # Check if token needs refresh soon
                needs_refresh = (session.expires_at - datetime.utcnow()).total_seconds() < (
                    self.config['token_refresh_threshold_minutes'] * 60
                )
                
                return {
                    'user_id': session.user_id,
                    'session_id': session.session_id,
                    'mfa_verified': session.mfa_verified,
                    'expires_at': session.expires_at.isoformat(),
                    'needs_refresh': needs_refresh,
                    'last_activity': session.last_activity.isoformat()
                }
                
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Session validation error: {str(e)}")
            raise AuthenticationError(f"Session validation failed: {str(e)}")
    
    def revoke_session(self, session_id: str, reason: str = "user_logout") -> bool:
        """
        Revoke a user session
        
        Args:
            session_id: Session to revoke
            reason: Reason for revocation
            
        Returns:
            bool: True if session was revoked
        """
        try:
            with self._lock:
                session = self._user_sessions.get(session_id)
                if not session:
                    return False
                
                session.status = SessionStatus.REVOKED
                
                self._log_security_event('session_revoked', {
                    'user_id': session.user_id,
                    'session_id': session_id,
                    'reason': reason
                })
                
                return True
                
        except Exception as e:
            logger.error(f"Session revocation error: {str(e)}")
            return False
    
    def get_user_activity(self, user_id: str, limit: int = 100,
                         since: datetime = None) -> List[Dict[str, Any]]:
        """
        Get user activity history
        
        Args:
            user_id: User identifier
            limit: Maximum number of activities to return
            since: Filter activities since this timestamp
            
        Returns:
            List of user activities
        """
        try:
            with self._lock:
                activities = [a for a in self._user_activities if a.user_id == user_id]
                
                if since:
                    activities = [a for a in activities if a.timestamp >= since]
                
                # Sort by timestamp (newest first) and limit
                activities.sort(key=lambda a: a.timestamp, reverse=True)
                activities = activities[:limit]
                
                return [asdict(activity) for activity in activities]
                
        except Exception as e:
            logger.error(f"Error retrieving user activity: {str(e)}")
            return []
    
    def detect_suspicious_behavior(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Detect suspicious behavior patterns for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            List of suspicious behavior indicators
        """
        try:
            with self._lock:
                suspicious_indicators = []
                
                # Get recent activities
                recent_activities = [
                    a for a in self._user_activities 
                    if a.user_id == user_id and 
                    a.timestamp >= datetime.utcnow() - timedelta(hours=24)
                ]
                
                # Check for multiple failed attempts
                failed_attempts = [a for a in recent_activities if not a.success]
                if len(failed_attempts) >= self.config['max_failed_attempts']:
                    suspicious_indicators.append({
                        'type': 'multiple_failed_attempts',
                        'severity': 'high',
                        'count': len(failed_attempts),
                        'description': f'Multiple failed authentication attempts ({len(failed_attempts)})'
                    })
                
                # Check for unusual access patterns
                unique_ips = set(a.ip_address for a in recent_activities)
                if len(unique_ips) > 5:  # More than 5 different IPs in 24 hours
                    suspicious_indicators.append({
                        'type': 'multiple_ip_addresses',
                        'severity': 'medium',
                        'count': len(unique_ips),
                        'description': f'Access from multiple IP addresses ({len(unique_ips)})'
                    })
                
                # Check for unusual time patterns
                unusual_times = [
                    a for a in recent_activities 
                    if a.timestamp.hour < 6 or a.timestamp.hour > 22
                ]
                if len(unusual_times) > 10:  # More than 10 activities outside normal hours
                    suspicious_indicators.append({
                        'type': 'unusual_access_times',
                        'severity': 'low',
                        'count': len(unusual_times),
                        'description': f'Unusual access times detected ({len(unusual_times)} activities)'
                    })
                
                # Check for rapid successive requests
                if len(recent_activities) > 100:  # More than 100 activities in 24 hours
                    suspicious_indicators.append({
                        'type': 'high_activity_volume',
                        'severity': 'medium',
                        'count': len(recent_activities),
                        'description': f'Unusually high activity volume ({len(recent_activities)} activities)'
                    })
                
                return suspicious_indicators
                
        except Exception as e:
            logger.error(f"Error detecting suspicious behavior: {str(e)}")
            return []
    
    def get_security_alerts(self, user_id: str = None, 
                          threat_level: SecurityThreatLevel = None,
                          limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get security alerts
        
        Args:
            user_id: Filter by user ID
            threat_level: Filter by threat level
            limit: Maximum number of alerts to return
            
        Returns:
            List of security alerts
        """
        try:
            with self._lock:
                alerts = self._security_alerts.copy()
                
                if user_id:
                    alerts = [a for a in alerts if a.user_id == user_id]
                
                if threat_level:
                    alerts = [a for a in alerts if a.threat_level == threat_level]
                
                # Sort by timestamp (newest first) and limit
                alerts.sort(key=lambda a: a.timestamp, reverse=True)
                alerts = alerts[:limit]
                
                return [asdict(alert) for alert in alerts]
                
        except Exception as e:
            logger.error(f"Error retrieving security alerts: {str(e)}")
            return []
    
    def cleanup_expired_data(self):
        """Clean up expired challenges, sessions, and old data"""
        try:
            with self._lock:
                current_time = datetime.utcnow()
                
                # Clean expired MFA challenges
                expired_challenges = [
                    cid for cid, challenge in self._mfa_challenges.items()
                    if current_time > challenge.expires_at
                ]
                for cid in expired_challenges:
                    del self._mfa_challenges[cid]
                
                # Clean expired sessions
                for session in self._user_sessions.values():
                    if current_time > session.expires_at and session.status == SessionStatus.ACTIVE:
                        session.status = SessionStatus.EXPIRED
                
                # Clean old activities (keep last 30 days)
                cutoff_time = current_time - timedelta(days=30)
                self._user_activities = [
                    a for a in self._user_activities if a.timestamp >= cutoff_time
                ]
                
                # Clean old security alerts (keep last 90 days)
                cutoff_time = current_time - timedelta(days=90)
                self._security_alerts = [
                    a for a in self._security_alerts if a.timestamp >= cutoff_time
                ]
                
                # Clean old failed attempts
                cutoff_time = current_time - timedelta(hours=24)
                for user_id in list(self._failed_attempts.keys()):
                    self._failed_attempts[user_id] = [
                        attempt for attempt in self._failed_attempts[user_id]
                        if attempt >= cutoff_time
                    ]
                    if not self._failed_attempts[user_id]:
                        del self._failed_attempts[user_id]
                
                logger.debug("Expired authentication data cleaned up")
                
        except Exception as e:
            logger.error(f"Error cleaning up expired data: {str(e)}")
    
    # Private helper methods
    
    def _generate_secure_id(self) -> str:
        """Generate a secure random identifier"""
        return secrets.token_urlsafe(32)
    
    def _generate_mfa_code(self, method: MFAMethod) -> str:
        """Generate MFA code based on method"""
        if method == MFAMethod.TOTP:
            # For TOTP, this would integrate with a TOTP library
            # For now, generate a 6-digit code
            return f"{secrets.randbelow(1000000):06d}"
        elif method in [MFAMethod.SMS, MFAMethod.EMAIL]:
            return f"{secrets.randbelow(1000000):06d}"
        elif method == MFAMethod.BACKUP_CODES:
            return secrets.token_hex(8).upper()
        else:
            return f"{secrets.randbelow(1000000):06d}"
    
    def _deliver_mfa_challenge(self, challenge: MFAChallenge, 
                             contact_info: str = None) -> Dict[str, Any]:
        """Deliver MFA challenge to user (mock implementation)"""
        # In a real implementation, this would integrate with SMS/email services
        logger.info(f"MFA challenge delivered: {challenge.method.value} code {challenge.code} to user {challenge.user_id}")
        
        return {
            'success': True,
            'delivery_method': challenge.method.value,
            'masked_contact': self._mask_contact_info(contact_info) if contact_info else None
        }
    
    def _mask_contact_info(self, contact_info: str) -> str:
        """Mask contact information for security"""
        if '@' in contact_info:  # Email
            parts = contact_info.split('@')
            return f"{parts[0][:2]}***@{parts[1]}"
        else:  # Phone
            return f"***-***-{contact_info[-4:]}"
    
    def _verify_mfa_code(self, challenge: MFAChallenge, code: str) -> bool:
        """Verify MFA code"""
        return challenge.code == code
    
    def _generate_mfa_token(self, user_id: str) -> str:
        """Generate temporary MFA verification token"""
        return secrets.token_urlsafe(32)
    
    def _validate_mfa_token(self, mfa_token: str, user_id: str) -> bool:
        """Validate MFA verification token (simplified implementation)"""
        # In a real implementation, this would validate against stored tokens
        return len(mfa_token) == 43  # URL-safe base64 32 bytes
    
    def _generate_access_token(self, user_id: str, session_id: str) -> str:
        """Generate access token"""
        return secrets.token_urlsafe(32)
    
    def _generate_refresh_token(self, user_id: str, session_id: str) -> str:
        """Generate refresh token"""
        return secrets.token_urlsafe(32)
    
    def _should_rotate_refresh_token(self, session: UserSession) -> bool:
        """Determine if refresh token should be rotated"""
        # Rotate if session is older than 7 days
        return (datetime.utcnow() - session.created_at).days >= 7
    
    def _calculate_risk_score(self, user_id: str, ip_address: str, user_agent: str) -> float:
        """Calculate risk score for authentication attempt"""
        risk_score = 0.0
        
        try:
            # Check for new device/user agent
            user_sessions = [s for s in self._user_sessions.values() if s.user_id == user_id]
            known_user_agents = set(s.user_agent for s in user_sessions)
            if user_agent not in known_user_agents:
                risk_score += self.risk_patterns['new_device']
            
            # Check for new IP address
            known_ips = set(s.ip_address for s in user_sessions)
            if ip_address not in known_ips:
                risk_score += self.risk_patterns['new_location']
            
            # Check for unusual time (outside 6 AM - 10 PM)
            current_hour = datetime.utcnow().hour
            if current_hour < 6 or current_hour > 22:
                risk_score += self.risk_patterns['unusual_time']
            
            # Check for recent failed attempts
            recent_failures = len([
                attempt for attempt in self._failed_attempts.get(user_id, [])
                if attempt >= datetime.utcnow() - timedelta(hours=1)
            ])
            if recent_failures >= 3:
                risk_score += self.risk_patterns['multiple_failed_attempts']
            
            # Check for suspicious user agent patterns
            suspicious_patterns = ['bot', 'crawler', 'scanner', 'automated']
            if any(pattern in user_agent.lower() for pattern in suspicious_patterns):
                risk_score += self.risk_patterns['suspicious_user_agent']
            
            # Check for blocked IP
            if ip_address in self._blocked_ips:
                risk_score += self.risk_patterns['known_malicious_ip']
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {str(e)}")
            risk_score = 1.0  # Default moderate risk
        
        return risk_score
    
    def _track_user_activity(self, user_id: str, session_id: str, activity_type: str,
                           ip_address: str, user_agent: str, endpoint: str, success: bool,
                           details: Dict[str, Any] = None):
        """Track user activity"""
        activity = UserActivity(
            user_id=user_id,
            session_id=session_id,
            activity_type=activity_type,
            timestamp=datetime.utcnow(),
            ip_address=ip_address,
            user_agent=user_agent,
            endpoint=endpoint,
            success=success,
            details=details or {},
            risk_score=self._calculate_risk_score(user_id, ip_address, user_agent)
        )
        
        self._user_activities.append(activity)
        
        # Keep only recent activities (last 10000)
        if len(self._user_activities) > 10000:
            self._user_activities = self._user_activities[-10000:]
    
    def _create_security_alert(self, user_id: str, threat_level: SecurityThreatLevel,
                             alert_type: str, description: str, details: Dict[str, Any]):
        """Create security alert"""
        alert = SecurityAlert(
            alert_id=self._generate_secure_id(),
            user_id=user_id,
            threat_level=threat_level,
            alert_type=alert_type,
            description=description,
            timestamp=datetime.utcnow(),
            details=details
        )
        
        self._security_alerts.append(alert)
        
        # Log to monitoring system
        self.monitoring.track_custom_metric(
            'security_alert_created',
            1.0,
            {
                'user_id': user_id,
                'threat_level': threat_level.value,
                'alert_type': alert_type
            }
        )
        
        logger.warning(f"Security alert created: {alert_type} for user {user_id} (level: {threat_level.value})")
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security event"""
        self.enhanced_auth._log_security_event(event_type, details)


# Global service instance
_advanced_auth_service = None


def get_advanced_auth_service() -> AdvancedAuthService:
    """Get the global advanced authentication service instance"""
    global _advanced_auth_service
    if _advanced_auth_service is None:
        _advanced_auth_service = AdvancedAuthService()
    return _advanced_auth_service