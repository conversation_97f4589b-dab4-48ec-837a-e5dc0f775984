"""
Data Access Module

This module provides repository classes for accessing various Azure data services.
It implements the repository pattern to abstract data access logic from business logic.

Best practices implemented:
- Repository pattern for data access abstraction
- Proper error handling and logging
- Consistent interface across different storage types
- Automatic container/table/queue creation
- Support for local development with Azurite
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# Optional import for SQL database functionality (deprecated in SFDC service)
try:
    import pyodbc
    PYODBC_AVAILABLE = True
except ImportError:
    PYODBC_AVAILABLE = False
    # Note: This is expected for SFDC service since SQL functionality moved to DB service

from src.shared.azure_services import (
    get_blob_client,
    get_table_client,
    get_queue_client,
    get_credential,
    is_local_dev
)

# Configure module-level logger
logger = logging.getLogger(__name__)

class BlobStorageRepository:
    """Repository for accessing Blob Storage"""

    def __init__(self, container_name: str = "data"):
        """
        Initialize the blob storage repository

        Args:
            container_name: Name of the blob container
        """
        self.blob_service = get_blob_client()
        self.container_name = container_name

        # Ensure container exists
        try:
            self.container_client = self.blob_service.get_container_client(container_name)
            # Check if container exists and create if it doesn't
            try:
                self.container_client.get_container_properties()
                logger.debug(f"Using existing blob container: {container_name}")
            except Exception:
                self.container_client = self.blob_service.create_container(container_name)
                logger.info(f"Created blob container: {container_name}")
        except Exception as e:
            logger.error(f"Error initializing blob container: {str(e)}")
            raise

    def save_json(self, blob_name: str, data: Any) -> bool:
        """
        Save JSON data to a blob

        Args:
            blob_name: Name of the blob
            data: Data to save (will be converted to JSON)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            json_data = json.dumps(data)
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.upload_blob(json_data, overwrite=True)
            logger.info(f"Saved JSON data to blob: {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Error saving JSON to blob {blob_name}: {str(e)}")
            return False

    def get_json(self, blob_name: str) -> Optional[Any]:
        """
        Get JSON data from a blob

        Args:
            blob_name: Name of the blob

        Returns:
            Any: Parsed JSON data or None if error
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            data = blob_client.download_blob().readall()
            logger.debug(f"Retrieved data from blob: {blob_name}")
            return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting JSON from blob {blob_name}: {str(e)}")
            return None

    def list_blobs(self, name_starts_with: Optional[str] = None) -> List[str]:
        """
        List blobs in the container

        Args:
            name_starts_with: Optional prefix to filter blobs

        Returns:
            List[str]: List of blob names
        """
        try:
            blobs = [blob.name for blob in self.container_client.list_blobs(name_starts_with=name_starts_with)]
            logger.debug(f"Listed {len(blobs)} blobs in container {self.container_name}")
            return blobs
        except Exception as e:
            logger.error(f"Error listing blobs: {str(e)}")
            return []

    def delete_blob(self, blob_name: str) -> bool:
        """
        Delete a blob

        Args:
            blob_name: Name of the blob to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.delete_blob()
            logger.info(f"Deleted blob: {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting blob {blob_name}: {str(e)}")
            return False

    def get_blob_bytes(self, blob_name: str) -> Optional[bytes]:
        """
        Download the raw bytes of a blob.
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            data = blob_client.download_blob().readall()
            logger.debug(f"Downloaded blob bytes: {blob_name}")
            return data
        except Exception as e:
            logger.error(f"Error downloading blob bytes {blob_name}: {str(e)}")
            return None

    def upload_blob(self, blob_name: str, data: bytes, overwrite: bool = True) -> bool:
        """
        Upload data to a blob

        Args:
            blob_name: Name of the blob
            data: Data to upload (bytes)
            overwrite: Whether to overwrite existing blob

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.upload_blob(data, overwrite=overwrite)
            logger.info(f"Uploaded blob: {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Error uploading blob {blob_name}: {str(e)}")
            return False

    def get_blob_url(self, blob_name: str) -> Optional[str]:
        """
        Get the URL of a blob

        Args:
            blob_name: Name of the blob

        Returns:
            str: Blob URL or None if error
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            return blob_client.url
        except Exception as e:
            logger.error(f"Error getting blob URL for {blob_name}: {str(e)}")
            return None


class TableStorageRepository:
    """Repository for accessing Table Storage"""

    def __init__(self, table_name: str):
        """
        Initialize the table storage repository

        Args:
            table_name: Name of the table
        """
        self.table_service = get_table_client()
        self.table_name = table_name

        # Ensure table exists
        try:
            logger.info(f"Initializing table: {table_name}")
            logger.info(f"Table service type: {type(self.table_service)}")

            # Check if table exists first
            try:
                tables = list(self.table_service.list_tables())
                table_names = [table.name for table in tables]
                logger.info(f"Existing tables: {table_names}")

                if table_name in table_names:
                    logger.info(f"Table {table_name} already exists")
                    self.table_client = self.table_service.get_table_client(table_name)
                else:
                    logger.info(f"Creating table {table_name}")
                    # Azure Data Tables uses a different method name
                    self.table_client = self.table_service.create_table_if_not_exists(table_name)
            except Exception as list_e:
                logger.warning(f"Error listing tables: {str(list_e)}, proceeding with create_table_if_not_exists")
                # Azure Data Tables uses a different method name
                self.table_client = self.table_service.create_table_if_not_exists(table_name)

            logger.info(f"Table initialized: {table_name}")
            logger.info(f"Table client type: {type(self.table_client)}")
        except Exception as e:
            logger.error(f"Error initializing table: {str(e)}")
            raise

    def insert_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Insert an entity into the table

        Args:
            entity: Entity to insert (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure partition key and row key
            if 'PartitionKey' not in entity:
                entity['PartitionKey'] = datetime.now().strftime("%Y%m%d")
                logger.info(f"Added default PartitionKey: {entity['PartitionKey']}")
            if 'RowKey' not in entity:
                entity['RowKey'] = datetime.now().strftime("%H%M%S%f")
                logger.info(f"Added default RowKey: {entity['RowKey']}")

            logger.info(f"Inserting entity into table {self.table_name}: {entity}")

            # Azure Data Tables uses upsert_entity instead of create_entity
            try:
                self.table_client.upsert_entity(entity)
                logger.info(f"Successfully inserted entity with RowKey {entity.get('RowKey')} into table {self.table_name}")

                # Verify the entity was inserted by trying to retrieve it
                try:
                    partition_key = entity.get('PartitionKey')
                    row_key = entity.get('RowKey')
                    filter_query = f"PartitionKey eq '{partition_key}' and RowKey eq '{row_key}'"
                    verification_entities = list(self.table_client.query_entities(query_filter=filter_query))
                    if verification_entities:
                        logger.info(f"Verified entity exists in table {self.table_name}: {verification_entities[0]}")
                    else:
                        logger.warning(f"Entity verification failed - could not find entity with PartitionKey={partition_key}, RowKey={row_key} in table {self.table_name}")
                except Exception as ve:
                    logger.warning(f"Error verifying entity insertion: {str(ve)}")

                return True
            except Exception as ue:
                logger.error(f"Error in upsert_entity operation: {str(ue)}")
                return False
        except Exception as e:
            logger.error(f"Error inserting entity: {str(e)}")
            return False

    def query_entities(self, filter_query: Optional[str] = None, field_filter: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Query entities from the table

        Args:
            filter_query: Optional OData filter query
            field_filter: Optional dictionary of field name to value for filtering

        Returns:
            List[Dict[str, Any]]: List of entities
        """
        try:
            # Log the query parameters
            logger.info(f"Querying table {self.table_name} with filter: {filter_query}")

            # Azure Data Tables uses query_entities with different parameters
            # Handle different versions of the Azure Data Tables SDK
            try:
                if filter_query:
                    logger.info(f"Using filter query: {filter_query}")
                    entities = self.table_client.query_entities(query_filter=filter_query)
                else:
                    # Try with empty string as query_filter (newer SDK versions)
                    logger.info("Using empty filter query")
                    entities = self.table_client.query_entities(query_filter="")
            except TypeError as te:
                logger.warning(f"TypeError in query_entities: {str(te)}")
                # For older SDK versions that don't require query_filter
                try:
                    logger.info("Falling back to query_entities without parameters")
                    entities = self.table_client.query_entities()
                except Exception as inner_e:
                    logger.error(f"Failed to query entities with older SDK approach: {str(inner_e)}")
                    return []

            # Convert to list for further processing
            result = list(entities)
            logger.info(f"Retrieved {len(result)} entities from table {self.table_name}")

            # Log the first few entities for debugging
            if result:
                for i, entity in enumerate(result[:3]):  # Log up to 3 entities
                    logger.info(f"Entity {i+1}: {entity}")
                if len(result) > 3:
                    logger.info(f"... and {len(result) - 3} more entities")

            # Apply field filtering if provided
            if field_filter and result:
                filtered_result = []
                for entity in result:
                    include = True
                    for field, value in field_filter.items():
                        if field in entity:
                            entity_value = str(entity[field]).lower()
                            filter_value = str(value).lower()
                            if filter_value not in entity_value:
                                include = False
                                break
                    if include:
                        filtered_result.append(entity)
                logger.debug(f"Filtered from {len(result)} to {len(filtered_result)} entities")
                return filtered_result

            return result
        except Exception as e:
            logger.error(f"Error querying entities: {str(e)}")
            return []

    def update_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Update an entity in the table

        Args:
            entity: Entity to update (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure entity has partition key and row key
            if 'PartitionKey' not in entity or 'RowKey' not in entity:
                logger.error("Entity must have PartitionKey and RowKey")
                return False

            # Azure Data Tables uses upsert_entity for updates
            self.table_client.upsert_entity(entity)
            logger.debug(f"Updated entity with RowKey {entity.get('RowKey')} in table {self.table_name}")
            return True
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False

    def get_entity(self, partition_key: str, row_key: str) -> Optional[Dict[str, Any]]:
        """
        Get a single entity from the table by partition key and row key

        Args:
            partition_key: Partition key of the entity
            row_key: Row key of the entity

        Returns:
            Optional[Dict[str, Any]]: Entity if found, None otherwise
        """
        try:
            # Create a filter query for the specific entity
            filter_query = f"PartitionKey eq '{partition_key}' and RowKey eq '{row_key}'"
            entities = self.query_entities(filter_query)

            if not entities:
                logger.warning(f"Entity with PartitionKey {partition_key} and RowKey {row_key} not found")
                return None

            # Return the first (and should be only) entity
            logger.debug(f"Retrieved entity with PartitionKey {partition_key} and RowKey {row_key}")
            return entities[0]
        except Exception as e:
            logger.error(f"Error getting entity: {str(e)}")
            return None

    def delete_entity(self, partition_key: str, row_key: str) -> bool:
        """
        Delete an entity from the table

        Args:
            partition_key: Partition key of the entity
            row_key: Row key of the entity

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.table_client.delete_entity(partition_key, row_key)
            logger.info(f"Deleted entity with PartitionKey {partition_key} and RowKey {row_key}")
            return True
        except Exception as e:
            logger.error(f"Error deleting entity: {str(e)}")
            return False


class QueueStorageRepository:
    """Repository for accessing Queue Storage"""

    def __init__(self, queue_name: str):
        """
        Initialize the queue storage repository

        Args:
            queue_name: Name of the queue
        """
        self.queue_service = get_queue_client()
        self.queue_name = queue_name

        # Ensure queue exists
        try:
            self.queue_client = self.queue_service.create_queue_if_not_exists(queue_name)
            logger.info(f"Queue initialized: {queue_name}")
        except Exception as e:
            logger.error(f"Error initializing queue: {str(e)}")
            raise

    def send_message(self, message_content: Union[str, Dict[str, Any]]) -> bool:
        """
        Send a message to the queue

        Args:
            message_content: Message content (string or dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if isinstance(message_content, dict):
                message_content = json.dumps(message_content)
            self.queue_client.send_message(message_content)
            logger.debug(f"Sent message to queue {self.queue_name}")
            return True
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return False

    def receive_messages(self, max_messages: int = 5) -> List[Any]:
        """
        Receive messages from the queue

        Args:
            max_messages: Maximum number of messages to receive

        Returns:
            List[Any]: List of messages
        """
        try:
            messages = self.queue_client.receive_messages(max_messages=max_messages)
            result = list(messages)
            logger.debug(f"Received {len(result)} messages from queue {self.queue_name}")
            return result
        except Exception as e:
            logger.error(f"Error receiving messages: {str(e)}")
            return []

    def delete_message(self, message: Any) -> bool:
        """
        Delete a message from the queue

        Args:
            message: Message to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.queue_client.delete_message(message)
            logger.debug(f"Deleted message from queue {self.queue_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting message: {str(e)}")
            return False


# DEPRECATED: SqlDatabaseRepository has been moved to atomsec-func-db-r service
# All database operations should now go through the DB service client
# This class is kept for backward compatibility but should not be used

class SqlDatabaseRepository:
    """
    DEPRECATED: Repository for accessing SQL Database

    This class has been deprecated and moved to the atomsec-func-db-r service.
    All database operations should now go through the DB service client.
    Use shared.db_service_client.DatabaseServiceClient instead.
    """

    def __init__(self, table_name: str):
        """
        DEPRECATED: Initialize the SQL database repository

        This method is deprecated. Use DB service client instead.
        """
        self.table_name = table_name
        logger.warning(f"SqlDatabaseRepository is deprecated. Use DB service client for table: {table_name}")

    def _get_connection(self):
        """DEPRECATED: Use DB service client instead"""
        logger.error("SqlDatabaseRepository._get_connection() is deprecated. Use DB service client instead.")
        return None

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[tuple]:
        """DEPRECATED: Use DB service client instead"""
        logger.error("SqlDatabaseRepository.execute_query() is deprecated. Use DB service client instead.")
        return []

    def execute_non_query(self, query: str, params: Optional[tuple] = None) -> bool:
        """DEPRECATED: Use DB service client instead"""
        logger.error("SqlDatabaseRepository.execute_non_query() is deprecated. Use DB service client instead.")
        return False


# Global repository instances (lazy initialized)
_table_storage_repos = {}
_sql_database_repos = {}


def get_table_storage_repository(table_name: str) -> Optional[TableStorageRepository]:
    """
    Get a table storage repository for the specified table name

    This function implements lazy initialization of table storage repositories
    and caches them for reuse.

    Args:
        table_name: Name of the table

    Returns:
        TableStorageRepository: Repository instance or None if error
    """
    global _table_storage_repos

    # Check if repository already exists
    if table_name in _table_storage_repos and _table_storage_repos[table_name] is not None:
        return _table_storage_repos[table_name]

    # Create new repository
    try:
        repo = TableStorageRepository(table_name=table_name)
        _table_storage_repos[table_name] = repo
        logger.info(f"Initialized table storage repository for {table_name}")
        return repo
    except Exception as e:
        logger.error(f"Failed to initialize table storage repository for {table_name}: {str(e)}")
        _table_storage_repos[table_name] = None
        return None


def get_sql_database_repository(table_name: str = None) -> Optional[SqlDatabaseRepository]:
    """
    DEPRECATED: Get a SQL database repository for the specified table name

    This function is deprecated. Use DB service client instead.
    All database operations should now go through the atomsec-func-db-r service.
    """
    logger.warning(f"get_sql_database_repository() is deprecated. Use DB service client for table: {table_name}")
    return SqlDatabaseRepository(table_name or "")

# --- PoliciesResult Table Support ---

POLICIES_RESULT_TABLE_NAME = "PoliciesResult"

POLICIES_RESULT_SCHEMA_SQL = '''
CREATE TABLE PoliciesResult (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OrgPolicyId TEXT,
    PolicyId TEXT,
    ExecutionLogId TEXT,
    OrgValue TEXT,
    OWASPCategory TEXT,
    StandardValue TEXT,
    IssueDescription TEXT,
    Recommendations TEXT,
    Severity TEXT,
    Weakness TEXT,
    IntegrationId TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    Type TEXT,
    ProfileName TEXT,
    PermissionSetName TEXT
);
'''

def get_policies_result_table_repo() -> TableStorageRepository:
    """Get the PoliciesResult table repository for local/Azurite development"""
    return get_table_storage_repository(POLICIES_RESULT_TABLE_NAME)

# --- ProfileAssignmentCount Table Support ---

PROFILE_ASSIGNMENT_COUNT_TABLE_NAME = "ProfileAssignmentCount"

PROFILE_ASSIGNMENT_COUNT_SCHEMA_SQL = '''
CREATE TABLE ProfileAssignmentCount (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ExecutionLogId TEXT,
    ProfileName TEXT,
    PermissionSetName TEXT,
    AssignmentCount INTEGER,
    AssignedProfiles TEXT,
    Type TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
'''

def get_profile_assignment_count_table_repo() -> TableStorageRepository:
    """Get the ProfileAssignmentCount table repository for local/Azurite development"""
    return get_table_storage_repository(PROFILE_ASSIGNMENT_COUNT_TABLE_NAME)

# --- PolicyRuleSetting Table Support ---

POLICY_RULE_SETTING_TABLE_NAME = "PolicyRuleSetting"

POLICY_RULE_SETTING_SCHEMA_SQL = '''
CREATE TABLE PolicyRuleSetting (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PolicyId TEXT,
    UserId TEXT,
    IntegrationId TEXT,
    TaskType TEXT,
    Enabled INTEGER DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
'''

def get_policy_rule_setting_table_repo() -> TableStorageRepository:
    """Get the PolicyRuleSetting table repository for local/Azurite development"""
    return get_table_storage_repository(POLICY_RULE_SETTING_TABLE_NAME)

# --- Policy Table Support ---
POLICY_TABLE_NAME = "Policy"
POLICY_TABLE_SCHEMA_SQL = '''
CREATE TABLE Policy (
    PolicyId TEXT PRIMARY KEY,
    Name TEXT,
    UserId TEXT,
    IntegrationId TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
'''
def get_policy_table_repo() -> TableStorageRepository:
    """Get the Policy table repository for local/Azurite development"""
    return get_table_storage_repository(POLICY_TABLE_NAME)

# --- Rule Table Support ---
RULE_TABLE_NAME = "Rule"
RULE_TABLE_SCHEMA_SQL = '''
CREATE TABLE Rule (
    RuleId TEXT PRIMARY KEY,
    PolicyId TEXT,
    TaskType TEXT,
    Enabled INTEGER DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
'''
def get_rule_table_repo() -> TableStorageRepository:
    """Get the Rule table repository for local/Azurite development"""
    return get_table_storage_repository(RULE_TABLE_NAME)

# NOTE: create_default_policies_and_rules_for_integration function has been moved
# to blueprints/integration.py to use the DB service client instead of direct table access


def enqueue_policy_tasks(processor, org_id, user_id, policy_name, params, priority=None, execution_log_id=None):
    """
    Enqueue all enabled rules for a given policy name and integration.
    This function works with the separated microservice architecture by using the DB service.

    Args:
        processor: BackgroundProcessor instance
        org_id: Organization/Integration ID
        user_id: User ID
        policy_name: Name of the policy to enqueue tasks for
        params: Common parameters to pass to all tasks
        priority: Task priority (high, medium, low)
        execution_log_id: Execution log ID for workflow tracking
    """
    from src.shared.db_service_client import get_db_client

    logger.info(f"Enqueueing policy tasks for policy '{policy_name}' and integration {org_id}")

    try:
        # Get DB service client
        db_client = get_db_client()
        if not db_client:
            logger.error("DB service client not available for policy task enqueueing")
            return False

        # Get enabled tasks for this integration and user
        enabled_tasks = db_client.get_enabled_tasks_for_integration(org_id, user_id)
        if not enabled_tasks:
            logger.info(f"No enabled tasks found for integration {org_id} and user {user_id}")
            return True  # Not an error, just no tasks to enqueue

        # Get policies for this integration to find the specific policy
        policies = db_client.get_policies_for_integration(org_id)
        if not policies:
            logger.error(f"Failed to get policies for integration {org_id}")
            return False

        target_policy = None

        # Find the policy by name
        for policy in policies:
            if policy.get('Name') == policy_name:
                target_policy = policy
                break

        if not target_policy:
            logger.warning(f"Policy '{policy_name}' not found for integration {org_id}")
            return True  # Not an error, policy might not exist yet

        policy_id = target_policy.get('PolicyId')
        if not policy_id:
            logger.error(f"Policy '{policy_name}' has no PolicyId")
            return False

        # Get rules for this policy
        rules = db_client.get_rules_for_policy(policy_id)
        if not rules:
            logger.warning(f"No rules found for policy '{policy_name}' (ID: {policy_id})")
            return True  # Not an error, policy might have no rules
        enqueued_count = 0

        # Enqueue each enabled task
        for rule in rules:
            if not rule.get('Enabled', False):
                continue

            task_type = rule.get('TaskType')
            if not task_type:
                continue

            # Check if this task type is in the enabled tasks list
            if task_type not in enabled_tasks:
                logger.info(f"Task {task_type} is not enabled for integration {org_id}, skipping")
                continue

            # Enqueue the task
            task_id = processor.enqueue_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                params=params.copy() if params else {},
                priority=priority,
                execution_log_id=execution_log_id
            )

            if task_id:
                enqueued_count += 1
                logger.info(f"Enqueued task {task_type} (ID: {task_id}) for policy '{policy_name}'")
            else:
                logger.warning(f"Failed to enqueue task {task_type} for policy '{policy_name}'")

        logger.info(f"Successfully enqueued {enqueued_count} tasks for policy '{policy_name}' and integration {org_id}")
        return True

    except Exception as e:
        logger.error(f"Error enqueueing policy tasks for policy '{policy_name}' and integration {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
