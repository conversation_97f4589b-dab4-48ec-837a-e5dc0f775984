"""
Enhanced Parameter Validation Module

This module provides comprehensive parameter validation with security features
for SFDC service requests, including access token validation, execution log ID
tracking, and secure parameter handling.
"""

import json
import logging
import re
import uuid
from typing import Dict, Any, Optional, List, Tuple, Union
from datetime import datetime, timedelta
import hashlib
import hmac
import base64

# Import existing modules
from src.shared.parameter_validator import ParameterValidator
from src.shared.azure_services import get_secret

logger = logging.getLogger(__name__)


class SecurityValidationError(Exception):
    """Custom exception for security validation failures"""
    pass


class ParameterValidationError(Exception):
    """Custom exception for parameter validation failures"""
    pass


class EnhancedParameterValidator(ParameterValidator):
    """
    Enhanced parameter validator with security features for SFDC service
    
    This class extends the base ParameterValidator with:
    - Access token validation and security
    - Execution log ID validation and tracking
    - Salesforce API authentication parameter validation
    - Sequential task parameter validation
    - Security monitoring and logging
    """
    
    def __init__(self):
        super().__init__()
        self._token_cache = {}
        self._validation_cache = {}
        self._security_events = []
        
    def validate_sfdc_request_parameters(self, params: Dict[str, Any], 
                                       required_fields: List[str] = None,
                                       validate_execution_log: bool = True) -> Dict[str, Any]:
        """
        Validate SFDC service request parameters with security checks
        
        Args:
            params: Parameters to validate
            required_fields: List of required field names
            validate_execution_log: Whether to validate execution_log_id
            
        Returns:
            Dict[str, Any]: Validated and sanitized parameters
            
        Raises:
            ParameterValidationError: If validation fails
            SecurityValidationError: If security validation fails
        """
        try:
            logger.info("Starting enhanced SFDC parameter validation")
            
            if not params or not isinstance(params, dict):
                raise ParameterValidationError("Parameters must be a non-empty dictionary")
            
            # Create a copy for validation
            validated_params = params.copy()
            
            # Validate required fields
            if required_fields:
                missing_fields = [field for field in required_fields if field not in validated_params]
                if missing_fields:
                    raise ParameterValidationError(f"Missing required fields: {missing_fields}")
            
            # Validate execution_log_id if required
            if validate_execution_log:
                validated_params = self._validate_execution_log_id(validated_params)
            
            # Validate access tokens if present
            if 'access_token' in validated_params:
                validated_params = self._validate_access_token(validated_params)
            
            # Validate Salesforce credentials if present
            if any(key in validated_params for key in ['client_id', 'client_secret', 'instance_url']):
                validated_params = self._validate_salesforce_credentials(validated_params)
            
            # Sanitize and validate JSON serialization
            validated_params = self.sanitize_parameters(validated_params)
            validated_params = self.validate_json_parameters(validated_params)
            
            # Log successful validation
            self._log_security_event("parameter_validation_success", {
                "param_count": len(validated_params),
                "has_access_token": 'access_token' in validated_params,
                "has_execution_log": 'execution_log_id' in validated_params
            })
            
            logger.info("Enhanced SFDC parameter validation completed successfully")
            return validated_params
            
        except (ParameterValidationError, SecurityValidationError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error in parameter validation: {str(e)}")
            raise ParameterValidationError(f"Parameter validation failed: {str(e)}")
    
    def _validate_execution_log_id(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate execution_log_id parameter
        
        Args:
            params: Parameters containing execution_log_id
            
        Returns:
            Dict[str, Any]: Parameters with validated execution_log_id
            
        Raises:
            ParameterValidationError: If execution_log_id is invalid
        """
        execution_log_id = params.get('execution_log_id')
        
        if not execution_log_id:
            # Generate a new execution_log_id if not provided
            execution_log_id = str(uuid.uuid4())
            params['execution_log_id'] = execution_log_id
            logger.warning(f"Generated new execution_log_id: {execution_log_id}")
        
        # Validate UUID format
        try:
            uuid.UUID(execution_log_id)
        except ValueError:
            raise ParameterValidationError(f"Invalid execution_log_id format: {execution_log_id}")
        
        # Ensure it's a string
        params['execution_log_id'] = str(execution_log_id)
        
        logger.debug(f"Validated execution_log_id: {execution_log_id}")
        return params
    
    def _validate_access_token(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate access token security
        
        Args:
            params: Parameters containing access_token
            
        Returns:
            Dict[str, Any]: Parameters with validated access_token
            
        Raises:
            SecurityValidationError: If access token is invalid or insecure
        """
        access_token = params.get('access_token')
        
        if not access_token or not isinstance(access_token, str):
            raise SecurityValidationError("Access token must be a non-empty string")
        
        # Check token length (Salesforce tokens are typically 112+ characters)
        if len(access_token) < 50:
            raise SecurityValidationError("Access token appears to be too short")
        
        # Check for common token patterns
        if not re.match(r'^[A-Za-z0-9._-]+$', access_token):
            raise SecurityValidationError("Access token contains invalid characters")
        
        # Check if token is not a placeholder or test value
        test_patterns = ['test', 'dummy', 'placeholder', 'example', 'fake']
        if any(pattern in access_token.lower() for pattern in test_patterns):
            raise SecurityValidationError("Access token appears to be a test/placeholder value")
        
        # Hash the token for logging (never log the actual token)
        token_hash = hashlib.sha256(access_token.encode()).hexdigest()[:16]
        
        # Cache validation result
        self._token_cache[token_hash] = {
            'validated_at': datetime.utcnow(),
            'valid': True
        }
        
        logger.debug(f"Validated access token (hash: {token_hash})")
        
        # Log security event
        self._log_security_event("access_token_validation", {
            "token_hash": token_hash,
            "token_length": len(access_token)
        })
        
        return params
    
    def _validate_salesforce_credentials(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate Salesforce API credentials
        
        Args:
            params: Parameters containing Salesforce credentials
            
        Returns:
            Dict[str, Any]: Parameters with validated credentials
            
        Raises:
            SecurityValidationError: If credentials are invalid
        """
        # Validate client_id
        if 'client_id' in params:
            client_id = params['client_id']
            if not client_id or not isinstance(client_id, str):
                raise SecurityValidationError("client_id must be a non-empty string")
            
            # Salesforce client IDs are typically 87 characters long
            if len(client_id) < 50:
                raise SecurityValidationError("client_id appears to be too short")
        
        # Validate client_secret
        if 'client_secret' in params:
            client_secret = params['client_secret']
            if not client_secret or not isinstance(client_secret, str):
                raise SecurityValidationError("client_secret must be a non-empty string")
            
            # Client secrets should be substantial length
            if len(client_secret) < 20:
                raise SecurityValidationError("client_secret appears to be too short")
        
        # Validate instance_url
        if 'instance_url' in params:
            instance_url = params['instance_url']
            if not instance_url or not isinstance(instance_url, str):
                raise SecurityValidationError("instance_url must be a non-empty string")
            
            # Validate URL format
            url_pattern = r'^https?://[a-zA-Z0-9.-]+\.salesforce\.com/?$|^https?://[a-zA-Z0-9.-]+-dev-ed\.develop\.my\.salesforce\.com/?$'
            if not re.match(url_pattern, instance_url):
                raise SecurityValidationError("instance_url must be a valid Salesforce URL")
        
        logger.debug("Validated Salesforce credentials")
        return params
    
    def validate_sequential_task_parameters(self, params: Dict[str, Any], 
                                          task_type: str,
                                          execution_log_id: str) -> Dict[str, Any]:
        """
        Validate parameters for sequential task processing
        
        Args:
            params: Task parameters to validate
            task_type: Type of task being processed
            execution_log_id: Execution log ID for task sequence tracking
            
        Returns:
            Dict[str, Any]: Validated parameters
            
        Raises:
            ParameterValidationError: If validation fails
        """
        try:
            logger.info(f"Validating sequential task parameters for {task_type}")
            
            # Ensure execution_log_id is present and valid
            params['execution_log_id'] = execution_log_id
            params = self._validate_execution_log_id(params)
            
            # Validate task-specific parameters
            validated_params = self._validate_task_specific_parameters(params, task_type)
            
            # Ensure parameters are JSON serializable
            validated_params = self.validate_json_parameters(validated_params)
            
            logger.info(f"Sequential task parameters validated for {task_type}")
            return validated_params
            
        except Exception as e:
            logger.error(f"Sequential task parameter validation failed for {task_type}: {str(e)}")
            raise ParameterValidationError(f"Sequential task validation failed: {str(e)}")
    
    def _validate_task_specific_parameters(self, params: Dict[str, Any], 
                                         task_type: str) -> Dict[str, Any]:
        """
        Validate parameters specific to each task type
        
        Args:
            params: Parameters to validate
            task_type: Type of task
            
        Returns:
            Dict[str, Any]: Validated parameters
        """
        # Define required parameters for each task type
        task_requirements = {
            'sfdc_authenticate': ['client_id', 'client_secret', 'instance_url'],
            'health_check': ['access_token', 'instance_url'],
            'metadata_extraction': ['access_token', 'instance_url'],
            'profiles': ['access_token', 'instance_url'],
            'permission_sets': ['access_token', 'instance_url'],
            'overview': ['access_token', 'instance_url'],
            'pmd_apex_security': ['access_token', 'instance_url'],
            'mfa_enforcement': ['access_token', 'instance_url'],
            'device_activation': ['access_token', 'instance_url'],
            'login_ip_ranges': ['access_token', 'instance_url'],
            'login_hours': ['access_token', 'instance_url'],
            'session_timeout': ['access_token', 'instance_url'],
            'api_whitelisting': ['access_token', 'instance_url'],
            'password_policy': ['access_token', 'instance_url']
        }
        
        required_fields = task_requirements.get(task_type, [])
        
        # Validate required fields for this task type
        if required_fields:
            missing_fields = [field for field in required_fields if field not in params]
            if missing_fields:
                raise ParameterValidationError(
                    f"Task {task_type} missing required parameters: {missing_fields}"
                )
        
        # Validate specific parameter formats
        if task_type == 'sfdc_authenticate':
            params = self._validate_salesforce_credentials(params)
        elif 'access_token' in params:
            params = self._validate_access_token(params)
        
        return params
    
    def validate_execution_log_tracking(self, execution_log_id: str, 
                                      task_sequence: List[str]) -> bool:
        """
        Validate execution log ID for task sequence tracking
        
        Args:
            execution_log_id: Execution log ID to validate
            task_sequence: List of task types in the sequence
            
        Returns:
            bool: True if valid for sequence tracking
            
        Raises:
            ParameterValidationError: If validation fails
        """
        try:
            # Validate UUID format
            uuid.UUID(execution_log_id)
            
            # Validate task sequence
            if not task_sequence or not isinstance(task_sequence, list):
                raise ParameterValidationError("Task sequence must be a non-empty list")
            
            # Log the validation
            logger.info(f"Validated execution log {execution_log_id} for sequence: {task_sequence}")
            
            return True
            
        except ValueError:
            raise ParameterValidationError(f"Invalid execution_log_id format: {execution_log_id}")
        except Exception as e:
            raise ParameterValidationError(f"Execution log validation failed: {str(e)}")
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log security events for monitoring
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details
        }
        
        self._security_events.append(event)
        
        # Log to application logger
        logger.info(f"Security event: {event_type}", extra=details)
        
        # Keep only recent events (last 100)
        if len(self._security_events) > 100:
            self._security_events = self._security_events[-100:]
    
    def get_security_events(self, event_type: str = None, 
                          since: datetime = None) -> List[Dict[str, Any]]:
        """
        Get security events for monitoring
        
        Args:
            event_type: Filter by event type
            since: Filter events since this timestamp
            
        Returns:
            List[Dict[str, Any]]: Filtered security events
        """
        events = self._security_events.copy()
        
        if event_type:
            events = [e for e in events if e['event_type'] == event_type]
        
        if since:
            since_str = since.isoformat()
            events = [e for e in events if e['timestamp'] >= since_str]
        
        return events
    
    def clear_caches(self):
        """Clear validation caches"""
        self._token_cache.clear()
        self._validation_cache.clear()
        logger.debug("Validation caches cleared")


# Global enhanced validator instance
_enhanced_validator = None


def get_enhanced_parameter_validator() -> EnhancedParameterValidator:
    """
    Get the global enhanced parameter validator instance
    
    Returns:
        EnhancedParameterValidator: The enhanced validator instance
    """
    global _enhanced_validator
    if _enhanced_validator is None:
        _enhanced_validator = EnhancedParameterValidator()
    return _enhanced_validator


def validate_sfdc_request(params: Dict[str, Any], 
                         required_fields: List[str] = None,
                         validate_execution_log: bool = True) -> Dict[str, Any]:
    """
    Convenience function for SFDC request validation
    
    Args:
        params: Parameters to validate
        required_fields: List of required field names
        validate_execution_log: Whether to validate execution_log_id
        
    Returns:
        Dict[str, Any]: Validated parameters
    """
    validator = get_enhanced_parameter_validator()
    return validator.validate_sfdc_request_parameters(
        params, required_fields, validate_execution_log
    )


def validate_sequential_task(params: Dict[str, Any], 
                           task_type: str,
                           execution_log_id: str) -> Dict[str, Any]:
    """
    Convenience function for sequential task validation
    
    Args:
        params: Task parameters to validate
        task_type: Type of task being processed
        execution_log_id: Execution log ID for task sequence tracking
        
    Returns:
        Dict[str, Any]: Validated parameters
    """
    validator = get_enhanced_parameter_validator()
    return validator.validate_sequential_task_parameters(params, task_type, execution_log_id)