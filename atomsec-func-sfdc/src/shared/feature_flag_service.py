"""
Feature Flag Service

This module provides dynamic feature flag management with:
- User context-based feature flag evaluation
- Feature usage tracking and analytics
- Dynamic feature toggling without deployment
- A/B testing support
- Gradual rollout capabilities

Best practices implemented:
- Context-aware feature evaluation
- Usage analytics and metrics
- Safe fallback mechanisms
- Performance optimization with caching
- <PERSON>t logging for feature flag changes
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import hashlib
import random

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import existing modules
from src.shared.common import is_local_dev, is_test_env
from src.shared.azure_services import get_secret

class FeatureFlagStrategy(Enum):
    """Feature flag evaluation strategies"""
    BOOLEAN = "boolean"  # Simple on/off
    PERCENTAGE = "percentage"  # Percentage-based rollout
    USER_LIST = "user_list"  # Specific user list
    ATTRIBUTE = "attribute"  # Based on user attributes
    TIME_WINDOW = "time_window"  # Time-based activation
    CANARY = "canary"  # Canary deployment

class FeatureFlagStatus(Enum):
    """Feature flag status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
    TESTING = "testing"

@dataclass
class UserContext:
    """User context for feature flag evaluation"""
    user_id: str
    organization_id: Optional[str] = None
    role: Optional[str] = None
    attributes: Dict[str, Any] = field(default_factory=dict)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class FeatureFlagRule:
    """Feature flag evaluation rule"""
    strategy: FeatureFlagStrategy
    parameters: Dict[str, Any] = field(default_factory=dict)
    weight: int = 100  # For weighted evaluation

@dataclass
class FeatureFlag:
    """Feature flag definition"""
    name: str
    description: str
    status: FeatureFlagStatus
    default_value: bool = False
    rules: List[FeatureFlagRule] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FeatureFlagEvaluation:
    """Result of feature flag evaluation"""
    flag_name: str
    enabled: bool
    rule_matched: Optional[str] = None
    evaluation_time: datetime = field(default_factory=datetime.now)
    user_context: Optional[UserContext] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FeatureUsageMetrics:
    """Feature usage metrics"""
    flag_name: str
    total_evaluations: int = 0
    enabled_count: int = 0
    disabled_count: int = 0
    unique_users: int = 0
    last_evaluation: Optional[datetime] = None
    evaluation_times: List[float] = field(default_factory=list)  # Response times in ms

class FeatureFlagService:
    """
    Feature flag service with dynamic feature toggling,
    user context evaluation, and usage analytics
    """
    
    def __init__(self, config_source: str = "file"):
        """
        Initialize the feature flag service
        
        Args:
            config_source: Source for feature flag configuration ("file", "keyvault", "database")
        """
        self._config_source = config_source
        self._flags: Dict[str, FeatureFlag] = {}
        self._usage_metrics: Dict[str, FeatureUsageMetrics] = {}
        self._evaluation_cache: Dict[str, Dict[str, FeatureFlagEvaluation]] = {}
        self._cache_ttl = timedelta(minutes=5)
        self._lock = threading.RLock()
        self._evaluators: Dict[FeatureFlagStrategy, Callable] = {}
        
        logger.info(f"Initializing Feature Flag Service with config source: {config_source}")
        
        # Register built-in evaluators
        self._register_evaluators()
        
        # Load initial feature flags
        self._load_feature_flags()
    
    def _register_evaluators(self):
        """Register built-in feature flag evaluators"""
        self._evaluators = {
            FeatureFlagStrategy.BOOLEAN: self._evaluate_boolean,
            FeatureFlagStrategy.PERCENTAGE: self._evaluate_percentage,
            FeatureFlagStrategy.USER_LIST: self._evaluate_user_list,
            FeatureFlagStrategy.ATTRIBUTE: self._evaluate_attribute,
            FeatureFlagStrategy.TIME_WINDOW: self._evaluate_time_window,
            FeatureFlagStrategy.CANARY: self._evaluate_canary
        }
    
    def _load_feature_flags(self):
        """Load feature flags from configured source"""
        try:
            if self._config_source == "file":
                self._load_flags_from_file()
            elif self._config_source == "keyvault":
                self._load_flags_from_keyvault()
            elif self._config_source == "database":
                self._load_flags_from_database()
            else:
                logger.warning(f"Unknown config source '{self._config_source}', using default flags")
                self._load_default_flags()
            
            logger.info(f"Loaded {len(self._flags)} feature flags")
            
        except Exception as e:
            logger.error(f"Error loading feature flags: {str(e)}")
            self._load_default_flags()
    
    def _load_flags_from_file(self):
        """Load feature flags from configuration file"""
        try:
            from pathlib import Path
            
            config_dir = Path(__file__).parent.parent / "config"
            flags_file = config_dir / "feature_flags.json"
            
            if flags_file.exists():
                with open(flags_file, 'r') as f:
                    flags_data = json.load(f)
                    self._parse_flags_data(flags_data)
                    logger.info(f"Loaded feature flags from {flags_file}")
            else:
                logger.warning(f"Feature flags file not found: {flags_file}")
                self._load_default_flags()
                
        except Exception as e:
            logger.error(f"Error loading feature flags from file: {str(e)}")
            self._load_default_flags()
    
    def _load_flags_from_keyvault(self):
        """Load feature flags from Key Vault"""
        try:
            flags_json = get_secret("feature-flags")
            if flags_json:
                flags_data = json.loads(flags_json)
                self._parse_flags_data(flags_data)
                logger.info("Loaded feature flags from Key Vault")
            else:
                logger.warning("Feature flags not found in Key Vault")
                self._load_default_flags()
                
        except Exception as e:
            logger.error(f"Error loading feature flags from Key Vault: {str(e)}")
            self._load_default_flags()
    
    def _load_flags_from_database(self):
        """Load feature flags from database"""
        # TODO: Implement database loading
        logger.warning("Database feature flag loading not implemented yet")
        self._load_default_flags()
    
    def _load_default_flags(self):
        """Load default feature flags"""
        default_flags = {
            "enhanced_security": {
                "description": "Enable enhanced security features",
                "status": "active",
                "default_value": True,
                "rules": [
                    {
                        "strategy": "boolean",
                        "parameters": {"enabled": True}
                    }
                ]
            },
            "performance_monitoring": {
                "description": "Enable advanced performance monitoring",
                "status": "active",
                "default_value": True,
                "rules": [
                    {
                        "strategy": "boolean",
                        "parameters": {"enabled": True}
                    }
                ]
            },
            "new_ui_components": {
                "description": "Enable new UI components",
                "status": "testing",
                "default_value": False,
                "rules": [
                    {
                        "strategy": "percentage",
                        "parameters": {"percentage": 10}
                    }
                ]
            },
            "advanced_caching": {
                "description": "Enable advanced caching mechanisms",
                "status": "active",
                "default_value": False,
                "rules": [
                    {
                        "strategy": "attribute",
                        "parameters": {
                            "attribute": "role",
                            "values": ["admin", "power_user"]
                        }
                    }
                ]
            },
            "beta_features": {
                "description": "Enable beta features for testing",
                "status": "testing",
                "default_value": False,
                "rules": [
                    {
                        "strategy": "user_list",
                        "parameters": {
                            "users": ["admin", "test_user", "beta_tester"]
                        }
                    }
                ]
            }
        }
        
        self._parse_flags_data(default_flags)
        logger.info("Loaded default feature flags")
    
    def _parse_flags_data(self, flags_data: Dict[str, Any]):
        """Parse feature flags data into FeatureFlag objects"""
        with self._lock:
            self._flags.clear()
            
            for flag_name, flag_config in flags_data.items():
                try:
                    # Parse rules
                    rules = []
                    for rule_config in flag_config.get("rules", []):
                        strategy = FeatureFlagStrategy(rule_config["strategy"])
                        parameters = rule_config.get("parameters", {})
                        weight = rule_config.get("weight", 100)
                        rules.append(FeatureFlagRule(strategy, parameters, weight))
                    
                    # Create feature flag
                    flag = FeatureFlag(
                        name=flag_name,
                        description=flag_config.get("description", ""),
                        status=FeatureFlagStatus(flag_config.get("status", "active")),
                        default_value=flag_config.get("default_value", False),
                        rules=rules,
                        tags=flag_config.get("tags", []),
                        metadata=flag_config.get("metadata", {})
                    )
                    
                    self._flags[flag_name] = flag
                    
                    # Initialize usage metrics
                    if flag_name not in self._usage_metrics:
                        self._usage_metrics[flag_name] = FeatureUsageMetrics(flag_name)
                    
                except Exception as e:
                    logger.error(f"Error parsing feature flag '{flag_name}': {str(e)}")
    
    def is_feature_enabled(self, feature_name: str, user_context: Optional[UserContext] = None) -> bool:
        """
        Check if a feature is enabled for the given user context
        
        Args:
            feature_name: Name of the feature flag
            user_context: User context for evaluation
            
        Returns:
            bool: True if feature is enabled, False otherwise
        """
        start_time = datetime.now()
        
        try:
            evaluation = self._evaluate_feature_flag(feature_name, user_context)
            
            # Track usage metrics
            self._track_usage(feature_name, evaluation, start_time)
            
            return evaluation.enabled
            
        except Exception as e:
            logger.error(f"Error evaluating feature flag '{feature_name}': {str(e)}")
            # Return default value on error
            return self._get_default_value(feature_name)
    
    def _evaluate_feature_flag(self, feature_name: str, user_context: Optional[UserContext] = None) -> FeatureFlagEvaluation:
        """Evaluate a feature flag with caching"""
        # Check cache first
        cache_key = self._get_cache_key(feature_name, user_context)
        cached_evaluation = self._get_cached_evaluation(cache_key)
        
        if cached_evaluation:
            return cached_evaluation
        
        # Evaluate feature flag
        evaluation = self._perform_evaluation(feature_name, user_context)
        
        # Cache the result
        self._cache_evaluation(cache_key, evaluation)
        
        return evaluation
    
    def _perform_evaluation(self, feature_name: str, user_context: Optional[UserContext] = None) -> FeatureFlagEvaluation:
        """Perform actual feature flag evaluation"""
        with self._lock:
            # Get feature flag
            if feature_name not in self._flags:
                logger.warning(f"Feature flag '{feature_name}' not found")
                return FeatureFlagEvaluation(
                    flag_name=feature_name,
                    enabled=False,
                    user_context=user_context
                )
            
            flag = self._flags[feature_name]
            
            # Check if flag is active
            if flag.status != FeatureFlagStatus.ACTIVE:
                return FeatureFlagEvaluation(
                    flag_name=feature_name,
                    enabled=flag.default_value,
                    rule_matched="default_inactive",
                    user_context=user_context
                )
            
            # Evaluate rules in order
            for rule in flag.rules:
                try:
                    evaluator = self._evaluators.get(rule.strategy)
                    if evaluator:
                        result = evaluator(rule, user_context)
                        if result is not None:
                            return FeatureFlagEvaluation(
                                flag_name=feature_name,
                                enabled=result,
                                rule_matched=rule.strategy.value,
                                user_context=user_context,
                                metadata=rule.parameters
                            )
                except Exception as e:
                    logger.error(f"Error evaluating rule {rule.strategy.value} for flag '{feature_name}': {str(e)}")
                    continue
            
            # No rules matched, return default value
            return FeatureFlagEvaluation(
                flag_name=feature_name,
                enabled=flag.default_value,
                rule_matched="default",
                user_context=user_context
            )
    
    def _evaluate_boolean(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate boolean rule"""
        return rule.parameters.get("enabled", False)
    
    def _evaluate_percentage(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate percentage-based rule"""
        percentage = rule.parameters.get("percentage", 0)
        
        if user_context and user_context.user_id:
            # Use consistent hashing for stable results
            hash_input = f"{rule.parameters.get('seed', 'default')}{user_context.user_id}"
            hash_value = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
            user_percentage = (hash_value % 100) + 1
            return user_percentage <= percentage
        else:
            # Random evaluation for anonymous users
            return random.randint(1, 100) <= percentage
    
    def _evaluate_user_list(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate user list rule"""
        if not user_context or not user_context.user_id:
            return None
        
        allowed_users = rule.parameters.get("users", [])
        return user_context.user_id in allowed_users
    
    def _evaluate_attribute(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate attribute-based rule"""
        if not user_context:
            return None
        
        attribute_name = rule.parameters.get("attribute")
        allowed_values = rule.parameters.get("values", [])
        
        if not attribute_name:
            return None
        
        # Check user attributes
        user_value = None
        if attribute_name == "role":
            user_value = user_context.role
        elif attribute_name == "organization_id":
            user_value = user_context.organization_id
        else:
            user_value = user_context.attributes.get(attribute_name)
        
        return user_value in allowed_values
    
    def _evaluate_time_window(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate time window rule"""
        now = datetime.now()
        
        start_time_str = rule.parameters.get("start_time")
        end_time_str = rule.parameters.get("end_time")
        
        if start_time_str:
            start_time = datetime.fromisoformat(start_time_str)
            if now < start_time:
                return False
        
        if end_time_str:
            end_time = datetime.fromisoformat(end_time_str)
            if now > end_time:
                return False
        
        return True
    
    def _evaluate_canary(self, rule: FeatureFlagRule, user_context: Optional[UserContext] = None) -> Optional[bool]:
        """Evaluate canary deployment rule"""
        # Canary deployment based on organization or user attributes
        canary_percentage = rule.parameters.get("percentage", 5)
        canary_organizations = rule.parameters.get("organizations", [])
        canary_users = rule.parameters.get("users", [])
        
        if user_context:
            # Check if user is in canary user list
            if user_context.user_id in canary_users:
                return True
            
            # Check if organization is in canary list
            if user_context.organization_id in canary_organizations:
                return True
            
            # Use percentage-based evaluation for other users
            if user_context.user_id:
                hash_input = f"canary_{user_context.user_id}"
                hash_value = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
                user_percentage = (hash_value % 100) + 1
                return user_percentage <= canary_percentage
        
        return False
    
    def _get_cache_key(self, feature_name: str, user_context: Optional[UserContext] = None) -> str:
        """Generate cache key for feature flag evaluation"""
        if user_context:
            context_hash = hashlib.md5(
                f"{user_context.user_id}_{user_context.organization_id}_{user_context.role}".encode()
            ).hexdigest()[:8]
            return f"{feature_name}_{context_hash}"
        else:
            return f"{feature_name}_anonymous"
    
    def _get_cached_evaluation(self, cache_key: str) -> Optional[FeatureFlagEvaluation]:
        """Get cached evaluation if still valid"""
        if cache_key in self._evaluation_cache:
            cached_data = self._evaluation_cache[cache_key]
            evaluation = cached_data.get("evaluation")
            cached_time = cached_data.get("cached_time")
            
            if evaluation and cached_time and datetime.now() - cached_time < self._cache_ttl:
                return evaluation
        
        return None
    
    def _cache_evaluation(self, cache_key: str, evaluation: FeatureFlagEvaluation):
        """Cache feature flag evaluation"""
        self._evaluation_cache[cache_key] = {
            "evaluation": evaluation,
            "cached_time": datetime.now()
        }
    
    def _track_usage(self, feature_name: str, evaluation: FeatureFlagEvaluation, start_time: datetime):
        """Track feature flag usage metrics"""
        try:
            with self._lock:
                if feature_name not in self._usage_metrics:
                    self._usage_metrics[feature_name] = FeatureUsageMetrics(feature_name)
                
                metrics = self._usage_metrics[feature_name]
                metrics.total_evaluations += 1
                
                if evaluation.enabled:
                    metrics.enabled_count += 1
                else:
                    metrics.disabled_count += 1
                
                metrics.last_evaluation = evaluation.evaluation_time
                
                # Track evaluation time
                evaluation_time_ms = (datetime.now() - start_time).total_seconds() * 1000
                metrics.evaluation_times.append(evaluation_time_ms)
                
                # Keep only recent evaluation times (last 1000)
                if len(metrics.evaluation_times) > 1000:
                    metrics.evaluation_times = metrics.evaluation_times[-1000:]
                
                # Track unique users (simplified)
                if evaluation.user_context and evaluation.user_context.user_id:
                    # In a real implementation, you'd use a more sophisticated unique user tracking
                    metrics.unique_users = len(set([evaluation.user_context.user_id]))
                
        except Exception as e:
            logger.error(f"Error tracking usage for feature '{feature_name}': {str(e)}")
    
    def _get_default_value(self, feature_name: str) -> bool:
        """Get default value for a feature flag"""
        if feature_name in self._flags:
            return self._flags[feature_name].default_value
        return False
    
    def get_feature_configuration(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """
        Get feature flag configuration
        
        Args:
            feature_name: Name of the feature flag
            
        Returns:
            Dict: Feature flag configuration or None if not found
        """
        with self._lock:
            if feature_name not in self._flags:
                return None
            
            flag = self._flags[feature_name]
            return {
                "name": flag.name,
                "description": flag.description,
                "status": flag.status.value,
                "default_value": flag.default_value,
                "rules": [
                    {
                        "strategy": rule.strategy.value,
                        "parameters": rule.parameters,
                        "weight": rule.weight
                    }
                    for rule in flag.rules
                ],
                "created_at": flag.created_at.isoformat(),
                "updated_at": flag.updated_at.isoformat(),
                "tags": flag.tags,
                "metadata": flag.metadata
            }
    
    def track_feature_usage(self, feature_name: str, user_id: str, action: str = "accessed", metadata: Optional[Dict[str, Any]] = None):
        """
        Track feature usage for analytics
        
        Args:
            feature_name: Name of the feature
            user_id: User ID
            action: Action performed (accessed, used, clicked, etc.)
            metadata: Additional metadata
        """
        try:
            # In a real implementation, you'd send this to an analytics service
            usage_event = {
                "feature_name": feature_name,
                "user_id": user_id,
                "action": action,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            logger.info(f"Feature usage tracked: {json.dumps(usage_event)}")
            
        except Exception as e:
            logger.error(f"Error tracking feature usage: {str(e)}")
    
    def get_usage_metrics(self, feature_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get usage metrics for feature flags
        
        Args:
            feature_name: Specific feature name (optional)
            
        Returns:
            Dict: Usage metrics
        """
        with self._lock:
            if feature_name:
                if feature_name in self._usage_metrics:
                    metrics = self._usage_metrics[feature_name]
                    avg_evaluation_time = sum(metrics.evaluation_times) / len(metrics.evaluation_times) if metrics.evaluation_times else 0
                    
                    return {
                        "flag_name": metrics.flag_name,
                        "total_evaluations": metrics.total_evaluations,
                        "enabled_count": metrics.enabled_count,
                        "disabled_count": metrics.disabled_count,
                        "enabled_percentage": (metrics.enabled_count / metrics.total_evaluations * 100) if metrics.total_evaluations > 0 else 0,
                        "unique_users": metrics.unique_users,
                        "last_evaluation": metrics.last_evaluation.isoformat() if metrics.last_evaluation else None,
                        "average_evaluation_time_ms": avg_evaluation_time
                    }
                else:
                    return {}
            else:
                # Return metrics for all flags
                all_metrics = {}
                for flag_name, metrics in self._usage_metrics.items():
                    avg_evaluation_time = sum(metrics.evaluation_times) / len(metrics.evaluation_times) if metrics.evaluation_times else 0
                    
                    all_metrics[flag_name] = {
                        "total_evaluations": metrics.total_evaluations,
                        "enabled_count": metrics.enabled_count,
                        "disabled_count": metrics.disabled_count,
                        "enabled_percentage": (metrics.enabled_count / metrics.total_evaluations * 100) if metrics.total_evaluations > 0 else 0,
                        "unique_users": metrics.unique_users,
                        "last_evaluation": metrics.last_evaluation.isoformat() if metrics.last_evaluation else None,
                        "average_evaluation_time_ms": avg_evaluation_time
                    }
                
                return all_metrics
    
    def reload_feature_flags(self):
        """Reload feature flags from source"""
        logger.info("Reloading feature flags...")
        self._load_feature_flags()
        
        # Clear cache after reload
        with self._lock:
            self._evaluation_cache.clear()
    
    def get_all_feature_flags(self) -> Dict[str, Dict[str, Any]]:
        """Get all feature flags configuration"""
        with self._lock:
            return {
                flag_name: self.get_feature_configuration(flag_name)
                for flag_name in self._flags.keys()
            }

# Global feature flag service instance
_feature_flag_service: Optional[FeatureFlagService] = None

def get_feature_flag_service() -> FeatureFlagService:
    """Get the global feature flag service instance"""
    global _feature_flag_service
    if _feature_flag_service is None:
        config_source = os.environ.get("FEATURE_FLAG_SOURCE", "file")
        _feature_flag_service = FeatureFlagService(config_source)
    return _feature_flag_service

def is_feature_enabled(feature_name: str, user_context: Optional[UserContext] = None) -> bool:
    """Convenience function to check if a feature is enabled"""
    return get_feature_flag_service().is_feature_enabled(feature_name, user_context)

def track_feature_usage(feature_name: str, user_id: str, action: str = "accessed", metadata: Optional[Dict[str, Any]] = None):
    """Convenience function to track feature usage"""
    get_feature_flag_service().track_feature_usage(feature_name, user_id, action, metadata)