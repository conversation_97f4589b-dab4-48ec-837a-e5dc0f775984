"""
Task Sequence Failure Handler Module

This module provides comprehensive failure handling for sequential task processing.
It implements fallback mechanisms, partial completion handling, and recovery strategies
for the SFDC service task sequence (authenticate → health_check → metadata_extraction → PMD).

Features:
- Fallback mechanisms for failed tasks in sequence
- Partial completion handling (e.g., if PMD fails but other tasks succeed)
- Task sequence recovery and retry strategies
- Graceful degradation for sequential task processing
- Integration with enhanced error handling framework
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable
from enum import Enum
from dataclasses import dataclass

# Import enhanced error handling
from src.shared.error_handler import (
    get_enhanced_error_handler,
    EnhancedRetryPolicy,
    RetryConfig,
    RetryStrategy,
    ErrorCategory
)

logger = logging.getLogger(__name__)

class TaskSequenceState(Enum):
    """Task sequence states"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIALLY_COMPLETED = "partially_completed"
    RECOVERING = "recovering"
    CANCELLED = "cancelled"

class TaskState(Enum):
    """Individual task states"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"

class FailureStrategy(Enum):
    """Failure handling strategies"""
    FAIL_FAST = "fail_fast"  # Stop on first failure
    CONTINUE_ON_FAILURE = "continue_on_failure"  # Continue with remaining tasks
    RETRY_FAILED = "retry_failed"  # Retry failed tasks
    FALLBACK_TO_ALTERNATIVE = "fallback_to_alternative"  # Use alternative task
    PARTIAL_SUCCESS = "partial_success"  # Accept partial completion

@dataclass
class TaskDefinition:
    """Definition of a task in the sequence"""
    task_type: str
    required: bool = True
    dependencies: List[str] = None
    fallback_task: Optional[str] = None
    retry_config: Optional[RetryConfig] = None
    timeout_seconds: int = 300
    failure_strategy: FailureStrategy = FailureStrategy.FAIL_FAST
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

@dataclass
class TaskResult:
    """Result of a task execution"""
    task_type: str
    task_id: Optional[str]
    state: TaskState
    start_time: datetime
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    error_category: Optional[str] = None
    retry_count: int = 0
    output_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.output_data is None:
            self.output_data = {}

@dataclass
class SequenceResult:
    """Result of a sequence execution"""
    execution_log_id: str
    sequence_type: str
    state: TaskSequenceState
    start_time: datetime
    end_time: Optional[datetime] = None
    task_results: List[TaskResult] = None
    partial_success_data: Dict[str, Any] = None
    recovery_attempts: int = 0
    
    def __post_init__(self):
        if self.task_results is None:
            self.task_results = []
        if self.partial_success_data is None:
            self.partial_success_data = {}

class TaskSequenceFailureHandler:
    """Comprehensive failure handler for task sequences"""
    
    def __init__(self):
        """Initialize the failure handler"""
        self.error_handler = get_enhanced_error_handler()
        self.sequence_definitions = self._initialize_sequence_definitions()
        self.active_sequences: Dict[str, SequenceResult] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        self.recovery_strategies: Dict[str, Callable] = {}
        
        # Initialize built-in handlers
        self._initialize_fallback_handlers()
        self._initialize_recovery_strategies()
        
        logger.info("Task sequence failure handler initialized")
    
    def _initialize_sequence_definitions(self) -> Dict[str, List[TaskDefinition]]:
        """Initialize predefined sequence definitions"""
        
        # SFDC scan sequence definition
        sfdc_scan_sequence = [
            TaskDefinition(
                task_type="sfdc_authenticate",
                required=True,
                dependencies=[],
                retry_config=RetryConfig(
                    max_attempts=3,
                    base_delay=2.0,
                    strategy=RetryStrategy.EXPONENTIAL_BACKOFF
                ),
                timeout_seconds=60,
                failure_strategy=FailureStrategy.RETRY_FAILED
            ),
            TaskDefinition(
                task_type="health_check",
                required=True,
                dependencies=["sfdc_authenticate"],
                fallback_task="basic_health_check",
                retry_config=RetryConfig(
                    max_attempts=2,
                    base_delay=1.0,
                    strategy=RetryStrategy.LINEAR_BACKOFF
                ),
                timeout_seconds=120,
                failure_strategy=FailureStrategy.FALLBACK_TO_ALTERNATIVE
            ),
            TaskDefinition(
                task_type="metadata_extraction",
                required=True,
                dependencies=["sfdc_authenticate", "health_check"],
                fallback_task="basic_metadata_extraction",
                retry_config=RetryConfig(
                    max_attempts=2,
                    base_delay=3.0,
                    strategy=RetryStrategy.EXPONENTIAL_BACKOFF
                ),
                timeout_seconds=600,
                failure_strategy=FailureStrategy.CONTINUE_ON_FAILURE
            ),
            TaskDefinition(
                task_type="pmd_apex_security",
                required=False,  # PMD is optional
                dependencies=["metadata_extraction"],
                retry_config=RetryConfig(
                    max_attempts=2,
                    base_delay=5.0,
                    strategy=RetryStrategy.EXPONENTIAL_BACKOFF
                ),
                timeout_seconds=900,
                failure_strategy=FailureStrategy.PARTIAL_SUCCESS
            )
        ]
        
        return {
            "sfdc_scan": sfdc_scan_sequence,
            "sfdc_security_analysis": sfdc_scan_sequence,  # Same sequence
        }
    
    def _initialize_fallback_handlers(self):
        """Initialize fallback task handlers"""
        
        def basic_health_check_fallback(execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
            """Fallback for health check - basic connectivity test"""
            try:
                logger.info(f"Executing basic health check fallback for {execution_log_id}")
                
                # Simulate basic health check
                result = TaskResult(
                    task_type="basic_health_check",
                    task_id=str(uuid.uuid4()),
                    state=TaskState.COMPLETED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    output_data={
                        "health_status": "basic_check_passed",
                        "fallback_used": True,
                        "message": "Basic connectivity verified"
                    }
                )
                
                logger.info(f"Basic health check fallback completed for {execution_log_id}")
                return result
                
            except Exception as e:
                logger.error(f"Basic health check fallback failed: {e}")
                return TaskResult(
                    task_type="basic_health_check",
                    task_id=str(uuid.uuid4()),
                    state=TaskState.FAILED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    error_message=str(e),
                    error_category=ErrorCategory.PERMANENT
                )
        
        def basic_metadata_extraction_fallback(execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
            """Fallback for metadata extraction - essential metadata only"""
            try:
                logger.info(f"Executing basic metadata extraction fallback for {execution_log_id}")
                
                # Simulate basic metadata extraction
                result = TaskResult(
                    task_type="basic_metadata_extraction",
                    task_id=str(uuid.uuid4()),
                    state=TaskState.COMPLETED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    output_data={
                        "metadata_types": ["Profile", "PermissionSet"],
                        "fallback_used": True,
                        "message": "Essential metadata extracted",
                        "limited_scope": True
                    }
                )
                
                logger.info(f"Basic metadata extraction fallback completed for {execution_log_id}")
                return result
                
            except Exception as e:
                logger.error(f"Basic metadata extraction fallback failed: {e}")
                return TaskResult(
                    task_type="basic_metadata_extraction",
                    task_id=str(uuid.uuid4()),
                    state=TaskState.FAILED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    error_message=str(e),
                    error_category=ErrorCategory.PERMANENT
                )
        
        self.fallback_handlers = {
            "basic_health_check": basic_health_check_fallback,
            "basic_metadata_extraction": basic_metadata_extraction_fallback
        }
    
    def _initialize_recovery_strategies(self):
        """Initialize recovery strategies for different failure scenarios"""
        
        def authentication_recovery(execution_log_id: str, failed_task: TaskResult, 
                                  params: Dict[str, Any]) -> Tuple[bool, str]:
            """Recovery strategy for authentication failures"""
            try:
                logger.info(f"Attempting authentication recovery for {execution_log_id}")
                
                # Check if credentials need refresh
                if "token" in params and "refresh_token" in params:
                    # Attempt token refresh
                    logger.info("Attempting token refresh for authentication recovery")
                    # In real implementation, this would call credential refresh service
                    return True, "Token refresh attempted"
                
                # Check for alternative authentication methods
                if "username" in params and "password" in params:
                    logger.info("Attempting username/password authentication recovery")
                    return True, "Alternative authentication method attempted"
                
                return False, "No recovery options available for authentication"
                
            except Exception as e:
                logger.error(f"Authentication recovery failed: {e}")
                return False, f"Recovery failed: {str(e)}"
        
        def network_recovery(execution_log_id: str, failed_task: TaskResult, 
                           params: Dict[str, Any]) -> Tuple[bool, str]:
            """Recovery strategy for network-related failures"""
            try:
                logger.info(f"Attempting network recovery for {execution_log_id}")
                
                # Check if alternative endpoint is available
                if "instance_url" in params:
                    # Try alternative endpoint or retry with backoff
                    logger.info("Network recovery: using exponential backoff")
                    return True, "Network recovery with backoff attempted"
                
                return False, "No network recovery options available"
                
            except Exception as e:
                logger.error(f"Network recovery failed: {e}")
                return False, f"Recovery failed: {str(e)}"
        
        def resource_recovery(execution_log_id: str, failed_task: TaskResult, 
                            params: Dict[str, Any]) -> Tuple[bool, str]:
            """Recovery strategy for resource exhaustion failures"""
            try:
                logger.info(f"Attempting resource recovery for {execution_log_id}")
                
                # Reduce scope or use alternative approach
                if failed_task.task_type == "metadata_extraction":
                    logger.info("Resource recovery: reducing metadata extraction scope")
                    return True, "Reduced scope metadata extraction attempted"
                
                if failed_task.task_type == "pmd_apex_security":
                    logger.info("Resource recovery: skipping PMD analysis")
                    return True, "PMD analysis skipped due to resource constraints"
                
                return False, "No resource recovery options available"
                
            except Exception as e:
                logger.error(f"Resource recovery failed: {e}")
                return False, f"Recovery failed: {str(e)}"
        
        self.recovery_strategies = {
            ErrorCategory.PERMANENT: authentication_recovery,
            ErrorCategory.TRANSIENT: network_recovery,
            ErrorCategory.RESOURCE_EXHAUSTED: resource_recovery,
            ErrorCategory.TIMEOUT: network_recovery
        }
    
    def start_sequence(self, sequence_type: str, execution_log_id: str, 
                      params: Dict[str, Any]) -> SequenceResult:
        """Start a new task sequence with failure handling"""
        try:
            if sequence_type not in self.sequence_definitions:
                raise ValueError(f"Unknown sequence type: {sequence_type}")
            
            sequence_result = SequenceResult(
                execution_log_id=execution_log_id,
                sequence_type=sequence_type,
                state=TaskSequenceState.PENDING,
                start_time=datetime.now()
            )
            
            self.active_sequences[execution_log_id] = sequence_result
            
            logger.info(f"Started sequence {sequence_type} with execution_log_id: {execution_log_id}")
            return sequence_result
            
        except Exception as e:
            logger.error(f"Error starting sequence: {e}")
            raise
    
    def execute_task_with_failure_handling(self, execution_log_id: str, task_def: TaskDefinition,
                                         params: Dict[str, Any], 
                                         task_executor: Callable) -> TaskResult:
        """Execute a single task with comprehensive failure handling"""
        
        sequence_result = self.active_sequences.get(execution_log_id)
        if not sequence_result:
            raise ValueError(f"No active sequence found for execution_log_id: {execution_log_id}")
        
        task_result = TaskResult(
            task_type=task_def.task_type,
            task_id=str(uuid.uuid4()),
            state=TaskState.PENDING,
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"Executing task {task_def.task_type} for sequence {execution_log_id}")
            
            # Check dependencies
            if not self._check_dependencies(task_def, sequence_result):
                task_result.state = TaskState.SKIPPED
                task_result.end_time = datetime.now()
                task_result.error_message = "Dependencies not met"
                return task_result
            
            # Execute task with retry policy
            task_result.state = TaskState.RUNNING
            
            if task_def.retry_config:
                retry_policy = EnhancedRetryPolicy(task_def.retry_config)
                result = retry_policy.execute_with_retry(
                    task_executor, 
                    task_def.task_type,
                    execution_log_id, 
                    params
                )
            else:
                result = task_executor(execution_log_id, params)
            
            # Process successful result
            task_result.state = TaskState.COMPLETED
            task_result.end_time = datetime.now()
            task_result.output_data = result if isinstance(result, dict) else {"result": result}
            
            logger.info(f"Task {task_def.task_type} completed successfully for {execution_log_id}")
            
        except Exception as e:
            logger.error(f"Task {task_def.task_type} failed for {execution_log_id}: {e}")
            
            task_result.state = TaskState.FAILED
            task_result.end_time = datetime.now()
            task_result.error_message = str(e)
            task_result.error_category = self.error_handler.categorize_error(e)
            
            # Apply failure strategy
            task_result = self._apply_failure_strategy(task_def, task_result, execution_log_id, params)
        
        return task_result
    
    def _check_dependencies(self, task_def: TaskDefinition, sequence_result: SequenceResult) -> bool:
        """Check if task dependencies are satisfied"""
        if not task_def.dependencies:
            return True
        
        completed_tasks = {
            result.task_type for result in sequence_result.task_results 
            if result.state == TaskState.COMPLETED
        }
        
        return all(dep in completed_tasks for dep in task_def.dependencies)
    
    def _apply_failure_strategy(self, task_def: TaskDefinition, task_result: TaskResult,
                              execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
        """Apply failure strategy for failed task"""
        
        try:
            if task_def.failure_strategy == FailureStrategy.RETRY_FAILED:
                return self._retry_failed_task(task_def, task_result, execution_log_id, params)
            
            elif task_def.failure_strategy == FailureStrategy.FALLBACK_TO_ALTERNATIVE:
                return self._execute_fallback_task(task_def, task_result, execution_log_id, params)
            
            elif task_def.failure_strategy == FailureStrategy.CONTINUE_ON_FAILURE:
                logger.info(f"Continuing sequence despite failure in {task_def.task_type}")
                return task_result
            
            elif task_def.failure_strategy == FailureStrategy.PARTIAL_SUCCESS:
                return self._handle_partial_success(task_def, task_result, execution_log_id, params)
            
            else:  # FAIL_FAST
                logger.error(f"Failing fast due to error in {task_def.task_type}")
                return task_result
                
        except Exception as e:
            logger.error(f"Error applying failure strategy: {e}")
            return task_result
    
    def _retry_failed_task(self, task_def: TaskDefinition, task_result: TaskResult,
                          execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
        """Retry a failed task with recovery strategy"""
        
        # Attempt recovery first
        if task_result.error_category in self.recovery_strategies:
            recovery_func = self.recovery_strategies[task_result.error_category]
            can_recover, recovery_message = recovery_func(execution_log_id, task_result, params)
            
            if can_recover:
                logger.info(f"Recovery successful for {task_def.task_type}: {recovery_message}")
                task_result.output_data = task_result.output_data or {}
                task_result.output_data["recovery_applied"] = recovery_message
                # Mark for retry
                task_result.state = TaskState.RETRYING
                return task_result
        
        logger.warning(f"No recovery available for {task_def.task_type}, keeping failed state")
        return task_result
    
    def _execute_fallback_task(self, task_def: TaskDefinition, task_result: TaskResult,
                             execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
        """Execute fallback task if available"""
        
        if not task_def.fallback_task or task_def.fallback_task not in self.fallback_handlers:
            logger.warning(f"No fallback available for {task_def.task_type}")
            return task_result
        
        try:
            logger.info(f"Executing fallback task {task_def.fallback_task} for {task_def.task_type}")
            
            fallback_handler = self.fallback_handlers[task_def.fallback_task]
            fallback_result = fallback_handler(execution_log_id, params)
            
            if fallback_result.state == TaskState.COMPLETED:
                logger.info(f"Fallback task {task_def.fallback_task} completed successfully")
                # Update original task result with fallback data
                task_result.state = TaskState.COMPLETED
                task_result.output_data = fallback_result.output_data
                task_result.output_data["fallback_used"] = True
                task_result.output_data["original_error"] = task_result.error_message
            else:
                logger.error(f"Fallback task {task_def.fallback_task} also failed")
            
            return task_result
            
        except Exception as e:
            logger.error(f"Error executing fallback task: {e}")
            return task_result
    
    def _handle_partial_success(self, task_def: TaskDefinition, task_result: TaskResult,
                              execution_log_id: str, params: Dict[str, Any]) -> TaskResult:
        """Handle partial success scenario"""
        
        sequence_result = self.active_sequences[execution_log_id]
        
        # Check if enough tasks have completed for partial success
        completed_required_tasks = sum(
            1 for result in sequence_result.task_results
            if result.state == TaskState.COMPLETED and 
            self._is_task_required(result.task_type, sequence_result.sequence_type)
        )
        
        total_required_tasks = sum(
            1 for task_def in self.sequence_definitions[sequence_result.sequence_type]
            if task_def.required
        )
        
        # If most required tasks completed, accept partial success
        if completed_required_tasks >= (total_required_tasks * 0.7):  # 70% threshold
            logger.info(f"Accepting partial success for {task_def.task_type} - {completed_required_tasks}/{total_required_tasks} required tasks completed")
            
            task_result.state = TaskState.COMPLETED
            task_result.output_data = task_result.output_data or {}
            task_result.output_data.update({
                "partial_success": True,
                "original_error": task_result.error_message,
                "completion_rate": completed_required_tasks / total_required_tasks
            })
            
            # Update sequence partial success data
            sequence_result.partial_success_data[task_def.task_type] = {
                "error": task_result.error_message,
                "completion_rate": completed_required_tasks / total_required_tasks,
                "timestamp": datetime.now().isoformat()
            }
        
        return task_result
    
    def _is_task_required(self, task_type: str, sequence_type: str) -> bool:
        """Check if a task is required in the sequence"""
        sequence_def = self.sequence_definitions.get(sequence_type, [])
        for task_def in sequence_def:
            if task_def.task_type == task_type:
                return task_def.required
        return False
    
    def complete_sequence(self, execution_log_id: str) -> SequenceResult:
        """Complete a task sequence and determine final state"""
        
        sequence_result = self.active_sequences.get(execution_log_id)
        if not sequence_result:
            raise ValueError(f"No active sequence found for execution_log_id: {execution_log_id}")
        
        sequence_result.end_time = datetime.now()
        
        # Analyze task results to determine sequence state
        failed_tasks = [r for r in sequence_result.task_results if r.state == TaskState.FAILED]
        completed_tasks = [r for r in sequence_result.task_results if r.state == TaskState.COMPLETED]
        
        # Check if any required tasks failed
        sequence_def = self.sequence_definitions[sequence_result.sequence_type]
        failed_required_tasks = [
            r for r in failed_tasks 
            if any(td.task_type == r.task_type and td.required for td in sequence_def)
        ]
        
        if not failed_tasks:
            sequence_result.state = TaskSequenceState.COMPLETED
        elif failed_required_tasks and not sequence_result.partial_success_data:
            sequence_result.state = TaskSequenceState.FAILED
        elif sequence_result.partial_success_data or (completed_tasks and failed_tasks):
            sequence_result.state = TaskSequenceState.PARTIALLY_COMPLETED
        else:
            sequence_result.state = TaskSequenceState.FAILED
        
        logger.info(f"Sequence {execution_log_id} completed with state: {sequence_result.state}")
        
        return sequence_result
    
    def get_sequence_status(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a sequence"""
        
        sequence_result = self.active_sequences.get(execution_log_id)
        if not sequence_result:
            return None
        
        return {
            "execution_log_id": execution_log_id,
            "sequence_type": sequence_result.sequence_type,
            "state": sequence_result.state.value,
            "start_time": sequence_result.start_time.isoformat(),
            "end_time": sequence_result.end_time.isoformat() if sequence_result.end_time else None,
            "task_results": [
                {
                    "task_type": tr.task_type,
                    "task_id": tr.task_id,
                    "state": tr.state.value,
                    "start_time": tr.start_time.isoformat(),
                    "end_time": tr.end_time.isoformat() if tr.end_time else None,
                    "error_message": tr.error_message,
                    "retry_count": tr.retry_count,
                    "has_output": bool(tr.output_data)
                }
                for tr in sequence_result.task_results
            ],
            "partial_success_data": sequence_result.partial_success_data,
            "recovery_attempts": sequence_result.recovery_attempts
        }
    
    def register_fallback_handler(self, task_type: str, handler: Callable):
        """Register a custom fallback handler"""
        self.fallback_handlers[task_type] = handler
        logger.info(f"Registered fallback handler for task type: {task_type}")
    
    def register_recovery_strategy(self, error_category: str, strategy: Callable):
        """Register a custom recovery strategy"""
        self.recovery_strategies[error_category] = strategy
        logger.info(f"Registered recovery strategy for error category: {error_category}")

# Global instance
_task_sequence_failure_handler = None

def get_task_sequence_failure_handler() -> TaskSequenceFailureHandler:
    """Get global task sequence failure handler instance"""
    global _task_sequence_failure_handler
    if _task_sequence_failure_handler is None:
        _task_sequence_failure_handler = TaskSequenceFailureHandler()
    return _task_sequence_failure_handler