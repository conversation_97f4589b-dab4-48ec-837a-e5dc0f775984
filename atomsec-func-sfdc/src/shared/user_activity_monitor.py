"""
User Activity Monitoring Service

This module provides comprehensive user activity monitoring including:
- Real-time activity tracking
- Suspicious behavior detection
- Activity pattern analysis
- Security event correlation

Requirements addressed: 1.1
"""

import logging
import json
import time
from typing import Dict, Any, Optional, List, Set, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import statistics
import re

from src.shared.advanced_auth_service import get_advanced_auth_service
from src.shared.monitoring import get_monitoring_service

logger = logging.getLogger(__name__)


class ActivityType(Enum):
    """Types of user activities"""
    LOGIN = "login"
    LOGOUT = "logout"
    API_ACCESS = "api_access"
    DATA_ACCESS = "data_access"
    CONFIGURATION_CHANGE = "configuration_change"
    SECURITY_EVENT = "security_event"
    FILE_UPLOAD = "file_upload"
    FILE_DOWNLOAD = "file_download"
    ADMIN_ACTION = "admin_action"
    FAILED_ATTEMPT = "failed_attempt"


class RiskLevel(Enum):
    """Risk levels for activities"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ActivityEvent:
    """User activity event data structure"""
    event_id: str
    user_id: str
    session_id: str
    activity_type: ActivityType
    timestamp: datetime
    ip_address: str
    user_agent: str
    endpoint: str
    method: str
    status_code: int
    response_time_ms: float
    request_size_bytes: int = 0
    response_size_bytes: int = 0
    success: bool = True
    error_message: Optional[str] = None
    risk_score: float = 0.0
    risk_level: RiskLevel = RiskLevel.LOW
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ActivityPattern:
    """Activity pattern analysis result"""
    pattern_type: str
    description: str
    frequency: int
    time_window_hours: int
    risk_score: float
    first_occurrence: datetime
    last_occurrence: datetime
    affected_endpoints: List[str]
    metadata: Dict[str, Any] = None


@dataclass
class BehaviorBaseline:
    """User behavior baseline"""
    user_id: str
    typical_hours: List[int]  # Hours of day user is typically active
    typical_endpoints: Set[str]
    typical_ip_ranges: Set[str]
    average_session_duration_minutes: float
    average_requests_per_hour: float
    typical_user_agents: Set[str]
    last_updated: datetime
    confidence_score: float = 0.0


class UserActivityMonitor:
    """
    Comprehensive user activity monitoring service
    """
    
    def __init__(self):
        self.auth_service = get_advanced_auth_service()
        self.monitoring = get_monitoring_service()
        
        # Storage for activity data
        self._activity_events: deque = deque(maxlen=50000)  # Keep last 50k events
        self._user_baselines: Dict[str, BehaviorBaseline] = {}
        self._activity_patterns: List[ActivityPattern] = []
        self._suspicious_activities: List[Dict[str, Any]] = []
        
        # Real-time tracking
        self._active_sessions: Dict[str, Dict[str, Any]] = {}
        self._request_counters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._failed_attempts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Configuration
        self.config = {
            'baseline_learning_days': 7,
            'pattern_detection_window_hours': 24,
            'suspicious_threshold_multiplier': 3.0,
            'max_requests_per_minute': 100,
            'max_failed_attempts_per_hour': 10,
            'unusual_hour_threshold': 0.1,  # Less than 10% of normal activity
            'new_location_risk_score': 2.0,
            'velocity_check_window_minutes': 5,
            'max_velocity_km_per_minute': 10,  # Impossible travel speed
        }
        
        # Risk scoring weights
        self.risk_weights = {
            'new_ip': 1.5,
            'new_user_agent': 1.0,
            'unusual_time': 2.0,
            'high_frequency': 2.5,
            'failed_attempts': 3.0,
            'admin_actions': 1.5,
            'data_access': 1.0,
            'configuration_changes': 2.0,
            'impossible_travel': 5.0,
            'suspicious_patterns': 3.0,
        }
    
    def track_activity(self, user_id: str, session_id: str, activity_type: ActivityType,
                      request_info: Dict[str, Any], response_info: Dict[str, Any],
                      metadata: Dict[str, Any] = None) -> str:
        """
        Track user activity event
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            activity_type: Type of activity
            request_info: Request information (IP, user agent, endpoint, etc.)
            response_info: Response information (status, timing, etc.)
            metadata: Additional metadata
            
        Returns:
            str: Event ID
        """
        try:
            with self._lock:
                # Generate event ID
                event_id = f"{user_id}_{int(time.time() * 1000000)}"
                
                # Create activity event
                event = ActivityEvent(
                    event_id=event_id,
                    user_id=user_id,
                    session_id=session_id,
                    activity_type=activity_type,
                    timestamp=datetime.utcnow(),
                    ip_address=request_info.get('ip_address', 'unknown'),
                    user_agent=request_info.get('user_agent', 'unknown'),
                    endpoint=request_info.get('endpoint', 'unknown'),
                    method=request_info.get('method', 'GET'),
                    status_code=response_info.get('status_code', 200),
                    response_time_ms=response_info.get('response_time_ms', 0.0),
                    request_size_bytes=request_info.get('size_bytes', 0),
                    response_size_bytes=response_info.get('size_bytes', 0),
                    success=response_info.get('success', True),
                    error_message=response_info.get('error_message'),
                    metadata=metadata or {}
                )
                
                # Calculate risk score
                event.risk_score = self._calculate_activity_risk_score(event)
                event.risk_level = self._determine_risk_level(event.risk_score)
                
                # Store event
                self._activity_events.append(event)
                
                # Update real-time tracking
                self._update_real_time_tracking(event)
                
                # Check for suspicious patterns
                self._check_suspicious_patterns(event)
                
                # Update user baseline (if enough data)
                self._update_user_baseline(user_id)
                
                # Send to monitoring system
                self.monitoring.track_custom_metric(
                    'user_activity_tracked',
                    1.0,
                    {
                        'user_id': user_id,
                        'activity_type': activity_type.value,
                        'risk_level': event.risk_level.value,
                        'risk_score': event.risk_score
                    }
                )
                
                # Log high-risk activities
                if event.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    logger.warning(f"High-risk activity detected: {activity_type.value} by user {user_id} "
                                 f"(risk: {event.risk_score:.2f})")
                
                return event_id
                
        except Exception as e:
            logger.error(f"Error tracking activity: {str(e)}")
            return ""
    
    def get_user_activity_summary(self, user_id: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get user activity summary for specified time period
        
        Args:
            user_id: User identifier
            hours: Time period in hours
            
        Returns:
            Dict containing activity summary
        """
        try:
            with self._lock:
                cutoff_time = datetime.utcnow() - timedelta(hours=hours)
                
                # Filter events for user and time period
                user_events = [
                    event for event in self._activity_events
                    if event.user_id == user_id and event.timestamp >= cutoff_time
                ]
                
                if not user_events:
                    return {
                        'user_id': user_id,
                        'time_period_hours': hours,
                        'total_activities': 0,
                        'activity_breakdown': {},
                        'risk_summary': {},
                        'unique_ips': 0,
                        'unique_endpoints': 0,
                        'failed_attempts': 0
                    }
                
                # Calculate summary statistics
                activity_breakdown = defaultdict(int)
                risk_breakdown = defaultdict(int)
                unique_ips = set()
                unique_endpoints = set()
                failed_attempts = 0
                total_response_time = 0
                
                for event in user_events:
                    activity_breakdown[event.activity_type.value] += 1
                    risk_breakdown[event.risk_level.value] += 1
                    unique_ips.add(event.ip_address)
                    unique_endpoints.add(event.endpoint)
                    total_response_time += event.response_time_ms
                    
                    if not event.success:
                        failed_attempts += 1
                
                return {
                    'user_id': user_id,
                    'time_period_hours': hours,
                    'total_activities': len(user_events),
                    'activity_breakdown': dict(activity_breakdown),
                    'risk_summary': dict(risk_breakdown),
                    'unique_ips': len(unique_ips),
                    'unique_endpoints': len(unique_endpoints),
                    'failed_attempts': failed_attempts,
                    'average_response_time_ms': total_response_time / len(user_events) if user_events else 0,
                    'most_active_hour': self._get_most_active_hour(user_events),
                    'most_accessed_endpoint': self._get_most_accessed_endpoint(user_events)
                }
                
        except Exception as e:
            logger.error(f"Error getting user activity summary: {str(e)}")
            return {'error': str(e)}
    
    def detect_suspicious_behavior(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Detect suspicious behavior patterns for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            List of suspicious behavior indicators
        """
        try:
            with self._lock:
                suspicious_indicators = []
                
                # Get recent activities (last 24 hours)
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                recent_events = [
                    event for event in self._activity_events
                    if event.user_id == user_id and event.timestamp >= cutoff_time
                ]
                
                if not recent_events:
                    return suspicious_indicators
                
                # Check for high-frequency requests
                request_times = [event.timestamp for event in recent_events]
                if len(request_times) > self.config['max_requests_per_minute'] * 60:  # Per hour
                    suspicious_indicators.append({
                        'type': 'high_frequency_requests',
                        'severity': 'medium',
                        'description': f'Unusually high request frequency: {len(request_times)} requests in 24 hours',
                        'count': len(request_times),
                        'threshold': self.config['max_requests_per_minute'] * 60
                    })
                
                # Check for multiple failed attempts
                failed_events = [event for event in recent_events if not event.success]
                if len(failed_events) > self.config['max_failed_attempts_per_hour']:
                    suspicious_indicators.append({
                        'type': 'multiple_failed_attempts',
                        'severity': 'high',
                        'description': f'Multiple failed attempts: {len(failed_events)} failures',
                        'count': len(failed_events),
                        'threshold': self.config['max_failed_attempts_per_hour']
                    })
                
                # Check for unusual access times
                if self._has_unusual_access_times(user_id, recent_events):
                    suspicious_indicators.append({
                        'type': 'unusual_access_times',
                        'severity': 'medium',
                        'description': 'Access during unusual hours detected',
                        'details': self._get_unusual_time_details(recent_events)
                    })
                
                # Check for new IP addresses
                baseline = self._user_baselines.get(user_id)
                if baseline:
                    current_ips = set(event.ip_address for event in recent_events)
                    new_ips = current_ips - baseline.typical_ip_ranges
                    if new_ips:
                        suspicious_indicators.append({
                            'type': 'new_ip_addresses',
                            'severity': 'medium',
                            'description': f'Access from {len(new_ips)} new IP address(es)',
                            'new_ips': list(new_ips)
                        })
                
                # Check for impossible travel
                travel_anomalies = self._detect_impossible_travel(recent_events)
                if travel_anomalies:
                    suspicious_indicators.extend(travel_anomalies)
                
                # Check for unusual endpoint access patterns
                endpoint_anomalies = self._detect_endpoint_anomalies(user_id, recent_events)
                if endpoint_anomalies:
                    suspicious_indicators.extend(endpoint_anomalies)
                
                return suspicious_indicators
                
        except Exception as e:
            logger.error(f"Error detecting suspicious behavior: {str(e)}")
            return []
    
    def get_activity_patterns(self, user_id: str = None, 
                            hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get detected activity patterns
        
        Args:
            user_id: Filter by user ID (optional)
            hours: Time window for pattern detection
            
        Returns:
            List of detected patterns
        """
        try:
            with self._lock:
                cutoff_time = datetime.utcnow() - timedelta(hours=hours)
                
                # Filter patterns by time and user
                patterns = []
                for pattern in self._activity_patterns:
                    if pattern.last_occurrence >= cutoff_time:
                        if user_id is None or user_id in pattern.metadata.get('affected_users', []):
                            patterns.append(asdict(pattern))
                
                return patterns
                
        except Exception as e:
            logger.error(f"Error getting activity patterns: {str(e)}")
            return []
    
    def get_user_baseline(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user behavior baseline
        
        Args:
            user_id: User identifier
            
        Returns:
            Dict containing baseline information or None if not available
        """
        try:
            baseline = self._user_baselines.get(user_id)
            if baseline:
                return {
                    'user_id': baseline.user_id,
                    'typical_hours': baseline.typical_hours,
                    'typical_endpoints': list(baseline.typical_endpoints),
                    'typical_ip_ranges': list(baseline.typical_ip_ranges),
                    'average_session_duration_minutes': baseline.average_session_duration_minutes,
                    'average_requests_per_hour': baseline.average_requests_per_hour,
                    'typical_user_agents': list(baseline.typical_user_agents),
                    'last_updated': baseline.last_updated.isoformat(),
                    'confidence_score': baseline.confidence_score
                }
            return None
            
        except Exception as e:
            logger.error(f"Error getting user baseline: {str(e)}")
            return None
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """
        Clean up old activity data
        
        Args:
            days_to_keep: Number of days of data to keep
        """
        try:
            with self._lock:
                cutoff_time = datetime.utcnow() - timedelta(days=days_to_keep)
                
                # Clean activity events (deque automatically limits size)
                # Clean patterns
                self._activity_patterns = [
                    pattern for pattern in self._activity_patterns
                    if pattern.last_occurrence >= cutoff_time
                ]
                
                # Clean suspicious activities
                self._suspicious_activities = [
                    activity for activity in self._suspicious_activities
                    if datetime.fromisoformat(activity['timestamp']) >= cutoff_time
                ]
                
                logger.debug(f"Cleaned up activity data older than {days_to_keep} days")
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {str(e)}")
    
    # Private helper methods
    
    def _calculate_activity_risk_score(self, event: ActivityEvent) -> float:
        """Calculate risk score for an activity event"""
        risk_score = 0.0
        
        try:
            # Base risk by activity type
            base_risks = {
                ActivityType.LOGIN: 0.5,
                ActivityType.LOGOUT: 0.1,
                ActivityType.API_ACCESS: 0.3,
                ActivityType.DATA_ACCESS: 1.0,
                ActivityType.CONFIGURATION_CHANGE: 2.0,
                ActivityType.SECURITY_EVENT: 1.5,
                ActivityType.FILE_UPLOAD: 1.0,
                ActivityType.FILE_DOWNLOAD: 0.8,
                ActivityType.ADMIN_ACTION: 2.5,
                ActivityType.FAILED_ATTEMPT: 3.0,
            }
            
            risk_score += base_risks.get(event.activity_type, 0.5)
            
            # Check against user baseline
            baseline = self._user_baselines.get(event.user_id)
            if baseline:
                # New IP address
                if event.ip_address not in baseline.typical_ip_ranges:
                    risk_score += self.risk_weights['new_ip']
                
                # New user agent
                if event.user_agent not in baseline.typical_user_agents:
                    risk_score += self.risk_weights['new_user_agent']
                
                # Unusual time
                if event.timestamp.hour not in baseline.typical_hours:
                    risk_score += self.risk_weights['unusual_time']
                
                # New endpoint
                if event.endpoint not in baseline.typical_endpoints:
                    risk_score += 0.5
            
            # Failed request
            if not event.success:
                risk_score += self.risk_weights['failed_attempts']
            
            # High response time (potential attack)
            if event.response_time_ms > 5000:  # > 5 seconds
                risk_score += 1.0
            
            # Admin endpoints
            if '/admin' in event.endpoint or '/config' in event.endpoint:
                risk_score += self.risk_weights['admin_actions']
            
            # Sensitive data endpoints
            if any(sensitive in event.endpoint.lower() for sensitive in ['password', 'secret', 'key', 'token']):
                risk_score += self.risk_weights['data_access']
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {str(e)}")
            risk_score = 1.0  # Default moderate risk
        
        return min(risk_score, 10.0)  # Cap at 10.0
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score"""
        if risk_score >= 7.0:
            return RiskLevel.CRITICAL
        elif risk_score >= 4.0:
            return RiskLevel.HIGH
        elif risk_score >= 2.0:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _update_real_time_tracking(self, event: ActivityEvent):
        """Update real-time tracking data"""
        # Update request counters
        current_minute = int(time.time() / 60)
        self._request_counters[event.user_id].append(current_minute)
        
        # Update failed attempts
        if not event.success:
            self._failed_attempts[event.user_id].append(event.timestamp)
        
        # Update active sessions
        if event.session_id not in self._active_sessions:
            self._active_sessions[event.session_id] = {
                'user_id': event.user_id,
                'start_time': event.timestamp,
                'last_activity': event.timestamp,
                'request_count': 0,
                'unique_ips': set(),
                'endpoints_accessed': set()
            }
        
        session_data = self._active_sessions[event.session_id]
        session_data['last_activity'] = event.timestamp
        session_data['request_count'] += 1
        session_data['unique_ips'].add(event.ip_address)
        session_data['endpoints_accessed'].add(event.endpoint)
    
    def _check_suspicious_patterns(self, event: ActivityEvent):
        """Check for suspicious patterns in real-time"""
        # Check request frequency
        recent_requests = [
            ts for ts in self._request_counters[event.user_id]
            if ts >= int(time.time() / 60) - 5  # Last 5 minutes
        ]
        
        if len(recent_requests) > self.config['max_requests_per_minute'] * 5:
            self._create_suspicious_activity_alert(
                event.user_id,
                'high_frequency_requests',
                f'High request frequency: {len(recent_requests)} requests in 5 minutes',
                {'request_count': len(recent_requests), 'threshold': self.config['max_requests_per_minute'] * 5}
            )
        
        # Check failed attempts
        recent_failures = [
            ts for ts in self._failed_attempts[event.user_id]
            if ts >= datetime.utcnow() - timedelta(hours=1)
        ]
        
        if len(recent_failures) > self.config['max_failed_attempts_per_hour']:
            self._create_suspicious_activity_alert(
                event.user_id,
                'multiple_failed_attempts',
                f'Multiple failed attempts: {len(recent_failures)} in last hour',
                {'failure_count': len(recent_failures), 'threshold': self.config['max_failed_attempts_per_hour']}
            )
    
    def _update_user_baseline(self, user_id: str):
        """Update user behavior baseline"""
        try:
            # Get user events from last N days
            cutoff_time = datetime.utcnow() - timedelta(days=self.config['baseline_learning_days'])
            user_events = [
                event for event in self._activity_events
                if event.user_id == user_id and event.timestamp >= cutoff_time
            ]
            
            if len(user_events) < 50:  # Need minimum data for baseline
                return
            
            # Calculate baseline metrics
            typical_hours = list(set(event.timestamp.hour for event in user_events))
            typical_endpoints = set(event.endpoint for event in user_events)
            typical_ip_ranges = set(event.ip_address for event in user_events)
            typical_user_agents = set(event.user_agent for event in user_events)
            
            # Calculate session duration (simplified)
            session_durations = []
            for session_id in set(event.session_id for event in user_events):
                session_events = [e for e in user_events if e.session_id == session_id]
                if len(session_events) > 1:
                    duration = (max(e.timestamp for e in session_events) - 
                              min(e.timestamp for e in session_events)).total_seconds() / 60
                    session_durations.append(duration)
            
            avg_session_duration = statistics.mean(session_durations) if session_durations else 60.0
            
            # Calculate requests per hour
            hours_active = len(set(event.timestamp.replace(minute=0, second=0, microsecond=0) 
                                 for event in user_events))
            avg_requests_per_hour = len(user_events) / max(hours_active, 1)
            
            # Calculate confidence score based on data amount and consistency
            confidence_score = min(len(user_events) / 1000.0, 1.0)  # Max confidence at 1000 events
            
            # Update baseline
            self._user_baselines[user_id] = BehaviorBaseline(
                user_id=user_id,
                typical_hours=typical_hours,
                typical_endpoints=typical_endpoints,
                typical_ip_ranges=typical_ip_ranges,
                average_session_duration_minutes=avg_session_duration,
                average_requests_per_hour=avg_requests_per_hour,
                typical_user_agents=typical_user_agents,
                last_updated=datetime.utcnow(),
                confidence_score=confidence_score
            )
            
        except Exception as e:
            logger.error(f"Error updating user baseline: {str(e)}")
    
    def _has_unusual_access_times(self, user_id: str, events: List[ActivityEvent]) -> bool:
        """Check if user has unusual access times"""
        baseline = self._user_baselines.get(user_id)
        if not baseline:
            return False
        
        unusual_events = [
            event for event in events
            if event.timestamp.hour not in baseline.typical_hours
        ]
        
        return len(unusual_events) > len(events) * self.config['unusual_hour_threshold']
    
    def _get_unusual_time_details(self, events: List[ActivityEvent]) -> Dict[str, Any]:
        """Get details about unusual access times"""
        hour_counts = defaultdict(int)
        for event in events:
            hour_counts[event.timestamp.hour] += 1
        
        return {
            'hour_distribution': dict(hour_counts),
            'unusual_hours': [hour for hour, count in hour_counts.items() if hour < 6 or hour > 22]
        }
    
    def _detect_impossible_travel(self, events: List[ActivityEvent]) -> List[Dict[str, Any]]:
        """Detect impossible travel between locations"""
        # Simplified implementation - would need geolocation service in production
        anomalies = []
        
        # Group events by IP and time
        ip_events = defaultdict(list)
        for event in events:
            ip_events[event.ip_address].append(event)
        
        # Check for rapid IP changes (simplified impossible travel detection)
        sorted_events = sorted(events, key=lambda e: e.timestamp)
        for i in range(1, len(sorted_events)):
            prev_event = sorted_events[i-1]
            curr_event = sorted_events[i]
            
            if (prev_event.ip_address != curr_event.ip_address and
                (curr_event.timestamp - prev_event.timestamp).total_seconds() < 300):  # 5 minutes
                
                anomalies.append({
                    'type': 'rapid_location_change',
                    'severity': 'high',
                    'description': f'Rapid IP change from {prev_event.ip_address} to {curr_event.ip_address}',
                    'time_difference_seconds': (curr_event.timestamp - prev_event.timestamp).total_seconds(),
                    'previous_ip': prev_event.ip_address,
                    'current_ip': curr_event.ip_address
                })
        
        return anomalies
    
    def _detect_endpoint_anomalies(self, user_id: str, events: List[ActivityEvent]) -> List[Dict[str, Any]]:
        """Detect unusual endpoint access patterns"""
        anomalies = []
        baseline = self._user_baselines.get(user_id)
        
        if not baseline:
            return anomalies
        
        # Check for access to new sensitive endpoints
        current_endpoints = set(event.endpoint for event in events)
        new_endpoints = current_endpoints - baseline.typical_endpoints
        
        sensitive_patterns = ['/admin', '/config', '/security', '/key-vault', '/policies']
        new_sensitive_endpoints = [
            endpoint for endpoint in new_endpoints
            if any(pattern in endpoint for pattern in sensitive_patterns)
        ]
        
        if new_sensitive_endpoints:
            anomalies.append({
                'type': 'new_sensitive_endpoint_access',
                'severity': 'medium',
                'description': f'Access to {len(new_sensitive_endpoints)} new sensitive endpoint(s)',
                'endpoints': new_sensitive_endpoints
            })
        
        return anomalies
    
    def _get_most_active_hour(self, events: List[ActivityEvent]) -> int:
        """Get the most active hour for events"""
        hour_counts = defaultdict(int)
        for event in events:
            hour_counts[event.timestamp.hour] += 1
        
        return max(hour_counts.items(), key=lambda x: x[1])[0] if hour_counts else 0
    
    def _get_most_accessed_endpoint(self, events: List[ActivityEvent]) -> str:
        """Get the most accessed endpoint"""
        endpoint_counts = defaultdict(int)
        for event in events:
            endpoint_counts[event.endpoint] += 1
        
        return max(endpoint_counts.items(), key=lambda x: x[1])[0] if endpoint_counts else ""
    
    def _create_suspicious_activity_alert(self, user_id: str, alert_type: str,
                                        description: str, details: Dict[str, Any]):
        """Create suspicious activity alert"""
        alert = {
            'user_id': user_id,
            'alert_type': alert_type,
            'description': description,
            'timestamp': datetime.utcnow().isoformat(),
            'details': details
        }
        
        self._suspicious_activities.append(alert)
        
        # Send to monitoring system
        self.monitoring.track_custom_metric(
            'suspicious_activity_detected',
            1.0,
            {
                'user_id': user_id,
                'alert_type': alert_type
            }
        )
        
        logger.warning(f"Suspicious activity detected: {alert_type} for user {user_id}")


# Global monitor instance
_activity_monitor = None


def get_user_activity_monitor() -> UserActivityMonitor:
    """Get the global user activity monitor instance"""
    global _activity_monitor
    if _activity_monitor is None:
        _activity_monitor = UserActivityMonitor()
    return _activity_monitor