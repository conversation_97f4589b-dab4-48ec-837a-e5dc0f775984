"""
Performance Optimizer Module

This module provides performance optimization capabilities for the queue-based task processing system.
It includes caching, connection pooling, batch processing, and performance tuning.

Features:
- Connection pooling
- Caching layer
- Batch processing
- Performance monitoring
- Resource optimization
"""

import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union
from functools import lru_cache
import threading
from collections import defaultdict
import psutil
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import weakref

# Configure module-level logger
logger = logging.getLogger(__name__)

class ConnectionPool:
    """Enhanced connection pool for database and external services"""
    
    def __init__(self, max_connections: int = 10, timeout: int = 30, 
                 connection_type: str = "generic", **kwargs):
        """Initialize connection pool"""
        self.max_connections = max_connections
        self.timeout = timeout
        self.connection_type = connection_type
        self.connections = []
        self.lock = threading.Lock()
        self.active_connections = 0
        self.connection_stats = {
            'created': 0,
            'reused': 0,
            'closed': 0,
            'errors': 0
        }
        self.kwargs = kwargs
        
    def get_connection(self) -> Any:
        """Get connection from pool"""
        with self.lock:
            if self.connections:
                conn = self.connections.pop()
                if self._is_connection_valid(conn):
                    self.connection_stats['reused'] += 1
                    return conn
                else:
                    self._close_connection(conn)
                    self.active_connections -= 1
            
            if self.active_connections < self.max_connections:
                self.active_connections += 1
                try:
                    conn = self._create_connection()
                    self.connection_stats['created'] += 1
                    return conn
                except Exception as e:
                    self.active_connections -= 1
                    self.connection_stats['errors'] += 1
                    logger.error(f"Failed to create connection: {e}")
                    raise
            
            # Wait for available connection
            start_time = time.time()
            while time.time() - start_time < self.timeout:
                if self.connections:
                    conn = self.connections.pop()
                    if self._is_connection_valid(conn):
                        self.connection_stats['reused'] += 1
                        return conn
                    else:
                        self._close_connection(conn)
                        self.active_connections -= 1
                time.sleep(0.1)
            
            raise Exception(f"Connection pool exhausted for {self.connection_type}")
    
    def return_connection(self, connection: Any):
        """Return connection to pool"""
        if connection is None:
            return
            
        with self.lock:
            if self._is_connection_valid(connection):
                self.connections.append(connection)
            else:
                self._close_connection(connection)
                self.active_connections -= 1
    
    def _create_connection(self) -> Any:
        """Create new connection - override in subclasses"""
        return None
    
    def _is_connection_valid(self, connection: Any) -> bool:
        """Check if connection is still valid - override in subclasses"""
        return connection is not None
    
    def close_all(self):
        """Close all connections"""
        with self.lock:
            for conn in self.connections:
                self._close_connection(conn)
            self.connections.clear()
            self.active_connections = 0
            self.connection_stats['closed'] += len(self.connections)
    
    def _close_connection(self, connection: Any):
        """Close individual connection - override in subclasses"""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        with self.lock:
            return {
                'connection_type': self.connection_type,
                'max_connections': self.max_connections,
                'active_connections': self.active_connections,
                'available_connections': len(self.connections),
                'stats': self.connection_stats.copy()
            }


class SQLServerConnectionPool(ConnectionPool):
    """SQL Server specific connection pool"""
    
    def __init__(self, connection_string: str, max_connections: int = 10, 
                 timeout: int = 30, **kwargs):
        super().__init__(max_connections, timeout, "sqlserver", **kwargs)
        self.connection_string = connection_string
        
    def _create_connection(self) -> Any:
        """Create SQL Server connection"""
        try:
            import pyodbc
            conn = pyodbc.connect(
                self.connection_string,
                timeout=self.timeout,
                autocommit=True
            )
            return conn
        except ImportError:
            logger.error("pyodbc not available for SQL Server connections")
            raise
        except Exception as e:
            logger.error(f"Failed to create SQL Server connection: {e}")
            raise
    
    def _is_connection_valid(self, connection: Any) -> bool:
        """Check if SQL Server connection is valid"""
        if connection is None:
            return False
        try:
            # Test connection with a simple query
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception:
            return False
    
    def _close_connection(self, connection: Any):
        """Close SQL Server connection"""
        try:
            if connection:
                connection.close()
        except Exception as e:
            logger.warning(f"Error closing SQL Server connection: {e}")


class HTTPClientPool(ConnectionPool):
    """HTTP client connection pool with session reuse"""
    
    def __init__(self, max_connections: int = 10, timeout: int = 30, 
                 retry_config: Optional[Dict] = None, **kwargs):
        super().__init__(max_connections, timeout, "http_client", **kwargs)
        self.retry_config = retry_config or {
            'total': 3,
            'backoff_factor': 0.3,
            'status_forcelist': [500, 502, 503, 504]
        }
        self.session_stats = defaultdict(int)
        
    def _create_connection(self) -> requests.Session:
        """Create HTTP session with optimized configuration"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.retry_config['total'],
            backoff_factor=self.retry_config['backoff_factor'],
            status_forcelist=self.retry_config['status_forcelist']
        )
        
        # Configure adapters for HTTP and HTTPS
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.max_connections,
            pool_maxsize=self.max_connections * 2
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default timeout
        session.timeout = self.timeout
        
        # Set default headers
        session.headers.update({
            'User-Agent': 'AtomSec-SFDC-Service/1.0',
            'Connection': 'keep-alive'
        })
        
        return session
    
    def _is_connection_valid(self, connection: requests.Session) -> bool:
        """Check if HTTP session is valid"""
        return connection is not None and hasattr(connection, 'request')
    
    def _close_connection(self, connection: requests.Session):
        """Close HTTP session"""
        try:
            if connection:
                connection.close()
        except Exception as e:
            logger.warning(f"Error closing HTTP session: {e}")
    
    def make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request using pooled session"""
        session = self.get_connection()
        try:
            self.session_stats[f'{method.upper()}_requests'] += 1
            response = session.request(method, url, **kwargs)
            self.session_stats['successful_requests'] += 1
            return response
        except Exception as e:
            self.session_stats['failed_requests'] += 1
            logger.error(f"HTTP request failed: {e}")
            raise
        finally:
            self.return_connection(session)
    
    def get_session_stats(self) -> Dict[str, int]:
        """Get HTTP session statistics"""
        return dict(self.session_stats)


class ConnectionPoolManager:
    """Manages multiple connection pools"""
    
    def __init__(self):
        self.pools: Dict[str, ConnectionPool] = {}
        self.lock = threading.Lock()
        
    def create_sqlserver_pool(self, name: str, connection_string: str, 
                             max_connections: int = 10, timeout: int = 30) -> SQLServerConnectionPool:
        """Create SQL Server connection pool"""
        with self.lock:
            if name in self.pools:
                logger.warning(f"Pool {name} already exists, returning existing pool")
                return self.pools[name]
            
            pool = SQLServerConnectionPool(
                connection_string=connection_string,
                max_connections=max_connections,
                timeout=timeout
            )
            self.pools[name] = pool
            logger.info(f"Created SQL Server connection pool: {name}")
            return pool
    
    def create_http_pool(self, name: str, max_connections: int = 10, 
                        timeout: int = 30, retry_config: Optional[Dict] = None) -> HTTPClientPool:
        """Create HTTP client pool"""
        with self.lock:
            if name in self.pools:
                logger.warning(f"Pool {name} already exists, returning existing pool")
                return self.pools[name]
            
            pool = HTTPClientPool(
                max_connections=max_connections,
                timeout=timeout,
                retry_config=retry_config
            )
            self.pools[name] = pool
            logger.info(f"Created HTTP client pool: {name}")
            return pool
    
    def get_pool(self, name: str) -> Optional[ConnectionPool]:
        """Get connection pool by name"""
        return self.pools.get(name)
    
    def close_all_pools(self):
        """Close all connection pools"""
        with self.lock:
            for name, pool in self.pools.items():
                try:
                    pool.close_all()
                    logger.info(f"Closed connection pool: {name}")
                except Exception as e:
                    logger.error(f"Error closing pool {name}: {e}")
            self.pools.clear()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all pools"""
        stats = {}
        for name, pool in self.pools.items():
            stats[name] = pool.get_stats()
            if isinstance(pool, HTTPClientPool):
                stats[name]['session_stats'] = pool.get_session_stats()
        return stats

class CacheManager:
    """Enhanced caching layer with Redis support and intelligent features"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600, 
                 cache_type: str = "memory", redis_config: Optional[Dict] = None):
        """Initialize cache manager"""
        self.max_size = max_size
        self.ttl = ttl
        self.cache_type = cache_type
        self.cache = {}
        self.timestamps = {}
        self.access_counts = defaultdict(int)
        self.lock = threading.Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0,
            'errors': 0
        }
        
        # Redis configuration
        self.redis_client = None
        if cache_type == "redis" and redis_config:
            self._initialize_redis(redis_config)
        
        # Cache warming configuration
        self.warming_enabled = False
        self.warming_keys = set()
        
    def _initialize_redis(self, redis_config: Dict):
        """Initialize Redis client"""
        try:
            import redis
            self.redis_client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                decode_responses=True,
                socket_timeout=redis_config.get('timeout', 5),
                socket_connect_timeout=redis_config.get('connect_timeout', 5),
                retry_on_timeout=True,
                health_check_interval=30
            )
            # Test connection
            self.redis_client.ping()
            logger.info("Redis cache initialized successfully")
        except ImportError:
            logger.warning("Redis not available, falling back to memory cache")
            self.cache_type = "memory"
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            self.cache_type = "memory"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache with intelligent features"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                return self._get_from_redis(key)
            else:
                return self._get_from_memory(key)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.stats['errors'] += 1
            return None
    
    def _get_from_memory(self, key: str) -> Optional[Any]:
        """Get value from memory cache"""
        with self.lock:
            if key in self.cache:
                # Check TTL
                if time.time() - self.timestamps[key] < self.ttl:
                    self.access_counts[key] += 1
                    self.stats['hits'] += 1
                    return self.cache[key]
                else:
                    # Expired, remove
                    del self.cache[key]
                    del self.timestamps[key]
                    if key in self.access_counts:
                        del self.access_counts[key]
            
            self.stats['misses'] += 1
            return None
    
    def _get_from_redis(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            value = self.redis_client.get(key)
            if value is not None:
                self.stats['hits'] += 1
                # Track access for warming
                self.access_counts[key] += 1
                return json.loads(value)
            else:
                self.stats['misses'] += 1
                return None
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            self.stats['errors'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set value in cache with intelligent eviction"""
        try:
            cache_ttl = ttl or self.ttl
            if self.cache_type == "redis" and self.redis_client:
                self._set_in_redis(key, value, cache_ttl)
            else:
                self._set_in_memory(key, value, cache_ttl)
            
            self.stats['sets'] += 1
            
            # Add to warming keys if frequently accessed
            if self.access_counts[key] > 5:
                self.warming_keys.add(key)
                
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.stats['errors'] += 1
    
    def _set_in_memory(self, key: str, value: Any, ttl: int):
        """Set value in memory cache"""
        with self.lock:
            # Intelligent eviction based on access frequency
            if len(self.cache) >= self.max_size:
                self._evict_least_used()
            
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    def _set_in_redis(self, key: str, value: Any, ttl: int):
        """Set value in Redis cache"""
        try:
            serialized_value = json.dumps(value, default=str)
            self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            raise
    
    def _evict_least_used(self):
        """Evict least frequently used items"""
        if not self.cache:
            return
        
        # Find least accessed key
        least_used_key = min(self.access_counts, key=self.access_counts.get, 
                           default=min(self.timestamps, key=self.timestamps.get))
        
        if least_used_key in self.cache:
            del self.cache[least_used_key]
            del self.timestamps[least_used_key]
            if least_used_key in self.access_counts:
                del self.access_counts[least_used_key]
            self.stats['evictions'] += 1
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                with self.lock:
                    if key in self.cache:
                        del self.cache[key]
                        del self.timestamps[key]
                        if key in self.access_counts:
                            del self.access_counts[key]
                        return True
                    return False
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def clear(self):
        """Clear all cache entries"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                self.redis_client.flushdb()
            else:
                with self.lock:
                    self.cache.clear()
                    self.timestamps.clear()
                    self.access_counts.clear()
            
            self.warming_keys.clear()
            logger.info("Cache cleared successfully")
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
    
    def warm_cache(self, warming_data: Dict[str, Any]):
        """Warm cache with frequently accessed data"""
        if not self.warming_enabled:
            return
        
        try:
            for key, value in warming_data.items():
                if key in self.warming_keys:
                    self.set(key, value)
            
            logger.info(f"Cache warmed with {len(warming_data)} items")
        except Exception as e:
            logger.error(f"Cache warming error: {e}")
    
    def enable_warming(self, enabled: bool = True):
        """Enable or disable cache warming"""
        self.warming_enabled = enabled
        logger.info(f"Cache warming {'enabled' if enabled else 'disabled'}")
    
    def get_hot_keys(self, limit: int = 10) -> List[str]:
        """Get most frequently accessed keys"""
        return sorted(self.access_counts.keys(), 
                     key=self.access_counts.get, reverse=True)[:limit]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        hit_rate = 0.0
        total_requests = self.stats['hits'] + self.stats['misses']
        if total_requests > 0:
            hit_rate = self.stats['hits'] / total_requests
        
        stats = {
            'cache_type': self.cache_type,
            'max_size': self.max_size,
            'ttl': self.ttl,
            'hit_rate': hit_rate,
            'warming_enabled': self.warming_enabled,
            'warming_keys_count': len(self.warming_keys),
            'stats': self.stats.copy()
        }
        
        if self.cache_type == "memory":
            with self.lock:
                stats.update({
                    'current_size': len(self.cache),
                    'memory_usage_percent': (len(self.cache) / self.max_size) * 100
                })
        elif self.cache_type == "redis" and self.redis_client:
            try:
                info = self.redis_client.info()
                stats.update({
                    'redis_memory_used': info.get('used_memory_human', 'N/A'),
                    'redis_connected_clients': info.get('connected_clients', 0),
                    'redis_keyspace_hits': info.get('keyspace_hits', 0),
                    'redis_keyspace_misses': info.get('keyspace_misses', 0)
                })
            except Exception as e:
                logger.warning(f"Could not get Redis stats: {e}")
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """Perform cache health check"""
        health = {
            'status': 'healthy',
            'cache_type': self.cache_type,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            if self.cache_type == "redis" and self.redis_client:
                # Test Redis connection
                self.redis_client.ping()
                health['redis_status'] = 'connected'
            else:
                # Test memory cache
                test_key = f"health_check_{int(time.time())}"
                self.set(test_key, "test_value", ttl=1)
                if self.get(test_key) == "test_value":
                    health['memory_cache_status'] = 'working'
                self.delete(test_key)
        except Exception as e:
            health['status'] = 'unhealthy'
            health['error'] = str(e)
        
        return health

class BatchProcessor:
    """Batch processing for improved performance"""
    
    def __init__(self, batch_size: int = 100, flush_interval: int = 60):
        """Initialize batch processor"""
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.batch = []
        self.last_flush = time.time()
        self.lock = threading.Lock()
        
    def add_item(self, item: Any) -> bool:
        """Add item to batch"""
        with self.lock:
            self.batch.append(item)
            
            if len(self.batch) >= self.batch_size:
                self._flush()
                return True
            
            if time.time() - self.last_flush >= self.flush_interval:
                self._flush()
                return True
        
        return False
    
    def _flush(self):
        """Flush batch"""
        if not self.batch:
            return
        
        batch_to_process = self.batch.copy()
        self.batch.clear()
        self.last_flush = time.time()
        
        # Process batch
        self._process_batch(batch_to_process)
    
    def _process_batch(self, batch: List[Any]):
        """Process batch of items"""
        # Override in subclasses
        pass
    
    def force_flush(self):
        """Force flush current batch"""
        with self.lock:
            self._flush()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get batch processor statistics"""
        with self.lock:
            return {
                'current_batch_size': len(self.batch),
                'batch_size_limit': self.batch_size,
                'flush_interval': self.flush_interval,
                'last_flush': self.last_flush
            }

class PerformanceMonitor:
    """Monitor system performance metrics"""
    
    def __init__(self):
        """Initialize performance monitor"""
        self.metrics = defaultdict(list)
        self.start_time = time.time()
        
    def record_metric(self, name: str, value: float):
        """Record performance metric"""
        self.metrics[name].append({
            'value': value,
            'timestamp': time.time()
        })
        
        # Keep only last 1000 entries
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]
    
    def get_metric_stats(self, name: str, time_window: int = 3600) -> Dict[str, Any]:
        """Get metric statistics"""
        cutoff = time.time() - time_window
        values = [m['value'] for m in self.metrics[name] if m['timestamp'] > cutoff]
        
        if not values:
            return {'count': 0}
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': sum(values) / len(values),
            'p95': sorted(values)[int(len(values) * 0.95)],
            'p99': sorted(values)[int(len(values) * 0.99)]
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system performance stats"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_mb': memory.available / 1024 / 1024,
                'disk_percent': (disk.used / disk.total) * 100,
                'uptime_seconds': time.time() - self.start_time
            }
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}

class QueryOptimizer:
    """Optimize database queries"""
    
    def __init__(self, cache_manager: CacheManager):
        """Initialize query optimizer"""
        self.cache_manager = cache_manager
        self.query_stats = defaultdict(int)
        
    def optimize_query(self, query: str, params: Dict[str, Any]) -> str:
        """Optimize query for performance"""
        # Add query hints based on parameters
        optimized = query
        
        # Cache query plans
        cache_key = f"query_plan:{hash(query + str(sorted(params.items())))}"
        cached_plan = self.cache_manager.get(cache_key)
        
        if cached_plan:
            return cached_plan
        
        # Simple optimization - add LIMIT for large queries
        if 'LIMIT' not in query.upper() and 'SELECT' in query.upper():
            optimized += " LIMIT 1000"
        
        self.cache_manager.set(cache_key, optimized)
        return optimized
    
    def get_query_stats(self) -> Dict[str, int]:
        """Get query statistics"""
        return dict(self.query_stats)

class ResourceOptimizer:
    """Optimize resource usage"""
    
    def __init__(self):
        """Initialize resource optimizer"""
        self.optimizations = []
        
    def optimize_memory_usage(self, data: List[Any]) -> List[Any]:
        """Optimize memory usage for large datasets"""
        # Implement memory-efficient processing
        return data
    
    def optimize_cpu_usage(self, task: Callable) -> Callable:
        """Optimize CPU usage for tasks"""
        def optimized_task(*args, **kwargs):
            start_time = time.time()
            result = task(*args, **kwargs)
            duration = time.time() - start_time
            
            # Log performance
            logger.info(f"Task completed in {duration:.2f}s")
            return result
        
        return optimized_task
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        # Check system resources
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                recommendations.append("High memory usage detected - consider reducing batch sizes")
            
            cpu = psutil.cpu_percent(interval=1)
            if cpu > 80:
                recommendations.append("High CPU usage detected - consider rate limiting")
            
            disk = psutil.disk_usage('/')
            if (disk.used / disk.total) * 100 > 90:
                recommendations.append("Low disk space - consider cleanup")
                
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {e}")
        
        return recommendations

class PerformanceOptimizer:
    """Main performance optimizer with enhanced connection pooling"""
    
    def __init__(self, cache_config: Optional[Dict] = None):
        """Initialize performance optimizer"""
        # Initialize enhanced cache manager
        cache_config = cache_config or {}
        self.cache_manager = CacheManager(
            max_size=cache_config.get('max_size', 1000),
            ttl=cache_config.get('ttl', 3600),
            cache_type=cache_config.get('type', 'memory'),
            redis_config=cache_config.get('redis_config')
        )
        
        self.connection_pool_manager = ConnectionPoolManager()
        self.batch_processor = BatchProcessor()
        self.performance_monitor = PerformanceMonitor()
        self.query_optimizer = QueryOptimizer(self.cache_manager)
        self.resource_optimizer = ResourceOptimizer()
        
        # Initialize default pools
        self._initialize_default_pools()
        
        # Enable cache warming for frequently accessed data
        self.cache_manager.enable_warming(True)
    
    def _initialize_default_pools(self):
        """Initialize default connection pools"""
        try:
            # Create HTTP client pool for external API calls
            self.connection_pool_manager.create_http_pool(
                name="default_http",
                max_connections=20,
                timeout=30,
                retry_config={
                    'total': 3,
                    'backoff_factor': 0.5,
                    'status_forcelist': [500, 502, 503, 504, 429]
                }
            )
            
            # Create Salesforce API specific HTTP pool
            self.connection_pool_manager.create_http_pool(
                name="salesforce_api",
                max_connections=10,
                timeout=60,
                retry_config={
                    'total': 2,
                    'backoff_factor': 1.0,
                    'status_forcelist': [500, 502, 503, 504]
                }
            )
            
            logger.info("Initialized default connection pools")
        except Exception as e:
            logger.error(f"Failed to initialize default pools: {e}")
    
    def get_http_session(self, pool_name: str = "default_http") -> requests.Session:
        """Get HTTP session from pool"""
        pool = self.connection_pool_manager.get_pool(pool_name)
        if isinstance(pool, HTTPClientPool):
            return pool.get_connection()
        raise ValueError(f"Pool {pool_name} is not an HTTP client pool")
    
    def return_http_session(self, session: requests.Session, pool_name: str = "default_http"):
        """Return HTTP session to pool"""
        pool = self.connection_pool_manager.get_pool(pool_name)
        if isinstance(pool, HTTPClientPool):
            pool.return_connection(session)
    
    def make_http_request(self, method: str, url: str, pool_name: str = "default_http", **kwargs) -> requests.Response:
        """Make HTTP request using connection pool"""
        pool = self.connection_pool_manager.get_pool(pool_name)
        if isinstance(pool, HTTPClientPool):
            return pool.make_request(method, url, **kwargs)
        raise ValueError(f"Pool {pool_name} is not an HTTP client pool")
    
    def warm_cache_with_common_data(self):
        """Warm cache with commonly accessed data"""
        try:
            # Common configuration data that's frequently accessed
            common_data = {
                'pmd_rules_config': self._get_pmd_rules_config(),
                'salesforce_metadata_types': self._get_salesforce_metadata_types(),
                'security_policies': self._get_security_policies(),
                'task_configurations': self._get_task_configurations()
            }
            
            self.cache_manager.warm_cache(common_data)
            logger.info("Cache warmed with common data")
        except Exception as e:
            logger.error(f"Cache warming failed: {e}")
    
    def _get_pmd_rules_config(self) -> Dict[str, Any]:
        """Get PMD rules configuration for caching"""
        # This would typically load from configuration files
        return {
            'enabled_rules': ['ApexUnitTestClassShouldHaveAsserts', 'AvoidDirectAccessTriggerMap'],
            'rule_categories': ['Security', 'Performance', 'Best Practices'],
            'severity_levels': ['High', 'Medium', 'Low']
        }
    
    def _get_salesforce_metadata_types(self) -> List[str]:
        """Get Salesforce metadata types for caching"""
        return [
            'Profile', 'PermissionSet', 'CustomObject', 'ApexClass', 
            'ApexTrigger', 'Flow', 'ValidationRule', 'CustomField'
        ]
    
    def _get_security_policies(self) -> Dict[str, Any]:
        """Get security policies for caching"""
        return {
            'password_policy': {'min_length': 8, 'require_special_chars': True},
            'session_timeout': 120,
            'mfa_required': True,
            'ip_restrictions': []
        }
    
    def _get_task_configurations(self) -> Dict[str, Any]:
        """Get task configurations for caching"""
        return {
            'sfdc_authenticate': {'timeout': 30, 'retry_count': 3},
            'health_check': {'timeout': 60, 'retry_count': 2},
            'metadata_extraction': {'timeout': 300, 'retry_count': 1},
            'pmd_apex_security': {'timeout': 600, 'retry_count': 1}
        }
    
    def preload_integration_data(self, integration_id: str):
        """Preload data for a specific integration"""
        try:
            # Preload integration-specific data that will be frequently accessed
            preload_data = {
                f'integration_{integration_id}_config': self._get_integration_config(integration_id),
                f'integration_{integration_id}_credentials': self._get_integration_credentials(integration_id),
                f'integration_{integration_id}_pmd_config': self._get_integration_pmd_config(integration_id)
            }
            
            for key, value in preload_data.items():
                if value:  # Only cache non-empty values
                    self.cache_manager.set(key, value, ttl=1800)  # 30 minutes TTL
            
            logger.info(f"Preloaded data for integration {integration_id}")
        except Exception as e:
            logger.error(f"Failed to preload data for integration {integration_id}: {e}")
    
    def _get_integration_config(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration configuration"""
        # This would typically fetch from database or API
        return {'integration_id': integration_id, 'type': 'salesforce', 'status': 'active'}
    
    def _get_integration_credentials(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration credentials (non-sensitive data only)"""
        # This would typically fetch from secure storage
        return {'client_id': 'cached_client_id', 'instance_url': 'https://example.salesforce.com'}
    
    def _get_integration_pmd_config(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get PMD configuration for integration"""
        return {'enabled_subtasks': ['security', 'performance'], 'scan_frequency': 'daily'}
        
    def optimize_task_processing(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize task processing"""
        start_time = time.time()
        
        # Use caching
        cache_key = f"task_config:{task_data.get('task_type')}"
        cached_config = self.cache_manager.get(cache_key)
        
        if cached_config:
            task_data.update(cached_config)
        
        # Batch processing
        self.batch_processor.add_item(task_data)
        
        # Record performance
        self.performance_monitor.record_metric('task_processing_time', time.time() - start_time)
        
        return task_data
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'system_stats': self.performance_monitor.get_system_stats(),
            'cache_stats': self.cache_manager.get_stats(),
            'batch_stats': self.batch_processor.get_stats(),
            'connection_pool_stats': self.connection_pool_manager.get_all_stats(),
            'recommendations': self.resource_optimizer.get_optimization_recommendations()
        }
    
    def tune_performance(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Auto-tune performance based on metrics"""
        tuning = {}
        
        # Adjust cache size based on hit rate
        cache_stats = self.cache_manager.get_stats()
        if cache_stats['hit_rate'] < 0.5:
            tuning['cache_size'] = min(self.cache_manager.max_size * 2, 10000)
        
        # Adjust batch size based on throughput
        batch_stats = self.batch_processor.get_stats()
        if batch_stats['current_batch_size'] < self.batch_processor.batch_size / 2:
            tuning['batch_size'] = max(self.batch_processor.batch_size // 2, 10)
        
        return tuning
    
    def cleanup(self):
        """Cleanup all resources"""
        try:
            self.connection_pool_manager.close_all_pools()
            self.cache_manager.clear()
            logger.info("Performance optimizer cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()


# Global performance optimizer with proper cleanup
_performance_optimizer = None
_optimizer_lock = threading.Lock()

def get_performance_optimizer() -> PerformanceOptimizer:
    """Get global performance optimizer instance"""
    global _performance_optimizer
    if _performance_optimizer is None:
        with _optimizer_lock:
            if _performance_optimizer is None:
                _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer

def cleanup_performance_optimizer():
    """Cleanup global performance optimizer"""
    global _performance_optimizer
    if _performance_optimizer is not None:
        with _optimizer_lock:
            if _performance_optimizer is not None:
                _performance_optimizer.cleanup()
                _performance_optimizer = None

# Decorator for performance monitoring
def monitor_performance(operation_name: str):
    """Decorator to monitor operation performance"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            optimizer = get_performance_optimizer()
            start_time = time.time()
            
            result = func(*args, **kwargs)
            
            duration = time.time() - start_time
            optimizer.performance_monitor.record_metric(
                f'operation.{operation_name}.duration', duration
            )
            
            return result
        return wrapper
    return decorator