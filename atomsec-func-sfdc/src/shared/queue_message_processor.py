"""
Queue Message Processor Module

This module provides queue message processing capabilities for the SFDC service
in the queue-based task processing system. It handles incoming queue messages,
extracts execution context, and manages message acknowledgment and error handling.

Features:
- Process incoming queue messages from Azure Storage Queues
- Extract and propagate executionLogId for task correlation
- Handle message acknowledgment and error scenarios
- Implement retry mechanisms with exponential backoff
- Support for priority-based queue processing
- Proper error handling and logging

Best practices implemented:
- Single responsibility principle
- Comprehensive error handling
- Structured logging with correlation IDs
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from azure.storage.queue import QueueClient
from azure.core.exceptions import ResourceNotFoundError

# Import shared modules
from src.shared.azure_services import get_queue_client
from src.shared.task_status_service import get_task_status_service, TaskStatusService
from src.shared.execution_context_manager import get_execution_context_manager, ExecutionContextManager
from src.shared.common import is_local_dev
from src.shared.config import get_storage_connection_string

# Configure module-level logger
logger = logging.getLogger(__name__)

# Queue configuration constants
PRIORITY_QUEUES = {
    "high": "task-queue-high",
    "medium": "task-queue-medium", 
    "low": "task-queue-low"
}

# Poison queue configuration
POISON_QUEUES = {
    "high": "task-queue-high-poison",
    "medium": "task-queue-medium-poison",
    "low": "task-queue-low-poison"
}

# Message processing constants
MAX_RETRY_COUNT = 3
EXPONENTIAL_BACKOFF_BASE = 2
MAX_VISIBILITY_TIMEOUT = 300  # 5 minutes
DEFAULT_VISIBILITY_TIMEOUT = 30  # 30 seconds
MESSAGE_PROCESSING_TIMEOUT = 600  # 10 minutes


class QueueMessageProcessor:
    """
    Queue message processor for handling incoming task messages
    
    This class provides functionality to process queue messages from Azure Storage Queues,
    extract execution context, and manage the complete message lifecycle including
    acknowledgment and error handling.
    """
    
    def __init__(self, task_status_service: Optional[TaskStatusService] = None, execution_context_manager: Optional[ExecutionContextManager] = None):
        """
        Initialize the queue message processor
        
        Args:
            task_status_service: Optional TaskStatusService instance
            execution_context_manager: Optional ExecutionContextManager instance
        """
        self.task_status_service = task_status_service or get_task_status_service()
        self.execution_context_manager = execution_context_manager or get_execution_context_manager()
        self.connection_string = get_storage_connection_string()
        self.queue_clients = {}
        self.is_local = is_local_dev()
        
        # Initialize queue clients for all priority levels
        self._initialize_queue_clients()
        
        logger.info(f"Queue message processor initialized for {'local' if self.is_local else 'production'} environment")
    
    def _initialize_queue_clients(self):
        """Initialize queue clients for all priority levels and poison queues"""
        try:
            # Initialize priority queues
            for priority, queue_name in PRIORITY_QUEUES.items():
                self.queue_clients[queue_name] = self._get_queue_client(queue_name)
                logger.debug(f"Initialized queue client for {priority} priority: {queue_name}")
            
            # Initialize poison queues
            for priority, poison_queue_name in POISON_QUEUES.items():
                self.queue_clients[poison_queue_name] = self._get_queue_client(poison_queue_name)
                logger.debug(f"Initialized poison queue client for {priority} priority: {poison_queue_name}")
            
            logger.info("All queue clients (priority and poison) initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing queue clients: {str(e)}")
    
    def _get_queue_client(self, queue_name: str) -> Optional[QueueClient]:
        """
        Get or create a queue client for the specified queue
        
        Args:
            queue_name: Name of the queue
        
        Returns:
            QueueClient: Queue client instance or None if failed
        """
        try:
            if not self.connection_string:
                logger.error("Storage connection string not available")
                return None
            
            queue_client = QueueClient.from_connection_string(
                conn_str=self.connection_string,
                queue_name=queue_name
            )
            
            # Verify queue exists by getting properties
            try:
                queue_client.get_queue_properties()
                logger.debug(f"Queue client created and verified for: {queue_name}")
            except ResourceNotFoundError:
                logger.warning(f"Queue {queue_name} does not exist")
                return None
            
            return queue_client
            
        except Exception as e:
            logger.error(f"Error creating queue client for {queue_name}: {str(e)}")
            return None
    
    def process_queue_message(self, message: Dict[str, Any]) -> bool:
        """
        Process a single queue message
        
        Args:
            message: Queue message data including metadata
        
        Returns:
            bool: True if message processed successfully, False otherwise
        """
        try:
            # Extract message metadata
            queue_metadata = message.get("_queue_metadata", {})
            message_id = queue_metadata.get("message_id")
            dequeue_count = queue_metadata.get("dequeue_count", 0)
            
            # Extract task data
            task_id = message.get("task_id")
            execution_log_id = message.get("execution_log_id")
            parent_execution_log_id = message.get("parent_execution_log_id")
            task_type = message.get("task_type")
            org_id = message.get("org_id")
            user_id = message.get("user_id")
            priority = message.get("priority", "medium")
            params = message.get("params", {})
            
            logger.info(f"Processing queue message: task_id={task_id}, execution_log_id={execution_log_id}, parent_execution_log_id={parent_execution_log_id}, message_id={message_id}")
            
            # Validate required fields
            if not all([task_id, execution_log_id, task_type, org_id, user_id]):
                logger.error(f"Missing required fields in message: task_id={task_id}, execution_log_id={execution_log_id}")
                return False
            
            # Validate and sanitize parameters using comprehensive service
            from src.shared.parameter_validation_service import get_parameter_validation_service
            validation_service = get_parameter_validation_service()
            params = validation_service.validate_and_sanitize_parameters(params)
            
            # Extract execution context
            execution_context = self.extract_execution_context(message)
            if not execution_context:
                logger.error(f"Failed to extract execution context from message: {message_id}")
                return False
            
            # Record execution context if this is a child task
            if parent_execution_log_id:
                try:
                    self.execution_context_manager.record_child_task(
                        parent_execution_log_id=parent_execution_log_id,
                        child_execution_log_id=execution_log_id,
                        child_task_id=task_id,
                        task_type=task_type,
                        params=params
                    )
                    logger.debug(f"Recorded child task relationship: parent={parent_execution_log_id}, child={execution_log_id}")
                except Exception as e:
                    logger.warning(f"Failed to record child task relationship: {str(e)}")
            
            # Update task status to running
            task_updated = self.task_status_service.update_task_status(
                task_id=task_id,
                status="running",
                progress=0,
                message=f"Processing task from queue (message_id: {message_id})"
            )
            
            if not task_updated:
                logger.warning(f"Failed to update task status to running: {task_id}")
            
            # Process the actual task based on task type
            processing_result = self._process_task_by_type(
                task_type=task_type,
                task_id=task_id,
                execution_log_id=execution_log_id,
                org_id=org_id,
                user_id=user_id,
                params=params,
                execution_context=execution_context
            )
            
            if processing_result["success"]:
                # Update task status to completed
                self.task_status_service.update_task_status(
                    task_id=task_id,
                    status="completed",
                    progress=100,
                    message="Task completed successfully",
                    result=json.dumps(processing_result.get("result", {}))
                )
                
                # Mark execution context as completed
                try:
                    self.execution_context_manager.mark_execution_completed(execution_log_id)
                    logger.debug(f"Marked execution as completed: execution_log_id={execution_log_id}")
                except Exception as e:
                    logger.warning(f"Failed to mark execution as completed: {str(e)}")
                
                logger.info(f"Successfully processed queue message: task_id={task_id}, execution_log_id={execution_log_id}")
                return True
            else:
                # Handle task processing failure
                error_message = processing_result.get("error", "Unknown error during task processing")
                
                # Update task status to failed
                self.task_status_service.update_task_status(
                    task_id=task_id,
                    status="failed",
                    progress=0,
                    message=f"Task processing failed: {error_message}",
                    error=error_message
                )
                
                # Mark execution context as failed
                try:
                    self.execution_context_manager.mark_execution_failed(execution_log_id, error_message)
                    logger.debug(f"Marked execution as failed: execution_log_id={execution_log_id}")
                except Exception as e:
                    logger.warning(f"Failed to mark execution as failed: {str(e)}")
                
                logger.error(f"Task processing failed: task_id={task_id}, error={error_message}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing queue message: {str(e)}")
            
            # Try to update task status if we have the task_id
            task_id = message.get("task_id")
            if task_id:
                try:
                    self.task_status_service.update_task_status(
                        task_id=task_id,
                        status="failed",
                        progress=0,
                        message=f"Message processing error: {str(e)}",
                        error=str(e)
                    )
                    
                    # Mark execution context as failed
                    execution_log_id = message.get("execution_log_id")
                    if execution_log_id:
                        self.execution_context_manager.mark_execution_failed(execution_log_id, str(e))
                        
                except Exception as update_error:
                    logger.error(f"Failed to update task status after processing error: {str(update_error)}")
            
            return False
    
    def extract_execution_context(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract execution context from queue message
        
        Args:
            message: Queue message data
        
        Returns:
            Dict[str, Any]: Execution context if successful, None otherwise
        """
        try:
            execution_log_id = message.get("execution_log_id")
            parent_execution_log_id = message.get("parent_execution_log_id")
            
            if not execution_log_id:
                logger.error("No execution_log_id found in message")
                return None
            
            # Build execution context
            execution_context = {
                "execution_log_id": execution_log_id,
                "parent_execution_log_id": parent_execution_log_id,
                "task_id": message.get("task_id"),
                "task_type": message.get("task_type"),
                "org_id": message.get("org_id"),
                "user_id": message.get("user_id"),
                "priority": message.get("priority", "medium"),
                "created_at": message.get("created_at"),
                "retry_count": message.get("retry_count", 0),
                "queue_metadata": message.get("queue_metadata", {}),
                "params": message.get("params", {})
            }
            
            logger.debug(f"Extracted execution context: execution_log_id={execution_log_id}")
            return execution_context
            
        except Exception as e:
            logger.error(f"Error extracting execution context: {str(e)}")
            return None
    
    def propagate_execution_context(
        self,
        execution_log_id: str,
        child_tasks: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Propagate execution context to child tasks
        
        Args:
            execution_log_id: Parent execution log ID
            child_tasks: List of child task data
        
        Returns:
            List[str]: List of created child task IDs
        """
        try:
            created_child_task_ids = []
            
            for child_task in child_tasks:
                try:
                    # Generate child execution log ID
                    child_execution_log_id = str(uuid.uuid4())
                    
                    # Create child task with execution context
                    child_task_created = self.task_status_service.create_task(
                        task_type=child_task.get("task_type"),
                        org_id=child_task.get("org_id"),
                        user_id=child_task.get("user_id"),
                        priority=child_task.get("priority", "medium"),
                        execution_log_id=child_execution_log_id,  # New execution log ID for child
                        params={
                            **child_task.get("params", {}),
                            "parent_execution_log_id": execution_log_id,
                            "parent_task_id": child_task.get("parent_task_id"),
                            "is_child_task": True
                        }
                    )
                    
                    if child_task_created:
                        # Record the parent-child relationship using ExecutionContextManager
                        self.execution_context_manager.record_child_task(
                            parent_execution_log_id=execution_log_id,
                            child_execution_log_id=child_execution_log_id,
                            child_task_id=child_task_created,
                            task_type=child_task.get("task_type"),
                            params=child_task.get("params", {})
                        )
                        
                        created_child_task_ids.append(child_task_created)
                        logger.info(f"Created child task with execution context: child_task_id={child_task_created}, child_execution_log_id={child_execution_log_id}, parent_execution_log_id={execution_log_id}")
                    else:
                        logger.error(f"Failed to create child task for execution_log_id={execution_log_id}")
                        
                except Exception as child_error:
                    logger.error(f"Error creating child task: {str(child_error)}")
                    continue
            
            logger.info(f"Propagated execution context to {len(created_child_task_ids)} child tasks")
            return created_child_task_ids
            
        except Exception as e:
            logger.error(f"Error propagating execution context: {str(e)}")
            return []
    
    def handle_message_error(
        self,
        message: Dict[str, Any],
        error: Exception,
        queue_name: str
    ) -> bool:
        """
        Handle message processing error with retry logic
        
        Args:
            message: Original message data
            error: Exception that occurred
            queue_name: Name of the source queue
        
        Returns:
            bool: True if message should be acknowledged (removed), False to retry
        """
        try:
            task_id = message.get("task_id")
            execution_log_id = message.get("execution_log_id")
            retry_count = message.get("retry_count", 0)
            dequeue_count = message.get("_queue_metadata", {}).get("dequeue_count", 0)
            
            logger.error(f"Handling message error: task_id={task_id}, error={str(error)}, retry_count={retry_count}")
            
            # Check if we should retry or move to poison queue
            if retry_count >= MAX_RETRY_COUNT or dequeue_count >= MAX_RETRY_COUNT:
                # Max retries exceeded, move to poison queue
                error_details = f"Task failed after {max(retry_count, dequeue_count)} retries: {str(error)}"
                
                # Move to poison queue (requirement 6.2)
                poison_success = self._move_to_poison_queue(
                    original_queue_name=queue_name,
                    message_data=message,
                    error_details=error_details
                )
                
                # Update task status to failed
                if task_id:
                    self.task_status_service.update_task_status(
                        task_id=task_id,
                        status="failed",
                        progress=0,
                        message=error_details,
                        error=str(error)
                    )
                
                if poison_success:
                    logger.error(f"Message moved to poison queue after max retries: task_id={task_id}")
                else:
                    logger.error(f"Failed to move message to poison queue, acknowledging anyway: task_id={task_id}")
                
                return True  # Acknowledge message (remove from original queue)
            
            # Implement exponential backoff retry
            delay_seconds = min(
                EXPONENTIAL_BACKOFF_BASE ** retry_count,
                MAX_VISIBILITY_TIMEOUT
            )
            
            # Update retry count in message
            retry_message = {
                **message,
                "retry_count": retry_count + 1,
                "retry_metadata": {
                    "last_retry_at": datetime.now().isoformat(),
                    "retry_delay_seconds": delay_seconds,
                    "error_details": str(error),
                    "retry_attempt": retry_count + 1
                }
            }
            
            # Remove queue metadata to avoid conflicts when re-enqueueing
            retry_message.pop("_queue_metadata", None)
            
            # Re-enqueue message with delay (visibility timeout)
            success = self._requeue_message_with_delay(
                queue_name=queue_name,
                message_data=retry_message,
                delay_seconds=delay_seconds
            )
            
            if success:
                logger.info(f"Message requeued for retry {retry_count + 1} with {delay_seconds}s delay: task_id={task_id}")
                return True  # Acknowledge original message (it's been requeued)
            else:
                logger.error(f"Failed to requeue message for retry: task_id={task_id}")
                return False  # Don't acknowledge, let it retry naturally
                
        except Exception as handle_error:
            logger.error(f"Error in message error handler: {str(handle_error)}")
            return True  # Acknowledge to prevent infinite loops
    
    def _move_to_poison_queue(
        self,
        original_queue_name: str,
        message_data: Dict[str, Any],
        error_details: str
    ) -> bool:
        """
        Move a message to the corresponding poison queue
        
        Args:
            original_queue_name: Name of the original queue
            message_data: Message data to move
            error_details: Details about why the message is being moved
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine poison queue name based on original queue
            poison_queue_name = None
            for priority, queue_name in PRIORITY_QUEUES.items():
                if queue_name == original_queue_name:
                    poison_queue_name = POISON_QUEUES[priority]
                    break
            
            if not poison_queue_name:
                logger.error(f"No poison queue mapping found for {original_queue_name}")
                return False
            
            poison_queue_client = self.queue_clients.get(poison_queue_name)
            if not poison_queue_client:
                logger.error(f"Poison queue client not found for {poison_queue_name}")
                return False
            
            # Add poison queue metadata
            poison_message_data = {
                **message_data,
                "poison_metadata": {
                    "original_queue": original_queue_name,
                    "moved_to_poison_at": datetime.now().isoformat(),
                    "error_details": error_details,
                    "final_retry_count": message_data.get("retry_count", 0),
                    "final_dequeue_count": message_data.get("_queue_metadata", {}).get("dequeue_count", 0)
                }
            }
            
            # Remove original queue metadata to avoid conflicts
            poison_message_data.pop("_queue_metadata", None)
            
            # Send to poison queue
            message_content = json.dumps(poison_message_data)
            poison_queue_client.send_message(
                content=message_content,
                time_to_live=-1  # Never expire poison messages
            )
            
            # Log detailed poison message information (requirement 6.3)
            task_id = message_data.get("task_id")
            execution_log_id = message_data.get("execution_log_id")
            
            logger.error(f"Message moved to poison queue: {poison_queue_name}")
            logger.error(f"  Task ID: {task_id}")
            logger.error(f"  Execution Log ID: {execution_log_id}")
            logger.error(f"  Original Queue: {original_queue_name}")
            logger.error(f"  Final Retry Count: {message_data.get('retry_count', 0)}")
            logger.error(f"  Error Details: {error_details}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error moving message to poison queue: {str(e)}")
            return False
    
    def _requeue_message_with_delay(
        self,
        queue_name: str,
        message_data: Dict[str, Any],
        delay_seconds: int
    ) -> bool:
        """
        Re-enqueue a message with a visibility delay
        
        Args:
            queue_name: Name of the queue
            message_data: Message data to enqueue
            delay_seconds: Delay in seconds before message becomes visible
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            # Serialize message data
            message_content = json.dumps(message_data)
            
            # Send message with visibility timeout (delay)
            queue_client.send_message(
                content=message_content,
                visibility_timeout=delay_seconds,
                time_to_live=MESSAGE_PROCESSING_TIMEOUT
            )
            
            logger.debug(f"Message requeued with {delay_seconds}s delay to {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error requeuing message: {str(e)}")
            return False
    
    def _process_task_by_type(
        self,
        task_type: str,
        task_id: str,
        execution_log_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any],
        execution_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process task based on its type
        
        Args:
            task_type: Type of task to process
            task_id: Task ID
            execution_log_id: Execution log ID
            org_id: Organization ID
            user_id: User ID
            params: Task parameters
            execution_context: Execution context
        
        Returns:
            Dict[str, Any]: Processing result with success status and data
        """
        try:
            logger.info(f"Processing task type: {task_type}, task_id={task_id}")
            
            # Add execution context to params for task processors
            enhanced_params = {
                **params,
                "execution_log_id": execution_log_id,
                "execution_context": execution_context
            }
            
            # Route to appropriate task processor based on task type
            if task_type == "Integration_Scan":
                return self._process_integration_scan(task_id, org_id, user_id, enhanced_params)
            elif task_type == "Data_Export":
                return self._process_data_export(task_id, org_id, user_id, enhanced_params)
            elif task_type == "Security_Analysis":
                return self._process_security_analysis(task_id, org_id, user_id, enhanced_params)
            elif task_type == "Profile_Analysis":
                return self._process_profile_analysis(task_id, org_id, user_id, enhanced_params)
            elif task_type == "Permission_Scan":
                return self._process_permission_scan(task_id, org_id, user_id, enhanced_params)
            elif task_type == "Metadata_Extraction":
                return self._process_metadata_extraction(task_id, org_id, user_id, enhanced_params)
            else:
                logger.warning(f"Unknown task type: {task_type}")
                return {
                    "success": False,
                    "error": f"Unknown task type: {task_type}"
                }
                
        except Exception as e:
            logger.error(f"Error processing task by type: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_integration_scan(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process integration scan task
        
        Args:
            task_id: Task ID
            org_id: Organization ID
            user_id: User ID
            params: Task parameters including execution context
        
        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing integration scan: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual integration scan logic
            # This would involve:
            # 1. Connecting to Salesforce using credentials
            # 2. Performing security scans
            # 3. Creating child tasks for specific scan components
            # 4. Propagating execution context to child tasks
            
            # For now, simulate successful processing
            result = {
                "scan_type": "integration_scan",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            # Example of creating child tasks with execution context propagation
            child_tasks = [
                {
                    "task_type": "Security_Analysis",
                    "org_id": org_id,
                    "user_id": user_id,
                    "priority": "medium",
                    "parent_task_id": task_id,
                    "params": {"scan_component": "profiles"}
                },
                {
                    "task_type": "Permission_Scan",
                    "org_id": org_id,
                    "user_id": user_id,
                    "priority": "medium",
                    "parent_task_id": task_id,
                    "params": {"scan_component": "permission_sets"}
                }
            ]
            
            # Propagate execution context to child tasks
            child_task_ids = self.propagate_execution_context(execution_log_id, child_tasks)
            result["child_task_ids"] = child_task_ids
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing integration scan: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_data_export(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process data export task"""
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing data export: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual data export logic
            result = {
                "export_type": "data_export",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing data export: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_security_analysis(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process security analysis task"""
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing security analysis: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual security analysis logic
            result = {
                "analysis_type": "security_analysis",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing security analysis: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_profile_analysis(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process profile analysis task"""
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing profile analysis: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual profile analysis logic
            result = {
                "analysis_type": "profile_analysis",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing profile analysis: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_permission_scan(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process permission scan task"""
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing permission scan: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual permission scan logic
            result = {
                "scan_type": "permission_scan",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing permission scan: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _process_metadata_extraction(
        self,
        task_id: str,
        org_id: str,
        user_id: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process metadata extraction task"""
        try:
            execution_log_id = params.get("execution_log_id")
            logger.info(f"Processing metadata extraction: task_id={task_id}, execution_log_id={execution_log_id}")
            
            # TODO: Implement actual metadata extraction logic
            result = {
                "extraction_type": "metadata_extraction",
                "org_id": org_id,
                "user_id": user_id,
                "execution_log_id": execution_log_id,
                "processed_at": datetime.now().isoformat(),
                "status": "completed"
            }
            
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error processing metadata extraction: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def acknowledge_message(
        self,
        queue_name: str,
        message_id: str,
        pop_receipt: str
    ) -> bool:
        """
        Acknowledge (delete) a message from the queue
        
        Args:
            queue_name: Name of the queue
            message_id: Message ID
            pop_receipt: Pop receipt from the message
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            queue_client.delete_message(message_id, pop_receipt)
            logger.debug(f"Message acknowledged: message_id={message_id}, queue={queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error acknowledging message: {str(e)}")
            return False
    
    def get_poison_queue_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all poison queues
        
        Returns:
            Dict[str, Dict[str, Any]]: Statistics for each poison queue
        """
        try:
            poison_stats = {}
            
            for priority, poison_queue_name in POISON_QUEUES.items():
                try:
                    poison_queue_client = self.queue_clients.get(poison_queue_name)
                    if poison_queue_client:
                        properties = poison_queue_client.get_queue_properties()
                        poison_stats[poison_queue_name] = {
                            "priority": priority,
                            "message_count": properties.approximate_message_count,
                            "metadata": properties.metadata
                        }
                    else:
                        poison_stats[poison_queue_name] = {
                            "priority": priority,
                            "message_count": 0,
                            "error": "Queue client not available"
                        }
                        
                except Exception as e:
                    poison_stats[poison_queue_name] = {
                        "priority": priority,
                        "message_count": 0,
                        "error": str(e)
                    }
            
            return poison_stats
            
        except Exception as e:
            logger.error(f"Error getting poison queue statistics: {str(e)}")
            return {}
    
    def peek_poison_messages(
        self,
        poison_queue_name: str,
        max_messages: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Peek at messages in a poison queue for troubleshooting
        
        Args:
            poison_queue_name: Name of the poison queue
            max_messages: Maximum number of messages to peek
        
        Returns:
            List[Dict[str, Any]]: List of poison messages with metadata
        """
        try:
            poison_queue_client = self.queue_clients.get(poison_queue_name)
            if not poison_queue_client:
                logger.error(f"Poison queue client not found: {poison_queue_name}")
                return []
            
            messages = poison_queue_client.peek_messages(max_messages=max_messages)
            poison_messages = []
            
            for message in messages:
                try:
                    message_data = json.loads(message.content)
                    poison_messages.append({
                        "message_id": message.id,
                        "inserted_on": message.inserted_on.isoformat() if message.inserted_on else None,
                        "expires_on": message.expires_on.isoformat() if message.expires_on else None,
                        "task_id": message_data.get("task_id"),
                        "execution_log_id": message_data.get("execution_log_id"),
                        "parent_execution_log_id": message_data.get("parent_execution_log_id"),
                        "task_type": message_data.get("task_type"),
                        "poison_metadata": message_data.get("poison_metadata", {}),
                        "original_queue": message_data.get("poison_metadata", {}).get("original_queue"),
                        "error_details": message_data.get("poison_metadata", {}).get("error_details")
                    })
                except json.JSONDecodeError:
                    poison_messages.append({
                        "message_id": message.id,
                        "error": "Failed to parse message content",
                        "raw_content": message.content[:200]
                    })
            
            return poison_messages
            
        except Exception as e:
            logger.error(f"Error peeking poison messages: {str(e)}")
            return []

    def get_execution_context_details(self, execution_log_id: str) -> Dict[str, Any]:
        """
        Get detailed execution context information including parent-child relationships
        
        Args:
            execution_log_id: The execution log ID to query
            
        Returns:
            Dict[str, Any]: Execution context details including parent and child relationships
        """
        try:
            return self.execution_context_manager.get_execution_context(execution_log_id)
        except Exception as e:
            logger.error(f"Error getting execution context details: {str(e)}")
            return {}

    def get_child_tasks(self, execution_log_id: str) -> List[Dict[str, Any]]:
        """
        Get all child tasks for a given execution log ID
        
        Args:
            execution_log_id: The parent execution log ID
            
        Returns:
            List[Dict[str, Any]]: List of child task details
        """
        try:
            return self.execution_context_manager.get_child_tasks(execution_log_id)
        except Exception as e:
            logger.error(f"Error getting child tasks: {str(e)}")
            return []

    def get_parent_task(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get parent task information for a given execution log ID
        
        Args:
            execution_log_id: The child execution log ID
            
        Returns:
            Optional[Dict[str, Any]]: Parent task details or None if no parent
        """
        try:
            return self.execution_context_manager.get_parent_task(execution_log_id)
        except Exception as e:
            logger.error(f"Error getting parent task: {str(e)}")
            return None
    
    def receive_messages(
        self,
        queue_name: str,
        max_messages: int = 1,
        visibility_timeout: int = DEFAULT_VISIBILITY_TIMEOUT
    ) -> List[Dict[str, Any]]:
        """
        Receive messages from a queue
        
        Args:
            queue_name: Name of the queue
            max_messages: Maximum number of messages to receive
            visibility_timeout: Visibility timeout in seconds
        
        Returns:
            List[Dict[str, Any]]: List of received messages with metadata
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return []
            
            # Receive messages from queue
            messages = queue_client.receive_messages(
                max_messages=max_messages,
                visibility_timeout=visibility_timeout
            )
            
            decoded_messages = []
            for message in messages:
                try:
                    # Parse message content
                    message_data = json.loads(message.content)
                    
                    # Add queue metadata
                    message_data["_queue_metadata"] = {
                        "message_id": message.id,
                        "pop_receipt": message.pop_receipt,
                        "dequeue_count": message.dequeue_count,
                        "next_visible_on": message.next_visible_on.isoformat() if message.next_visible_on else None,
                        "queue_name": queue_name
                    }
                    
                    decoded_messages.append(message_data)
                    
                except Exception as decode_error:
                    logger.error(f"Error decoding message from {queue_name}: {str(decode_error)}")
                    continue
            
            logger.debug(f"Received {len(decoded_messages)} messages from {queue_name}")
            return decoded_messages
            
        except Exception as e:
            logger.error(f"Error receiving messages from {queue_name}: {str(e)}")
            return []


# Global processor instance
_queue_message_processor = None


def get_queue_message_processor() -> QueueMessageProcessor:
    """
    Get the global queue message processor instance
    
    Returns:
        QueueMessageProcessor: The processor instance
    """
    global _queue_message_processor
    
    if _queue_message_processor is None:
        _queue_message_processor = QueueMessageProcessor()
        logger.debug("Created global queue message processor instance")
    
    return _queue_message_processor