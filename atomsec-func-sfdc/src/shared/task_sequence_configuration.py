"""
Task Sequence Configuration and Validation

This module provides configuration and validation for sequential task execution:
- Task sequence definition and validation
- Execution log ID validation and tracking configuration
- Task dependency and sequence validation
- Configuration for proper task sequence execution and coordination

Best practices implemented:
- Sequential task execution configuration
- Execution log coordination
- Task dependency validation
- Configuration-driven task sequences
- Validation rules for task parameters
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import re
import uuid

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import existing modules
from src.shared.common import is_local_dev, is_test_env

class TaskType(Enum):
    """Task types in the SFDC service"""
    SFDC_AUTHENTICATE = "sfdc_authenticate"
    HEALTH_CHECK = "health_check"
    METADATA_EXTRACTION = "metadata_extraction"
    PROFILE_ANALYSIS = "profile_analysis"
    PERMISSION_SET_ANALYSIS = "permission_set_analysis"
    SECURITY_ANALYSIS = "security_analysis"
    PMD_APEX_SECURITY = "pmd_apex_security"
    CUSTOM_TASK = "custom_task"

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"

class ValidationSeverity(Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

@dataclass
class TaskParameter:
    """Task parameter definition"""
    name: str
    type: str  # string, integer, boolean, object, array
    required: bool = True
    default_value: Any = None
    validation_rules: List[str] = field(default_factory=list)
    description: str = ""
    sensitive: bool = False  # For secure access tokens, etc.

@dataclass
class TaskDependency:
    """Task dependency definition"""
    task_type: TaskType
    required: bool = True
    condition: Optional[str] = None  # Condition for dependency (e.g., "status == 'completed'")

@dataclass
class TaskDefinition:
    """Task definition with configuration and validation rules"""
    task_type: TaskType
    name: str
    description: str
    parameters: List[TaskParameter] = field(default_factory=list)
    dependencies: List[TaskDependency] = field(default_factory=list)
    timeout_seconds: int = 300
    retry_attempts: int = 3
    retry_delay_seconds: int = 5
    required_in_sequence: bool = True
    can_run_parallel: bool = False
    validation_rules: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TaskSequenceDefinition:
    """Task sequence definition"""
    name: str
    description: str
    tasks: List[TaskDefinition] = field(default_factory=list)
    execution_timeout_seconds: int = 1800  # 30 minutes
    allow_partial_completion: bool = False
    stop_on_first_failure: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationResult:
    """Validation result"""
    is_valid: bool
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    code: Optional[str] = None

@dataclass
class ExecutionLogValidation:
    """Execution log validation configuration"""
    execution_log_id_pattern: str = r"^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"
    required_fields: List[str] = field(default_factory=lambda: ["execution_log_id", "task_type", "timestamp"])
    max_execution_time_hours: int = 24
    enable_tracking: bool = True
    enable_correlation: bool = True

class TaskSequenceConfiguration:
    """
    Task sequence configuration manager for SFDC service
    Manages sequential task execution configuration and validation
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize task sequence configuration
        
        Args:
            config_file: Path to configuration file (optional)
        """
        self._config_file = config_file
        self._task_definitions: Dict[TaskType, TaskDefinition] = {}
        self._sequence_definitions: Dict[str, TaskSequenceDefinition] = {}
        self._execution_log_validation = ExecutionLogValidation()
        self._validation_rules: Dict[str, Callable] = {}
        
        logger.info("Initializing Task Sequence Configuration")
        
        # Register built-in validation rules
        self._register_validation_rules()
        
        # Load configuration
        self._load_configuration()
    
    def _register_validation_rules(self):
        """Register built-in validation rules"""
        self._validation_rules = {
            "required": self._validate_required,
            "uuid": self._validate_uuid,
            "email": self._validate_email,
            "url": self._validate_url,
            "positive_integer": self._validate_positive_integer,
            "non_empty_string": self._validate_non_empty_string,
            "secure_token": self._validate_secure_token,
            "execution_log_id": self._validate_execution_log_id,
            "task_type": self._validate_task_type,
            "json_object": self._validate_json_object,
            "max_length": self._validate_max_length,
            "min_length": self._validate_min_length,
            "regex": self._validate_regex
        }
    
    def _load_configuration(self):
        """Load task sequence configuration"""
        try:
            if self._config_file and os.path.exists(self._config_file):
                self._load_from_file(self._config_file)
            else:
                self._load_default_configuration()
            
            logger.info(f"Loaded {len(self._task_definitions)} task definitions and {len(self._sequence_definitions)} sequence definitions")
            
        except Exception as e:
            logger.error(f"Error loading task sequence configuration: {str(e)}")
            self._load_default_configuration()
    
    def _load_from_file(self, config_file: str):
        """Load configuration from file"""
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
                self._parse_configuration(config_data)
                logger.info(f"Loaded task sequence configuration from {config_file}")
        except Exception as e:
            logger.error(f"Error loading configuration from file {config_file}: {str(e)}")
            raise
    
    def _load_default_configuration(self):
        """Load default task sequence configuration"""
        default_config = {
            "execution_log_validation": {
                "execution_log_id_pattern": r"^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$",
                "required_fields": ["execution_log_id", "task_type", "timestamp"],
                "max_execution_time_hours": 24,
                "enable_tracking": True,
                "enable_correlation": True
            },
            "task_definitions": {
                "sfdc_authenticate": {
                    "name": "SFDC Authentication",
                    "description": "Authenticate with Salesforce using provided credentials",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "client_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "non_empty_string"],
                            "description": "Salesforce client ID",
                            "sensitive": True
                        },
                        {
                            "name": "client_secret",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "secure_token"],
                            "description": "Salesforce client secret",
                            "sensitive": True
                        },
                        {
                            "name": "username",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "email"],
                            "description": "Salesforce username"
                        },
                        {
                            "name": "private_key",
                            "type": "string",
                            "required": False,
                            "validation_rules": ["secure_token"],
                            "description": "Private key for JWT authentication",
                            "sensitive": True
                        }
                    ],
                    "dependencies": [],
                    "timeout_seconds": 60,
                    "retry_attempts": 3,
                    "retry_delay_seconds": 5,
                    "required_in_sequence": True,
                    "can_run_parallel": False
                },
                "health_check": {
                    "name": "Health Check",
                    "description": "Perform health checks on Salesforce connection and services",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "check_types",
                            "type": "array",
                            "required": False,
                            "default_value": ["connection", "permissions", "limits"],
                            "description": "Types of health checks to perform"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "sfdc_authenticate",
                            "required": True,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 120,
                    "retry_attempts": 2,
                    "retry_delay_seconds": 10,
                    "required_in_sequence": True,
                    "can_run_parallel": False
                },
                "metadata_extraction": {
                    "name": "Metadata Extraction",
                    "description": "Extract Salesforce metadata for analysis",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "metadata_types",
                            "type": "array",
                            "required": False,
                            "default_value": ["Profile", "PermissionSet", "CustomObject"],
                            "description": "Types of metadata to extract"
                        },
                        {
                            "name": "include_managed",
                            "type": "boolean",
                            "required": False,
                            "default_value": False,
                            "description": "Include managed package metadata"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "health_check",
                            "required": True,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 600,
                    "retry_attempts": 2,
                    "retry_delay_seconds": 15,
                    "required_in_sequence": True,
                    "can_run_parallel": False
                },
                "profile_analysis": {
                    "name": "Profile Analysis",
                    "description": "Analyze Salesforce profiles for security risks",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "analysis_types",
                            "type": "array",
                            "required": False,
                            "default_value": ["permissions", "settings", "restrictions"],
                            "description": "Types of profile analysis to perform"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "metadata_extraction",
                            "required": True,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 300,
                    "retry_attempts": 2,
                    "retry_delay_seconds": 10,
                    "required_in_sequence": False,
                    "can_run_parallel": True
                },
                "permission_set_analysis": {
                    "name": "Permission Set Analysis",
                    "description": "Analyze Salesforce permission sets for security risks",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "analysis_depth",
                            "type": "string",
                            "required": False,
                            "default_value": "standard",
                            "validation_rules": ["regex:^(basic|standard|deep)$"],
                            "description": "Depth of permission set analysis"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "metadata_extraction",
                            "required": True,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 300,
                    "retry_attempts": 2,
                    "retry_delay_seconds": 10,
                    "required_in_sequence": False,
                    "can_run_parallel": True
                },
                "security_analysis": {
                    "name": "Security Analysis",
                    "description": "Perform comprehensive security analysis",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "security_categories",
                            "type": "array",
                            "required": False,
                            "default_value": ["authentication", "authorization", "data_access"],
                            "description": "Categories of security analysis"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "profile_analysis",
                            "required": False,
                            "condition": "status == 'completed'"
                        },
                        {
                            "task_type": "permission_set_analysis",
                            "required": False,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 240,
                    "retry_attempts": 2,
                    "retry_delay_seconds": 10,
                    "required_in_sequence": False,
                    "can_run_parallel": True
                },
                "pmd_apex_security": {
                    "name": "PMD Apex Security Scan",
                    "description": "Perform PMD security scanning on Apex code",
                    "parameters": [
                        {
                            "name": "execution_log_id",
                            "type": "string",
                            "required": True,
                            "validation_rules": ["required", "execution_log_id"],
                            "description": "Execution log ID for tracking"
                        },
                        {
                            "name": "scan_categories",
                            "type": "array",
                            "required": False,
                            "default_value": ["security", "performance"],
                            "description": "PMD scan categories"
                        },
                        {
                            "name": "max_findings",
                            "type": "integer",
                            "required": False,
                            "default_value": 10000,
                            "validation_rules": ["positive_integer"],
                            "description": "Maximum number of findings to process"
                        }
                    ],
                    "dependencies": [
                        {
                            "task_type": "metadata_extraction",
                            "required": True,
                            "condition": "status == 'completed'"
                        }
                    ],
                    "timeout_seconds": 900,
                    "retry_attempts": 1,
                    "retry_delay_seconds": 30,
                    "required_in_sequence": True,
                    "can_run_parallel": False
                }
            },
            "sequence_definitions": {
                "standard_sfdc_scan": {
                    "name": "Standard SFDC Security Scan",
                    "description": "Standard sequential task execution for SFDC security scanning",
                    "tasks": [
                        "sfdc_authenticate",
                        "health_check",
                        "metadata_extraction",
                        "profile_analysis",
                        "permission_set_analysis",
                        "security_analysis",
                        "pmd_apex_security"
                    ],
                    "execution_timeout_seconds": 1800,
                    "allow_partial_completion": False,
                    "stop_on_first_failure": True
                },
                "quick_health_check": {
                    "name": "Quick Health Check",
                    "description": "Quick health check sequence",
                    "tasks": [
                        "sfdc_authenticate",
                        "health_check"
                    ],
                    "execution_timeout_seconds": 300,
                    "allow_partial_completion": True,
                    "stop_on_first_failure": True
                },
                "metadata_only_scan": {
                    "name": "Metadata Only Scan",
                    "description": "Metadata extraction and analysis only",
                    "tasks": [
                        "sfdc_authenticate",
                        "health_check",
                        "metadata_extraction",
                        "profile_analysis",
                        "permission_set_analysis"
                    ],
                    "execution_timeout_seconds": 1200,
                    "allow_partial_completion": True,
                    "stop_on_first_failure": False
                }
            }
        }
        
        self._parse_configuration(default_config)
        logger.info("Loaded default task sequence configuration")
    
    def _parse_configuration(self, config_data: Dict[str, Any]):
        """Parse configuration data"""
        try:
            # Parse execution log validation
            if "execution_log_validation" in config_data:
                validation_config = config_data["execution_log_validation"]
                self._execution_log_validation = ExecutionLogValidation(
                    execution_log_id_pattern=validation_config.get("execution_log_id_pattern", self._execution_log_validation.execution_log_id_pattern),
                    required_fields=validation_config.get("required_fields", self._execution_log_validation.required_fields),
                    max_execution_time_hours=validation_config.get("max_execution_time_hours", self._execution_log_validation.max_execution_time_hours),
                    enable_tracking=validation_config.get("enable_tracking", self._execution_log_validation.enable_tracking),
                    enable_correlation=validation_config.get("enable_correlation", self._execution_log_validation.enable_correlation)
                )
            
            # Parse task definitions
            if "task_definitions" in config_data:
                for task_type_str, task_config in config_data["task_definitions"].items():
                    try:
                        task_type = TaskType(task_type_str)
                        
                        # Parse parameters
                        parameters = []
                        for param_config in task_config.get("parameters", []):
                            parameters.append(TaskParameter(
                                name=param_config["name"],
                                type=param_config["type"],
                                required=param_config.get("required", True),
                                default_value=param_config.get("default_value"),
                                validation_rules=param_config.get("validation_rules", []),
                                description=param_config.get("description", ""),
                                sensitive=param_config.get("sensitive", False)
                            ))
                        
                        # Parse dependencies
                        dependencies = []
                        for dep_config in task_config.get("dependencies", []):
                            dependencies.append(TaskDependency(
                                task_type=TaskType(dep_config["task_type"]),
                                required=dep_config.get("required", True),
                                condition=dep_config.get("condition")
                            ))
                        
                        # Create task definition
                        task_def = TaskDefinition(
                            task_type=task_type,
                            name=task_config["name"],
                            description=task_config["description"],
                            parameters=parameters,
                            dependencies=dependencies,
                            timeout_seconds=task_config.get("timeout_seconds", 300),
                            retry_attempts=task_config.get("retry_attempts", 3),
                            retry_delay_seconds=task_config.get("retry_delay_seconds", 5),
                            required_in_sequence=task_config.get("required_in_sequence", True),
                            can_run_parallel=task_config.get("can_run_parallel", False),
                            validation_rules=task_config.get("validation_rules", []),
                            metadata=task_config.get("metadata", {})
                        )
                        
                        self._task_definitions[task_type] = task_def
                        
                    except Exception as e:
                        logger.error(f"Error parsing task definition '{task_type_str}': {str(e)}")
            
            # Parse sequence definitions
            if "sequence_definitions" in config_data:
                for seq_name, seq_config in config_data["sequence_definitions"].items():
                    try:
                        # Parse task list
                        tasks = []
                        for task_type_str in seq_config.get("tasks", []):
                            task_type = TaskType(task_type_str)
                            if task_type in self._task_definitions:
                                tasks.append(self._task_definitions[task_type])
                            else:
                                logger.warning(f"Task type '{task_type_str}' not found in task definitions")
                        
                        # Create sequence definition
                        seq_def = TaskSequenceDefinition(
                            name=seq_config["name"],
                            description=seq_config["description"],
                            tasks=tasks,
                            execution_timeout_seconds=seq_config.get("execution_timeout_seconds", 1800),
                            allow_partial_completion=seq_config.get("allow_partial_completion", False),
                            stop_on_first_failure=seq_config.get("stop_on_first_failure", True),
                            metadata=seq_config.get("metadata", {})
                        )
                        
                        self._sequence_definitions[seq_name] = seq_def
                        
                    except Exception as e:
                        logger.error(f"Error parsing sequence definition '{seq_name}': {str(e)}")
            
        except Exception as e:
            logger.error(f"Error parsing configuration: {str(e)}")
            raise
    
    def validate_execution_log_id(self, execution_log_id: str) -> ValidationResult:
        """
        Validate execution log ID format and requirements
        
        Args:
            execution_log_id: Execution log ID to validate
            
        Returns:
            ValidationResult: Validation result
        """
        try:
            if not execution_log_id:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="Execution log ID is required",
                    field="execution_log_id",
                    code="MISSING_EXECUTION_LOG_ID"
                )
            
            # Validate format
            if not re.match(self._execution_log_validation.execution_log_id_pattern, execution_log_id):
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Execution log ID format is invalid. Expected UUID format.",
                    field="execution_log_id",
                    code="INVALID_EXECUTION_LOG_ID_FORMAT"
                )
            
            return ValidationResult(
                is_valid=True,
                severity=ValidationSeverity.INFO,
                message="Execution log ID is valid",
                field="execution_log_id"
            )
            
        except Exception as e:
            logger.error(f"Error validating execution log ID: {str(e)}")
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Validation error: {str(e)}",
                field="execution_log_id",
                code="VALIDATION_ERROR"
            )
    
    def validate_task_parameters(self, task_type: TaskType, parameters: Dict[str, Any]) -> List[ValidationResult]:
        """
        Validate task parameters against task definition
        
        Args:
            task_type: Type of task
            parameters: Task parameters to validate
            
        Returns:
            List[ValidationResult]: List of validation results
        """
        results = []
        
        try:
            if task_type not in self._task_definitions:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Task type '{task_type.value}' not found in configuration",
                    code="UNKNOWN_TASK_TYPE"
                ))
                return results
            
            task_def = self._task_definitions[task_type]
            
            # Validate each parameter
            for param_def in task_def.parameters:
                param_value = parameters.get(param_def.name)
                
                # Check required parameters
                if param_def.required and param_value is None:
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Required parameter '{param_def.name}' is missing",
                        field=param_def.name,
                        code="MISSING_REQUIRED_PARAMETER"
                    ))
                    continue
                
                # Use default value if parameter is missing
                if param_value is None and param_def.default_value is not None:
                    param_value = param_def.default_value
                
                # Validate parameter value
                if param_value is not None:
                    param_results = self._validate_parameter_value(param_def, param_value)
                    results.extend(param_results)
            
            # Check for unexpected parameters
            expected_params = {param.name for param in task_def.parameters}
            for param_name in parameters.keys():
                if param_name not in expected_params:
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.WARNING,
                        message=f"Unexpected parameter '{param_name}' for task type '{task_type.value}'",
                        field=param_name,
                        code="UNEXPECTED_PARAMETER"
                    ))
            
        except Exception as e:
            logger.error(f"Error validating task parameters: {str(e)}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter validation error: {str(e)}",
                code="VALIDATION_ERROR"
            ))
        
        return results
    
    def _validate_parameter_value(self, param_def: TaskParameter, value: Any) -> List[ValidationResult]:
        """Validate individual parameter value"""
        results = []
        
        try:
            # Type validation
            type_result = self._validate_parameter_type(param_def, value)
            if type_result:
                results.append(type_result)
                if not type_result.is_valid:
                    return results  # Stop validation if type is wrong
            
            # Apply validation rules
            for rule in param_def.validation_rules:
                rule_result = self._apply_validation_rule(rule, value, param_def.name)
                if rule_result:
                    results.append(rule_result)
            
        except Exception as e:
            logger.error(f"Error validating parameter value: {str(e)}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter validation error: {str(e)}",
                field=param_def.name,
                code="VALIDATION_ERROR"
            ))
        
        return results
    
    def _validate_parameter_type(self, param_def: TaskParameter, value: Any) -> Optional[ValidationResult]:
        """Validate parameter type"""
        expected_type = param_def.type.lower()
        
        if expected_type == "string" and not isinstance(value, str):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter '{param_def.name}' must be a string",
                field=param_def.name,
                code="INVALID_TYPE"
            )
        elif expected_type == "integer" and not isinstance(value, int):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter '{param_def.name}' must be an integer",
                field=param_def.name,
                code="INVALID_TYPE"
            )
        elif expected_type == "boolean" and not isinstance(value, bool):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter '{param_def.name}' must be a boolean",
                field=param_def.name,
                code="INVALID_TYPE"
            )
        elif expected_type == "array" and not isinstance(value, list):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter '{param_def.name}' must be an array",
                field=param_def.name,
                code="INVALID_TYPE"
            )
        elif expected_type == "object" and not isinstance(value, dict):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Parameter '{param_def.name}' must be an object",
                field=param_def.name,
                code="INVALID_TYPE"
            )
        
        return None
    
    def _apply_validation_rule(self, rule: str, value: Any, field_name: str) -> Optional[ValidationResult]:
        """Apply validation rule to value"""
        try:
            # Parse rule (format: rule_name:parameter)
            if ":" in rule:
                rule_name, rule_param = rule.split(":", 1)
            else:
                rule_name = rule
                rule_param = None
            
            # Get validation function
            validation_func = self._validation_rules.get(rule_name)
            if not validation_func:
                logger.warning(f"Unknown validation rule: {rule_name}")
                return None
            
            # Apply validation
            if rule_param:
                is_valid, message = validation_func(value, rule_param)
            else:
                is_valid, message = validation_func(value)
            
            if not is_valid:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=message,
                    field=field_name,
                    code=f"VALIDATION_RULE_{rule_name.upper()}"
                )
            
        except Exception as e:
            logger.error(f"Error applying validation rule '{rule}': {str(e)}")
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Validation rule error: {str(e)}",
                field=field_name,
                code="VALIDATION_RULE_ERROR"
            )
        
        return None
    
    def validate_task_sequence(self, sequence_name: str, execution_log_id: str) -> List[ValidationResult]:
        """
        Validate task sequence configuration and execution log ID
        
        Args:
            sequence_name: Name of the task sequence
            execution_log_id: Execution log ID for tracking
            
        Returns:
            List[ValidationResult]: List of validation results
        """
        results = []
        
        try:
            # Validate execution log ID
            log_result = self.validate_execution_log_id(execution_log_id)
            results.append(log_result)
            
            # Validate sequence exists
            if sequence_name not in self._sequence_definitions:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Task sequence '{sequence_name}' not found in configuration",
                    code="UNKNOWN_SEQUENCE"
                ))
                return results
            
            sequence_def = self._sequence_definitions[sequence_name]
            
            # Validate task dependencies
            dependency_results = self._validate_task_dependencies(sequence_def)
            results.extend(dependency_results)
            
            # Validate sequence configuration
            if not sequence_def.tasks:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Task sequence '{sequence_name}' has no tasks defined",
                    code="EMPTY_SEQUENCE"
                ))
            
        except Exception as e:
            logger.error(f"Error validating task sequence: {str(e)}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Sequence validation error: {str(e)}",
                code="VALIDATION_ERROR"
            ))
        
        return results
    
    def _validate_task_dependencies(self, sequence_def: TaskSequenceDefinition) -> List[ValidationResult]:
        """Validate task dependencies in sequence"""
        results = []
        
        try:
            # Build task index
            task_index = {task.task_type: i for i, task in enumerate(sequence_def.tasks)}
            
            # Validate each task's dependencies
            for i, task in enumerate(sequence_def.tasks):
                for dependency in task.dependencies:
                    if dependency.task_type not in task_index:
                        results.append(ValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.ERROR,
                            message=f"Task '{task.task_type.value}' depends on '{dependency.task_type.value}' which is not in the sequence",
                            code="MISSING_DEPENDENCY"
                        ))
                    elif task_index[dependency.task_type] >= i:
                        results.append(ValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.ERROR,
                            message=f"Task '{task.task_type.value}' depends on '{dependency.task_type.value}' which comes after it in the sequence",
                            code="CIRCULAR_DEPENDENCY"
                        ))
            
        except Exception as e:
            logger.error(f"Error validating task dependencies: {str(e)}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Dependency validation error: {str(e)}",
                code="VALIDATION_ERROR"
            ))
        
        return results
    
    def get_task_definition(self, task_type: TaskType) -> Optional[TaskDefinition]:
        """Get task definition by type"""
        return self._task_definitions.get(task_type)
    
    def get_sequence_definition(self, sequence_name: str) -> Optional[TaskSequenceDefinition]:
        """Get sequence definition by name"""
        return self._sequence_definitions.get(sequence_name)
    
    def get_execution_log_validation(self) -> ExecutionLogValidation:
        """Get execution log validation configuration"""
        return self._execution_log_validation
    
    def get_all_task_types(self) -> List[TaskType]:
        """Get all configured task types"""
        return list(self._task_definitions.keys())
    
    def get_all_sequence_names(self) -> List[str]:
        """Get all configured sequence names"""
        return list(self._sequence_definitions.keys())
    
    # Built-in validation functions
    def _validate_required(self, value: Any) -> tuple[bool, str]:
        """Validate required field"""
        if value is None or (isinstance(value, str) and not value.strip()):
            return False, "Field is required"
        return True, "Valid"
    
    def _validate_uuid(self, value: Any) -> tuple[bool, str]:
        """Validate UUID format"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        try:
            uuid.UUID(value)
            return True, "Valid UUID"
        except ValueError:
            return False, "Invalid UUID format"
    
    def _validate_email(self, value: Any) -> tuple[bool, str]:
        """Validate email format"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, value):
            return True, "Valid email"
        return False, "Invalid email format"
    
    def _validate_url(self, value: Any) -> tuple[bool, str]:
        """Validate URL format"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        if re.match(url_pattern, value):
            return True, "Valid URL"
        return False, "Invalid URL format"
    
    def _validate_positive_integer(self, value: Any) -> tuple[bool, str]:
        """Validate positive integer"""
        if not isinstance(value, int):
            return False, "Must be an integer"
        
        if value <= 0:
            return False, "Must be a positive integer"
        return True, "Valid positive integer"
    
    def _validate_non_empty_string(self, value: Any) -> tuple[bool, str]:
        """Validate non-empty string"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        if not value.strip():
            return False, "String cannot be empty"
        return True, "Valid non-empty string"
    
    def _validate_secure_token(self, value: Any) -> tuple[bool, str]:
        """Validate secure token format"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        if len(value) < 10:
            return False, "Token must be at least 10 characters long"
        
        # Additional security checks could be added here
        return True, "Valid secure token"
    
    def _validate_execution_log_id(self, value: Any) -> tuple[bool, str]:
        """Validate execution log ID"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        if not re.match(self._execution_log_validation.execution_log_id_pattern, value):
            return False, "Invalid execution log ID format"
        return True, "Valid execution log ID"
    
    def _validate_task_type(self, value: Any) -> tuple[bool, str]:
        """Validate task type"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        try:
            TaskType(value)
            return True, "Valid task type"
        except ValueError:
            return False, f"Invalid task type. Valid types: {[t.value for t in TaskType]}"
    
    def _validate_json_object(self, value: Any) -> tuple[bool, str]:
        """Validate JSON object"""
        if isinstance(value, dict):
            return True, "Valid JSON object"
        
        if isinstance(value, str):
            try:
                json.loads(value)
                return True, "Valid JSON string"
            except json.JSONDecodeError:
                return False, "Invalid JSON format"
        
        return False, "Must be a JSON object or valid JSON string"
    
    def _validate_max_length(self, value: Any, max_length: str) -> tuple[bool, str]:
        """Validate maximum length"""
        try:
            max_len = int(max_length)
            if isinstance(value, str) and len(value) > max_len:
                return False, f"String length exceeds maximum of {max_len} characters"
            return True, "Valid length"
        except ValueError:
            return False, "Invalid max_length parameter"
    
    def _validate_min_length(self, value: Any, min_length: str) -> tuple[bool, str]:
        """Validate minimum length"""
        try:
            min_len = int(min_length)
            if isinstance(value, str) and len(value) < min_len:
                return False, f"String length is below minimum of {min_len} characters"
            return True, "Valid length"
        except ValueError:
            return False, "Invalid min_length parameter"
    
    def _validate_regex(self, value: Any, pattern: str) -> tuple[bool, str]:
        """Validate regex pattern"""
        if not isinstance(value, str):
            return False, "Must be a string"
        
        try:
            if re.match(pattern, value):
                return True, "Matches pattern"
            return False, f"Does not match required pattern: {pattern}"
        except re.error:
            return False, "Invalid regex pattern"

# Global configuration instance
_task_sequence_config: Optional[TaskSequenceConfiguration] = None

def get_task_sequence_configuration() -> TaskSequenceConfiguration:
    """Get the global task sequence configuration instance"""
    global _task_sequence_config
    if _task_sequence_config is None:
        config_file = os.environ.get("TASK_SEQUENCE_CONFIG_FILE")
        _task_sequence_config = TaskSequenceConfiguration(config_file)
    return _task_sequence_config