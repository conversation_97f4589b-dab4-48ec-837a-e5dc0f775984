"""
Base Repository

This module provides a base repository class for database operations.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from src.shared.db_service_client import get_db_client

logger = logging.getLogger(__name__)

class BaseRepository:
    """Base repository class for database operations"""
    
    def __init__(self):
        """Initialize base repository"""
        self.db_client = get_db_client()
    
    def execute_query(self, query: str, params: Tuple = None) -> List[Tuple]:
        """
        Execute a SQL query
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of tuples containing query results
        """
        try:
            if not self.db_client:
                logger.error("Database client not available")
                return []
            
            # Use the DB service client to execute the query
            result = self.db_client.execute_query(query, params or ())
            return result
            
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []
    
    def execute_non_query(self, query: str, params: Tuple = None) -> int:
        """
        Execute a non-query SQL statement (INSERT, UPDATE, DELETE)
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows
        """
        try:
            if not self.db_client:
                logger.error("Database client not available")
                return 0
            
            # Use the DB service client to execute the non-query
            result = self.db_client.execute_non_query(query, params or ())
            return result
            
        except Exception as e:
            logger.error(f"Error executing non-query: {e}")
            return 0
    
    def get_single_result(self, query: str, params: Tuple = None) -> Optional[Tuple]:
        """
        Get a single result from a query
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Single result tuple or None
        """
        try:
            results = self.execute_query(query, params)
            return results[0] if results else None
            
        except Exception as e:
            logger.error(f"Error getting single result: {e}")
            return None
    
    def get_scalar_result(self, query: str, params: Tuple = None) -> Optional[Any]:
        """
        Get a scalar result from a query
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Scalar result or None
        """
        try:
            result = self.get_single_result(query, params)
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"Error getting scalar result: {e}")
            return None 