import asyncio
import logging
import json
from datetime import datetime
from src.shared.data_access import BlobStorageRepository, get_policies_result_table_repo, get_table_storage_repository
from src.shared.salesforce_utils import execute_salesforce_query
from .utils import (
    load_best_practices_xml,
    normalize_profile_name,
    normalize_permission_name,
    normalize_value,
    parse_profile_permissions
)

def process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id=None):
    """
    Compare profile metadata in blob storage with best practices, store results in PoliciesResult, and mark task as completed.
    For each profile, fetch the number of active (not frozen) users assigned to that profile and store in ProfileAssignmentCount table.
    Also process permission set best-practice analysis and store those results with the same execution_log_id.
    """
    org_name = params.get('org_name')
    blob_prefix = params.get('blob_prefix')
    access_token = params.get('access_token')
    instance_url = params.get('instance_url')
    environment = params.get('environment', 'production')
    if not org_name or not blob_prefix:
        logging.error("Missing org_name or blob_prefix in params for profiles_permission_sets task.")
        return

    import os
    best_practices_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'best_practices', 'Profiles_PermissionSetRisks-BestPractice.xml'))
    best_practices = load_best_practices_xml(best_practices_path)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")

    try:
        # Call the core logic and get results
        profile_results = process_profiles_in_blob(blob_repo, blob_prefix, best_practices)

        # --- Build normalized Salesforce profile name map ---
        normalized_profile_id_map = {}
        profile_active_user_counts = {}
        if access_token and instance_url:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            # Query all profiles
            profile_query = "SELECT Id, Name FROM Profile"
            profile_query_result = loop.run_until_complete(
                execute_salesforce_query(
                    profile_query,
                    access_token=access_token,
                    instance_url=instance_url,
                    environment=environment
                )
            )
            if profile_query_result and "records" in profile_query_result:
                for rec in profile_query_result["records"]:
                    norm_name = normalize_profile_name(rec["Name"])
                    normalized_profile_id_map[norm_name] = rec["Id"]
            # Query active user counts per profile (PII-free)
            try:
                user_count_query = ("SELECT ProfileId, Profile.Name, COUNT(Id) cnt "
                                   "FROM User WHERE IsActive = TRUE AND Id NOT IN "
                                   "(SELECT UserId FROM UserLogin WHERE IsFrozen = true) "
                                   "GROUP BY ProfileId, Profile.Name")
                user_count_result = loop.run_until_complete(
                    execute_salesforce_query(
                        user_count_query,
                        access_token=access_token,
                        instance_url=instance_url,
                        environment=environment
                    )
                )
                if user_count_result and 'records' in user_count_result:
                    for rec in user_count_result['records']:
                        profile_id = rec.get('ProfileId')
                        profile_name = rec.get('Name')
                        count = rec.get('cnt')
                        if profile_id and profile_name and count is not None:
                            profile_active_user_counts[profile_id] = {
                                'ProfileId': profile_id,
                                'ProfileName': profile_name,
                                'AssignmentCount': int(count)
                            }
                else:
                    logging.error("Active user count query returned no records or unexpected format. Skipping assignment count insertion for this run.")
            except Exception as e:
                logging.error(f"Error querying active user counts per profile: {e}")
                logging.error("Skipping assignment count insertion for this run due to query error.")
            finally:
                loop.close()

        unmatched_profiles = []
        repo = get_policies_result_table_repo()
        assignment_repo = get_table_storage_repository("ProfileAssignmentCount")
        inserted_count = 0
        inserted_profiles = set()  # Track inserted profile names to prevent duplicates
        for profile_name, results in profile_results.items():
            norm_blob_name = normalize_profile_name(profile_name)
            profile_id = normalized_profile_id_map.get(norm_blob_name)
            # Special-case: Admin.profile should match System Administrator
            if not profile_id and norm_blob_name == 'admin':
                profile_id = normalized_profile_id_map.get('system administrator')
            if not profile_id and norm_blob_name == 'standard':
                profile_id = normalized_profile_id_map.get('standard platform user')
            if not profile_id:
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_name:
                        profile_id = sf_id
                        break
            if not profile_id:
                norm_blob_no_space = norm_blob_name.replace(' ', '')
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_no_space:
                        profile_id = sf_id
                        break
            if not profile_id:
                unmatched_profiles.append(profile_name)

            # Only insert one PoliciesResult per profile per run
            if profile_name not in inserted_profiles and results and len(results) > 0:
                entity = {
                    'PartitionKey': str(org_id),
                    'RowKey': f"{profile_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                    'OrgValue': json.dumps(results),  # Store the full array as JSON
                    'IntegrationId': str(org_id),
                    'TaskStatusId': execution_log_id,
                    'CreatedAt': datetime.now().isoformat(),
                    'ProfileName': profile_name,
                    'Type': 'ProfilePermissions',
                }
                try:
                    repo.insert_entity(entity)
                    inserted_count += 1
                    inserted_profiles.add(profile_name)
                except Exception as e:
                    logging.error(f"Failed to insert PoliciesResult for profile {profile_name}: {e}")

            # Always insert assignment count for every profile (restore previous behavior)
            assignment_count = 0
            try:
                if profile_id and profile_id in profile_active_user_counts:
                    assignment_count = profile_active_user_counts[profile_id]['AssignmentCount']
                if assignment_repo:
                    assignment_entity = {
                        'PartitionKey': str(org_id),
                        'RowKey': f"{profile_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                        'ProfileName': profile_name,
                        'ProfileId': profile_id or '',
                        'AssignmentCount': assignment_count,
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        # PoliciesResultProfileRowKey is a logical foreign key to PoliciesResult.RowKey (may be missing if no PoliciesResult row)
                        'PoliciesResultProfileRowKey': None,
                        'CreatedAt': datetime.now().isoformat(),
                        'Type': 'ProfilePermissions',
                    }
                    assignment_repo.insert_entity(assignment_entity)

                    # --- Restore permission set assignment count logic per profile ---
                    if profile_id and access_token and instance_url:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        # 1. Get all active user IDs for this profile
                        user_ids_query = f"SELECT Id FROM User WHERE ProfileId = '{profile_id}' AND IsActive = true AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true)"
                        user_ids_result = loop.run_until_complete(
                            execute_salesforce_query(
                                user_ids_query,
                                access_token=access_token,
                                instance_url=instance_url,
                                environment=environment
                            )
                        )
                        user_ids = [rec['Id'] for rec in user_ids_result.get('records', [])] if user_ids_result and 'records' in user_ids_result else []
                        if user_ids:
                            # 2. Query PermissionSetAssignment for these user IDs, group by PermissionSet.Name
                            # Salesforce SOQL has a 2000 item IN clause limit, so chunk if needed
                            chunk_size = 200
                            for i in range(0, len(user_ids), chunk_size):
                                chunk = user_ids[i:i+chunk_size]
                                ids_str = ",".join([f"'{uid}'" for uid in chunk])
                                psa_query = f"SELECT PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId IN ({ids_str})"
                                psa_result = loop.run_until_complete(
                                    execute_salesforce_query(
                                        psa_query,
                                        access_token=access_token,
                                        instance_url=instance_url,
                                        environment=environment
                                    )
                                )
                                psa_counts = {}
                                if psa_result and 'records' in psa_result:
                                    for rec in psa_result['records']:
                                        ps_name = rec.get('PermissionSet', {}).get('Name')
                                        if ps_name:
                                            psa_counts[ps_name] = psa_counts.get(ps_name, 0) + 1
                                # Insert each PermissionSet assignment count for this profile chunk
                                for ps_name, count in psa_counts.items():
                                    if count > 0:
                                        ps_assignment_entity = {
                                            'PartitionKey': str(org_id),
                                            'RowKey': f"{profile_name}-{ps_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                                            'ProfileName': profile_name,
                                            'ProfileId': profile_id or '',
                                            'PermissionSetName': ps_name,
                                            'AssignmentCount': count,
                                            'IntegrationId': str(org_id),
                                            'TaskStatusId': execution_log_id,
                                            'PoliciesResultProfileRowKey': None,
                                            'CreatedAt': datetime.now().isoformat(),
                                            'Type': 'ProfilePermissionSetAssignment',
                                        }
                                        assignment_repo.insert_entity(ps_assignment_entity)
                        loop.close()
            except Exception as e:
                logging.error(f"Error inserting assignment count for profile {profile_name}: {e}")

        # Log unmatched profiles for review
        if unmatched_profiles:
            logging.warning(f"Unmatched profiles (could not find ProfileId in Salesforce): {unmatched_profiles}")

        # After processing profiles, process permission sets with the same execution_log_id
        from .permission_sets import process_permissionsets_in_blob
        process_permissionsets_in_blob(
            blob_repo,
            blob_prefix,
            best_practices,
            org_id,
            execution_log_id,
            access_token=access_token,
            instance_url=instance_url,
            environment=environment
        )
        # Mark task as completed
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Profiles and Permission Sets scan completed and results stored."
        )
    except Exception as e:
        logging.error(f"Error processing profiles_permission_sets_task: {e}")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing profiles_permission_sets_task: {e}"
        )

# You may need to move process_profiles_in_blob and related helpers to utils.py or this file if not already present. 