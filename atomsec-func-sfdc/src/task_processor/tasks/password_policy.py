import os
import asyncio
import json
import logging
from datetime import datetime
import xml.etree.ElementTree as ET
from src.shared.data_access import BlobStorageRepository
from src.shared.db_service_client import get_db_client
from src.shared.salesforce_utils import execute_salesforce_query
from .utils import (
    load_best_practices_xml,
    normalize_profile_name,
    normalize_permission_name,
    normalize_value,
    extract_user_license_from_profile,
    get_active_profiles_and_permissionsets
)

def is_profile_match(norm_profile_name, policy_norm):
    admin_names = {'admin', 'system administrator'}
    standard_names = {'standard', 'standard platform user'}
    if norm_profile_name == policy_norm:
        return True
    if norm_profile_name in admin_names and policy_norm in admin_names:
        return True
    if norm_profile_name in standard_names and policy_norm in standard_names:
        return True
    return False

def parse_best_practices_with_picklists(bp_path):
    tree = ET.parse(bp_path)
    root = tree.getroot()
    practices_by_usertype = {}
    for usertype in root.findall('.//UserType'):
        usertype_name = usertype.attrib.get('name', 'BLANK')
        for practice in usertype.findall('Practice'):
            bp = {child.tag: child.text for child in practice if child.tag != 'PicklistValues'}
            picklist_elem = practice.find('PicklistValues')
            if picklist_elem is not None:
                picklist_map = {}
                for val in picklist_elem.findall('Value'):
                    code = val.attrib.get('code')
                    label = val.text
                    if code and label:
                        picklist_map[code] = label
                bp['PicklistValues'] = picklist_map
            bp['UserType'] = usertype_name
            practices_by_usertype.setdefault(usertype_name, []).append(bp)
    return practices_by_usertype

def process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id):
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[PasswordPolicyTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for Password Policy task."
        )
        return
    # Load Password Policy best practices XML
    bp_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'best_practices', 'Password_Policy-BestPractice.xml'))
    best_practices_by_usertype = parse_best_practices_with_picklists(bp_path)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_db_client()
    try:
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        # 2. Traverse password policy files in blob storage
        policy_folder = f"{blob_prefix}/profilePasswordPolicies/"
        policy_blobs = blob_repo.list_blobs(name_starts_with=policy_folder)
        # Build a map of normalized profile name (from <profile> tag) to password policy blob name
        policy_blob_map = {}
        for blob_name in policy_blobs:
            if not blob_name.endswith('.profilePasswordPolicy'):
                continue
            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                root = ET.fromstring(xml_bytes)
                profile_tag = None
                for elem in root.iter():
                    if elem.tag.endswith('profile'):
                        profile_tag = elem
                        break
                if profile_tag is not None and profile_tag.text:
                    norm_profile = profile_tag.text.strip().lower()
                    policy_blob_map[norm_profile] = blob_name
            except Exception as e:
                logger.error(f"[PasswordPolicyTask {task_id}] Error reading <profile> tag in {blob_name}: {e}")
        inserted_profiles = set()
        # Traverse each password policy file and match to profile metadata
        for norm_profile_name in set(active_profile_names):  # Deduplicate upfront
            if norm_profile_name in inserted_profiles:
                continue  # Skip duplicate
            policy_blob = policy_blob_map.get(norm_profile_name)
            # Special-case: Admin.profile should match System Administrator
            is_admin = norm_profile_name == 'admin'
            is_standard = norm_profile_name == 'standard'
            # Find the corresponding profile metadata file
            profile_folder = f"{blob_prefix}/profiles/"
            profile_blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
            matched_profile_blob = None
            for p_blob in profile_blobs:
                if not p_blob.endswith('.profile'):
                    continue
                base_name = os.path.basename(p_blob).replace('.profile', '')
                norm_base = base_name.strip().lower()
                if (
                    norm_base == norm_profile_name or
                    (is_admin and norm_base == 'system administrator') or
                    (is_standard and norm_base == 'standard platform user')
                ):
                    matched_profile_blob = p_blob
                    break
            user_license = 'BLANK'
            if matched_profile_blob:
                try:
                    profile_xml_bytes = blob_repo.get_blob_bytes(matched_profile_blob)
                    user_license = extract_user_license_from_profile(profile_xml_bytes) or 'BLANK'
                except Exception as e:
                    logger.error(f"[PasswordPolicyTask {task_id}] Error extracting userLicense from {matched_profile_blob}: {e}")
            best_practices = best_practices_by_usertype.get(user_license, best_practices_by_usertype.get('BLANK', []))
            results_arr = []
            if not policy_blob:
                # No password policy file found for this profile
                for bp in best_practices:
                    display_standard_value = bp.get('StandardValue')
                    picklist_map = bp.get('PicklistValues', {})
                    if picklist_map and bp.get('StandardValue') in picklist_map:
                        display_standard_value = picklist_map[bp.get('StandardValue')]
                    results_arr.append({
                        'SalesforceSetting': bp.get('SalesforceSetting'),
                        'StandardValue': display_standard_value,
                        'OrgValue': None,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'Issue': 'Password policy file missing for profile'
                    })
            else:
                try:
                    xml_bytes = blob_repo.get_blob_bytes(policy_blob)
                    xml_str = xml_bytes.decode('utf-8', errors='ignore')
                    root = ET.fromstring(xml_bytes)
                    for bp in best_practices:
                        bp_setting = (bp.get('SalesforceSetting') or '').strip()
                        bp_standard_value = (bp.get('StandardValue') or '').strip()
                        picklist_map = bp.get('PicklistValues', {})
                        found_elem = None
                        for elem in root.iter():
                            if elem.tag.endswith(bp_setting):
                                found_elem = elem
                                break
                        if found_elem is not None:
                            org_value = found_elem.text.strip() if found_elem.text else None
                            match = normalize_value(org_value) == normalize_value(bp_standard_value)
                            display_org_value = org_value
                            display_standard_value = bp_standard_value
                            if picklist_map:
                                display_org_value = picklist_map.get(org_value, org_value)
                                display_standard_value = picklist_map.get(bp_standard_value, bp_standard_value)
                            if not match:
                                results_arr.append({
                                    'SalesforceSetting': bp_setting,
                                    'StandardValue': display_standard_value,
                                    'OrgValue': display_org_value,
                                    'Description': bp.get('Description'),
                                    'OWASP': bp.get('OWASP'),
                                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                    'Issue': 'Value does not match best practice'
                                })
                        else:
                            display_standard_value = bp_standard_value
                            if picklist_map and bp_standard_value in picklist_map:
                                display_standard_value = picklist_map[bp_standard_value]
                            results_arr.append({
                                'SalesforceSetting': bp_setting,
                                'StandardValue': display_standard_value,
                                'OrgValue': None,
                                'Description': bp.get('Description'),
                                'OWASP': bp.get('OWASP'),
                                'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                'Issue': 'Setting missing from password policy file'
                            })
                except Exception as e:
                    logger.error(f"[PasswordPolicyTask {task_id}] Error parsing password policy XML for {norm_profile_name}: {e}")
                    for bp in best_practices:
                        results_arr.append({
                            'SalesforceSetting': bp.get('SalesforceSetting'),
                            'StandardValue': bp.get('StandardValue'),
                            'OrgValue': None,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                            'Issue': 'Password policy file unreadable for profile'
                        })
            unique_results = []
            seen = set()
            for entry in results_arr:
                entry_json = json.dumps(entry, sort_keys=True)
                if entry_json not in seen:
                    unique_results.append(entry)
                    seen.add(entry_json)
            
            # Store results using DB service client
            policies_data = []
            for result in unique_results:
                policy_data = {
                    'Setting': result.get('SalesforceSetting', 'PasswordPolicy'),
                    'OrgValue': result.get('OrgValue', ''),
                    'StandardValue': result.get('StandardValue', ''),
                    'OWASPCategory': result.get('OWASP', ''),
                    'IssueDescription': result.get('Description', ''),
                    'Recommendations': result.get('Issue', ''),
                    'Severity': result.get('RiskTypeBasedOnSeverity', 'Medium'),
                    'Weakness': 'PASSWORD_POLICY_ISSUE',
                    'IntegrationId': str(org_id),
                    'TaskStatusId': execution_log_id,
                    'CreatedAt': datetime.now().isoformat(),
                    'Type': 'PasswordPolicyProfilePermissions',
                    'ProfileName': norm_profile_name
                }
                policies_data.append(policy_data)

            try:
                repo.store_policies_result_data(str(org_id), execution_log_id, policies_data)
                inserted_profiles.add(norm_profile_name)
            except Exception as e:
                logger.error(f"[PasswordPolicyTask {task_id}] Failed to store password policy results for profile {norm_profile_name}: {e}")
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Password Policy check completed and results stored."
        )
    except Exception as e:
        logger.error(f"[PasswordPolicyTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing Password Policy task: {e}"
        ) 