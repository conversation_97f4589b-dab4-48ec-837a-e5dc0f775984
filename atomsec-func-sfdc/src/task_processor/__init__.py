"""
Task Processor Function

This function processes tasks from the task queue and updates their status.
"""

import json
import logging
import azure.functions as func
import requests
import urllib.parse
import traceback
import re
import xml.etree.ElementTree as ET
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import uuid

# Try to import fuzzywuzzy with fallback
try:
    from fuzzywuzzy import fuzz
    FUZZYWUZZY_AVAILABLE = True
except ImportError:
    FUZZYWUZZY_AVAILABLE = False
    # Note: logger is defined later, so we'll log this warning in the functions that need it

# Import shared modules
from src.shared.background_processor import (
    BackgroundProcessor,
    TASK_STATUS_PENDING,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_STATUS_RETRY,
    TASK_STATUS_CANCELLED,
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_TYPE_PROFILES_PERMISSION_SETS,
    TASK_TYPE_DATA_EXPORT,
    TASK_TYPE_REPORT_GENERATION,
    TASK_TYPE_SCHEDULED_SCAN,
    TASK_TYPE_NOTIFICATION,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_TYPE_PERMISSION_SETS,
    TASK_TYPE_MFA_ENFORCEMENT,
    TASK_TYPE_DEVICE_ACTIVATION,
    TASK_TYPE_LOGIN_IP_RANGES,
    TASK_TYPE_LOGIN_HOURS,
    TASK_TYPE_SESSION_TIMEOUT,
    TASK_TYPE_API_WHITELISTING,
    TASK_TYPE_PASSWORD_POLICY,
    TASK_TYPE_PMD_APEX_SECURITY,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)

from src.shared.salesforce_utils import (
    execute_salesforce_query,
    execute_salesforce_tooling_query,
    get_salesforce_access_token,
    test_salesforce_connection,
    get_salesforce_client
)
from src.shared.auth_service import authenticate_salesforce_integration
from src.shared.data_access import BlobStorageRepository, get_table_storage_repository  # Still needed for blob storage and credentials operations
from src.shared.common import is_local_dev
from src.shared.azure_services import get_secret
from src.shared.metadata_extraction import extract_salesforce_metadata
from src.blueprints.security_analysis import fetch_security_health_check_risks, calculate_health_score, process_and_store_policies_results # For health check task
from src.shared.db_service_client import get_db_client
from fastapi import APIRouter

# Import task processors - temporarily disabled problematic imports
from .tasks.pmd_task import process_pmd_task
from .tasks.password_policy import process_password_policy_task
from .tasks.device_activation import process_device_activation_task
from .tasks.session_timeout import process_session_timeout_task
from .tasks.mfa_enforcement import process_mfa_enforcement_task
from .tasks.login_ip_ranges import process_login_ip_ranges_task
from .tasks.login_hours import process_login_hours_task
from .tasks.api_whitelisting import process_api_whitelisting_task
from .tasks.profiles_permission_sets import process_profiles_permission_sets_task
from .tasks.pmd_task import process_pmd_task

router = APIRouter()

# Configure module-level logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Task type mapping from string to constants
TASK_TYPE_MAPPING = {
    "sfdc_authenticate": TASK_TYPE_SFDC_AUTHENTICATE,
    "metadata_extraction": TASK_TYPE_METADATA_EXTRACTION,
    "health_check": TASK_TYPE_HEALTH_CHECK,
    "profiles": TASK_TYPE_PROFILES,
    "profiles_permission_sets": TASK_TYPE_PROFILES_PERMISSION_SETS,
    "permission_sets": TASK_TYPE_PERMISSION_SETS,
    "mfa_enforcement": TASK_TYPE_MFA_ENFORCEMENT,
    "device_activation": TASK_TYPE_DEVICE_ACTIVATION,
    "login_ip_ranges": TASK_TYPE_LOGIN_IP_RANGES,
    "login_hours": TASK_TYPE_LOGIN_HOURS,
    "session_timeout": TASK_TYPE_SESSION_TIMEOUT,
    "api_whitelisting": TASK_TYPE_API_WHITELISTING,
    "password_policy": TASK_TYPE_PASSWORD_POLICY,
    "pmd_apex_security": TASK_TYPE_PMD_APEX_SECURITY,
    "overview": TASK_TYPE_OVERVIEW,
    "data_export": TASK_TYPE_DATA_EXPORT,
    "report_generation": TASK_TYPE_REPORT_GENERATION,
    "scheduled_scan": TASK_TYPE_SCHEDULED_SCAN,
    "notification": TASK_TYPE_NOTIFICATION
}

# Task dependency definitions
TASK_DEPENDENCIES = {
    # Health check runs immediately after authentication to verify connection
    TASK_TYPE_HEALTH_CHECK: [TASK_TYPE_SFDC_AUTHENTICATE],

    # Overview and profiles tasks run immediately after authentication (no dependencies on metadata)
    TASK_TYPE_OVERVIEW: [TASK_TYPE_SFDC_AUTHENTICATE],
    TASK_TYPE_PROFILES: [TASK_TYPE_SFDC_AUTHENTICATE],

    # Metadata extraction runs after authentication (not after health check)
    TASK_TYPE_METADATA_EXTRACTION: [TASK_TYPE_SFDC_AUTHENTICATE],

    # All policy tasks depend on metadata extraction being completed
    TASK_TYPE_PROFILES_PERMISSION_SETS: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_MFA_ENFORCEMENT: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_DEVICE_ACTIVATION: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_LOGIN_IP_RANGES: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_LOGIN_HOURS: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_SESSION_TIMEOUT: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_API_WHITELISTING: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PASSWORD_POLICY: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PMD_APEX_SECURITY: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PERMISSION_SETS: [TASK_TYPE_METADATA_EXTRACTION]
}

def check_task_dependencies_and_wait(processor: BackgroundProcessor, task_type: str, org_id: str, execution_log_id: str) -> tuple[bool, str]:
    """
    Check if all prerequisite tasks for a given task type have been completed.
    Implements sequential task execution with a 5-second delay before dependent tasks start.

    This replaces the retry-based approach with a cleaner sequential execution model:
    - sfdc_authenticate runs first (no dependencies)
    - health_check waits 5 seconds after authentication completion
    - metadata_extraction waits 5 seconds after health_check completion

    Args:
        processor: Background processor instance
        task_type: Type of task to check dependencies for (string or constant)
        org_id: Organization ID
        execution_log_id: Execution log ID for the current workflow

    Returns:
        tuple: (can_proceed, message)
    """
    import time

    try:
        # Convert string task type to constant if needed
        if task_type in TASK_TYPE_MAPPING:
            constant_task_type = TASK_TYPE_MAPPING[task_type]
            logger.debug(f"Mapped task type '{task_type}' to constant '{constant_task_type}'")
        else:
            # Assume it's already a constant
            constant_task_type = task_type

        # Get required dependencies for this task type
        required_deps = TASK_DEPENDENCIES.get(constant_task_type, [])

        if not required_deps:
            # No dependencies required (e.g., sfdc_authenticate)
            logger.info(f"Task {task_type} has no dependencies, proceeding immediately")
            return True, "No dependencies required"

        db_client = get_db_client()

        # Check if all dependencies are completed with retry mechanism for database consistency
        max_retries = 5  # Increased from 3 to 5
        retry_delay = 3  # Increased from 2 to 3 seconds
        
        for attempt in range(max_retries):
            all_deps_satisfied = True
            missing_deps = []

            for dep_task_type in required_deps:
                latest_task = None
                
                # Try DB service first
                if db_client:
                    try:
                        # Find the most recent completed prerequisite task for this org, task type, and execution_log_id
                        latest_task = db_client.get_latest_task_by_type(org_id, dep_task_type, "completed", execution_log_id)
                        logger.info(f"[DEPENDENCY CHECK DEBUG] org_id={org_id}, dep_task_type={dep_task_type}, execution_log_id={execution_log_id}, latest_task={latest_task}")
                        
                        # Enhanced logging for dependency checking
                        if latest_task:
                            logger.info(f"[DEPENDENCY CHECK SUCCESS] Found completed dependency task: {dep_task_type} (ID: {latest_task.get('task_id')})")
                        else:
                            logger.warning(f"[DEPENDENCY CHECK MISSING] Could not find completed dependency task: {dep_task_type}")
                            # Log all tasks for this org to help diagnose issues
                            all_org_tasks = db_client.get_tasks_by_org(org_id)
                            if all_org_tasks:
                                logger.info(f"[DEPENDENCY CHECK DETAIL] Found {len(all_org_tasks)} total tasks for org {org_id}")
                                for task in all_org_tasks:
                                    if task.get('task_type') == dep_task_type:
                                        logger.info(f"[DEPENDENCY CHECK DETAIL] Found task of type {dep_task_type}: ID={task.get('task_id')}, status={task.get('status')}, execution_log_id={task.get('execution_log_id')}")
                            else:
                                logger.warning(f"[DEPENDENCY CHECK DETAIL] No tasks found for org {org_id}")

                        if not latest_task:
                            # Try without status filter as fallback
                            latest_task = db_client.get_latest_task_by_type(org_id, dep_task_type, None, execution_log_id)
                            logger.info(f"[DEPENDENCY CHECK FALLBACK] org_id={org_id}, dep_task_type={dep_task_type}, execution_log_id={execution_log_id}, latest_task={latest_task}")
                            if latest_task:
                                logger.info(f"[DEPENDENCY CHECK FALLBACK] Found task with status '{latest_task.get('status')}' instead of 'completed'")
                    except Exception as db_error:
                        logger.warning(f"[DEPENDENCY CHECK] DB service error for {dep_task_type}: {str(db_error)}")
                        latest_task = None
                
                # If DB service failed or returned no result, try local repository
                if not latest_task:
                    try:
                        logger.info(f"[DEPENDENCY CHECK] Trying local repository for {dep_task_type}")
                        from src.shared.background_processor import BackgroundProcessor
                        local_processor = BackgroundProcessor()
                        
                        # Get tasks for the org and filter by type and status
                        org_tasks = local_processor.get_tasks_by_org(org_id, include_completed=True)
                        
                        # Filter by task type, execution_log_id, and completed status
                        matching_tasks = []
                        for task in org_tasks:
                            task_type_match = task.get('task_type') == dep_task_type
                            execution_log_match = task.get('execution_log_id') == execution_log_id
                            status_match = task.get('status', '').lower() == 'completed'
                            
                            if task_type_match and execution_log_match and status_match:
                                matching_tasks.append(task)
                        
                        if matching_tasks:
                            # Sort by created_at descending and take the first one
                            matching_tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                            latest_task = matching_tasks[0]
                            logger.info(f"[DEPENDENCY CHECK] Found completed task via local repository: {latest_task.get('task_id')}")
                        else:
                            logger.warning(f"[DEPENDENCY CHECK] No completed task found in local repository for {dep_task_type}")
                    except Exception as local_error:
                        logger.error(f"[DEPENDENCY CHECK] Local repository error for {dep_task_type}: {str(local_error)}")
                        latest_task = None
                
                # Check if we found a completed task
                if latest_task and latest_task.get('status', '').lower() == 'completed':
                    logger.info(f"[DEPENDENCY CHECK] Found completed task: {latest_task.get('task_id')}")
                else:
                    all_deps_satisfied = False
                    missing_deps.append(dep_task_type)

            if all_deps_satisfied:
                # All dependencies are satisfied - implement 5-second sequential delay
                logger.info(f"[DEPENDENCY CHECK] All dependencies satisfied for {task_type} on attempt {attempt + 1}. Implementing 5-second sequential delay before processing.")
                time.sleep(5.0)  # 5-second delay for sequential execution
                logger.info(f"Sequential delay completed for {task_type}. Proceeding with task processing.")
                return True, "All dependencies satisfied - sequential delay completed"
            
            # Dependencies not met on this attempt
            missing_dep_str = ', '.join(missing_deps)
            logger.warning(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met on attempt {attempt + 1}: {missing_dep_str}")
            
            if attempt < max_retries - 1:
                logger.info(f"[DEPENDENCY CHECK] Retrying in {retry_delay} seconds... (attempt {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
            else:
                # Final attempt failed - try to proceed anyway for critical tasks
                if task_type in ['health_check', 'metadata_extraction', 'profiles_permission_sets', 'mfa_enforcement', 'login_ip_ranges']:
                    logger.warning(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met after {max_retries} attempts, but proceeding anyway for critical task: {missing_dep_str}")
                    return True, f"Proceeding despite missing dependencies: {missing_dep_str}"
                else:
                    # Final attempt failed
                    logger.error(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met after {max_retries} attempts: {missing_dep_str}")
                    return False, f"Prerequisite task '{missing_dep_str}' has not completed successfully after {max_retries} attempts"

    except Exception as e:
        logger.error(f"Error checking task dependencies for {task_type}: {str(e)}")
        # For critical tasks, proceed anyway
        if task_type in ['health_check', 'metadata_extraction', 'profiles_permission_sets', 'mfa_enforcement', 'login_ip_ranges']:
            logger.warning(f"Proceeding with {task_type} despite dependency check error: {str(e)}")
            return True, f"Proceeding despite dependency check error: {str(e)}"
        else:
            return False, f"Error checking dependencies: {str(e)}"

@router.get("/api/profiles-permissions")
def get_profiles_permissions(orgId: str):
    """
    Get profiles and permissions data for an organization (legacy API endpoint)

    Args:
        orgId: Organization ID

    Returns:
        Dict containing profiles and permissions data
    """
    try:
        db_client = get_db_client()

        # 1. Get latest profiles_permission_sets task
        latest_profiles_task = db_client.get_latest_task_by_type(orgId, "profiles_permission_sets", "completed")
        if not latest_profiles_task:
            return {"status": "processing", "message": "Profiles and permissions scan is still running."}

        execution_log_id_profiles = latest_profiles_task.get("execution_log_id")

        # 2. Get latest permission_sets task
        latest_psets_task = db_client.get_latest_task_by_type(orgId, "permission_sets", "completed")
        execution_log_id_psets = None
        if latest_psets_task:
            execution_log_id_psets = latest_psets_task.get("execution_log_id")

        # 3. Query for profile policies (all types for profiles_permission_sets)
        all_policies = db_client.get_policies_by_execution_log(orgId, execution_log_id_profiles)

        # 4. Query for permission set policies (Type = 'PermissionSetPermissions' for permission_sets)
        if execution_log_id_psets:
            permission_set_policies = db_client.get_policies_by_execution_log_and_type(
                orgId, execution_log_id_psets, "PermissionSetPermissions"
            )
            all_policies.extend(permission_set_policies)

        # 5. Also fetch PermissionSetPermissions records for profiles_permission_sets task (legacy support)
        permission_set_policies_profiles = db_client.get_policies_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "PermissionSetPermissions"
        )
        all_policies.extend(permission_set_policies_profiles)

        # 6. Fetch assignments (for latest profiles_permission_sets task)
        profile_assignments = db_client.get_assignments_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "ProfilePermissions"
        )
        permissionset_assignments = db_client.get_assignments_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "ProfilePermissionSetAssignment"
        )

        # 7. Collect all unique permission setting names for dynamic columns
        all_settings = set()
        for policy in all_policies:
            settings_data = policy.get('Settings', '{}')
            if isinstance(settings_data, str):
                try:
                    import json
                    settings = json.loads(settings_data)
                    all_settings.update(settings.keys())
                except json.JSONDecodeError:
                    continue
            elif isinstance(settings_data, dict):
                all_settings.update(settings_data.keys())

        return {
            "status": "completed",
            "settings": sorted(list(all_settings)),
            "policies": all_policies,
            "profileAssignments": profile_assignments,
            "permissionSetAssignments": permissionset_assignments
        }

    except Exception as e:
        logger.error(f"Error getting profiles permissions for org {orgId}: {str(e)}")
        return {
            "status": "error",
            "message": f"Error retrieving profiles and permissions data: {str(e)}"
        }

def main(msg: func.QueueMessage) -> None:
    """
    Process a task from the queue

    Args:
        msg: Queue message
    """
    import traceback
    logger.info("[MAIN] Entered main function for task processing.")
    try:
        # Handle message content
        logger.info("[MAIN] Processing queue message...")

        # With messageEncoding="none" in host.json, Azure Functions provides the message as text
        # We just need to get the message body and parse it as JSON
        try:
            # Get the message body as string
            message_body = msg.get_body().decode('utf-8')
            logger.debug(f"[MAIN] Message body: {message_body}")

            # Parse the message as JSON
            task_data = json.loads(message_body)
            logger.debug(f"[MAIN] Successfully parsed message as JSON: {task_data}")
        except Exception as e:
            logger.error(f"[MAIN] Error processing message: {str(e)}")
            # Log the raw message for debugging
            logger.error(f"[MAIN] Message type: {type(msg)}")
            if hasattr(msg, 'get_body'):
                try:
                    body = msg.get_body()
                    logger.error(f"[MAIN] Message body: {body}")
                    logger.error(f"[MAIN] Message body type: {type(body)}")
                except Exception as body_error:
                    logger.error(f"[MAIN] Error getting message body: {str(body_error)}")
            logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")
            raise ValueError(f"Could not process message: {str(e)}")

        # Log the parsed task data for debugging
        logger.debug(f"[MAIN] Parsed task data: {task_data}")

        # Extract task information
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")

        # Normalize task_type to avoid whitespace/case issues
        task_type = (task_type or "").strip().lower()
        logger.info(f"[MAIN] Received task_type: '{task_type}'")

        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id}")
        logger.debug(f"[MAIN] Task parameters: {params}")

        # Initialize background processor
        processor = BackgroundProcessor()

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=5,
                message="Task started - preventing duplicate processing"
            )
            logger.info(f"[MAIN] Updated task {task_id} status to RUNNING to prevent race conditions")
        except Exception as status_error:
            logger.error(f"[MAIN] Error updating task status: {str(status_error)}")
            # Continue processing even if status update fails

        # Update task status to running
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Starting overview data collection"
        )

        # Extract priority and retry count
        priority = task_data.get("priority", TASK_PRIORITY_MEDIUM)
        retry_count = task_data.get("retry_count", 0)

        # Log task details
        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id} with priority {priority} (retry {retry_count})")

        # Check if this is a recurring task that needs to be rescheduled
        is_recurring = params.get("is_recurring", False)
        if is_recurring and task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Rescheduling recurring task {task_id}.")
            # Reschedule the task for the next occurrence
            schedule_type = params.get("schedule_type", "daily")
            processor.schedule_recurring_task(
                task_type=TASK_TYPE_SCHEDULED_SCAN,
                org_id=org_id,
                user_id=user_id,
                schedule_type=schedule_type,
                params=params,
                priority=priority
            )
            logger.info(f"[MAIN] Rescheduled recurring task {task_id} with schedule type {schedule_type}")

        # Check task dependencies and implement sequential delay before processing
        logger.info(f"[MAIN] Checking dependencies and implementing sequential delay for task {task_id} of type '{task_type}'")
        can_proceed, dependency_message = check_task_dependencies_and_wait(processor, task_type, org_id, execution_log_id)
        if not can_proceed:
            logger.warning(f"[MAIN] Task {task_id} cannot proceed: {dependency_message}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Dependencies not met: {dependency_message}"
            )
            return

        logger.info(f"[MAIN] Dependencies satisfied and sequential delay completed for task {task_id}: {dependency_message}")

        # Convert string task type to constant if needed
        if task_type in TASK_TYPE_MAPPING:
            constant_task_type = TASK_TYPE_MAPPING[task_type]
            logger.debug(f"[MAIN] Mapped task type '{task_type}' to constant '{constant_task_type}'")
        else:
            # Assume it's already a constant
            constant_task_type = task_type

        # Process task based on type
        logger.info(f"[MAIN] Branching to task type handler for {constant_task_type}")
        if constant_task_type == TASK_TYPE_OVERVIEW:
            logger.info(f"[MAIN] Calling process_overview_task for {task_id}")
            process_overview_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_HEALTH_CHECK:
            logger.info(f"[MAIN] Calling process_health_check_task for {task_id}")
            process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PROFILES:
            logger.info(f"[MAIN] Calling process_profiles_task for {task_id}")
            process_profiles_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PROFILES_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_profiles_permission_sets_task for {task_id}")
            process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_permission_sets_task for {task_id}")
            process_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_MFA_ENFORCEMENT:
            logger.info(f"[MAIN] Calling process_mfa_enforcement_task for {task_id}")
            process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_DEVICE_ACTIVATION:
            logger.info(f"[MAIN] Calling process_device_activation_task for {task_id}")
            process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_IP_RANGES:
            logger.info(f"[MAIN] Calling process_login_ip_ranges_task for {task_id}")
            process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_HOURS:
            logger.info(f"[MAIN] Calling process_login_hours_task for {task_id}")
            process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SESSION_TIMEOUT:
            logger.info(f"[MAIN] Calling process_session_timeout_task for {task_id}")
            process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_API_WHITELISTING:
            logger.info(f"[MAIN] Calling process_api_whitelisting_task for {task_id}")
            process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PASSWORD_POLICY:
            logger.info(f"[MAIN] Calling process_password_policy_task for {task_id}")
            process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PMD_APEX_SECURITY:
            logger.info(f"[MAIN] Calling process_pmd_task for {task_id}")
            process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_DATA_EXPORT:
            logger.info(f"[MAIN] Calling process_data_export_task for {task_id}")
            process_data_export_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_REPORT_GENERATION:
            logger.info(f"[MAIN] Calling process_report_generation_task for {task_id}")
            process_report_generation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Calling process_scheduled_scan_task for {task_id}")
            # For scheduled scans, run all the data collection tasks
            process_scheduled_scan_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_NOTIFICATION:
            logger.info(f"[MAIN] Calling process_notification_task for {task_id}")
            process_notification_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_METADATA_EXTRACTION:
            logger.info(f"[MAIN] Calling process_metadata_extraction_task for {task_id}")
            process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SFDC_AUTHENTICATE:
            logger.info(f"[MAIN] Calling process_sfdc_authenticate_task for {task_id}")
            process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id)
        else:
            logger.error(f"[MAIN] Unknown task type: {task_type} (mapped to: {constant_task_type})")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Unknown task type: {task_type}"
            )
        logger.info(f"[MAIN] Exiting main function for task {task_id}")
    except Exception as e:
        logger.error(f"[MAIN] Error processing task: {str(e)}")
        logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")

        # Try to update task status if possible
        try:
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing task: {str(e)}"
            )
        except Exception as update_error:
            logger.error(f"[MAIN] Error updating task status: {str(update_error)}")
            logger.error(f"[MAIN] Traceback (update error):\n{traceback.format_exc()}")

def get_integration_by_id(org_id: str) -> Dict[str, Any]:
    """
    Get integration by ID

    Args:
        org_id: Organization ID

    Returns:
        Dict[str, Any]: Integration if found, None otherwise
    """
    try:
        db_client = get_db_client()
        integration = db_client.get_integration_by_id(org_id)

        if integration:
            logger.info(f"Found integration: {integration.get('name', 'Unknown')} (ID: {org_id})")
            return integration
        else:
            logger.warning(f"No integration found with ID: {org_id}")
            return None

    except Exception as e:
        logger.error(f"Error getting integration by ID {org_id}: {str(e)}")
        return None

# Salesforce API functions are now imported from src.shared.salesforce_utils

def process_overview_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process an overview task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        logger.info(f"Processing overview task {task_id} for organization {org_id}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting overview data collection")

        access_token = params.get("access_token")
        instance_url = params.get("instance_url")
        integration_id_from_params = params.get("integration_id") # Should be same as org_id

        if not all([access_token, instance_url, integration_id_from_params]):
            err_msg = "Missing access_token, instance_url, or integration_id in task parameters for overview."
            logger.error(f"[OverviewTask {task_id}] {err_msg}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
            return

        if org_id != integration_id_from_params:
            logger.warning(f"[OverviewTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=40,
                message="Fetching overview data from Salesforce using provided token."
            )

            # Fetch data from Salesforce
            # 1. Get total profiles
            profiles_query = "SELECT COUNT() FROM Profile"
            profiles_result = execute_salesforce_query(profiles_query, access_token, instance_url)
            total_profiles = profiles_result.get("totalSize", 0) if profiles_result else 0
            logger.info(f"Found {total_profiles} profiles")

            # 2. Get total permission sets
            permission_sets_query = "SELECT COUNT() FROM PermissionSet WHERE IsOwnedByProfile = false"
            permission_sets_result = execute_salesforce_query(permission_sets_query, access_token, instance_url)
            total_permission_sets = permission_sets_result.get("totalSize", 0) if permission_sets_result else 0
            logger.info(f"Found {total_permission_sets} permission sets")

            # Initialize risk counts
            total_risks = 0
            high_risks = 0
            medium_risks = 0
            low_risks = 0

            # Get health score directly from SecurityHealthCheck
            score_query = "SELECT Score FROM SecurityHealthCheck"
            score_result = execute_salesforce_tooling_query(score_query, access_token, instance_url, api_version="v58.0")

            if score_result and "records" in score_result and len(score_result["records"]) > 0:
                # Use the score directly from Salesforce
                health_score = score_result["records"][0].get("Score", 0)
                logger.info(f"Retrieved health score directly from Salesforce: {health_score}")

                # Get total risks count for reference
                risks_count_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                risks_count_result = execute_salesforce_tooling_query(risks_count_query, access_token, instance_url)
                total_risks = risks_count_result.get("totalSize", 0) if risks_count_result else 0
                logger.info(f"Found {total_risks} security health check risks")
            else:
                logger.warning("Could not retrieve health score directly from SecurityHealthCheck, using fallback method")

                # 3. Get security health check data
                health_check_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                health_check_result = execute_salesforce_tooling_query(health_check_query, access_token, instance_url)
                total_risks = health_check_result.get("totalSize", 0) if health_check_result else 0
                logger.info(f"Found {total_risks} security health check risks")

                # 4. Get risk counts by severity
                high_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'HIGH_RISK'"
                high_risks_result = execute_salesforce_tooling_query(high_risks_query, access_token, instance_url)
                high_risks = high_risks_result.get("totalSize", 0) if high_risks_result else 0
                logger.info(f"Found {high_risks} high risks")

                medium_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'MEDIUM_RISK'"
                medium_risks_result = execute_salesforce_tooling_query(medium_risks_query, access_token, instance_url)
                medium_risks = medium_risks_result.get("totalSize", 0) if medium_risks_result else 0
                logger.info(f"Found {medium_risks} medium risks")

                low_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'LOW_RISK'"
                low_risks_result = execute_salesforce_tooling_query(low_risks_query, access_token, instance_url)
                low_risks = low_risks_result.get("totalSize", 0) if low_risks_result else 0
                logger.info(f"Found {low_risks} low risks")

                # Calculate health score (simple algorithm: 100 - (high_risks * 5 + medium_risks * 2 + low_risks))
                health_score = 100 - (high_risks * 5 + medium_risks * 2 + low_risks)
                health_score = max(0, min(100, health_score))  # Ensure score is between 0 and 100
                logger.info(f"Calculated health score using fallback method: {health_score}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=70,
                message="Saving overview data"
            )

            # Save overview data to database via DB service
            db_client = get_db_client()
            overview_data = {
                "health_score": health_score,
                "total_profiles": total_profiles,
                "total_permission_sets": total_permission_sets,
                "total_risks": total_risks,
                "high_risks": high_risks,
                "medium_risks": medium_risks,
                "low_risks": low_risks,
                "last_updated": datetime.now().isoformat(),
                "execution_log_id": execution_log_id
            }

            success = db_client.store_overview_data(org_id, overview_data)
            if success:
                logger.info(f"Saved overview data via DB service for organization {org_id}")
            else:
                logger.error(f"Failed to save overview data via DB service for organization {org_id}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                message="Overview data fetched and saved successfully"
            )
        except Exception as e:
            logger.error(f"Error processing overview task: {str(e)}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing overview task: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error processing overview task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing overview task: {str(e)}"
        )

def process_health_check_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[HealthCheckTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[HealthCheckTask {task_id}] Using execution_log_id: {execution_log_id}")
    # Task-level idempotency check
    task_status = processor.get_task_status(task_id)
    if task_status and task_status.get("status") in (TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED):
        logger.info(f"[HealthCheckTask {task_id}] Task already {task_status.get('status')}, skipping processing.")
        return
    # Execution_log_id-level idempotency check
    if execution_log_id:
        db_client = get_db_client()
        if db_client:
            existing_health_check_records = db_client.get_policies_by_execution_log_and_type(
                org_id=str(org_id),
                execution_log_id=str(execution_log_id),
                policy_type='HealthCheck'
            )
            if existing_health_check_records:
                logger.info(f"[HealthCheckTask {task_id}] Health check data already exists for execution_log_id {execution_log_id}. Marking task as completed.")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message=f"Health check already completed for {org_id} with execution_log_id {execution_log_id}",
                    result=json.dumps({"health_score": 0, "risks_count": len(existing_health_check_records)})
                )
                return
    # Immediate task status update to prevent race conditions
    try:
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=5,
            message="Health check task started - preventing duplicate processing"
        )
        logger.info(f"[HealthCheckTask {task_id}] Updated task status to RUNNING to prevent race conditions")
    except Exception as status_error:
        logger.error(f"[HealthCheckTask {task_id}] Error updating task status: {str(status_error)}")
    """
    Process a health check task
    """
    logger.info(f"[HealthCheckTask {task_id}] Received params: {params}")
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")
    if not all([access_token, instance_url]):
        err_msg = "Missing access_token or instance_url in task parameters for health check."
        logger.error(f"[HealthCheckTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[HealthCheckTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id.")

    try:
        logger.info(f"[HealthCheckTask {task_id}] Fetching security health check risks for {org_id} using provided token.")

        # Handle async operations using asyncio.run to avoid event loop conflicts
        logger.info(f"[HealthCheckTask {task_id}] Running health check async operations")

        def run_async_health_check():
            async def async_health_check():
                risks = await fetch_security_health_check_risks(access_token, instance_url)
                health_score = await calculate_health_score(risks)
                return risks, health_score

            return asyncio.run(async_health_check())

        risks, health_score = run_async_health_check()

        logger.info(f"[HealthCheckTask {task_id}] Calculated health score for {org_id}: {health_score}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, f"Health score calculated: {health_score}")

        # Store raw health check data via DB service
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 75, "Storing health check data")
        
        # CRITICAL FIX: Remove duplicate health check storage - only store in PoliciesResult table
        # The old architecture only stored health check data in PoliciesResult table, not in HealthCheck table
        logger.info(f"[HealthCheckTask {task_id}] Skipping HealthCheck table storage - using PoliciesResult table only")

        # Process and store policies results (this is the correct storage location)
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Processing policies results")
        try:
            db_client = get_db_client()
            process_and_store_policies_results(risks, org_id, execution_log_id)
            logger.info(f"[HealthCheckTask {task_id}] Successfully processed and stored policies results for {org_id}")
        except Exception as policies_error:
            logger.error(f"[HealthCheckTask {task_id}] Error processing policies results: {str(policies_error)}")
            # Don't fail the entire task for this error, just log it

        # Update Integration record via DB service
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 90, "Updating integration record")
        db_client = get_db_client()
        now_iso = datetime.now().isoformat()

        update_data = {
            "health_score": str(health_score),
            "last_health_check_scan": now_iso
        }

        success = db_client.update_integration(org_id, update_data)
        if success:
            logger.info(f"[HealthCheckTask {task_id}] Updated integration {org_id} with health score {health_score} via DB service.")
        else:
            logger.error(f"[HealthCheckTask {task_id}] Failed to update integration {org_id} via DB service.")

        # After health check completes successfully, enqueue metadata extraction task
        logger.info(f"[HealthCheckTask {task_id}] Health check completed successfully.")


        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Health check completed for {org_id}. Score: {health_score}",
            result=json.dumps({"health_score": health_score, "risks_count": len(risks) if isinstance(risks, list) else 0})
        )
    except TypeError as te:
        logger.error(f"[HealthCheckTask {task_id}] TypeError processing health check task for {org_id}: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError during health check: {str(te)}")
    except Exception as e:
        logger.error(f"[HealthCheckTask {task_id}] Error processing health check task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during health check: {str(e)}")

def process_profiles_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a profiles task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    logger.info(f"Processing profiles task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting profiles data collection")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for profiles task."
        logger.error(f"[ProfilesTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[ProfilesTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    try:
        from src.blueprints.profile_system_permissions import (
            fetch_profile_system_permissions,
            fetch_permission_set_system_permissions,
            save_profile_system_permissions,
            save_permission_set_system_permissions
        )

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message="Fetching profile system permissions from Salesforce using provided token."
        )

        # Use the existing event loop instead of creating a new one
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Handle async operations properly
        if loop.is_running():
            # If loop is already running, we need to use a separate thread
            import concurrent.futures

            def run_async_profiles_task():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    profiles = new_loop.run_until_complete(fetch_profile_system_permissions(access_token, instance_url))
                    return profiles
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_profiles_task)
                profiles = future.result()
        else:
            # If loop is not running, we can use run_until_complete
            profiles = loop.run_until_complete(fetch_profile_system_permissions(access_token, instance_url))

        logger.info(f"Fetched {len(profiles)} profiles with system permissions from Salesforce")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=60,
            message="Fetching permission set system permissions from Salesforce using provided token."
        )

        # fetch_permission_set_system_permissions is sync, call directly
        permission_sets = fetch_permission_set_system_permissions(access_token, instance_url)
        logger.info(f"Fetched {len(permission_sets)} permission sets with system permissions from Salesforce")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving profile system permissions via DB service"
        )

        # Store profiles data via DB service (maintaining microservice architecture)
        db_client = get_db_client()
        profile_success = db_client.store_profile_data(org_id, execution_log_id, profiles)
        if profile_success:
            logger.info(f"Saved {len(profiles)} profiles with system permissions via DB service")
        else:
            logger.error(f"Failed to save profiles data via DB service for organization {org_id}")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=85,
            message="Saving permission set system permissions via DB service"
        )

        # Store permission sets data via DB service (maintaining microservice architecture)
        permission_set_success = db_client.store_permission_sets_data(org_id, execution_log_id, permission_sets)
        if permission_set_success:
            logger.info(f"Saved {len(permission_sets)} permission sets with system permissions via DB service")
        else:
            logger.error(f"Failed to save permission sets data via DB service for organization {org_id}")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Profile and permission set system permissions fetched and saved successfully"
        )
    except TypeError as te: # Specific catch for TypeError
        logger.error(f"[ProfilesTask {task_id}] TypeError: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError: {str(te)}")
    except NameError as ne: # Specific catch for NameError
        logger.error(f"[ProfilesTask {task_id}] NameError: {str(ne)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"NameError: {str(ne)}")
    except Exception as e:
        logger.error(f"[ProfilesTask {task_id}] Error processing profiles task: {str(e)}") # Corrected log task_id
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error processing profiles task: {str(e)}")

def process_data_export_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a data export task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing data for export"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract export parameters
        export_format = params.get("format", "csv")
        data_type = params.get("data_type", "health_check")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {export_format.upper()} export for {data_type}"
        )

        # In a real implementation, you would fetch data and generate the export file
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving export file"
        )

        # Generate a file name for the export
        file_name = f"{integration.get('name', 'export')}-{data_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{export_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Export completed successfully",
            result=json.dumps({
                "file_name": file_name,
                "format": export_format,
                "data_type": data_type,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing data export task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing data export task: {str(e)}"
        )

def process_report_generation_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a report generation task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing report data"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract report parameters
        report_type = params.get("report_type", "security_summary")
        report_format = params.get("format", "pdf")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {report_type} report in {report_format.upper()} format"
        )

        # In a real implementation, you would fetch data and generate the report
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving report file"
        )

        # Generate a file name for the report
        file_name = f"{integration.get('name', 'report')}-{report_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{report_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Report generated successfully",
            result=json.dumps({
                "file_name": file_name,
                "report_type": report_type,
                "format": report_format,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing report generation task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing report generation task: {str(e)}"
        )

def process_scheduled_scan_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str = None) -> None:
    try:
        db_client = get_db_client()
        from datetime import datetime
        
        # Get execution_log_id from params if not provided directly
        execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
        
        if not execution_log_id:
            logger.error(f"[ScheduledScan {task_id}] execution_log_id is missing. Refusing to proceed with scheduled scan.")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing for scheduled scan.")
            return
            
        logger.info(f"[ScheduledScan {task_id}] Using execution_log_id: {execution_log_id}")
        
        # Store execution_log_id in params for all downstream tasks
        params = dict(params or {})
        params['execution_log_id'] = execution_log_id
        
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Starting scheduled scan"
        )
        
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return
            
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message=f"Enqueuing authentication task for scheduled scan"
        )
        
        auth_task_params = {
            "integration_id": org_id,
            "tenant_url": integration.get("tenant_url") or integration.get("TenantUrl"),
            "environment": integration.get("environment") or integration.get("Environment"),
            "execution_log_id": execution_log_id
        }
        
        logger.info(f"[ScheduledScan] Enqueuing sfdc_authenticate with execution_log_id: {execution_log_id}")
        auth_task_id = processor.enqueue_task(
            task_type=TASK_TYPE_SFDC_AUTHENTICATE,
            org_id=org_id,
            user_id=user_id,
            params=auth_task_params,
            priority=TASK_PRIORITY_HIGH,
            execution_log_id=execution_log_id
        )
        
        if auth_task_id:
            logger.info(f"Enqueued sfdc_authenticate task {auth_task_id} for scheduled scan {task_id} with execution_log_id: {execution_log_id}")
        else:
            logger.error(f"Failed to enqueue sfdc_authenticate task for scheduled scan {task_id}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message="Failed to enqueue authentication task."
            )
            return
            
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Scheduled scan started. Enqueued authentication task.",
            result=json.dumps({
                "auth_task_id": auth_task_id
            })
        )
        
    except Exception as e:
        logger.error(f"Error processing scheduled scan task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing scheduled scan task: {str(e)}"
        )

def process_metadata_extraction_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[MetadataTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[MetadataTask {task_id}] Using execution_log_id: {execution_log_id}")
    """
    Process metadata extraction task

    Args:
        processor: Background processor instance
        task_id: Task ID
        org_id: Organization ID (integration ID)
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    # Idempotency check: skip if already completed/failed/cancelled
    task_status = processor.get_task_status(task_id)
    if task_status and task_status["status"] in (TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED):
        logger.info(f"[Idempotency] Task {task_id} already {task_status['status']}, skipping processing.")
        return

    logger.info(f"[MetadataTask {task_id}] Received params: {params}")
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")
    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for metadata extraction."
        logger.error(f"[MetadataTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[MetadataTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    integration_id = org_id # Use org_id as the integration_id for clarity in this task

    try:
        # Get integration details for org name via DB service
        db_client = get_db_client()
        integration_entity = db_client.get_integration_by_id(integration_id)

        if not integration_entity:
            error_message = f"Integration with ID {integration_id} not found for metadata task."
            logger.error(f"[MetadataTask {task_id}] {error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, error_message)
            return

        integration_type = integration_entity.get("Type", "Salesforce")
        # Try multiple fields for org name, with better fallback
        org_name = (
            integration_entity.get("Name") or
            integration_entity.get("name") or
            integration_entity.get("OrganizationName") or
            integration_entity.get("organization_name") or
            f"org-{integration_id[:8]}"  # Use first 8 chars of integration ID as fallback
        )

        # Get environment from integration entity or params
        environment = (
            integration_entity.get("Environment") or
            integration_entity.get("environment") or
            params.get("environment") or
            "production"  # Default fallback
        )

        logger.info(f"[MetadataTask {task_id}] Retrieved integration details: Type={integration_type}, Name={org_name}, Environment={environment}, ID={integration_id}")

        logger.info(f"[MetadataTask {task_id}] Extracting metadata for {integration_type} integration {integration_id} ({org_name}) using provided token.")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=30,
            message="Extracting metadata from Salesforce using provided token."
        )

        # Extract metadata
        logger.info(f"[ORCHESTRATION] extract_salesforce_metadata called from process_metadata_extraction_task. Task ID: {task_id}, Execution Log ID: {execution_log_id}")
        blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
        success, error_message, returned_blob_path = extract_salesforce_metadata(
            access_token=access_token,
            instance_url=instance_url,
            blob_repo=blob_repo,
            integration_id=integration_id,
            org_name=org_name,
            orchestration_context=f"process_metadata_extraction_task:{task_id}",
            task_id=task_id,  # Pass the task_id for consistent output folder
            execution_log_id=execution_log_id  # Pass execution_log_id for isolation
        )

        if not success:
            logger.error(f"[MetadataTask {task_id}] Failed to extract metadata from Salesforce: {error_message}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Failed to extract metadata: {error_message}"
            )
            return

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Metadata successfully retrieved/extracted."
        )

        # Update integration with last scan timestamp via DB service
        update_data = {
            "last_metadata_scan_time": datetime.now().isoformat()
        }
        success = db_client.update_integration(integration_id, update_data)
        if success:
            logger.info(f"[MetadataTask {task_id}] Updated integration {integration_id} with last metadata scan timestamp via DB service.")
        else:
            logger.error(f"[MetadataTask {task_id}] Failed to update integration {integration_id} via DB service.")

        # NOTE: Policy creation removed - policies should only be created when a new integration is added
        # not on every scan. Policy creation is now handled in the integration creation flow.

        # CRITICAL FIX: Mark task as 100% completed BEFORE enqueueing dependent tasks
        # This prevents the race condition where dependent tasks check for completed metadata_extraction
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Metadata extraction completed successfully.",
            result=json.dumps({
                "blob_path": returned_blob_path,
                "timestamp": datetime.now().isoformat()
            })
        )

        # Now enqueue policy-based tasks AFTER metadata extraction is 100% complete
        logger.info(f"[MetadataTask {task_id}] Metadata extraction completed. Now enqueueing policy-based tasks.")

        try:
            # Prepare common parameters for all policy tasks
            common_task_params = {
                "org_name": org_name,
                "blob_prefix": returned_blob_path,
                "integration_id": integration_id,
                "access_token": access_token,
                "instance_url": instance_url,
                "environment": environment
            }

            # CRITICAL FIX: Directly enqueue the missing tasks instead of relying on policy system
            # This ensures tasks are enqueued even if DB service policy endpoints are not working
            
            # Enqueue profiles_permission_sets task
            logger.info(f"[MetadataTask {task_id}] Directly enqueueing profiles_permission_sets task")
            processor.enqueue_task(
                task_type="profiles_permission_sets",
                org_id=org_id,
                user_id=user_id,
                params=common_task_params.copy(),
                priority=TASK_PRIORITY_MEDIUM,
                execution_log_id=execution_log_id
            )

            # Enqueue mfa_enforcement task
            logger.info(f"[MetadataTask {task_id}] Directly enqueueing mfa_enforcement task")
            processor.enqueue_task(
                task_type="mfa_enforcement",
                org_id=org_id,
                user_id=user_id,
                params=common_task_params.copy(),
                priority=TASK_PRIORITY_MEDIUM,
                execution_log_id=execution_log_id
            )

            # Enqueue login_ip_ranges task
            logger.info(f"[MetadataTask {task_id}] Directly enqueueing login_ip_ranges task")
            processor.enqueue_task(
                task_type="login_ip_ranges",
                org_id=org_id,
                user_id=user_id,
                params=common_task_params.copy(),
                priority=TASK_PRIORITY_MEDIUM,
                execution_log_id=execution_log_id
            )

            # Enqueue other security policy tasks
            security_tasks = [
                "device_activation",
                "login_hours", 
                "session_timeout",
                "api_whitelisting",
                "password_policy",
                "pmd_apex_security"
            ]

            for task_type in security_tasks:
                logger.info(f"[MetadataTask {task_id}] Directly enqueueing {task_type} task")
                processor.enqueue_task(
                    task_type=task_type,
                    org_id=org_id,
                    user_id=user_id,
                    params=common_task_params.copy(),
                    priority=TASK_PRIORITY_MEDIUM,
                    execution_log_id=execution_log_id
                )

            logger.info(f"[MetadataTask {task_id}] Successfully enqueued all security policy tasks directly")

        except Exception as e:
            logger.error(f"[MetadataTask {task_id}] Error enqueueing policy-based tasks: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Don't fail the entire task if policy task enqueueing fails, just log the error
            # The metadata extraction task is already marked as completed, so this is just a warning

    except Exception as e:
        error_message = f"Error processing metadata extraction task: {str(e)}"
        logger.error(error_message)
        import traceback
        logger.error(traceback.format_exc())

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=100,
            message=error_message
        )

def process_notification_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a notification task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing notification"
        )

        # Extract notification parameters
        notification_type = params.get("notification_type", "task_completed")
        recipients = params.get("recipients", [])
        subject = params.get("subject", "AtomSec Notification")
        message = params.get("message", "")
        related_task_id = params.get("related_task_id", "")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Sending {notification_type} notification to {len(recipients)} recipients"
        )

        # In a real implementation, you would send the notification via email, SMS, or in-app
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Recording notification delivery"
        )

        # In a real implementation, you would record the notification delivery in a database
        # For now, we'll just simulate the process

        # Update task status with the result
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Notification sent successfully",
            result=json.dumps({
                "notification_type": notification_type,
                "recipients": recipients,
                "subject": subject,
                "sent_at": datetime.now().isoformat(),
                "related_task_id": related_task_id
            })
        )
    except Exception as e:
        logger.error(f"Error processing notification task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing notification task: {str(e)}"
        )

def get_execution_log_id_from_params(params, parent_execution_log_id=None):
    """
    Extract execution_log_id from params or use provided value.
    
    This function ensures consistent handling of execution_log_id across all task processors.
    It prioritizes:
    1. The execution_log_id from params (if present)
    2. The explicitly provided parent_execution_log_id (if present)
    3. Returns None (caller should handle this case)
    
    Args:
        params: Task parameters dictionary
        parent_execution_log_id: Optional explicit execution_log_id
        
    Returns:
        str: The execution_log_id to use, or None if not found
    """
    execution_log_id = None
    
    # First try to get from params
    if params and isinstance(params, dict):
        execution_log_id = params.get('execution_log_id')
        if execution_log_id:
            logger.debug(f"Using execution_log_id from params: {execution_log_id}")
    
    # Then use provided parent_execution_log_id
    if not execution_log_id and parent_execution_log_id:
        execution_log_id = parent_execution_log_id
        logger.debug(f"Using provided parent_execution_log_id: {execution_log_id}")
    
    # Log if no execution_log_id was found
    if not execution_log_id:
        logger.warning(f"No execution_log_id found in params or provided explicitly.")
        
    return execution_log_id

def process_sfdc_authenticate_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[AuthTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[AuthTask {task_id}] Using execution_log_id: {execution_log_id}")
    """
    Processes the Salesforce authentication task.
    Retrieves credentials, authenticates with Salesforce, and if successful,
    enqueues subsequent data collection tasks.
    """
    logger.info(f"Processing Salesforce authentication task {task_id} for integration {org_id}")
    logger.info(f"[AuthTask {task_id}] Received user_id: '{user_id}' (type: {type(user_id)})")

    # Ensure user_id is not None
    if user_id is None:
        user_id = 'system'
        logger.info(f"[AuthTask {task_id}] Fixed user_id to: '{user_id}'")

    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, f"Starting Salesforce authentication for {org_id}")

    integration_id = params.get("integration_id") # This should be the same as org_id for this task
    if not integration_id:
        logger.error(f"[AuthTask {task_id}] Missing integration_id in task parameters.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Missing integration_id in task parameters.")
        return

    db_client = get_db_client()
    integration_entity = db_client.get_integration_by_id(integration_id)

    if not integration_entity:
        logger.error(f"[AuthTask {task_id}] Integration {integration_id} not found.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Integration {integration_id} not found.")
        return

    tenant_url = integration_entity.get("tenant_url")
    environment = integration_entity.get("environment", "production")
    is_sandbox = environment.lower() == "sandbox"
    service_name = f"salesforce-{integration_id}"

    logger.info(f"[AuthTask {task_id}] Attempting to retrieve credentials for {service_name}")

    client_id = None
    client_secret = None
    jwt_username = None
    private_key = None
    auth_flow_type = None
    auth_success = False
    try:
        if is_local_dev():
            logger.info(f"[AuthTask {task_id}] Local dev: Retrieving creds from Credentials table via DB service for {service_name}")
            # Use DB service for credentials retrieval
            credentials = db_client.get_integration_credentials(integration_id)
            if credentials:
                # Handle different credential formats from DB service
                if "client-id" in credentials:
                    # Table storage format with individual credential entries
                    client_id_entry = credentials.get("client-id", {})
                    client_id = client_id_entry.get("value")
                    auth_flow_type = client_id_entry.get("auth_flow")

                    if auth_flow_type == "jwt":
                        username_entry = credentials.get("username", {})
                        jwt_username = username_entry.get("value")
                        pkey_entry = credentials.get("private-key", {})
                        private_key = pkey_entry.get("value")
                    else:
                        secret_entry = credentials.get("client-secret", {})
                        client_secret = secret_entry.get("value")
                        if client_id and client_secret: auth_flow_type = "client_credentials"
                else:
                    # SQL format with direct fields
                    client_id = credentials.get("client_id")
                    client_secret = credentials.get("client_secret")
                    auth_flow_type = "client_credentials"  # Default for SQL format

                if client_id and not auth_flow_type:
                    # if auth flow not specified, try to infer based on available creds
                    if jwt_username and private_key: auth_flow_type = "jwt"
                    elif client_secret: auth_flow_type = "client_credentials"
            else:
                raise Exception("Credentials not available via DB service in local dev.")
        else:
            logger.info(f"[AuthTask {task_id}] Production: Retrieving creds from Key Vault for {service_name}")
            client_id = get_secret(f"{service_name}-client-id")
            # Check for AuthFlow hint if stored with client-id or as a separate secret
            auth_flow_hint = get_secret(f"{service_name}-auth-flow")
            if auth_flow_hint and auth_flow_hint.lower() in ["jwt", "client_credentials"]:
                auth_flow_type = auth_flow_hint.lower()

            if auth_flow_type == "jwt":
                jwt_username = get_secret(f"{service_name}-username")
                private_key = get_secret(f"{service_name}-private-key")
            else: # Default to client_credentials or if AuthFlow is not jwt
                client_secret = get_secret(f"{service_name}-client-secret")
                if client_id and client_secret : auth_flow_type = "client_credentials"
            if client_id and not auth_flow_type:
                # Try to infer if not specified
                jwt_username_check = get_secret(f"{service_name}-username")
                if jwt_username_check: # If username exists, assume JWT
                    jwt_username = jwt_username_check
                    private_key = get_secret(f"{service_name}-private-key")
                    auth_flow_type = "jwt"
                else: # else assume client_credentials
                    client_secret = get_secret(f"{service_name}-client-secret")
                    auth_flow_type = "client_credentials"

        if not client_id:
            raise Exception("Client ID could not be retrieved.")
        if auth_flow_type == "jwt" and (not jwt_username or not private_key):
            raise Exception("JWT username or private key could not be retrieved for JWT flow.")
        if auth_flow_type == "client_credentials" and not client_secret:
            raise Exception("Client Secret could not be retrieved for Client Credentials flow.")
        if not auth_flow_type:
            raise Exception("Could not determine authentication flow type (JWT or Client Credentials).")

        logger.info(f"[AuthTask {task_id}] Credentials retrieved. Determined auth flow: {auth_flow_type}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 30, f"Credentials retrieved. Attempting {auth_flow_type} authentication.")

        logger.info(f"[AuthTask {task_id}] Calling central authentication service. Flow type: {auth_flow_type}")
        auth_step_log = [f"Central Auth Init. Flow: {auth_flow_type}, User: {jwt_username if auth_flow_type == 'jwt' else 'N/A (CC)'}, ClientID: {client_id[:5]}..."]

        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow_type,
            tenant_url=tenant_url, # This is the my-domain or login URL base
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=jwt_username,
            private_key=private_key
        )

        # Log all steps from the central auth service if it returns them, or rely on its internal logging.
        # For simplicity, we assume central service logs sufficiently.

        if auth_success and sf_instance and connection_details:
            access_token = connection_details.get("access_token")
            instance_url_fqdn = connection_details.get("instance_url") # This is the full FQDN like https://mydomain.my.salesforce.com

            if not access_token or not instance_url_fqdn:
                logger.error(f"[AuthTask {task_id}] Central auth succeeded but access_token or instance_url missing in returned details.")
                processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Auth token/instance URL missing post central auth.")
                return

            logger.info(f"[AuthTask {task_id}] Salesforce central authentication successful for {org_id}. Instance URL: {instance_url_fqdn}")
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, "Authentication successful. Enqueuing data collection tasks.")

            common_task_params = {
                "access_token": access_token,
                "instance_url": instance_url_fqdn, # Use the FQDN from auth service
                "integration_id": integration_id,
                "tenant_url": tenant_url,
                "environment": environment,
                "execution_log_id": execution_log_id
            }

            # NOTE: Policy creation removed - policies should only be created when a new integration is added
            # not on every scan. Policy creation is now handled in the integration creation flow.

            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Enqueuing data collection tasks.")

            # Sequential task enqueueing for proper dependency management
            # Tasks will be processed sequentially with 5-second delays between dependent tasks

            # 1. Overview data collection (medium priority, no dependencies)
            logger.info(f"[AuthTask {task_id}] About to enqueue overview task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
            processor.enqueue_task(TASK_TYPE_OVERVIEW, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_MEDIUM, execution_log_id=execution_log_id)

            # 2. Profiles and Permissions retrieval (medium priority, no dependencies)
            logger.info(f"[AuthTask {task_id}] About to enqueue profiles task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
            processor.enqueue_task(TASK_TYPE_PROFILES, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_MEDIUM, execution_log_id=execution_log_id)

            # 3. Health Check security analysis (high priority, depends on sfdc_authenticate)
            latest_health_check_task = processor.get_latest_task_for_org(org_id, TASK_TYPE_HEALTH_CHECK)
            if latest_health_check_task and latest_health_check_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[AuthTask {task_id}] Skipping health check enqueue for {org_id}: existing task {latest_health_check_task['task_id']} is {latest_health_check_task['status']}")
            else:
                logger.info(f"[AuthTask {task_id}] About to enqueue health check task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
                processor.enqueue_task(TASK_TYPE_HEALTH_CHECK, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH, execution_log_id=execution_log_id)

            # 4. Metadata extraction (high priority, depends on health_check)
            latest_metadata_task = processor.get_latest_task_for_org(org_id, TASK_TYPE_METADATA_EXTRACTION)
            if latest_metadata_task and latest_metadata_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[AuthTask {task_id}] Skipping metadata extraction enqueue for {org_id}: existing task {latest_metadata_task['task_id']} is {latest_metadata_task['status']}")
            else:
                logger.info(f"[AuthTask {task_id}] About to enqueue metadata extraction task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
                processor.enqueue_task(TASK_TYPE_METADATA_EXTRACTION, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH, execution_log_id=execution_log_id)
                logger.info(f"[AuthTask {task_id}] Enqueued metadata extraction task for {org_id}.")

            logger.info(f"[AuthTask {task_id}] Successfully enqueued data collection tasks for {org_id}.")
            
            # Update LastScan timestamp on the integration record upon successful auth and task enqueuing via DB service
            now_iso = datetime.now().isoformat()
            update_data = {"last_scan": now_iso}
            success = db_client.update_integration(integration_id, update_data)
            if success:
                logger.info(f"[AuthTask {task_id}] Updated integration {integration_id} with last scan timestamp via DB service.")
            else:
                logger.error(f"[AuthTask {task_id}] Failed to update integration {integration_id} via DB service.")
            
            # CRITICAL FIX: Update task status to COMPLETED AFTER enqueuing dependent tasks
            # This prevents the race condition where dependent tasks check for completed auth task
            processor.update_task_status(task_id, TASK_STATUS_COMPLETED, 100, f"Authentication successful. Data collection tasks for {org_id} enqueued.")
            logger.info(f"[AuthTask {task_id}] Status set to COMPLETED after enqueueing dependent tasks.")
            


        else:
            logger.error(f"[AuthTask {task_id}] Salesforce central authentication failed for {org_id}: {auth_error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Salesforce authentication failed: {auth_error_message}")
            # Update Integration IsActive to False via DB service
            update_data = {"is_active": False}
            success = db_client.update_integration(integration_id, update_data)
            if success:
                logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to auth failure via DB service.")
            else:
                logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive via DB service.")

        # If authentication and enqueuing succeeded, set auth_success = True
        auth_success = True
    except Exception as e:
        logger.error(f"[AuthTask {task_id}] Error during Salesforce authentication task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during authentication: {str(e)}")
        if integration_id:
            try:
                update_data = {"is_active": False}
                success = db_client.update_integration(integration_id, update_data)
                if success:
                    logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to an exception in the auth task via DB service.")
                else:
                    logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive via DB service.")
            except Exception as update_err:
                logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive after exception: {str(update_err)}")
        return
    finally:
        # Note: Task status is now updated to COMPLETED before enqueuing dependent tasks
        # to prevent race conditions. No need to update again here.
        pass

def profile_active_user_counts(access_token: str, instance_url: str) -> Dict[str, int]:
    """
    Optimized function to get active user counts for all profiles in a single batch query.

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        Dictionary mapping profile names to active user counts
    """
    try:
        # Single query to get all profile assignments with counts
        query = """
        SELECT Profile.Name, COUNT(Id) UserCount
        FROM User
        WHERE IsActive = true
        AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true)
        GROUP BY Profile.Name
        """

        # Execute the query in the event loop
        import asyncio
        import concurrent.futures

        def run_query():
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                return new_loop.run_until_complete(
                    execute_salesforce_query(query, access_token, instance_url)
                )
            finally:
                new_loop.close()

        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_query)
                result = future.result()
        except Exception as e:
            logger.error(f"Error executing profile assignment query: {e}")
            result = None

        if not result or "records" not in result:
            logger.warning("No profile assignment data returned from Salesforce")
            return {}

        # Build the mapping
        profile_counts = {}
        for record in result["records"]:
            profile_name = record.get("Name", "")
            user_count = record.get("UserCount", 0)
            if profile_name:
                profile_counts[profile_name] = user_count

        logger.info(f"Retrieved user counts for {len(profile_counts)} profiles in single batch query")
        return profile_counts

    except Exception as e:
        logger.error(f"Error getting profile active user counts: {str(e)}")
        return {}

def process_profiles_permission_sets_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process profiles and permission sets task with best practices comparison

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    logger.info(f"Processing profiles permission sets task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting profiles permission sets analysis")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for profiles permission sets task."
        logger.error(f"[ProfilesPermissionSetsTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[ProfilesPermissionSetsTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    try:
        # Load best practices XML
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 20, "Loading best practices configuration")

        # Try to load best practices from the best_practices directory
        best_practices_path = os.path.join(os.path.dirname(__file__), "..", "best_practices", "Profiles_PermissionSetRisks-BestPractice.xml")
        if not os.path.exists(best_practices_path):
            # Fallback to a default path or create minimal best practices
            logger.warning(f"Best practices XML not found at {best_practices_path}, using minimal default practices")
            best_practices = []
        else:
            best_practices = load_best_practices_xml(best_practices_path)
            logger.info(f"Loaded {len(best_practices)} best practices from XML")

        # Get blob repository for metadata
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 30, "Initializing blob storage")
        blob_repo = BlobStorageRepository(container_name="salesforce-metadata")

        # Get blob prefix from task parameters (set by metadata extraction task)
        blob_prefix = params.get("blob_prefix")
        if not blob_prefix:
            # Fallback: construct blob prefix based on org_id and task_id
            blob_prefix = f"{org_id}/{task_id}"
            logger.warning(f"[ProfilesPermissionSetsTask {task_id}] blob_prefix not found in params, using fallback: {blob_prefix}")

        # Call the core logic and get results
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 50, "Processing profiles in blob storage")
        profile_results = process_profiles_in_blob(blob_repo, blob_prefix, best_practices)

        # --- Fetch Profile Name -> Id mapping from Salesforce ---
        normalized_profile_id_map = {}
        if access_token and instance_url:
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 60, "Fetching profile mappings from Salesforce")
            profile_query = "SELECT Id, Name FROM Profile"

            # Execute the query in the event loop
            import asyncio
            import concurrent.futures

            def run_query():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        execute_salesforce_query(profile_query, access_token, instance_url)
                    )
                finally:
                    new_loop.close()

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_query)
                    profile_query_result = future.result()
            except Exception as e:
                logger.error(f"Error executing profile query: {e}")
                profile_query_result = None

            if profile_query_result and "records" in profile_query_result:
                for rec in profile_query_result["records"]:
                    norm_name = normalize_profile_name(rec["Name"])
                    normalized_profile_id_map[norm_name] = rec["Id"]

        unmatched_profiles = []
        inserted_count = 0
        inserted_profiles = set()  # Track inserted profiles to prevent duplicates

        # Get all profile assignment counts in a single batch query (optimization)
        profile_assignment_counts = {}
        if access_token and instance_url:
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 65, "Fetching profile assignment counts")
            profile_assignment_counts = profile_active_user_counts(access_token, instance_url)
            logger.info(f"Retrieved assignment counts for {len(profile_assignment_counts)} profiles")

        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, "Storing analysis results")

        # Get DB service client for database operations
        db_client = get_db_client()
        if not db_client:
            logger.error(f"[ProfilesPermissionSetsTask {task_id}] DB service client not available")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "DB service client not available")
            return

        for profile_name, results in profile_results.items():
            # Skip if we've already processed this profile (duplicate prevention)
            if profile_name in inserted_profiles:
                logger.info(f"Skipping duplicate profile: {profile_name}")
                continue

            # Normalize the profile name from the blob
            norm_blob_name = normalize_profile_name(profile_name)
            profile_id = normalized_profile_id_map.get(norm_blob_name)

            # Special-case: Admin.profile should match System Administrator
            if not profile_id and norm_blob_name == 'admin':
                profile_id = normalized_profile_id_map.get('system administrator')
            # Special-case: Standard.profile should match Standard Platform User
            if not profile_id and norm_blob_name == 'standard':
                profile_id = normalized_profile_id_map.get('standard platform user')
            # Special-case: Try matching by removing spaces from Salesforce profile names
            if not profile_id:
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_name:
                        profile_id = sf_id
                        break
            # NEW: Try matching by removing spaces from both blob and Salesforce names
            if not profile_id:
                norm_blob_no_space = norm_blob_name.replace(' ', '')
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_no_space:
                        profile_id = sf_id
                        break

            if not profile_id:
                unmatched_profiles.append(profile_name)

            # Store policies result using DB service
            policies_data = {
                'org_id': str(org_id),
                'profile_name': profile_name,
                'results': results,
                'execution_log_id': execution_log_id,
                'created_at': datetime.now().isoformat(),
            }
            try:
                if db_client.store_policies_result_data(str(org_id), execution_log_id, [policies_data]):
                    inserted_count += 1
                    inserted_profiles.add(profile_name)  # Track successful insertion
                else:
                    logger.error(f"Failed to store PoliciesResult for profile {profile_name}")
            except Exception as e:
                logger.error(f"Failed to store PoliciesResult for profile {profile_name}: {e}")

            # --- Get assignment count from batch query results (optimization) ---
            assignment_count = None

            # Try to find assignment count using profile name from Salesforce mapping
            salesforce_profile_name = None
            if profile_id:
                # Find the Salesforce profile name that matches this profile_id
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_id == profile_id:
                        # Find the original (non-normalized) profile name
                        for orig_name, counts in profile_assignment_counts.items():
                            if normalize_profile_name(orig_name) == sf_name:
                                salesforce_profile_name = orig_name
                                assignment_count = counts
                                break
                        break

            # Fallback: try direct name matching if profile_id lookup failed
            if assignment_count is None and profile_assignment_counts:
                # Try exact match first
                if profile_name in profile_assignment_counts:
                    assignment_count = profile_assignment_counts[profile_name]
                else:
                    # Try normalized matching
                    norm_profile_name = normalize_profile_name(profile_name)
                    for sf_name, count in profile_assignment_counts.items():
                        if normalize_profile_name(sf_name) == norm_profile_name:
                            assignment_count = count
                            break

            if assignment_count is not None:
                assignment_data = {
                    'org_id': str(org_id),
                    'profile_name': profile_name,
                    'profile_id': profile_id or '',
                    'assignment_count': assignment_count,
                    'execution_log_id': execution_log_id,
                    'created_at': datetime.now().isoformat(),
                }
                try:
                    # Store profile assignment count using DB service
                    if not db_client.store_profile_assignment_count_data(str(org_id), execution_log_id, [assignment_data]):
                        logger.error(f"Failed to store ProfileAssignmentCount for profile {profile_name}")
                except Exception as e:
                    logger.error(f"Failed to store ProfileAssignmentCount for profile {profile_name}: {e}")

        # Log unmatched profiles for review
        if unmatched_profiles:
            logger.warning(f"Unmatched profiles (could not find ProfileId in Salesforce): {unmatched_profiles}")

        logger.info(f"Inserted {inserted_count} PoliciesResult entities for org {org_id}")

        # After processing profiles, process permission sets with the same execution_log_id
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Processing permission sets")
        try:
            # Load best practices for permission sets
            best_practices_path = os.path.join(os.path.dirname(__file__), "..", "best_practices", "Profiles_PermissionSetRisks-BestPractice.xml")
            if os.path.exists(best_practices_path):
                best_practices = load_best_practices_xml(best_practices_path)

                # Process permission sets in blob storage
                process_permissionsets_in_blob(
                    blob_repo,
                    blob_prefix,
                    best_practices,
                    org_id,
                    execution_log_id,
                    access_token=access_token,
                    instance_url=instance_url,
                    environment=params.get('environment', 'production')
                )
                logger.info(f"Completed permission set processing for org_id={org_id}")
            else:
                logger.warning(f"Best practices XML not found at {best_practices_path}, skipping permission set processing")
        except Exception as e:
            logger.error(f"Error processing permission sets: {e}")
            # Don't fail the entire task for permission set errors

        # Mark task as completed
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Profiles and Permission Sets scan completed and results stored.",
            result=json.dumps({
                "profiles_processed": len(profile_results),
                "policies_results_inserted": inserted_count,
                "unmatched_profiles": len(unmatched_profiles)
            })
        )
    except Exception as e:
        logger.error(f"[ProfilesPermissionSetsTask {task_id}] Error processing profiles permission sets task: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error processing profiles permission sets task: {str(e)}")

# --- Helper functions for profiles permission sets processing ---

def load_best_practices_xml(xml_path):
    """Load best practices from XML file"""
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        best_practices = []
        for usertype_elem in root.findall('UserType'):
            usertype = usertype_elem.attrib.get('name', '')
            for practice in usertype_elem.findall('Practice'):
                bp = {child.tag: child.text for child in practice}
                bp['UserType'] = usertype  # Add UserType from attribute
                best_practices.append(bp)
        return best_practices
    except Exception as e:
        logger.error(f"Error loading best practices XML: {e}")
        return []

def extract_user_license_from_xml(xml_bytes):
    """Extract user license from profile XML"""
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<userLicense>(.*?)</userLicense>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <userLicense> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting userLicense with regex: {e}")
        logger.info(f"[DEBUG] Error extracting userLicense. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def normalize_permission_name(name):
    """Normalize permission name for comparison"""
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_user_type(val):
    """Normalize user type for comparison"""
    return (val or '').strip().lower().replace(' ', '')

def normalize_value(val):
    """Normalize value for comparison"""
    if val is None:
        return ''
    return str(val).strip().lower()

def normalize_profile_name(name):
    """Normalize profile name for comparison"""
    if not name:
        return ''
    # Decode URL-encoded characters, strip whitespace, and lowercase
    return urllib.parse.unquote(name).strip().lower()

def parse_profile_permissions(xml_bytes):
    """Parse user permissions from profile XML"""
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        # Find all userPermissions, regardless of namespace
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in profile: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def process_profiles_in_blob(blob_repo, blob_prefix, best_practices, summary_output_path=None):
    """Process profiles in blob storage and compare with best practices"""
    profile_folder = f"{blob_prefix}/profiles/"
    logger.info(f"[DEBUG] Looking for profiles in folder: {profile_folder}")
    blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
    logger.info(f"[DEBUG] Blobs found: {blobs}")
    profile_results = {}
    summary_lines = []
    for blob_name in blobs:
        if not blob_name.endswith('.profile'):
            continue
        profile_name = os.path.basename(blob_name).replace('.profile', '')
        try:
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            if xml_bytes is None:
                logger.error(f"Failed to download blob bytes for {blob_name}")
                continue
        except Exception as e:
            logger.error(f"Error downloading blob {blob_name}: {e}")
            continue
        # Extra debug logs for extraction/parsing
        logger.info(f"[DEBUG] About to extract userLicense for {profile_name}")
        user_license = extract_user_license_from_xml(xml_bytes)
        logger.info(f"[DEBUG] Extracted userLicense for {profile_name}: '{user_license}'")
        logger.info(f"[DEBUG] About to parse userPermissions for {profile_name}")
        user_permissions = parse_profile_permissions(xml_bytes)
        logger.info(f"[DEBUG] Parsed {len(user_permissions)} userPermissions for {profile_name}: {user_permissions}")
        # Log all normalized permission names for the profile
        all_normalized_names = [normalize_permission_name(p['name']) for p in user_permissions]
        logger.info(f"[DEBUG] All normalized permission names for '{profile_name}': {all_normalized_names}")
        # Compare with best practices for this userLicense
        results = []
        for bp in best_practices:
            bp_user_type = normalize_user_type(bp.get('UserType'))
            profile_user_type = normalize_user_type(user_license)
            logger.debug(f"[DEBUG] Comparing profile_user_type '{profile_user_type}' with bp_user_type '{bp_user_type}' for profile '{profile_name}'")
            if (bp_user_type == profile_user_type) or (not profile_user_type and bp_user_type == 'blank'):
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                bp_description = bp.get('Description', '')
                bp_owasp = bp.get('OWASP', '')
                bp_risk = bp.get('RiskTypeBasedOnSeverity', '')
                normalized_bp_setting = normalize_permission_name(bp_setting)
                logger.info(f"[DEBUG] Checking best practice setting '{bp_setting}' (normalized: '{normalized_bp_setting}') against profile permissions for '{profile_name}'")
                # Log each comparison
                for p in user_permissions:
                    logger.info(f"[DEBUG] Comparing normalized permission '{normalize_permission_name(p['name'])}' to '{normalized_bp_setting}' for '{profile_name}'")
                profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                logger.debug(f"[DEBUG] Checking best practice setting '{bp_setting}' (normalized: '{normalized_bp_setting}') in profile '{profile_name}'. Found: {profile_perm}")
                if profile_perm:
                    logger.info(f"[INFO] Matched best practice setting '{bp_setting}' in profile '{profile_name}': enabled='{profile_perm['enabled']}'")
                else:
                    logger.info(f"[INFO] Best practice setting '{bp_setting}' NOT found in profile '{profile_name}'")
                profile_value = profile_perm['enabled'] if profile_perm else ''
                match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                missing_in_profile = profile_perm is None
                results.append({
                    'SalesforceSetting': bp_setting,
                    'StandardValue': bp_standard_value,
                    'ProfileValue': profile_value,
                    'Match': match,
                    'MissingInProfile': missing_in_profile,
                    'Description': bp_description,
                    'OWASP': bp_owasp,
                    'RiskTypeBasedOnSeverity': bp_risk
                })
        logger.info(f"[DEBUG] Final results for profile '{profile_name}': {results}")
        profile_results[profile_name] = results
    if summary_output_path:
        try:
            with open(summary_output_path, 'w', encoding='utf-8') as f:
                f.write("Profile,TotalChecks,Mismatches\n")
                for line in summary_lines:
                    f.write(line + "\n")
        except Exception as e:
            logger.error(f"Error writing summary output file: {e}")
    return profile_results
# --- End copied logic ---



def process_permissionsets_in_blob(blob_repo, blob_prefix, best_practices, org_id, execution_log_id, access_token=None, instance_url=None, environment='production'):
    import xml.etree.ElementTree as ET
    import urllib.parse
    logger = logging.getLogger(__name__)
    permissionset_folder = f"{blob_prefix}/permissionsets/"
    permission_set_blobs = blob_repo.list_blobs(name_starts_with=permissionset_folder)
    for blob_name in permission_set_blobs:
        if not blob_name.endswith('.permissionset'):
            continue
        permission_set_name = blob_name.split('/')[-1].replace('.permissionset', '')
        logger.debug(f"[DEBUG] Processing Permission Set: {permission_set_name}")
        try:
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            # Extract <License> (case-insensitive)
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
            except Exception as e:
                logger.error(f"Failed to decode XML for {permission_set_name}: {e}")
                continue
            match = re.search(r'<license>(.*?)</license>', xml_str, re.DOTALL | re.IGNORECASE)
            license_value = match.group(1).strip() if match else ''
            normalized_license = (license_value or '').strip().lower().replace(' ', '') or 'blank'
            logger.debug(f"[DEBUG] Permission Set '{permission_set_name}': License='{license_value}' (normalized: '{normalized_license}')")
            # Parse userPermissions
            try:
                root = ET.fromstring(xml_str)
                user_permissions = []
                for perm in root.iter():
                    if perm.tag.endswith('userPermissions'):
                        name_elem = None
                        enabled_elem = None
                        for child in perm:
                            if child.tag.endswith('name'):
                                name_elem = child
                            elif child.tag.endswith('enabled'):
                                enabled_elem = child
                        if name_elem is not None and enabled_elem is not None:
                            user_permissions.append({
                                'name': name_elem.text.strip() if name_elem.text else '',
                                'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                            })
            except Exception as e:
                logger.error(f"Failed to parse userPermissions for {permission_set_name}: {e}")
                continue
            norm_names = [normalize_permission_name(p['name']) for p in user_permissions]
            # Best-practices matching
            relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == normalized_license]
            matched_user_type = normalized_license if relevant_bps else 'blank'
            if not relevant_bps:
                relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == 'blank']
            results_arr = []
            for bp in relevant_bps:
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                ps_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                if ps_perm is not None:
                    ps_value = ps_perm['enabled']
                    match = normalize_value(ps_value) == normalize_value(bp_standard_value)
                    if not match:
                        results_arr.append({
                            'SalesforceSetting': bp_setting,
                            'StandardValue': bp_standard_value,
                            'PermissionSetValue-UserPermissions': ps_value,
                            'Match': False,
                            'UserType': matched_user_type,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                        })
            # Only insert if there are discrepancies
            if results_arr:
                # Prepare data for DB service
                policies_data = []
                for result in results_arr:
                    policies_data.append({
                        'OrgPolicyId': f"{permission_set_name}-{result.get('SalesforceSetting', '')}",
                        'PolicyId': result.get('SalesforceSetting', ''),
                        'ExecutionLogId': execution_log_id,
                        'OrgValue': result.get('PermissionSetValue-UserPermissions', ''),
                        'OWASPCategory': result.get('OWASP', ''),
                        'StandardValue': result.get('StandardValue', ''),
                        'IssueDescription': result.get('Description', ''),
                        'Recommendations': '',
                        'Severity': result.get('RiskTypeBasedOnSeverity', ''),
                        'Weakness': '',
                        'IntegrationId': str(org_id),
                        'Type': 'PermissionSetPermissions',
                        'ProfileName': '',
                        'PermissionSetName': permission_set_name
                    })

                # Store via DB service
                try:
                    db_client = get_db_client()
                    success = db_client.store_policies_result_data(
                        org_id=str(org_id),
                        execution_log_id=execution_log_id,
                        policies_data=policies_data
                    )
                    if success:
                        logger.info(f"Successfully stored {len(policies_data)} policies results for permission set {permission_set_name}")
                    else:
                        logger.error(f"Failed to store policies results for permission set {permission_set_name}")
                except Exception as e:
                    logger.error(f"Error storing policies results via DB service: {e}")
        except Exception as e:
            logger.error(f"[DEBUG] Permission Set '{permission_set_name}': Error processing: {e}")

def load_best_practices_xml(xml_path):
    """Load best practices from XML file"""
    import xml.etree.ElementTree as ET
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        best_practices = []
        for user_type in root.findall('UserType'):
            user_type_name = user_type.get('name', '')
            for practice in user_type.findall('Practice'):
                bp = {'UserType': user_type_name}
                for child in practice:
                    bp[child.tag] = child.text.strip() if child.text else ''
                best_practices.append(bp)
        logger.info(f"Loaded {len(best_practices)} best practices from {xml_path}")
        return best_practices
    except Exception as e:
        logger.error(f"Error loading best practices from {xml_path}: {e}")
        return []

def normalize_user_type(user_type):
    """Normalize user type for comparison"""
    if not user_type:
        return 'blank'
    return user_type.strip().lower().replace(' ', '')

def normalize_permission_name(name):
    """Normalize permission name for comparison"""
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_value(val):
    """Normalize values for comparison"""
    if val is None:
        return 'false'
    val_str = str(val).strip().lower()
    # Handle various representations of true/false
    if val_str in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    elif val_str in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val_str

def process_permission_sets_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a permission sets task - extracts permission set data from Salesforce

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        logger.info(f"Processing permission sets task {task_id} for organization {org_id}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting permission sets data collection")

        access_token = params.get("access_token")
        instance_url = params.get("instance_url")
        integration_id_from_params = params.get("integration_id")

        if not all([access_token, instance_url, integration_id_from_params]):
            err_msg = "Missing access_token, instance_url, or integration_id in task parameters for permission sets."
            logger.error(f"[PermissionSetsTask {task_id}] {err_msg}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
            return

        if org_id != integration_id_from_params:
            logger.warning(f"[PermissionSetsTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=30,
                message="Fetching permission sets from Salesforce"
            )

            # Query permission sets (excluding profile-owned ones)
            permission_sets_query = """
                SELECT Id, Name, Label, Description, License, IsOwnedByProfile, Type
                FROM PermissionSet
                WHERE IsOwnedByProfile = false
                ORDER BY Name
            """

            # Execute the query in the event loop
            import asyncio
            import concurrent.futures

            def run_query():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        execute_salesforce_query(permission_sets_query, access_token, instance_url)
                    )
                finally:
                    new_loop.close()

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_query)
                    permission_sets_result = future.result()
            except Exception as e:
                logger.error(f"Error executing permission sets query: {e}")
                permission_sets_result = None

            if not permission_sets_result or "records" not in permission_sets_result:
                logger.warning(f"[PermissionSetsTask {task_id}] No permission sets found")
                processor.update_task_status(task_id, TASK_STATUS_COMPLETED, 100, "No permission sets found")
                return

            permission_sets = permission_sets_result["records"]
            logger.info(f"[PermissionSetsTask {task_id}] Found {len(permission_sets)} permission sets")

            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=60,
                message=f"Processing {len(permission_sets)} permission sets"
            )

            # Store permission sets data via DB service
            db_client = get_db_client()

            # Transform permission sets data for storage
            permission_sets_data = []
            for ps in permission_sets:
                permission_sets_data.append({
                    'permission_set_id': ps.get('Id', ''),
                    'name': ps.get('Name', ''),
                    'label': ps.get('Label', ''),
                    'description': ps.get('Description', ''),
                    'license': ps.get('License', ''),
                    'type': ps.get('Type', ''),
                    'is_owned_by_profile': ps.get('IsOwnedByProfile', False)
                })

            # Store permission sets data via DB service
            success = db_client.store_permission_sets_data(
                org_id=org_id,
                execution_log_id=execution_log_id,
                permission_sets_data=permission_sets_data
            )

            if success:
                logger.info(f"[PermissionSetsTask {task_id}] Successfully stored {len(permission_sets_data)} permission sets for {org_id}")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message=f"Successfully processed {len(permission_sets_data)} permission sets"
                )
            else:
                logger.error(f"[PermissionSetsTask {task_id}] Failed to store permission sets data for {org_id}")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_FAILED,
                    progress=100,
                    message="Failed to store permission sets data"
                )

        except Exception as e:
            logger.error(f"[PermissionSetsTask {task_id}] Error processing permission sets task: {str(e)}")
            logger.error(f"[PermissionSetsTask {task_id}] Traceback: {traceback.format_exc()}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Error processing permission sets task: {str(e)}"
            )

    except Exception as e:
        logger.error(f"[PermissionSetsTask {task_id}] Error processing permission sets task: {str(e)}")
        logger.error(f"[PermissionSetsTask {task_id}] Traceback: {traceback.format_exc()}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=100,
            message=f"Error processing permission sets task: {str(e)}"
        )