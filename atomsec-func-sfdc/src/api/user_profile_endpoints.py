"""
User Profile Endpoints

This module provides endpoints for user profile management operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, Optional
from azure.functions import Blueprint

from src.shared.data_access import get_table_storage_repository, get_sql_database_repository
from src.shared.azure_services import is_local_dev, get_current_user_id
from src.shared.service_bus_client import get_service_bus_client

logger = logging.getLogger(__name__)
bp = Blueprint()

@bp.route(route="user/profile", methods=["GET"])
def get_user_profile(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get current user's profile
    
    Returns:
        JSON response with user profile data
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Get user repository
        if is_local_dev():
            user_repo = get_table_storage_repository("App_User")
        else:
            user_repo = get_sql_database_repository("App_User")
        
        if not user_repo:
            return func.HttpResponse(
                json.dumps({"error": "Database connection failed"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Get user profile
        user_profile = user_repo.get_by_id(user_id)
        if not user_profile:
            return func.HttpResponse(
                json.dumps({"error": "User profile not found"}),
                mimetype="application/json",
                status_code=404
            )
        
        # Remove sensitive information
        if "password" in user_profile:
            del user_profile["password"]
        if "password_hash" in user_profile:
            del user_profile["password_hash"]
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": user_profile
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get user profile: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="user/profile", methods=["PUT"])
def update_user_profile(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update current user's profile
    
    Returns:
        JSON response with updated user profile
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate required fields
        allowed_fields = ["first_name", "last_name", "email", "phone", "timezone", "preferences"]
        update_data = {}
        
        for field in allowed_fields:
            if field in request_data:
                update_data[field] = request_data[field]
        
        if not update_data:
            return func.HttpResponse(
                json.dumps({"error": "No valid fields to update"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get user repository
        if is_local_dev():
            user_repo = get_table_storage_repository("App_User")
        else:
            user_repo = get_sql_database_repository("App_User")
        
        if not user_repo:
            return func.HttpResponse(
                json.dumps({"error": "Database connection failed"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Get existing user profile
        existing_user = user_repo.get_by_id(user_id)
        if not existing_user:
            return func.HttpResponse(
                json.dumps({"error": "User profile not found"}),
                mimetype="application/json",
                status_code=404
            )
        
        # Update user profile
        existing_user.update(update_data)
        existing_user["updated_at"] = user_repo.get_current_timestamp()
        
        # Save updated profile
        updated_user = user_repo.update(user_id, existing_user)
        if not updated_user:
            return func.HttpResponse(
                json.dumps({"error": "Failed to update user profile"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Publish user update event
        service_bus_client = get_service_bus_client()
        if service_bus_client:
            service_bus_client.publish_user_event(
                user_id=user_id,
                event_type="UserProfileUpdated",
                user_data=update_data
            )
        
        # Remove sensitive information from response
        if "password" in updated_user:
            del updated_user["password"]
        if "password_hash" in updated_user:
            del updated_user["password_hash"]
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": updated_user,
                "message": "User profile updated successfully"
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to update user profile: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="user/password", methods=["PUT"])
def update_user_password(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update current user's password
    
    Returns:
        JSON response with success status
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate required fields
        required_fields = ["current_password", "new_password"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({"error": f"Missing required field: {field}"}),
                    mimetype="application/json",
                    status_code=400
                )
        
        current_password = request_data["current_password"]
        new_password = request_data["new_password"]
        
        # Validate password strength
        if len(new_password) < 8:
            return func.HttpResponse(
                json.dumps({"error": "New password must be at least 8 characters long"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get user repository
        if is_local_dev():
            user_repo = get_table_storage_repository("App_User")
        else:
            user_repo = get_sql_database_repository("App_User")
        
        if not user_repo:
            return func.HttpResponse(
                json.dumps({"error": "Database connection failed"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Get existing user profile
        existing_user = user_repo.get_by_id(user_id)
        if not existing_user:
            return func.HttpResponse(
                json.dumps({"error": "User profile not found"}),
                mimetype="application/json",
                status_code=404
            )
        
        # Verify current password (simplified for demo - in production, use proper hashing)
        stored_password = existing_user.get("password", "")
        if stored_password != current_password:
            return func.HttpResponse(
                json.dumps({"error": "Current password is incorrect"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Update password
        existing_user["password"] = new_password
        existing_user["updated_at"] = user_repo.get_current_timestamp()
        
        # Save updated profile
        updated_user = user_repo.update(user_id, existing_user)
        if not updated_user:
            return func.HttpResponse(
                json.dumps({"error": "Failed to update password"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Publish password change event
        service_bus_client = get_service_bus_client()
        if service_bus_client:
            service_bus_client.publish_user_event(
                user_id=user_id,
                event_type="UserPasswordChanged",
                user_data={"password_changed_at": user_repo.get_current_timestamp()}
            )
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "Password updated successfully"
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error updating user password: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to update password: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        ) 