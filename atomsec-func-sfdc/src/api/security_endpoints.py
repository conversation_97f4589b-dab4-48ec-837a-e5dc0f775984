"""
Security Data Management Endpoints

This module provides security-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/security/health-check/{org_id} - Get health check data
- POST /api/security/health-check - Store health check data
- GET /api/security/profiles/{org_id} - Get profile permissions
- POST /api/security/profiles - Store profile permissions
- GET /api/security/permission-sets/{org_id} - Get permission set data
- POST /api/security/permission-sets - Store permission set data
- GET /api/security/overview/{org_id} - Get security overview
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from src.shared.azure_services import is_local_dev
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.database_models_new import HealthCheck, ProfilePermission, Overview, ExecutionLog, ProfileAssignmentCount, PoliciesResult

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_health_check_table_repo = None
_health_check_sql_repo = None
_profile_permission_table_repo = None
_profile_permission_sql_repo = None
_overview_table_repo = None
_overview_sql_repo = None
_policies_result_table_repo = None
_policies_result_sql_repo = None
_profile_assignment_count_table_repo = None
_profile_assignment_count_sql_repo = None


def get_health_check_table_repo() -> Optional[TableStorageRepository]:
    """Get health check table repository for local development"""
    global _health_check_table_repo
    if _health_check_table_repo is None:
        try:
            _health_check_table_repo = TableStorageRepository(table_name="HealthCheck")
            logger.info("Initialized health check table repository")
        except Exception as e:
            logger.error(f"Failed to initialize health check table repository: {str(e)}")
            _health_check_table_repo = None
    return _health_check_table_repo


def get_health_check_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get health check SQL repository for production"""
    global _health_check_sql_repo
    if _health_check_sql_repo is None and not is_local_dev():
        try:
            _health_check_sql_repo = SqlDatabaseRepository(table_name="App_HealthCheck")
            logger.info("Initialized health check SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize health check SQL repository: {str(e)}")
            _health_check_sql_repo = None
    return _health_check_sql_repo


def get_profile_permission_table_repo() -> Optional[TableStorageRepository]:
    """Get profile permission table repository for local development"""
    global _profile_permission_table_repo
    if _profile_permission_table_repo is None:
        try:
            _profile_permission_table_repo = TableStorageRepository(table_name="ProfilePermission")
            logger.info("Initialized profile permission table repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile permission table repository: {str(e)}")
            _profile_permission_table_repo = None
    return _profile_permission_table_repo


def get_profile_permission_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get profile permission SQL repository for production"""
    global _profile_permission_sql_repo
    if _profile_permission_sql_repo is None and not is_local_dev():
        try:
            _profile_permission_sql_repo = SqlDatabaseRepository(table_name="App_ProfilePermissions")
            logger.info("Initialized profile permission SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile permission SQL repository: {str(e)}")
            _profile_permission_sql_repo = None
    return _profile_permission_sql_repo


def get_overview_table_repo() -> Optional[TableStorageRepository]:
    """Get overview table repository for local development"""
    global _overview_table_repo
    if _overview_table_repo is None:
        try:
            _overview_table_repo = TableStorageRepository(table_name="Overview")
            logger.info("Initialized overview table repository")
        except Exception as e:
            logger.error(f"Failed to initialize overview table repository: {str(e)}")
            _overview_table_repo = None
    return _overview_table_repo


def get_overview_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get overview SQL repository for production"""
    global _overview_sql_repo
    if _overview_sql_repo is None and not is_local_dev():
        try:
            _overview_sql_repo = SqlDatabaseRepository(table_name="App_Overview")
            logger.info("Initialized overview SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize overview SQL repository: {str(e)}")
            _overview_sql_repo = None
    return _overview_sql_repo


def get_policies_result_table_repo() -> Optional[TableStorageRepository]:
    """Get policies result table repository for local development"""
    global _policies_result_table_repo
    if _policies_result_table_repo is None:
        try:
            _policies_result_table_repo = TableStorageRepository(table_name="PoliciesResult")
            logger.info("Initialized policies result table repository")
        except Exception as e:
            logger.error(f"Failed to initialize policies result table repository: {str(e)}")
            _policies_result_table_repo = None
    return _policies_result_table_repo


def get_policies_result_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get policies result SQL repository for production"""
    global _policies_result_sql_repo
    if _policies_result_sql_repo is None and not is_local_dev():
        try:
            _policies_result_sql_repo = SqlDatabaseRepository(table_name="App_PoliciesResult")
            logger.info("Initialized policies result SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize policies result SQL repository: {str(e)}")
            _policies_result_sql_repo = None
    return _policies_result_sql_repo


def get_profile_assignment_count_table_repo() -> Optional[TableStorageRepository]:
    """Get profile assignment count table repository for local development"""
    global _profile_assignment_count_table_repo
    if _profile_assignment_count_table_repo is None:
        try:
            _profile_assignment_count_table_repo = TableStorageRepository(table_name="ProfileAssignmentCount")
            logger.info("Initialized profile assignment count table repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile assignment count table repository: {str(e)}")
            _profile_assignment_count_table_repo = None
    return _profile_assignment_count_table_repo


def get_profile_assignment_count_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get profile assignment count SQL repository for production"""
    global _profile_assignment_count_sql_repo
    if _profile_assignment_count_sql_repo is None and not is_local_dev():
        try:
            _profile_assignment_count_sql_repo = SqlDatabaseRepository(table_name="App_ProfileAssignmentCount")
            logger.info("Initialized profile assignment count SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile assignment count SQL repository: {str(e)}")
            _profile_assignment_count_sql_repo = None
    return _profile_assignment_count_sql_repo


def store_security_health_check_risks(org_id: str, execution_log_id: str, risks_data: List[Dict[str, Any]]) -> bool:
    """
    Store security health check risks

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        risks_data: List of risk data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_health_check_table_repo()
            if not repo:
                logger.error("Health check table repository not available")
                return False

            for risk_data in risks_data:
                health_check = HealthCheck(
                    OrgId=str(org_id),
                    ExecutionLogId=str(execution_log_id),  # Keep as string for both UUID and integer cases
                    RiskType=risk_data.get('risk_type', ''),
                    Setting=risk_data.get('setting', ''),
                    SettingGroup=risk_data.get('setting_group', ''),
                    OrgValue=risk_data.get('org_value'),
                    StandardValue=risk_data.get('standard_value'),
                    LastUpdated=datetime.now()
                )

                entity = health_check.to_table_entity()
                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert health check risk: {risk_data}")
                    return False

            logger.info(f"Stored {len(risks_data)} health check risks for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_health_check_sql_repo()
            if not repo:
                logger.error("Health check SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_HealthCheck (OrgId, ExecutionLogId, RiskType, Setting, SettingGroup,
                                       OrgValue, StandardValue, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            for risk_data in risks_data:
                params = (
                    str(org_id),
                    str(execution_log_id),  # Store as string for consistency
                    risk_data.get('risk_type', ''),
                    risk_data.get('setting', ''),
                    risk_data.get('setting_group', ''),
                    risk_data.get('org_value'),
                    risk_data.get('standard_value'),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert health check risk: {params}")
                    return False

            logger.info(f"Stored {len(risks_data)} health check risks for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing security health check risks: {str(e)}")
        return False


def store_profile_data(org_id: int, execution_log_id: int, profile_data: List[Dict[str, Any]]) -> bool:
    """
    Store profile permission data

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        profile_data: List of profile permission data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_permission_table_repo()
            if not repo:
                logger.error("Profile permission table repository not available")
                return False

            for profile in profile_data:
                profile_permission = ProfilePermission(
                    OrgId=org_id,
                    ExecutionLogId=execution_log_id,
                    SalesforceId=profile.get('salesforce_id', ''),
                    ProfileName=profile.get('profile_name', ''),
                    ObjectName=profile.get('object_name', ''),
                    Read=profile.get('read', False),
                    Create=profile.get('create', False),
                    Edit=profile.get('edit', False),
                    Delete=profile.get('delete', False),
                    ViewAllRecords=profile.get('view_all_records', False),
                    ModifyAllRecords=profile.get('modify_all_records', False),
                    SecurityRisk=profile.get('security_risk'),
                    LastUpdated=datetime.now()
                )

                entity = profile_permission.to_table_entity()
                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert profile permission: {profile}")
                    return False

            logger.info(f"Stored {len(profile_data)} profile permissions for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_profile_permission_sql_repo()
            if not repo:
                logger.error("Profile permission SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_ProfilePermissions (OrgId, ExecutionLogId, SalesforceId, ProfileName,
                                              ObjectName, [Read], [Create], Edit, [Delete],
                                              ViewAllRecords, ModifyAllRecords, SecurityRisk, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            for profile in profile_data:
                params = (
                    org_id,
                    execution_log_id,
                    profile.get('salesforce_id', ''),
                    profile.get('profile_name', ''),
                    profile.get('object_name', ''),
                    profile.get('read', False),
                    profile.get('create', False),
                    profile.get('edit', False),
                    profile.get('delete', False),
                    profile.get('view_all_records', False),
                    profile.get('modify_all_records', False),
                    profile.get('security_risk'),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert profile permission: {profile}")
                    return False

            logger.info(f"Stored {len(profile_data)} profile permissions for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing profile data: {str(e)}")
        return False


def get_security_health_check_data(org_id: str, risk_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get security health check data for an organization

    Args:
        org_id: Organization ID
        risk_type: Optional filter by risk type

    Returns:
        List of health check dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_health_check_table_repo()
            if not repo:
                logger.error("Health check table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if risk_type:
                filter_query += f" and RiskType eq '{risk_type}'"

            entities = list(repo.query_entities(filter_query))

            health_checks = []
            for entity in entities:
                health_checks.append({
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'risk_type': entity.get('RiskType'),
                    'setting': entity.get('Setting'),
                    'setting_group': entity.get('SettingGroup'),
                    'org_value': entity.get('OrgValue'),
                    'standard_value': entity.get('StandardValue'),
                    'last_updated': entity.get('LastUpdated')
                })

            return health_checks

        else:
            # Use SQL Database for production
            repo = get_health_check_sql_repo()
            if not repo:
                logger.error("Health check SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, OrgId, ExecutionLogId, RiskType, Setting, SettingGroup,
                   OrgValue, StandardValue, LastUpdated
            FROM App_HealthCheck WHERE OrgId = ?
            """
            params = [org_id]

            if risk_type:
                query += " AND RiskType = ?"
                params.append(risk_type)

            query += " ORDER BY LastUpdated DESC"

            results = repo.execute_query(query, tuple(params))

            health_checks = []
            for row in results:
                health_checks.append({
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'risk_type': row[3],
                    'setting': row[4],
                    'setting_group': row[5],
                    'org_value': row[6],
                    'standard_value': row[7],
                    'last_updated': row[8].isoformat() if row[8] else None
                })

            return health_checks

    except Exception as e:
        logger.error(f"Error getting security health check data: {str(e)}")
        return []


def get_profile_permissions_data(org_id: int, profile_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get profile permissions data for an organization

    Args:
        org_id: Organization ID
        profile_name: Optional filter by profile name

    Returns:
        List of profile permission dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_permission_table_repo()
            if not repo:
                logger.error("Profile permission table repository not available")
                return []

            filter_query = f"PartitionKey eq 'profile_permission' and OrgId eq {org_id}"
            if profile_name:
                filter_query += f" and ProfileName eq '{profile_name}'"

            entities = list(repo.query_entities(filter_query))

            permissions = []
            for entity in entities:
                permissions.append({
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'salesforce_id': entity.get('SalesforceId'),
                    'profile_name': entity.get('ProfileName'),
                    'object_name': entity.get('ObjectName'),
                    'read': entity.get('Read', False),
                    'create': entity.get('Create', False),
                    'edit': entity.get('Edit', False),
                    'delete': entity.get('Delete', False),
                    'view_all_records': entity.get('ViewAllRecords', False),
                    'modify_all_records': entity.get('ModifyAllRecords', False),
                    'security_risk': entity.get('SecurityRisk'),
                    'last_updated': entity.get('LastUpdated')
                })

            return permissions

        else:
            # Use SQL Database for production
            repo = get_profile_permission_sql_repo()
            if not repo:
                logger.error("Profile permission SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, OrgId, ExecutionLogId, SalesforceId, ProfileName, ObjectName,
                   [Read], [Create], Edit, [Delete], ViewAllRecords, ModifyAllRecords,
                   SecurityRisk, LastUpdated
            FROM App_ProfilePermissions WHERE OrgId = ?
            """
            params = [org_id]

            if profile_name:
                query += " AND ProfileName = ?"
                params.append(profile_name)

            query += " ORDER BY ProfileName, ObjectName"

            results = repo.execute_query(query, tuple(params))

            permissions = []
            for row in results:
                permissions.append({
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'salesforce_id': row[3],
                    'profile_name': row[4],
                    'object_name': row[5],
                    'read': row[6],
                    'create': row[7],
                    'edit': row[8],
                    'delete': row[9],
                    'view_all_records': row[10],
                    'modify_all_records': row[11],
                    'security_risk': row[12],
                    'last_updated': row[13].isoformat() if row[13] else None
                })

            return permissions

    except Exception as e:
        logger.error(f"Error getting profile permissions data: {str(e)}")
        return []


def get_security_overview_data(org_id: int) -> Optional[Dict[str, Any]]:
    """
    Get security overview data for an organization

    Args:
        org_id: Organization ID

    Returns:
        Overview data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_overview_table_repo()
            if not repo:
                logger.error("Overview table repository not available")
                return None

            filter_query = f"PartitionKey eq 'overview' and OrgId eq {org_id}"
            entities = list(repo.query_entities(filter_query))

            if entities:
                # Get the most recent overview
                entity = max(entities, key=lambda x: x.get('LastUpdated', ''))
                return {
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'health_score': entity.get('HealthScore'),
                    'total_profiles': entity.get('TotalProfiles'),
                    'total_permission_sets': entity.get('TotalPermissionSets'),
                    'total_risks': entity.get('TotalRisks'),
                    'high_risks': entity.get('HighRisks'),
                    'medium_risks': entity.get('MediumRisks'),
                    'low_risks': entity.get('LowRisks'),
                    'last_updated': entity.get('LastUpdated')
                }
            else:
                return None

        else:
            # Use SQL Database for production
            repo = get_overview_sql_repo()
            if not repo:
                logger.error("Overview SQL repository not available")
                return None

            query = """
            SELECT TOP 1 Id, OrgId, ExecutionLogId, HealthScore, TotalProfiles,
                   TotalPermissionSets, TotalRisks, HighRisks, MediumRisks, LowRisks, LastUpdated
            FROM App_Overview
            WHERE OrgId = ?
            ORDER BY LastUpdated DESC
            """

            results = repo.execute_query(query, (org_id,))

            if results:
                row = results[0]
                return {
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'health_score': row[3],
                    'total_profiles': row[4],
                    'total_permission_sets': row[5],
                    'total_risks': row[6],
                    'high_risks': row[7],
                    'medium_risks': row[8],
                    'low_risks': row[9],
                    'last_updated': row[10].isoformat() if row[10] else None
                }
            else:
                return None

    except Exception as e:
        logger.error(f"Error getting security overview data: {str(e)}")
        return None


def store_policies_result_data(org_id: str, execution_log_id: str, policies_data: List[Dict[str, Any]]) -> bool:
    """
    Store policies result data

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        policies_data: List of policies result data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policies_result_table_repo()
            if not repo:
                logger.error("Policies result table repository not available")
                return False

            for policy_data in policies_data:
                entity = {
                    "PartitionKey": str(org_id),
                    "RowKey": f"{policy_data.get('Setting', '')}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                    "OrgValue": policy_data.get('OrgValue'),
                    "OWASPCategory": policy_data.get('OWASPCategory', ''),
                    "StandardValue": policy_data.get('StandardValue', ''),
                    "IssueDescription": policy_data.get('IssueDescription', ''),
                    "Recommendations": policy_data.get('Recommendations', ''),
                    "Severity": policy_data.get('Severity', ''),
                    "Weakness": policy_data.get('Weakness', ''),
                    "IntegrationId": str(org_id),
                    "TaskStatusId": execution_log_id,
                    "CreatedAt": datetime.now().isoformat(),
                    "Setting": policy_data.get('Setting'),
                    "Type": policy_data.get('Type', '')  # CRITICAL: Add Type field for health check identification
                }

                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert policies result: {policy_data}")
                    return False

            logger.info(f"Stored {len(policies_data)} policies results for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_policies_result_sql_repo()
            if not repo:
                logger.error("Policies result SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_PoliciesResult (OrgId, ExecutionLogId, Setting, OrgValue, OWASPCategory,
                                          StandardValue, IssueDescription, Recommendations, Severity,
                                          Weakness, IntegrationId, TaskStatusId, CreatedAt, Type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            for policy_data in policies_data:
                params = (
                    org_id,
                    execution_log_id,
                    policy_data.get('Setting', ''),
                    policy_data.get('OrgValue'),
                    policy_data.get('OWASPCategory', ''),
                    policy_data.get('StandardValue', ''),
                    policy_data.get('IssueDescription', ''),
                    policy_data.get('Recommendations', ''),
                    policy_data.get('Severity', ''),
                    policy_data.get('Weakness', ''),
                    str(org_id),
                    execution_log_id,
                    datetime.now(),
                    policy_data.get('Type', '')  # CRITICAL: Add Type field for health check identification
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert policies result: {params}")
                    return False

            logger.info(f"Stored {len(policies_data)} policies results for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing policies result data: {str(e)}")
        return False


def get_policies_result_data(org_id: str, execution_log_id: Optional[str] = None, policy_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get policies result data for an organization

    Args:
        org_id: Organization ID
        execution_log_id: Optional filter by execution log ID
        policy_type: Optional filter by policy type (e.g., 'HealthCheck', 'ProfilePermission')

    Returns:
        List of policies result dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policies_result_table_repo()
            if not repo:
                logger.error("Policies result table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if execution_log_id:
                filter_query += f" and ExecutionLogId eq '{execution_log_id}'"
            if policy_type:
                filter_query += f" and Type eq '{policy_type}'"

            entities = list(repo.query_entities(filter_query))

            policies_results = []
            for entity in entities:
                policies_results.append({
                    'PolicyId': entity.get('PolicyId') or entity.get('Setting'),
                    'OrgValue': entity.get('OrgValue'),
                    'StandardValue': entity.get('StandardValue'),
                    'Weakness': entity.get('Weakness'),
                    'OWASPCategory': entity.get('OWASPCategory'),
                    'Severity': entity.get('Severity'),
                    'Recommendations': entity.get('Recommendations'),
                    'IssueDescription': entity.get('IssueDescription'),
                    'CreatedAt': entity.get('CreatedAt'),
                    'ExecutionLogId': entity.get('ExecutionLogId') or entity.get('TaskStatusId'),
                    'IntegrationId': entity.get('IntegrationId'),
                    'Type': entity.get('Type'),
                    'ProfileName': entity.get('ProfileName'),
                    'PermissionSetName': entity.get('PermissionSetName')
                })

            # Sort by CreatedAt descending
            policies_results.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
            return policies_results

        else:
            # Use SQL Database for production
            repo = get_policies_result_sql_repo()
            if not repo:
                logger.error("Policies result SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Setting, OrgValue, StandardValue, Weakness, OWASPCategory, Severity,
                   Recommendations, IssueDescription, CreatedAt, TaskStatusId, Type
            FROM App_PoliciesResult WHERE OrgId = ?
            """
            params = [org_id]

            if execution_log_id:
                query += " AND TaskStatusId = ?"
                params.append(execution_log_id)
            if policy_type:
                query += " AND Type = ?"
                params.append(policy_type)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            policies_results = []
            for row in results:
                policies_results.append({
                    'PolicyId': row[0],  # Setting field mapped to PolicyId
                    'OrgValue': row[1],
                    'StandardValue': row[2],
                    'Weakness': row[3],
                    'OWASPCategory': row[4],
                    'Severity': row[5],
                    'Recommendations': row[6],
                    'IssueDescription': row[7],
                    'CreatedAt': row[8].isoformat() if row[8] else None,
                    'ExecutionLogId': row[9],  # TaskStatusId mapped to ExecutionLogId
                    'Type': row[10]  # Add Type field
                })

            return policies_results

    except Exception as e:
        logger.error(f"Error getting policies result data: {str(e)}")
        return []


def store_profile_assignment_count_data(org_id: str, execution_log_id: str, assignment_data: List[Dict[str, Any]]) -> bool:
    """
    Store profile assignment count data

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        assignment_data: List of profile assignment count data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_assignment_count_table_repo()
            if not repo:
                logger.error("Profile assignment count table repository not available")
                return False

            for assignment in assignment_data:
                entity = {
                    "PartitionKey": str(org_id),
                    "RowKey": f"{assignment.get('ProfileName', '')}-{assignment.get('PermissionSetName', '')}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                    "ExecutionLogId": execution_log_id,
                    "ProfileName": assignment.get('ProfileName', ''),
                    "PermissionSetName": assignment.get('PermissionSetName', ''),
                    "AssignmentCount": assignment.get('AssignmentCount', 0),
                    "AssignedProfiles": assignment.get('AssignedProfiles', ''),
                    "Type": assignment.get('Type', ''),
                    "CreatedAt": datetime.now().isoformat()
                }

                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert profile assignment count: {assignment}")
                    return False

            logger.info(f"Stored {len(assignment_data)} profile assignment counts for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_profile_assignment_count_sql_repo()
            if not repo:
                logger.error("Profile assignment count SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_ProfileAssignmentCount (ExecutionLogId, ProfileName, PermissionSetName,
                                                   AssignmentCount, AssignedProfiles, Type, CreatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            for assignment in assignment_data:
                params = (
                    execution_log_id,
                    assignment.get('ProfileName', ''),
                    assignment.get('PermissionSetName', ''),
                    assignment.get('AssignmentCount', 0),
                    assignment.get('AssignedProfiles', ''),
                    assignment.get('Type', ''),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert profile assignment count: {params}")
                    return False

            logger.info(f"Stored {len(assignment_data)} profile assignment counts for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing profile assignment count data: {str(e)}")
        return False


def get_profile_assignment_count_data(org_id: str, execution_log_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get profile assignment count data for an organization

    Args:
        org_id: Organization ID
        execution_log_id: Optional filter by execution log ID

    Returns:
        List of profile assignment count dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_assignment_count_table_repo()
            if not repo:
                logger.error("Profile assignment count table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if execution_log_id:
                filter_query += f" and ExecutionLogId eq '{execution_log_id}'"

            entities = list(repo.query_entities(filter_query))

            assignment_counts = []
            for entity in entities:
                assignment_counts.append({
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'profile_name': entity.get('ProfileName'),
                    'permission_set_name': entity.get('PermissionSetName'),
                    'assignment_count': entity.get('AssignmentCount', 0),
                    'assigned_profiles': entity.get('AssignedProfiles'),
                    'type': entity.get('Type'),
                    'created_at': entity.get('CreatedAt')
                })

            # Sort by CreatedAt descending
            assignment_counts.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return assignment_counts

        else:
            # Use SQL Database for production
            repo = get_profile_assignment_count_sql_repo()
            if not repo:
                logger.error("Profile assignment count SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT ExecutionLogId, ProfileName, PermissionSetName, AssignmentCount,
                   AssignedProfiles, Type, CreatedAt
            FROM App_ProfileAssignmentCount WHERE 1=1
            """
            params = []

            if execution_log_id:
                query += " AND ExecutionLogId = ?"
                params.append(execution_log_id)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            assignment_counts = []
            for row in results:
                assignment_counts.append({
                    'execution_log_id': row[0],
                    'profile_name': row[1],
                    'permission_set_name': row[2],
                    'assignment_count': row[3],
                    'assigned_profiles': row[4],
                    'type': row[5],
                    'created_at': row[6].isoformat() if row[6] else None
                })

            return assignment_counts

    except Exception as e:
        logger.error(f"Error getting profile assignment count data: {str(e)}")
        return []


# Create blueprint
bp = func.Blueprint()

@bp.route(route="security/health-checks", methods=["GET"])
def list_health_checks(req: func.HttpRequest) -> func.HttpResponse:
    """Get health check data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        risk_type = req.params.get('risk_type')

        if org_id:
            health_checks = get_security_health_check_data(org_id, risk_type)
        else:
            health_checks = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": health_checks
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting health check data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/health-checks", methods=["POST"])
def store_health_checks(req: func.HttpRequest) -> func.HttpResponse:
    """Store health check data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        risks_data = req_body.get('risks_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store health check data
        success = store_security_health_check_risks(org_id, execution_log_id, risks_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Successfully stored {len(risks_data)} health check records"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store health check data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing health check data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/policies-result", methods=["GET"])
def list_policies_results(req: func.HttpRequest) -> func.HttpResponse:
    """Get policies result data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        execution_log_id = req.params.get('execution_log_id')
        policy_type = req.params.get('type')  # Fix: use 'type' parameter name to match SFDC service

        if org_id:
            policies_results = get_policies_result_data(org_id, execution_log_id, policy_type)
        else:
            policies_results = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": policies_results
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting policies result data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/policies-result", methods=["POST"])
def store_policies_results(req: func.HttpRequest) -> func.HttpResponse:
    """Store policies result data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        policies_data = req_body.get('policies_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        success = store_policies_result_data(org_id, execution_log_id, policies_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Stored {len(policies_data)} policies results"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store policies result data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing policies result data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/profile-assignment-counts", methods=["GET"])
def list_profile_assignment_counts(req: func.HttpRequest) -> func.HttpResponse:
    """Get profile assignment count data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        execution_log_id = req.params.get('execution_log_id')

        if org_id:
            assignment_counts = get_profile_assignment_count_data(org_id, execution_log_id)
        else:
            assignment_counts = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": assignment_counts
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting profile assignment count data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/profile-assignment-counts", methods=["POST"])
def store_profile_assignment_counts(req: func.HttpRequest) -> func.HttpResponse:
    """Store profile assignment count data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        assignment_data = req_body.get('assignment_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        success = store_profile_assignment_count_data(org_id, execution_log_id, assignment_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Stored {len(assignment_data)} profile assignment counts"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store profile assignment count data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing profile assignment count data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )