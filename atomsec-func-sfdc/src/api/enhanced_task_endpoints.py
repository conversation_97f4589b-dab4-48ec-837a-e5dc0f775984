"""
Enhanced Task Management Endpoints

This module demonstrates the integration of enhanced security framework
with task management endpoints for SFDC service.
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import enhanced security modules
from src.shared.security_middleware import secure_endpoint, validate_sequential_task_request
from src.shared.enhanced_parameter_validator import (
    validate_sfdc_request,
    validate_sequential_task,
    ParameterValidationError,
    SecurityValidationError
)
from src.shared.enhanced_auth_service import (
    authenticate_salesforce_api,
    SalesforceAuthenticationError
)

# Import existing modules
from src.shared.azure_services import is_local_dev
from src.shared.data_access import TableStorageRepository, SqlDatabaseRepository
from src.shared.database_models_new import Task
from src.shared.event_publisher import publish_task_event
from src.shared.auth_utils import get_user_from_request_or_default

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()


@bp.route(route="tasks/secure", methods=["POST"])
@secure_endpoint(
    required_fields=['task_type', 'org_id'],
    validate_auth=True,
    validate_execution_log=True
)
def create_secure_task(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a new task with enhanced security validation
    
    This endpoint demonstrates the use of the enhanced security framework
    for task creation with comprehensive parameter validation.
    """
    try:
        # Access validated parameters from security middleware
        validated_params = req.validated_params
        user_info = req.user_info
        
        logger.info(f"Creating secure task with validated parameters: {list(validated_params.keys())}")
        
        # Extract task data from validated parameters
        task_data = {
            'task_type': validated_params['task_type'],
            'org_id': validated_params['org_id'],
            'user_id': user_info['id'] if user_info else 'system',
            'params': validated_params.get('params', {}),
            'priority': validated_params.get('priority', 'medium'),
            'execution_log_id': validated_params['execution_log_id']
        }
        
        # Additional validation for Salesforce credentials if present
        if any(key in validated_params for key in ['client_id', 'client_secret', 'instance_url']):
            try:
                # Test Salesforce authentication
                sf_credentials = {
                    'client_id': validated_params.get('client_id'),
                    'client_secret': validated_params.get('client_secret'),
                    'instance_url': validated_params.get('instance_url'),
                    'is_sandbox': validated_params.get('is_sandbox', False)
                }
                
                connection_details = authenticate_salesforce_api(sf_credentials)
                logger.info("Salesforce authentication successful for task creation")
                
                # Add connection details to task parameters
                task_data['params'].update({
                    'access_token': connection_details['access_token'],
                    'instance_url': connection_details['instance_url']
                })
                
            except SalesforceAuthenticationError as e:
                logger.error(f"Salesforce authentication failed: {str(e)}")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Salesforce authentication failed: {str(e)}",
                        "error_type": "salesforce_auth"
                    }),
                    mimetype="application/json",
                    status_code=401
                )
        
        # Create the task using existing logic
        from api.task_endpoints import create_task
        task_id = create_task(task_data)
        
        if task_id:
            logger.info(f"Successfully created secure task: {task_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "task_id": task_id,
                        "execution_log_id": task_data['execution_log_id']
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create task"
                }),
                mimetype="application/json",
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error in secure task creation: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error",
                "error_type": "internal_error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/sequential/{task_type}", methods=["POST"])
def create_sequential_task(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a task as part of a sequential task processing workflow
    
    This endpoint demonstrates sequential task parameter validation
    with execution log ID tracking.
    """
    try:
        # Extract task type from route
        task_type = req.route_params.get('task_type')
        if not task_type:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task type is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Parse request data
        try:
            request_data = req.get_json() or {}
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Extract execution_log_id from request
        execution_log_id = request_data.get('execution_log_id')
        if not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id is required for sequential tasks"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate sequential task parameters
        try:
            validated_params = validate_sequential_task(
                request_data,
                task_type,
                execution_log_id
            )
        except (ParameterValidationError, SecurityValidationError) as e:
            logger.warning(f"Sequential task validation failed: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Parameter validation failed: {str(e)}",
                    "error_type": "parameter_validation"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Create task data
        task_data = {
            'task_type': task_type,
            'org_id': validated_params.get('org_id'),
            'user_id': validated_params.get('user_id', 'system'),
            'params': validated_params,
            'priority': validated_params.get('priority', 'medium'),
            'execution_log_id': execution_log_id
        }
        
        # Validate required fields
        if not task_data['org_id']:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Create the task
        from api.task_endpoints import create_task
        task_id = create_task(task_data)
        
        if task_id:
            logger.info(f"Successfully created sequential task: {task_id} (type: {task_type}, execution_log: {execution_log_id})")
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "task_id": task_id,
                        "task_type": task_type,
                        "execution_log_id": execution_log_id
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create sequential task"
                }),
                mimetype="application/json",
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error in sequential task creation: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/validate-credentials", methods=["POST"])
@secure_endpoint(
    required_fields=['client_id', 'client_secret', 'instance_url'],
    validate_auth=True,
    validate_execution_log=False
)
def validate_salesforce_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    Validate Salesforce credentials with enhanced security
    
    This endpoint demonstrates secure credential validation
    using the enhanced authentication service.
    """
    try:
        # Access validated parameters from security middleware
        validated_params = req.validated_params
        user_info = req.user_info
        
        logger.info(f"Validating Salesforce credentials for user: {user_info.get('id') if user_info else 'unknown'}")
        
        # Extract Salesforce credentials
        sf_credentials = {
            'client_id': validated_params['client_id'],
            'client_secret': validated_params['client_secret'],
            'instance_url': validated_params['instance_url'],
            'is_sandbox': validated_params.get('is_sandbox', False)
        }
        
        # Test Salesforce authentication
        try:
            connection_details = authenticate_salesforce_api(sf_credentials)
            
            # Return success without exposing sensitive details
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "valid": True,
                        "instance_url": connection_details['instance_url'],
                        "token_type": connection_details.get('token_type'),
                        "authenticated_at": datetime.utcnow().isoformat()
                    }
                }),
                mimetype="application/json",
                status_code=200
            )
            
        except SalesforceAuthenticationError as e:
            logger.warning(f"Salesforce credential validation failed: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "data": {
                        "valid": False,
                        "error": str(e)
                    }
                }),
                mimetype="application/json",
                status_code=200  # Return 200 for validation result, not auth failure
            )
            
    except Exception as e:
        logger.error(f"Error in credential validation: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/sequence/start", methods=["POST"])
@secure_endpoint(
    required_fields=['sequence_type', 'org_id'],
    validate_auth=True,
    validate_execution_log=False
)
def start_task_sequence(req: func.HttpRequest) -> func.HttpResponse:
    """
    Start a new task sequence with execution log tracking
    
    This endpoint demonstrates sequential task coordination and validation.
    """
    try:
        # Access validated parameters from security middleware
        validated_params = req.validated_params
        user_info = req.user_info
        
        # Import task sequence coordinator
        from src.shared.task_sequence_coordinator import get_task_sequence_coordinator
        coordinator = get_task_sequence_coordinator()
        
        # Extract parameters
        sequence_type = validated_params['sequence_type']
        org_id = validated_params['org_id']
        user_id = user_info['id'] if user_info else 'system'
        base_params = validated_params.get('params', {})
        execution_log_id = validated_params.get('execution_log_id')
        
        # Start the task sequence
        execution_log_id = coordinator.start_task_sequence(
            sequence_type=sequence_type,
            org_id=org_id,
            user_id=user_id,
            params=base_params,
            execution_log_id=execution_log_id
        )
        
        # Get sequence status
        sequence_status = coordinator.get_sequence_status(execution_log_id)
        
        logger.info(f"Started task sequence {sequence_type} with execution_log_id: {execution_log_id}")
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "execution_log_id": execution_log_id,
                    "sequence_status": sequence_status
                }
            }),
            mimetype="application/json",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Error starting task sequence: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to start task sequence: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/sequence/{execution_log_id}/status", methods=["GET"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def get_sequence_status(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get the status of a task sequence
    
    This endpoint demonstrates execution log tracking and monitoring.
    """
    try:
        # Extract execution log ID from route
        execution_log_id = req.route_params.get('execution_log_id')
        if not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Import task sequence coordinator
        from src.shared.task_sequence_coordinator import get_task_sequence_coordinator
        coordinator = get_task_sequence_coordinator()
        
        # Get sequence status
        sequence_status = coordinator.get_sequence_status(execution_log_id)
        
        if not sequence_status:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Sequence not found"
                }),
                mimetype="application/json",
                status_code=404
            )
        
        # Get next task if sequence is still running
        next_task = coordinator.get_next_task(execution_log_id)
        if next_task:
            sequence_status['next_task'] = next_task
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": sequence_status
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error getting sequence status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/sequence/{execution_log_id}/next", methods=["POST"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def execute_next_task(req: func.HttpRequest) -> func.HttpResponse:
    """
    Execute the next task in a sequence with validation
    
    This endpoint demonstrates sequential task execution with dependency validation.
    """
    try:
        # Extract execution log ID from route
        execution_log_id = req.route_params.get('execution_log_id')
        if not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Parse additional parameters
        try:
            additional_params = req.get_json() or {}
        except ValueError:
            additional_params = {}
        
        # Import task sequence coordinator
        from src.shared.task_sequence_coordinator import get_task_sequence_coordinator
        coordinator = get_task_sequence_coordinator()
        
        # Get next task
        next_task = coordinator.get_next_task(execution_log_id)
        if not next_task:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "No next task available or sequence completed"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate task execution
        can_execute, message, validated_params = coordinator.validate_task_execution(
            next_task, execution_log_id, additional_params
        )
        
        if not can_execute:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Cannot execute task {next_task}: {message}"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Create the task using existing logic
        from api.task_endpoints import create_task
        task_data = {
            'task_type': next_task,
            'org_id': validated_params['org_id'],
            'user_id': validated_params.get('user_id', 'system'),
            'params': validated_params,
            'priority': validated_params.get('priority', 'medium'),
            'execution_log_id': execution_log_id
        }
        
        task_id = create_task(task_data)
        
        if task_id:
            logger.info(f"Created next task in sequence: {task_id} (type: {next_task}, execution_log: {execution_log_id})")
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "task_id": task_id,
                        "task_type": next_task,
                        "execution_log_id": execution_log_id,
                        "message": message
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create next task"
                }),
                mimetype="application/json",
                status_code=500
            )
        
    except Exception as e:
        logger.error(f"Error executing next task: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/security-status", methods=["GET"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def get_security_status(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get security status and events for monitoring
    
    This endpoint demonstrates security monitoring capabilities.
    """
    try:
        user_info = req.user_info
        
        # Get security events from enhanced services
        from src.shared.enhanced_parameter_validator import get_enhanced_parameter_validator
        from src.shared.enhanced_auth_service import get_enhanced_auth_service
        from src.shared.internal_service_security import get_internal_service_security
        
        validator = get_enhanced_parameter_validator()
        auth_service = get_enhanced_auth_service()
        internal_security = get_internal_service_security()
        
        # Get recent security events
        since_param = req.params.get('since')
        since_datetime = None
        if since_param:
            try:
                since_datetime = datetime.fromisoformat(since_param.replace('Z', '+00:00'))
            except ValueError:
                pass
        
        validation_events = validator.get_security_events(since=since_datetime)
        auth_events = auth_service.get_security_events(since=since_datetime)
        internal_events = internal_security.get_security_events(since=since_datetime)
        
        # Get rate limit status for current user
        user_id = user_info.get('id', 'unknown')
        rate_limit_status = {
            'authentication': auth_service.get_rate_limit_status('authentication', user_id),
            'token_validation': auth_service.get_rate_limit_status('token_validation', user_id),
            'salesforce_api': auth_service.get_rate_limit_status('salesforce_api', user_id)
        }
        
        # Get trusted services information
        trusted_services = internal_security.get_trusted_services()
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "user_id": user_id,
                    "validation_events": validation_events,
                    "auth_events": auth_events,
                    "internal_service_events": internal_events,
                    "rate_limits": rate_limit_status,
                    "trusted_services": trusted_services,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error getting security status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Internal server error"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/credentials/store", methods=["POST"])
@secure_endpoint(
    required_fields=['org_id', 'client_id', 'client_secret', 'instance_url'],
    validate_auth=True,
    validate_execution_log=False
)
def store_salesforce_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    Store Salesforce credentials securely
    
    This endpoint demonstrates enhanced Salesforce credential management.
    """
    try:
        # Access validated parameters from security middleware
        validated_params = req.validated_params
        user_info = req.user_info
        
        # Import credential manager
        from src.shared.enhanced_salesforce_credential_manager import get_salesforce_credential_manager
        credential_manager = get_salesforce_credential_manager()
        
        # Extract parameters
        org_id = validated_params['org_id']
        user_id = user_info['id'] if user_info else 'system'
        
        # Prepare credentials
        credentials = {
            'client_id': validated_params['client_id'],
            'client_secret': validated_params['client_secret'],
            'instance_url': validated_params['instance_url'],
            'is_sandbox': validated_params.get('is_sandbox', False),
            'username': validated_params.get('username'),
            'private_key': validated_params.get('private_key')
        }
        
        # Store credentials
        success = credential_manager.store_salesforce_credentials(org_id, credentials, user_id)
        
        if success:
            logger.info(f"Successfully stored Salesforce credentials for org {org_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Credentials stored successfully",
                    "org_id": org_id
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store credentials"
                }),
                mimetype="application/json",
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error storing Salesforce credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to store credentials: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/credentials/{org_id}/validate", methods=["POST"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def validate_stored_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    Validate stored Salesforce credentials
    
    This endpoint demonstrates credential validation and refresh capabilities.
    """
    try:
        # Extract org_id from route
        org_id = req.route_params.get('org_id')
        if not org_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Parse additional parameters
        try:
            params = req.get_json() or {}
        except ValueError:
            params = {}
        
        force_validation = params.get('force_validation', False)
        
        # Import credential manager
        from src.shared.enhanced_salesforce_credential_manager import get_salesforce_credential_manager
        credential_manager = get_salesforce_credential_manager()
        
        # Validate credentials
        is_valid, message, connection_details = credential_manager.validate_salesforce_credentials(
            org_id, force_validation=force_validation
        )
        
        if is_valid:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "valid": True,
                        "message": message,
                        "org_id": org_id,
                        "instance_url": connection_details.get('instance_url') if connection_details else None,
                        "validated_at": datetime.utcnow().isoformat()
                    }
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "valid": False,
                        "message": message,
                        "org_id": org_id,
                        "validated_at": datetime.utcnow().isoformat()
                    }
                }),
                mimetype="application/json",
                status_code=200
            )
            
    except Exception as e:
        logger.error(f"Error validating stored credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Validation error: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/credentials/{org_id}/refresh", methods=["POST"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def refresh_salesforce_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    Refresh Salesforce credentials
    
    This endpoint demonstrates credential refresh and lifecycle management.
    """
    try:
        # Extract org_id from route
        org_id = req.route_params.get('org_id')
        if not org_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Import credential manager
        from src.shared.enhanced_salesforce_credential_manager import get_salesforce_credential_manager
        credential_manager = get_salesforce_credential_manager()
        
        # Refresh credentials
        success, message, connection_details = credential_manager.refresh_salesforce_credentials(org_id)
        
        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "refreshed": True,
                        "message": message,
                        "org_id": org_id,
                        "instance_url": connection_details.get('instance_url') if connection_details else None,
                        "refreshed_at": datetime.utcnow().isoformat()
                    }
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to refresh credentials: {message}"
                }),
                mimetype="application/json",
                status_code=400
            )
            
    except Exception as e:
        logger.error(f"Error refreshing credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Refresh error: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/credentials", methods=["GET"])
@secure_endpoint(validate_auth=True, validate_execution_log=False)
def list_stored_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    List stored Salesforce credentials (metadata only)
    
    This endpoint demonstrates credential lifecycle management.
    """
    try:
        # Import credential manager
        from src.shared.enhanced_salesforce_credential_manager import get_salesforce_credential_manager
        credential_manager = get_salesforce_credential_manager()
        
        # List credentials
        credentials_list = credential_manager.list_stored_credentials()
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "credentials": credentials_list,
                    "count": len(credentials_list),
                    "retrieved_at": datetime.utcnow().isoformat()
                }
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error listing stored credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to list credentials: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/internal/health", methods=["GET"])
def internal_service_health(req: func.HttpRequest) -> func.HttpResponse:
    """
    Internal service health check endpoint
    
    This endpoint is specifically for internal service-to-service health checks.
    """
    from src.shared.security_middleware import internal_service_endpoint
    
    @internal_service_endpoint()
    def _health_check(req: func.HttpRequest) -> func.HttpResponse:
        try:
            service_info = req.internal_service_info
            
            # Perform basic health checks
            health_status = {
                "status": "healthy",
                "service": "atomsec-func-sfdc",
                "timestamp": datetime.utcnow().isoformat(),
                "calling_service": service_info.get('service_name'),
                "request_id": service_info.get('request_id'),
                "checks": {
                    "internal_security": "operational",
                    "parameter_validation": "operational",
                    "task_processing": "operational"
                }
            }
            
            # Test enhanced security components
            try:
                from src.shared.enhanced_parameter_validator import get_enhanced_parameter_validator
                from src.shared.enhanced_auth_service import get_enhanced_auth_service
                from src.shared.internal_service_security import get_internal_service_security
                from src.shared.enhanced_salesforce_credential_manager import get_salesforce_credential_manager
                
                # Test validator
                validator = get_enhanced_parameter_validator()
                test_params = {"test": "value", "org_id": "test-org"}
                validator.validate_json_parameters(test_params)
                
                # Test auth service
                auth_service = get_enhanced_auth_service()
                
                # Test internal security
                internal_security = get_internal_service_security()
                
                # Test credential manager
                credential_manager = get_salesforce_credential_manager()
                
                health_status["checks"]["enhanced_security"] = "operational"
                health_status["checks"]["credential_management"] = "operational"
                
            except Exception as e:
                health_status["checks"]["enhanced_security"] = f"error: {str(e)}"
                health_status["status"] = "degraded"
            
            status_code = 200 if health_status["status"] == "healthy" else 503
            
            return func.HttpResponse(
                json.dumps(health_status),
                mimetype="application/json",
                status_code=status_code
            )
            
        except Exception as e:
            logger.error(f"Internal service health check failed: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }),
                mimetype="application/json",
                status_code=503
            )
    
    return _health_check(req)