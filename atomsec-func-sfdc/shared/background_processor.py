"""
Background Processor Module

This module provides functionality for processing long-running tasks in the background.
It uses Azure Functions Durable Entities to manage task state and Azure Queue Storage
for task execution.
"""

import json
import logging
import ast
from azure.storage.queue import QueueClient
from azure.core.exceptions import ResourceExistsError
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import uuid

from shared.config import get_storage_connection_string
from shared.common import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository, get_policy_table_repo, get_rule_table_repo
from shared.db_service_client import get_db_client
from shared.service_bus_client import get_service_bus_task_client

# Configure module-level logger
logger = logging.getLogger(__name__)

# Task status constants
TASK_STATUS_PENDING = "pending"
TASK_STATUS_RUNNING = "running"
TASK_STATUS_COMPLETED = "completed"
TASK_STATUS_FAILED = "failed"
TASK_STATUS_RETRY = "retry"
TASK_STATUS_CANCELLED = "cancelled"

# Task types
TASK_TYPE_OVERVIEW = "overview"
TASK_TYPE_HEALTH_CHECK = "health_check"
TASK_TYPE_PROFILES = "profiles"
TASK_TYPE_PROFILES_PERMISSION_SETS = "profiles_permission_sets"
# TASK_TYPE_GUEST_USER_RISKS = "guest_user_risks" // Removed Guest User Risks
# TASK_TYPE_PMD_ISSUES = "pmd_issues" # Removed PMD Issues
TASK_TYPE_DATA_EXPORT = "data_export"
TASK_TYPE_REPORT_GENERATION = "report_generation"
TASK_TYPE_SCHEDULED_SCAN = "scheduled_scan"
TASK_TYPE_NOTIFICATION = "notification"
TASK_TYPE_METADATA_EXTRACTION = "metadata_extraction"
TASK_TYPE_SFDC_AUTHENTICATE = "sfdc_authenticate"
TASK_TYPE_PERMISSION_SETS = "permission_sets"

# Additional security policy task types
TASK_TYPE_MFA_ENFORCEMENT = "mfa_enforcement"
TASK_TYPE_DEVICE_ACTIVATION = "device_activation"
TASK_TYPE_LOGIN_IP_RANGES = "login_ip_ranges"
TASK_TYPE_LOGIN_HOURS = "login_hours"
TASK_TYPE_SESSION_TIMEOUT = "session_timeout"
TASK_TYPE_API_WHITELISTING = "api_whitelisting"
TASK_TYPE_PASSWORD_POLICY = "password_policy"
TASK_TYPE_PMD_APEX_SECURITY = "pmd_apex_security"

# Task priorities
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_LOW = "low"

# Queue names for different priorities
QUEUE_PRIORITY_HIGH = "task-queue-high"
QUEUE_PRIORITY_MEDIUM = "task-queue-medium"
QUEUE_PRIORITY_LOW = "task-queue-low"

# Task type to priority mapping
TASK_TYPE_PRIORITY_MAP = {
    # High priority tasks - critical for security monitoring
    TASK_TYPE_HEALTH_CHECK: TASK_PRIORITY_HIGH,
    # TASK_TYPE_GUEST_USER_RISKS: TASK_PRIORITY_HIGH, // Removed Guest User Risks
    TASK_TYPE_NOTIFICATION: TASK_PRIORITY_HIGH,
    TASK_TYPE_METADATA_EXTRACTION: TASK_PRIORITY_HIGH,
    TASK_TYPE_SFDC_AUTHENTICATE: TASK_PRIORITY_HIGH,

    # Medium priority tasks - important but not as time-critical
    TASK_TYPE_PROFILES: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PROFILES_PERMISSION_SETS: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PERMISSION_SETS: TASK_PRIORITY_MEDIUM,
    # TASK_TYPE_PMD_ISSUES: TASK_PRIORITY_MEDIUM, # Removed PMD Issues
    TASK_TYPE_OVERVIEW: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_SCHEDULED_SCAN: TASK_PRIORITY_MEDIUM,

    # Security policy tasks - medium priority
    TASK_TYPE_MFA_ENFORCEMENT: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_DEVICE_ACTIVATION: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_LOGIN_IP_RANGES: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_LOGIN_HOURS: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_SESSION_TIMEOUT: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_API_WHITELISTING: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PASSWORD_POLICY: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PMD_APEX_SECURITY: TASK_PRIORITY_MEDIUM,

    # Low priority tasks - useful but can be processed when resources are available
    TASK_TYPE_DATA_EXPORT: TASK_PRIORITY_LOW,
    TASK_TYPE_REPORT_GENERATION: TASK_PRIORITY_LOW
}

class BackgroundProcessor:
    """Background processor for handling long-running tasks"""

    def __init__(self):
        """Initialize the background processor"""
        try:
            # Get storage connection string
            self.connection_string = get_storage_connection_string()
            if not self.connection_string:
                logger.error("Failed to get storage connection string")
            else:
                logger.info(f"Successfully got storage connection string with length: {len(self.connection_string)}")
        except Exception as e:
            logger.error(f"Error getting storage connection string: {str(e)}")
            import traceback
            logger.error(f"Connection string error traceback: {traceback.format_exc()}")
            self.connection_string = None

        self.default_queue_name = "task-queue"
        self.priority_queue_map = {
            TASK_PRIORITY_HIGH: QUEUE_PRIORITY_HIGH,
            TASK_PRIORITY_MEDIUM: QUEUE_PRIORITY_MEDIUM,
            TASK_PRIORITY_LOW: QUEUE_PRIORITY_LOW
        }
        self.task_status_table_name = "TaskStatus"
        self.max_retries = 3  # Maximum number of retries for failed tasks

        # Initialize queues
        self._queue_clients = {}

        # Log initialization
        logger.info("BackgroundProcessor initialized")

    def get_task_status_repo(self) -> Optional[TableStorageRepository]:
        """
        Get task status repository for local development

        Returns:
            TableStorageRepository: Task status repository or None if not available
        """
        try:
            if is_local_dev():
                return TableStorageRepository(table_name=self.task_status_table_name)
            else:
                logger.warning("Task status repository not available in production")
                return None
        except Exception as e:
            logger.error(f"Error getting task status repository: {str(e)}")
            return None

    def get_queue_client(self, queue_name: str) -> QueueClient:
        """
        Get a queue client for the specified queue name

        Args:
            queue_name: Name of the queue

        Returns:
            QueueClient: Queue client
        """
        if queue_name not in self._queue_clients:
            try:
                logger.info(f"Creating queue client for queue: {queue_name}")

                # Check if connection string is available
                if not self.connection_string:
                    logger.error("Storage connection string is not available")
                    raise ValueError("Storage connection string is not available")

                # Log connection string availability (without revealing the actual string)
                logger.info(f"Storage connection string is available with length: {len(self.connection_string)}")

                # Use default text encoding to match host.json messageEncoding: "none"
                queue_client = QueueClient.from_connection_string(
                    conn_str=self.connection_string,
                    queue_name=queue_name
                )
                logger.info(f"Queue client created for queue: {queue_name}")

                # Create the queue if it doesn't exist
                try:
                    logger.info(f"Attempting to create queue: {queue_name}")
                    queue_client.create_queue()
                    logger.info(f"Queue {queue_name} created successfully")
                except ResourceExistsError:
                    logger.info(f"Queue {queue_name} already exists")
                except Exception as create_error:
                    logger.error(f"Error creating queue {queue_name}: {str(create_error)}")
                    import traceback
                    logger.error(f"Queue creation error traceback: {traceback.format_exc()}")
                    # Continue anyway, as the queue might still be accessible

                # Verify the queue exists by getting properties
                try:
                    properties = queue_client.get_queue_properties()
                    logger.info(f"Queue {queue_name} properties retrieved: {properties.approximate_message_count} messages")
                except Exception as prop_error:
                    logger.error(f"Error getting queue properties for {queue_name}: {str(prop_error)}")
                    import traceback
                    logger.error(f"Queue properties error traceback: {traceback.format_exc()}")
                    # Continue anyway, as we'll catch any issues when sending messages

                # Store the queue client in the cache
                self._queue_clients[queue_name] = queue_client
                logger.info(f"Queue client for {queue_name} stored in cache")

                # Test the queue client with a simple operation
                try:
                    # List the messages without removing them (peek)
                    messages = list(queue_client.peek_messages(max_messages=1))
                    logger.info(f"Queue {queue_name} peek test successful. Found {len(messages)} messages.")
                except Exception as peek_error:
                    logger.warning(f"Queue {queue_name} peek test failed: {str(peek_error)}")
                    # Continue anyway, as we'll catch any issues when sending messages

            except Exception as e:
                logger.error(f"Error initializing queue client for {queue_name}: {str(e)}")
                import traceback
                logger.error(f"Queue client initialization error traceback: {traceback.format_exc()}")

                # Return a dummy client that logs errors instead of failing
                from unittest.mock import MagicMock
                mock_client = MagicMock()
                # Create a function that logs the error
                def mock_send_message(*args, **kwargs):
                    logger.error(f"Mock queue client for {queue_name} cannot send messages")
                mock_client.send_message.side_effect = mock_send_message
                self._queue_clients[queue_name] = mock_client
                logger.info(f"Created mock queue client for {queue_name}")

        return self._queue_clients[queue_name]

    def _is_task_enabled_for_user_policy(self, user_id: str, integration_id: str, task_type: str) -> bool:
        """
        Check if a task is enabled for a user based on their policy/rule configuration using DB service.

        Args:
            user_id: User ID
            integration_id: Integration ID
            task_type: Task type to check

        Returns:
            bool: True if task is enabled, False otherwise
        """
        try:
            # Always allow authentication and metadata extraction tasks
            if task_type in [TASK_TYPE_SFDC_AUTHENTICATE, TASK_TYPE_METADATA_EXTRACTION]:
                return True

            # Use DB service client to check enabled tasks
            db_client = get_db_client()

            # Get enabled tasks for this integration and user
            enabled_tasks = db_client.get_enabled_tasks_for_integration(integration_id, user_id)

            # Check if the task type is in the enabled tasks list
            if task_type in enabled_tasks:
                logger.info(f"Task {task_type} is enabled for user {user_id} and integration {integration_id}")
                return True
            else:
                logger.info(f"Task {task_type} is disabled for user {user_id} and integration {integration_id}")
                return False

        except Exception as e:
            logger.error(f"Error checking task policy via DB service: {str(e)}")
            # Fall back to local policy checking if DB service fails
            try:
                # Get policy and rule repositories as fallback
                policy_repo = get_policy_table_repo()
                rule_repo = get_rule_table_repo()

                if not policy_repo or not rule_repo:
                    logger.warning("Policy/Rule repositories not available, allowing task by default")
                    return True

                # Find policies for this user and integration
                policies = policy_repo.query_entities(f"IntegrationId eq '{integration_id}' and UserId eq '{user_id}'")
                if not policies:
                    logger.info(f"No policies found for user {user_id} and integration {integration_id}, allowing task by default")
                    return True

                # Check rules for each policy
                for policy in policies:
                    policy_id = policy.get('PolicyId')
                    if not policy_id:
                        continue

                    # Query rules for this policy and task type
                    rules = rule_repo.query_entities(f"PolicyId eq '{policy_id}' and TaskType eq '{task_type}'")
                    for rule in rules:
                        enabled = rule.get('Enabled', 1)  # Default to enabled
                        if enabled:
                            logger.info(f"Task {task_type} is enabled for user {user_id} via policy {policy_id} (fallback)")
                            return True
                        else:
                            logger.info(f"Task {task_type} is disabled for user {user_id} via policy {policy_id} (fallback)")
                            return False

                # If no specific rule found, default to enabled
                logger.info(f"No specific rule found for task {task_type}, allowing by default (fallback)")
                return True

            except Exception as fallback_error:
                logger.error(f"Error in fallback policy checking: {str(fallback_error)}")
                return True  # Default to enabled on error

    def enqueue_task(self, task_type: str, org_id: str, user_id: str,
                     params: Optional[Dict[str, Any]] = None,
                     priority: Optional[str] = None,
                     scheduled_time: Optional[datetime] = None,
                     force: bool = False,
                     execution_log_id: Optional[str] = None) -> Optional[str]:
        """
        Enqueue a task for background processing.

        Args:
            task_type: Type of task (overview, health_check, profiles, etc.)
            org_id: Organization ID
            user_id: User ID
            params: Additional parameters for the task
            priority: Task priority (high, medium, low). If None, priority will be determined based on task type.
            scheduled_time: Time to schedule the task for execution
            force: If True, always enqueue
            execution_log_id: Explicit execution log ID to use

        Returns:
            str: Task ID if successful, None otherwise
        """

        try:
            logger.info(f"Starting to enqueue task of type {task_type} for org {org_id}")

            # Validate required parameters
            if not task_type:
                logger.error("Task type is required")
                return None

            if not org_id:
                logger.error("Organization ID is required")
                return None

            if not user_id:
                logger.error("User ID is required")
                return None

            # Check if task is enabled for user based on policy/rule configuration
            # Extract integration_id from params if available
            integration_id = None
            if params and isinstance(params, dict):
                integration_id = params.get('integration_id') or params.get('org_id')
            if not integration_id:
                integration_id = org_id  # Fallback to org_id as integration_id

            if not self._is_task_enabled_for_user_policy(user_id, integration_id, task_type):
                logger.info(f"Task {task_type} is disabled for user {user_id} and integration {integration_id} by policy")
                return None

            # CRITICAL FIX: Add idempotency check for same execution_log_id and task_type
            if execution_log_id:
                try:
                    db_client = get_db_client()
                    if db_client:
                        existing_task = db_client.get_task_by_execution_log_and_type(execution_log_id, task_type)
                        if existing_task and existing_task.get('status') in ['pending', 'running']:
                            logger.info(f"[Idempotency] Skipping enqueue for {task_type} with execution_log_id {execution_log_id}: existing task {existing_task.get('task_id')} is {existing_task.get('status')}")
                            return existing_task.get('task_id')
                except Exception as idempotency_error:
                    logger.warning(f"Error checking idempotency for {task_type}: {str(idempotency_error)}")
                    # Continue with task creation if idempotency check fails

            # Determine priority based on task type if not explicitly provided
            if priority is None:
                # Use the task type to priority mapping, defaulting to medium if not found
                priority = TASK_TYPE_PRIORITY_MAP.get(task_type, TASK_PRIORITY_MEDIUM)
                logger.info(f"Auto-assigned priority '{priority}' to task type '{task_type}'")

            # Generate a task ID using UUID for better uniqueness
            task_id = str(uuid.uuid4())
            logger.info(f"Generated task ID: {task_id}")

            # Use provided execution_log_id or error
            if not execution_log_id:
                logger.error(f"No execution_log_id provided for task {task_type} ({org_id}), refusing to enqueue task.")
                return None
            else:
                logger.info(f"Using provided ExecutionLogId: {execution_log_id}")

            # Get current time
            current_time = datetime.now()

            # Create task data
            task_data = {
                "task_id": task_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "params": params or {},
                "created_at": current_time.isoformat(),
                "status": TASK_STATUS_PENDING,
                "priority": priority,
                "retry_count": 0,
                "scheduled_time": scheduled_time.isoformat() if scheduled_time else None,
                "execution_log_id": execution_log_id  # Always present
            }
            logger.info(f"Created task data for task {task_id}")

            # Create task using database service
            try:
                db_client = get_db_client()

                # Prepare task data for database service
                task_creation_data = {
                    "task_id": task_id,
                    "task_type": task_type,
                    "org_id": org_id,
                    "user_id": user_id,
                    "params": params or {},
                    "status": TASK_STATUS_PENDING,
                    "priority": priority,
                    "scheduled_time": scheduled_time.isoformat() if scheduled_time else None,
                    "message": "Task queued",
                    "execution_log_id": execution_log_id  # Add the execution_log_id to pass to DB service
                }

                # Create task via database service
                created_task_id = db_client.create_task(task_creation_data)

                if created_task_id:
                    logger.info(f"Successfully created task via database service: {created_task_id}")
                    # Use the returned task ID (should be the same as what we generated)
                    task_id = created_task_id

                    # NEW: Send task to Azure Storage Queue instead of Service Bus
                    try:
                        from shared.queue_manager import get_queue_manager
                        queue_manager = get_queue_manager()
                        
                        if queue_manager:
                            # Determine queue name based on priority
                            queue_name_map = {
                                "high": "task-queue-high",
                                "medium": "task-queue-medium", 
                                "low": "task-queue-low"
                            }
                            queue_name = queue_name_map.get(priority, "task-queue-medium")
                            
                            # Calculate visibility timeout for scheduled tasks
                            visibility_timeout = None
                            if scheduled_time and scheduled_time > datetime.now():
                                delay_seconds = int((scheduled_time - datetime.now()).total_seconds())
                                visibility_timeout = delay_seconds
                                logger.info(f"Task {task_id} scheduled for {scheduled_time} (visibility timeout: {visibility_timeout}s)")
                            
                            # Send message to queue
                            success = queue_manager.send_message(
                                queue_name=queue_name,
                                message_data=task_data,
                                visibility_timeout=visibility_timeout
                            )
                            
                            if success:
                                logger.info(f"Successfully sent task {task_id} to {queue_name} queue for processing")
                            else:
                                logger.warning(f"Failed to send task {task_id} to {queue_name} queue, but task is created in database")
                        else:
                            logger.warning(f"Queue manager not available, task {task_id} created in database but not sent to queue")
                            
                    except Exception as e:
                        logger.error(f"Error sending task {task_id} to queue: {str(e)}")
                        # Don't fail the task creation if sending fails
                else:
                    logger.error(f"Failed to create task via database service")
                    return None

            except Exception as task_creation_error:
                logger.error(f"Error creating task via database service: {str(task_creation_error)}")
                return None

            # Execution log is now created upfront in DB service - no need to create here
            # This prevents race conditions and ensures unique execution log IDs
            logger.info(f"Using execution log ID provided by DB service: {execution_log_id}")

            # Task creation completed successfully
            logger.info(f"Task {task_id} created and sent to SFDC service successfully")
            return task_id

        except Exception as e:
            logger.error(f"Error enqueuing task: {str(e)}")
            import traceback
            logger.error(f"Enqueue task error traceback: {traceback.format_exc()}")
            return None

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task

        Args:
            task_id: Task ID

        Returns:
            Dict[str, Any]: Task status if found, None otherwise
        """
        try:
            # Try to get task status from DB service first
            db_client = get_db_client()

            task_data = db_client.get_task_by_id(task_id)
            if task_data:
                logger.info(f"Found task status via DB service for task {task_id}")
                return task_data
            else:
                logger.warning(f"No task status found for task {task_id}")
                return None
        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return None

    def update_task_status(self, task_id: str, status: str, progress: int = None,
                         message: str = None, result: str = None, error: str = None) -> bool:
        """
        Update the status of a task with atomic operation to prevent race conditions

        Args:
            task_id: Task ID
            status: New status
            progress: Progress percentage (0-100)
            message: Status message
            result: Task result
            error: Error message if task failed

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"[TASK-STATUS] Updating task {task_id} to status '{status}' (progress={progress}, message={message}, error={error})")
        try:
            # Try to update task status via DB service first
            db_client = get_db_client()

            # Update via DB service with all parameters
            success = db_client.update_task_status(task_id, status, progress, message, result, error)
            if success:
                logger.info(f"Updated task status via DB service for task {task_id} to {status}")
                return True
            else:
                logger.error(f"Failed to update task status via DB service for task {task_id}")
                # Fall back to local storage
                return self._update_task_status_local(task_id, status, progress, message, result, error)
        except Exception as e:
            logger.error(f"Error updating task status via DB service: {str(e)}")
            # Fall back to local storage
            return self._update_task_status_local(task_id, status, progress, message, result, error)

    def _update_task_status_local(self, task_id: str, status: str, progress: int = None,
                                message: str = None, result: str = None, error: str = None) -> bool:
        """
        Update task status using local storage as fallback - ONLY for existing records
        
        Args:
            task_id: Task ID
            status: New status
            progress: Progress percentage (0-100)
            message: Status message
            result: Task result
            error: Error message if task failed
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"[TASK-STATUS] Using local storage fallback for task {task_id}")
            
            # Get task status repository
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error(f"Task status repository not available for task {task_id}")
                return False
            
            # Try to get the task from the DB service first to get proper org_id
            org_id = None
            try:
                from shared.db_service_client import get_db_client
                db_service = get_db_client()
                if db_service:
                    task_info = db_service.get_task_by_id(task_id)
                    if task_info:
                        org_id = task_info.get('OrgId') or task_info.get('org_id')
            except Exception as e:
                logger.warning(f"Could not get task info from DB service for {task_id}: {str(e)}")
            
            # CRITICAL FIX: Only look for existing records, never create new ones
            # This prevents duplicate record creation
            
            # Try common partition key patterns to find existing task
            possible_partition_keys = []
            if org_id:
                possible_partition_keys.append(f"task_{org_id}")
            
            # Add timestamp-based patterns for recent dates
            for days_back in range(7):  # Check last 7 days
                date_str = (datetime.now() - timedelta(days=days_back)).strftime("%Y%m%d")
                possible_partition_keys.append(f"task_unknown_{date_str}")
            
            # Search for existing task
            existing_task = None
            existing_partition_key = None
            
            for partition_key in possible_partition_keys:
                try:
                    task_entity = task_status_repo.get_entity(partition_key, task_id)
                    if task_entity:
                        existing_task = task_entity
                        existing_partition_key = partition_key
                        logger.info(f"Found existing task {task_id} in partition {partition_key}")
                        break
                except Exception:
                    continue
            
            # CRITICAL: Only update if task exists in local storage
            if existing_task:
                logger.info(f"Updating existing task {task_id} in partition {existing_partition_key}")
                
                # Update the existing task status
                existing_task["Status"] = status
                if progress is not None:
                    existing_task["Progress"] = progress
                if message is not None:
                    existing_task["Message"] = message
                if result is not None:
                    existing_task["Result"] = result
                if error is not None:
                    existing_task["Error"] = error
                
                # Update timestamp
                existing_task["LastUpdated"] = datetime.now().isoformat()
                
                # Save the updated entity
                task_status_repo.update_entity(existing_task)
                
                logger.info(f"Updated existing task {task_id} status to {status} via local storage")
                return True
            else:
                # CRITICAL: Don't create new records in local storage
                # This prevents duplicate record creation
                logger.warning(f"Task {task_id} not found in local storage. Not creating new record to prevent duplicates.")
                logger.warning(f"Task status update failed for {task_id} - DB service unavailable and no local record exists.")
                return False
            
        except Exception as e:
            logger.error(f"Error updating task status via local storage for task {task_id}: {str(e)}")
            return False

    def update_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """
        Update the result of a task

        Args:
            task_id: Task ID
            result: Task result data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert result to JSON string for storage
            import json
            result_json = json.dumps(result) if result else None

            # Update task status with result
            return self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                result=result_json
            )
        except Exception as e:
            logger.error(f"Error updating task result for {task_id}: {str(e)}")
            return False

    def _update_execution_log(self, task_status_entity: Dict[str, Any], status: str) -> None:
        """
        Update the execution log for a task

        Args:
            task_status_entity: Task status entity
            status: New status
        """
        execution_log_id = task_status_entity.get("ExecutionLogId")
        if execution_log_id and not is_local_dev():
            execution_log_repo = self.get_execution_log_repo()
            if execution_log_repo:
                # Update execution log
                query = """
                UPDATE App_ExecutionLog
                SET Status = ?, EndTime = ?
                WHERE Id = ?
                """
                params = (
                    status,
                    datetime.now().isoformat(),
                    execution_log_id
                )

                execution_log_repo.execute_non_query(query, params)

    def _retry_task(self, task_status_entity: Dict[str, Any], retry_count: int) -> None:
        """
        Retry a failed task

        Args:
            task_status_entity: Task status entity
            retry_count: Current retry count
        """
        try:
            # Extract task data
            task_id = task_status_entity.get("RowKey")
            task_type = task_status_entity.get("TaskType")
            org_id = task_status_entity.get("OrgId")
            user_id = task_status_entity.get("UserId")
            params = json.loads(task_status_entity.get("Params", "{}"))
            priority = task_status_entity.get("Priority", TASK_PRIORITY_MEDIUM)
            execution_log_id = task_status_entity.get("ExecutionLogId")

            # Create task data for re-enqueueing
            task_data = {
                "task_id": task_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "params": params,
                "created_at": datetime.now().isoformat(),
                "status": TASK_STATUS_PENDING,
                "priority": priority,
                "retry_count": retry_count,
                "execution_log_id": execution_log_id
            }

            # Calculate delay based on retry count (exponential backoff)
            delay_seconds = min(30, 2 ** retry_count)  # Max delay of 30 seconds
            scheduled_time = datetime.now() + timedelta(seconds=delay_seconds)

            # Send retry task to Service Bus with scheduled delivery
            try:
                service_bus_client = get_service_bus_task_client()

                success = service_bus_client.send_task_message(
                    task_data=task_data,
                    priority=priority,
                    scheduled_time=scheduled_time
                )

                if success:
                    logger.info(f"Re-enqueued task {task_id} for retry (attempt {retry_count} of {self.max_retries}) with delay of {delay_seconds} seconds")
                else:
                    logger.error(f"Failed to re-enqueue task {task_id} for retry")
                    raise Exception("Failed to send retry message to Service Bus")

            except Exception as service_bus_error:
                logger.error(f"Error sending retry message to Service Bus: {str(service_bus_error)}")
                raise  # Re-raise the exception to be caught by the outer try-except
        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")

    def get_latest_task_for_org(self, org_id: str, task_type: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest task for an organization and task type

        Args:
            org_id: Organization ID
            task_type: Task type

        Returns:
            Dict[str, Any]: Task status if found, None otherwise
        """
        try:
            # Try to get latest task from DB service first
            from shared.db_service_client import get_db_client
            db_client = get_db_client()

            try:
                # Get tasks from DB service
                tasks = db_client.get_tasks_by_org(org_id)
                if tasks:
                    # Filter by task type and get the latest one
                    filtered_tasks = [t for t in tasks if t.get('task_type') == task_type]
                    if filtered_tasks:
                        # Sort by created_at in descending order
                        filtered_tasks.sort(key=lambda t: t.get("created_at", ""), reverse=True)
                        latest_task = filtered_tasks[0]
                        logger.info(f"Found latest task via DB service for {org_id}/{task_type}: {latest_task.get('task_id')}")
                        return latest_task
                    else:
                        logger.info(f"No tasks of type {task_type} found via DB service for org {org_id}")
                        return None
                else:
                    logger.info(f"No tasks found via DB service for org {org_id}")
                    return None
            except Exception as db_error:
                logger.error(f"Failed to get tasks from DB service: {str(db_error)}")
                return None
        except Exception as e:
            logger.error(f"Error getting latest task: {str(e)}")
            return None

    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all pending tasks

        Returns:
            List[Dict[str, Any]]: List of pending tasks
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query all entities and filter by PartitionKey pattern and status in memory
            # Azure Table Storage doesn't support startswith in OData queries
            all_entities = list(task_status_repo.query_entities())
            
            # Filter entities that start with 'task_' and have pending status
            pending_entities = []
            for entity in all_entities:
                partition_key = entity.get('PartitionKey', '')
                status = entity.get('Status', '')
                if partition_key.startswith('task_') and status == TASK_STATUS_PENDING:
                    pending_entities.append(entity)
            
            logger.info(f"[POLL-DEBUG] Found {len(pending_entities)} pending tasks out of {len(all_entities)} total entities")

            if not pending_entities:
                logger.info("No pending tasks found")
                return []

            # Convert entities to dictionaries with normalized field names
            pending_tasks = []
            for entity in pending_entities:
                # Parse params from JSON string if it's a string
                params_raw = entity.get('Params', {})
                if isinstance(params_raw, str):
                    try:
                        # First try to parse as JSON
                        params = json.loads(params_raw)
                    except json.JSONDecodeError:
                        try:
                            # If JSON fails, try to parse as Python literal (handles single quotes)
                            params = ast.literal_eval(params_raw)
                        except (ValueError, SyntaxError):
                            logger.warning(f"Failed to parse params for task {entity.get('RowKey')}: {params_raw}")
                            params = {}
                else:
                    params = params_raw

                # Convert entity field names to expected format
                task = {
                    'task_id': entity.get('RowKey'),  # TaskId is stored as RowKey
                    'org_id': entity.get('OrgId'),
                    'task_type': entity.get('TaskType'),
                    'user_id': entity.get('UserId'),
                    'status': entity.get('Status'),
                    'priority': entity.get('Priority'),
                    'progress': entity.get('Progress'),
                    'message': entity.get('Message'),
                    'created_at': entity.get('CreatedAt'),
                    'updated_at': entity.get('UpdatedAt'),
                    'completed_at': entity.get('CompletedAt'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'params': params
                }
                pending_tasks.append(task)
            return pending_tasks
        except Exception as e:
            logger.error(f"Error getting pending tasks: {str(e)}")
            return []

    def get_tasks_by_org(self, org_id: str, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all tasks for an org, optionally including completed/failed/cancelled
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query for tasks
            filter_query = f"PartitionKey eq 'task_{org_id}'"
            if not include_completed:
                filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"

            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No tasks found for organization {org_id}")
                return []

            # Convert entities to dictionaries
            tasks = []
            for entity in entities:
                tasks.append(dict(entity))
            return tasks
        except Exception as e:
            logger.error(f"Error getting tasks by org: {str(e)}")
            return []

    def schedule_task(self, task_type: str, org_id: str, user_id: str,
                      scheduled_time: datetime,
                      params: Optional[Dict[str, Any]] = None,
                      priority: Optional[str] = None) -> Optional[str]:
        """
        Schedule a task for future execution

        Args:
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            scheduled_time: Time to schedule the task for execution
            params: Additional parameters for the task
            priority: Task priority. If None, priority will be determined based on task type.

        Returns:
            str: Task ID if successful, None otherwise
        """
        # Validate scheduled time
        if scheduled_time <= datetime.now():
            logger.warning("Scheduled time must be in the future")
            return None

        # Enqueue the task with a scheduled time
        return self.enqueue_task(
            task_type=task_type,
            org_id=org_id,
            user_id=user_id,
            params=params,
            priority=priority,
            scheduled_time=scheduled_time
        )

    def schedule_recurring_task(self, task_type: str, org_id: str, user_id: str,
                               schedule_type: str,
                               params: Optional[Dict[str, Any]] = None,
                               priority: Optional[str] = None) -> Optional[str]:
        """
        Schedule a recurring task

        Args:
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            schedule_type: Type of schedule (daily, weekly, monthly)
            params: Additional parameters for the task
            priority: Task priority. If None, priority will be determined based on task type.

        Returns:
            str: Task ID if successful, None otherwise
        """
        # Calculate the next scheduled time based on schedule type
        now = datetime.now()

        if schedule_type == "daily":
            # Schedule for tomorrow at the same time
            scheduled_time = now.replace(day=now.day+1, hour=3, minute=0, second=0, microsecond=0)
        elif schedule_type == "weekly":
            # Schedule for next week on the same day
            scheduled_time = now + timedelta(days=7)
            scheduled_time = scheduled_time.replace(hour=3, minute=0, second=0, microsecond=0)
        elif schedule_type == "monthly":
            # Schedule for next month on the same day
            if now.month == 12:
                scheduled_time = now.replace(year=now.year+1, month=1, day=1, hour=3, minute=0, second=0, microsecond=0)
            else:
                scheduled_time = now.replace(month=now.month+1, day=1, hour=3, minute=0, second=0, microsecond=0)
        else:
            logger.warning(f"Unknown schedule type: {schedule_type}")
            return None

        # Add schedule information to params
        task_params = params or {}
        task_params["schedule_type"] = schedule_type
        task_params["is_recurring"] = True

        # Enqueue the task with a scheduled time
        return self.enqueue_task(
            task_type=task_type,
            org_id=org_id,
            user_id=user_id,
            params=task_params,
            priority=priority,
            scheduled_time=scheduled_time
        )

    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending or running task

        Args:
            task_id: Task ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return False

            # Get task status entity
            task_status_entity = task_status_repo.get_entity("task", task_id)

            if not task_status_entity:
                logger.warning(f"No task status found for task {task_id}")
                return False

            # Check if the task can be cancelled
            status = task_status_entity.get("Status")
            if status not in [TASK_STATUS_PENDING, TASK_STATUS_RUNNING, TASK_STATUS_RETRY]:
                logger.warning(f"Cannot cancel task {task_id} with status {status}")
                return False

            # Update task status to cancelled
            return self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_CANCELLED,
                message="Task cancelled by user"
            )
        except Exception as e:
            logger.error(f"Error cancelling task: {str(e)}")
            return False

    def get_tasks_by_org(self, org_id: str, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all tasks for an organization

        Args:
            org_id: Organization ID
            include_completed: Whether to include completed tasks

        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            # Try to use database service first
            try:
                db_client = get_db_client()

                # Get tasks from database service
                tasks = db_client.get_tasks_by_org(org_id)

                # Filter out completed tasks if not requested
                if not include_completed:
                    tasks = [task for task in tasks if task.get("status") not in [TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED]
                            and task.get("Status") not in [TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED]]

                logger.info(f"Retrieved {len(tasks)} tasks for organization {org_id} via database service")
                return tasks

            except Exception as db_service_error:
                logger.warning(f"Database service not available, falling back to direct access: {str(db_service_error)}")

                # Fallback to direct database access
                task_status_repo = self.get_task_status_repo()
                if not task_status_repo:
                    logger.error("Task status repository not available")
                    return []

                # Query for tasks
                filter_query = f"PartitionKey eq 'task_{org_id}'"
                if not include_completed:
                    filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"

                entities = task_status_repo.query_entities(filter_query)

                if not entities:
                    logger.info(f"No tasks found for organization {org_id}")
                    return []

                # Convert entities to dictionaries
                tasks = []
                for entity in entities:
                    task = {
                        "task_id": entity.get("RowKey"),
                        "task_type": entity.get("TaskType"),
                        "org_id": entity.get("OrgId"),
                        "user_id": entity.get("UserId"),
                        "params": json.loads(entity.get("Params", "{}")),
                        "created_at": entity.get("CreatedAt"),
                        "status": entity.get("Status"),
                        "progress": entity.get("Progress"),
                        "message": entity.get("Message"),
                        "result": entity.get("Result"),
                        "completed_at": entity.get("CompletedAt"),
                        "execution_log_id": entity.get("ExecutionLogId"),
                        "priority": entity.get("Priority", TASK_PRIORITY_MEDIUM),
                        "retry_count": entity.get("RetryCount", 0),
                        "scheduled_time": entity.get("ScheduledTime", "")
                    }

                    tasks.append(task)

                # Sort by created_at in descending order
                tasks.sort(key=lambda t: t.get("created_at", ""), reverse=True)

                logger.info(f"Retrieved {len(tasks)} tasks for organization {org_id} via fallback")
                return tasks

        except Exception as e:
            logger.error(f"Error getting tasks by organization: {str(e)}")
            return []

    def clean_old_tasks(self, days: int = 7) -> bool:
        """
        Clean up old completed or failed tasks

        Args:
            days: Number of days to keep tasks

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return False

            # Calculate cutoff date
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

            # Query for old completed or failed tasks
            filter_query = f"PartitionKey eq 'task' and (Status eq '{TASK_STATUS_COMPLETED}' or Status eq '{TASK_STATUS_FAILED}' or Status eq '{TASK_STATUS_CANCELLED}') and CompletedAt lt '{cutoff_date}'"
            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No old tasks found to clean up")
                return True

            # Delete old tasks
            for entity in entities:
                task_status_repo.delete_entity("task", entity.get("RowKey"))

            logger.info(f"Cleaned up {len(entities)} old tasks")

            return True
        except Exception as e:
            logger.error(f"Error cleaning old tasks: {str(e)}")
            return False