# SFDC Proxy Architecture Documentation

## Overview

The AtomSec SFDC service follows a microservices proxy architecture where the DB service acts as the single entry point for all frontend requests, and the SFDC service handles Salesforce-specific business logic. This document outlines the architectural patterns, communication flows, and best practices.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer"
        FE[React Frontend]
    end
    
    subgraph "API Gateway Layer"
        APIM[Azure API Management]
    end
    
    subgraph "Microservices Layer"
        subgraph "DB Service (Entry Point)"
            DB[DB Service<br/>Port: 7072]
            DB_API["/api/db/*"]
            DB_PROXY[SFDC Proxy Endpoints]
        end
        
        subgraph "SFDC Service (Business Logic)"
            SFDC[SFDC Service<br/>Port: 7071]
            SFDC_API["/api/*"]
            SFDC_INTERNAL[Internal Endpoints]
        end
        
        subgraph "Auth Service (Optional)"
            AUTH[Auth Service<br/>Port: 7073]
            AUTH_API["/api/auth/*"]
        end
    end
    
    subgraph "External Services"
        SF[Salesforce API]
        KV[Azure Key Vault]
        SB[Service Bus]
        AI[Application Insights]
    end
    
    subgraph "Data Layer"
        SQL[SQL Database]
        BLOB[Blob Storage]
    end
    
    %% Request Flow
    FE --> APIM
    APIM --> DB_API
    DB_API --> DB_PROXY
    DB_PROXY --> SFDC_API
    SFDC_API --> SFDC_INTERNAL
    
    %% Service Dependencies
    DB --> SQL
    SFDC --> SF
    SFDC --> KV
    SFDC --> SB
    SFDC --> AI
    SFDC --> BLOB
    
    %% Authentication Flow
    FE -.-> AUTH_API
    AUTH_API -.-> KV
```

## Service Responsibilities

### DB Service (Entry Point)
- **Primary Role**: Single entry point for all frontend requests
- **Responsibilities**:
  - User authentication and authorization
  - CORS handling for all requests
  - Database operations (CRUD for users, accounts, organizations)
  - Request validation and sanitization
  - Proxying SFDC-specific requests to SFDC service
  - Response aggregation and formatting

### SFDC Service (Business Logic)
- **Primary Role**: Salesforce-specific business logic and integrations
- **Responsibilities**:
  - Salesforce API integration and authentication
  - Security analysis and health checks
  - Metadata extraction and processing
  - PMD (Programming Mistake Detector) scanning
  - Task processing and coordination
  - Event publishing to Service Bus

### Communication Patterns

## Request Flow Patterns

### 1. Direct DB Operations
```
Frontend → DB Service → Database → Response
```
**Use Cases**: User management, account operations, basic CRUD operations

### 2. Proxied SFDC Operations
```
Frontend → DB Service → SFDC Service → Salesforce API → Response Chain
```
**Use Cases**: Integration scans, security analysis, metadata extraction

### 3. Async Task Processing
```
Frontend → DB Service → SFDC Service → Service Bus → Background Processing
```
**Use Cases**: Long-running scans, bulk operations, scheduled tasks

## Endpoint Mapping

### Frontend to DB Service Mapping
| Frontend Request | DB Service Endpoint | Description |
|------------------|-------------------|-------------|
| `GET /integrations` | `GET /api/db/integrations` | List integrations |
| `POST /integration/scan/{id}` | `POST /api/db/integration/scan/{id}` | Trigger scan (proxied) |
| `GET /security/health-score` | `GET /api/db/security/health-score` | Get health score (proxied) |
| `GET /tasks` | `GET /api/db/tasks` | List tasks |
| `POST /auth/login` | `POST /api/db/auth/login` | User authentication |

### DB Service to SFDC Service Mapping
| DB Service Request | SFDC Service Endpoint | Description |
|-------------------|---------------------|-------------|
| `POST /integration/scan/{id}` | `POST /api/integration/scan/{id}` | Trigger integration scan |
| `GET /security/health-score` | `GET /api/security/health-score` | Get security health score |
| `GET /tasks/{id}/status` | `GET /api/tasks/{id}/status` | Get task status |
| `POST /tasks` | `POST /api/tasks` | Create new task |

## Security Architecture

### Authentication Flow
1. **Frontend Authentication**: User authenticates with DB service
2. **Service-to-Service**: DB service uses internal headers for SFDC service calls
3. **Salesforce Authentication**: SFDC service handles SF API authentication independently

### Security Headers
```http
# Internal Service Communication
X-Internal-Service: db-service
X-Request-ID: unique-request-id
Authorization: Bearer internal-service-token

# CORS Headers (DB Service)
Access-Control-Allow-Origin: https://frontend-domain.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### Trust Boundaries
- **External → DB Service**: Full authentication and authorization required
- **DB Service → SFDC Service**: Trusted internal communication
- **SFDC Service → External APIs**: Service-specific authentication (SF JWT, Key Vault MSI)

## Data Flow Patterns

### 1. Synchronous Request-Response
```mermaid
sequenceDiagram
    participant FE as Frontend
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant SF as Salesforce
    
    FE->>DB: GET /api/db/integrations/{id}/overview
    DB->>SFDC: GET /api/integrations/{id}/overview
    SFDC->>SF: Salesforce API calls
    SF-->>SFDC: Salesforce data
    SFDC-->>DB: Processed overview data
    DB-->>FE: Final response
```

### 2. Asynchronous Task Processing
```mermaid
sequenceDiagram
    participant FE as Frontend
    participant DB as DB Service
    participant SFDC as SFDC Service
    participant SB as Service Bus
    participant BG as Background Processor
    
    FE->>DB: POST /api/db/integration/scan/{id}
    DB->>SFDC: POST /api/integration/scan/{id}
    SFDC->>SB: Enqueue scan task
    SFDC-->>DB: Task created response
    DB-->>FE: Scan initiated response
    
    SB->>BG: Process scan task
    BG->>SFDC: Task processing
    SFDC->>DB: Update task status
```

## Error Handling Patterns

### Error Propagation
1. **SFDC Service Error** → Structured error response
2. **DB Service** → Logs error, returns user-friendly message
3. **Frontend** → Displays appropriate error message

### Error Response Format
```json
{
  "success": false,
  "error": "User-friendly error message",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2025-01-24T10:00:00Z",
  "request_id": "unique-request-id",
  "details": {
    "field": "Additional error context"
  }
}
```

## Configuration Management

### Environment-Specific Configuration
```yaml
# Local Development
db_service_url: "http://localhost:7072"
sfdc_service_url: "http://localhost:7071"
auth_service_url: "http://localhost:7073"

# Production
db_service_url: "https://func-atomsec-db-prod.azurewebsites.net"
sfdc_service_url: "https://func-atomsec-sfdc-prod.azurewebsites.net"
auth_service_url: "https://func-atomsec-auth-prod.azurewebsites.net"
```

### Service Discovery
- **Local**: Hardcoded URLs for development
- **Production**: Azure Key Vault for service URLs
- **Health Checks**: Regular health check endpoints for service availability

## Monitoring and Observability

### Distributed Tracing
- **Correlation IDs**: Passed through all service calls
- **Request Tracking**: End-to-end request tracing
- **Performance Monitoring**: Response time tracking across services

### Metrics Collection
```json
{
  "service_metrics": {
    "db_service": {
      "requests_per_minute": 150,
      "average_response_time": "45ms",
      "error_rate": "0.5%"
    },
    "sfdc_service": {
      "requests_per_minute": 75,
      "average_response_time": "1.2s",
      "error_rate": "1.2%"
    }
  },
  "proxy_metrics": {
    "proxy_success_rate": "98.5%",
    "proxy_latency": "15ms",
    "proxy_errors": 3
  }
}
```

## Deployment Architecture

### Service Deployment
- **Independent Deployment**: Each service can be deployed independently
- **Blue-Green Deployment**: Zero-downtime deployments
- **Health Check Gates**: Deployment validation through health checks

### Infrastructure as Code
```bicep
// DB Service Function App
resource dbFunctionApp 'Microsoft.Web/sites@2021-02-01' = {
  name: 'func-atomsec-db-${environment}'
  location: location
  kind: 'functionapp'
  properties: {
    serverFarmId: appServicePlan.id
    siteConfig: {
      appSettings: [
        {
          name: 'SFDC_SERVICE_URL'
          value: sfdcFunctionApp.properties.defaultHostName
        }
      ]
    }
  }
}

// SFDC Service Function App
resource sfdcFunctionApp 'Microsoft.Web/sites@2021-02-01' = {
  name: 'func-atomsec-sfdc-${environment}'
  location: location
  kind: 'functionapp'
  properties: {
    serverFarmId: appServicePlan.id
    siteConfig: {
      appSettings: [
        {
          name: 'DB_SERVICE_URL'
          value: dbFunctionApp.properties.defaultHostName
        }
      ]
    }
  }
}
```

## Best Practices

### Service Communication
1. **Use Internal Headers**: Always include `X-Internal-Service` header
2. **Implement Timeouts**: Set appropriate timeouts for service calls
3. **Handle Failures Gracefully**: Implement circuit breaker patterns
4. **Log Correlation IDs**: Track requests across service boundaries

### Security Best Practices
1. **Validate All Inputs**: Both at DB service and SFDC service levels
2. **Use HTTPS**: All inter-service communication over HTTPS
3. **Implement Rate Limiting**: Protect against abuse
4. **Audit Logging**: Log all security-relevant events

### Performance Optimization
1. **Connection Pooling**: Reuse HTTP connections between services
2. **Caching**: Cache frequently accessed data
3. **Async Processing**: Use async patterns for long-running operations
4. **Resource Management**: Proper cleanup of resources

## Troubleshooting Guide

### Common Issues

#### 1. Service Communication Failures
**Symptoms**: 503 Service Unavailable errors
**Diagnosis**: Check service health endpoints
**Resolution**: Verify service URLs and network connectivity

#### 2. Authentication Failures
**Symptoms**: 401 Unauthorized errors
**Diagnosis**: Check internal service headers
**Resolution**: Verify `X-Internal-Service` header is present

#### 3. CORS Issues
**Symptoms**: Browser CORS errors
**Diagnosis**: Check CORS headers in DB service
**Resolution**: Verify allowed origins configuration

### Monitoring Commands
```bash
# Check service health
curl -s http://localhost:7072/api/db/health | jq .
curl -s http://localhost:7071/api/health | jq .

# Test service communication
curl -s -H "X-Internal-Service: test" http://localhost:7071/api/integrations | jq .

# Check CORS headers
curl -s -H "Origin: http://localhost:3000" -X OPTIONS http://localhost:7072/api/db/health -I
```

## Migration Guide

### From Monolith to Microservices
1. **Identify Service Boundaries**: Separate DB and SFDC concerns
2. **Implement Proxy Layer**: Create proxy endpoints in DB service
3. **Update Frontend**: Change API calls to use DB service endpoints
4. **Test Integration**: Verify end-to-end functionality
5. **Deploy Incrementally**: Deploy services independently

### Service Versioning
- **API Versioning**: Use version headers or URL versioning
- **Backward Compatibility**: Maintain compatibility during transitions
- **Deprecation Strategy**: Gradual deprecation of old endpoints

## Future Enhancements

### Planned Improvements
1. **Service Mesh**: Implement Istio or similar for advanced traffic management
2. **API Gateway**: Enhanced API management with Azure API Management
3. **Event-Driven Architecture**: More extensive use of Service Bus for decoupling
4. **Caching Layer**: Distributed caching with Redis
5. **Auto-Scaling**: Advanced auto-scaling based on metrics

### Scalability Considerations
- **Horizontal Scaling**: Scale services independently based on load
- **Load Balancing**: Distribute traffic across service instances
- **Database Sharding**: Scale database layer as needed
- **CDN Integration**: Cache static content and API responses

---

This architecture documentation provides a comprehensive guide to the SFDC proxy architecture, ensuring maintainability, scalability, and security of the microservices system.