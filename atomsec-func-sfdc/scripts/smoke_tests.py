#!/usr/bin/env python3
"""
Comprehensive Smoke Tests for SFDC Function App

This script runs comprehensive smoke tests to validate basic functionality
after deployment. It's designed to catch critical issues quickly.
"""

import sys
import json
import time
import urllib.request
import urllib.error
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class SmokeTestResult:
    """Result of a smoke test"""
    name: str
    success: bool
    message: str
    duration: float
    details: Dict[str, Any]
    timestamp: datetime


class SmokeTestRunner:
    """Comprehensive smoke test runner"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.results: List[SmokeTestResult] = []
        self.timeout = 30
        
    def make_request(self, endpoint: str, method: str = 'GET', 
                    data: Dict = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        start_time = time.time()
        
        try:
            # Prepare request
            request_data = None
            if data and method in ['POST', 'PUT', 'PATCH']:
                request_data = json.dumps(data).encode('utf-8')
                headers = headers or {}
                headers['Content-Type'] = 'application/json'
            
            req = urllib.request.Request(url, data=request_data, method=method)
            
            # Add headers
            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)
            
            req.add_header('User-Agent', 'SmokeTest/1.0')
            
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                status_code = response.getcode()
                content_type = response.headers.get('Content-Type', '')
                response_time = (time.time() - start_time) * 1000
                
                # Read response
                body = response.read().decode('utf-8')
                if 'application/json' in content_type:
                    try:
                        body = json.loads(body)
                    except json.JSONDecodeError:
                        pass
                
                return {
                    'success': True,
                    'status_code': status_code,
                    'body': body,
                    'response_time': response_time,
                    'content_type': content_type,
                    'headers': dict(response.headers)
                }
                
        except urllib.error.HTTPError as e:
            response_time = (time.time() - start_time) * 1000
            return {
                'success': False,
                'status_code': e.code,
                'body': None,
                'response_time': response_time,
                'error': f"HTTP {e.code}: {e.reason}"
            }
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return {
                'success': False,
                'status_code': 0,
                'body': None,
                'response_time': response_time,
                'error': str(e)
            }
    
    def test_health_endpoint(self) -> SmokeTestResult:
        """Test the health endpoint"""
        print("🔍 Testing health endpoint...")
        
        start_time = time.time()
        result = self.make_request('api/health')
        duration = time.time() - start_time
        
        success = result['success'] and result['status_code'] == 200
        
        if success:
            # Validate health response structure
            body = result['body']
            if isinstance(body, dict):
                required_fields = ['status', 'timestamp']
                missing_fields = [f for f in required_fields if f not in body]
                if missing_fields:
                    success = False
                    message = f"Health response missing fields: {missing_fields}"
                else:
                    message = f"Health check passed - Status: {body.get('status', 'unknown')}"
            else:
                message = "Health check passed but response format unexpected"
        else:
            message = f"Health check failed: {result.get('error', 'Unknown error')}"
        
        print(f"{'✅' if success else '❌'} {message}")
        
        return SmokeTestResult(
            'health_endpoint',
            success,
            message,
            duration,
            {
                'status_code': result['status_code'],
                'response_time_ms': result['response_time'],
                'response_body': result['body']
            },
            datetime.now()
        )
    
    def test_cors_configuration(self) -> SmokeTestResult:
        """Test CORS configuration"""
        print("🔍 Testing CORS configuration...")
        
        start_time = time.time()
        
        # Test preflight request
        headers = {
            'Origin': 'https://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        result = self.make_request('api/health', 'OPTIONS', headers=headers)
        duration = time.time() - start_time
        
        success = result['status_code'] in [200, 204]
        
        if success:
            # Check CORS headers
            cors_headers = result.get('headers', {})
            required_cors_headers = [
                'Access-Control-Allow-Origin',
                'Access-Control-Allow-Methods'
            ]
            
            present_headers = [h for h in required_cors_headers 
                             if any(h.lower() == k.lower() for k in cors_headers.keys())]
            
            if len(present_headers) >= 1:
                message = f"CORS configured correctly ({len(present_headers)}/{len(required_cors_headers)} headers)"
            else:
                success = False
                message = "CORS headers missing in preflight response"
        else:
            message = f"CORS preflight failed: {result.get('error', 'Unknown error')}"
        
        print(f"{'✅' if success else '❌'} {message}")
        
        return SmokeTestResult(
            'cors_configuration',
            success,
            message,
            duration,
            {
                'status_code': result['status_code'],
                'cors_headers': result.get('headers', {}),
                'response_time_ms': result['response_time']
            },
            datetime.now()
        )
    
    def test_api_endpoints_accessibility(self) -> SmokeTestResult:
        """Test that API endpoints are accessible (even if they require auth)"""
        print("🔍 Testing API endpoints accessibility...")
        
        start_time = time.time()
        
        endpoints_to_test = [
            'api/organizations',
            'api/integrations', 
            'api/policies',
            'api/security/health-score',
            'api/users',
            'api/tasks'
        ]
        
        accessible_endpoints = 0
        endpoint_results = {}
        
        for endpoint in endpoints_to_test:
            result = self.make_request(endpoint)
            # Consider 200, 401, 403 as "accessible" (not 404/500)
            is_accessible = result['status_code'] in [200, 401, 403]
            if is_accessible:
                accessible_endpoints += 1
            
            endpoint_results[endpoint] = {
                'status_code': result['status_code'],
                'accessible': is_accessible,
                'response_time_ms': result['response_time']
            }
        
        duration = time.time() - start_time
        success = accessible_endpoints >= len(endpoints_to_test) * 0.8  # 80% should be accessible
        
        message = f"API endpoints accessible: {accessible_endpoints}/{len(endpoints_to_test)}"
        
        print(f"{'✅' if success else '❌'} {message}")
        
        return SmokeTestResult(
            'api_endpoints_accessibility',
            success,
            message,
            duration,
            {
                'total_endpoints': len(endpoints_to_test),
                'accessible_endpoints': accessible_endpoints,
                'endpoint_results': endpoint_results
            },
            datetime.now()
        )
    
    def test_response_times(self) -> SmokeTestResult:
        """Test response times are within acceptable limits"""
        print("🔍 Testing response times...")
        
        start_time = time.time()
        
        # Test multiple requests to health endpoint
        response_times = []
        for i in range(5):
            result = self.make_request('api/health')
            if result['success']:
                response_times.append(result['response_time'])
        
        duration = time.time() - start_time
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            # Performance thresholds
            avg_threshold = 2000  # 2 seconds average
            max_threshold = 5000  # 5 seconds max
            
            success = avg_response_time < avg_threshold and max_response_time < max_threshold
            
            message = f"Response times - Avg: {avg_response_time:.1f}ms, Max: {max_response_time:.1f}ms"
        else:
            success = False
            message = "Could not measure response times - all requests failed"
            avg_response_time = 0
            max_response_time = 0
        
        print(f"{'✅' if success else '❌'} {message}")
        
        return SmokeTestResult(
            'response_times',
            success,
            message,
            duration,
            {
                'avg_response_time_ms': avg_response_time,
                'max_response_time_ms': max_response_time,
                'sample_count': len(response_times),
                'all_response_times': response_times
            },
            datetime.now()
        )
    
    def test_error_handling(self) -> SmokeTestResult:
        """Test error handling for invalid requests"""
        print("🔍 Testing error handling...")
        
        start_time = time.time()
        
        error_test_cases = [
            ('api/nonexistent-endpoint', 404),
            ('api/organizations/invalid-id', [400, 404]),
            ('api/integrations/999999', [400, 404])
        ]
        
        correct_error_responses = 0
        error_results = {}
        
        for endpoint, expected_status in error_test_cases:
            result = self.make_request(endpoint)
            
            if isinstance(expected_status, list):
                correct_response = result['status_code'] in expected_status
            else:
                correct_response = result['status_code'] == expected_status
            
            if correct_response:
                correct_error_responses += 1
            
            error_results[endpoint] = {
                'status_code': result['status_code'],
                'expected': expected_status,
                'correct': correct_response
            }
        
        duration = time.time() - start_time
        success = correct_error_responses >= len(error_test_cases) * 0.8
        
        message = f"Error handling: {correct_error_responses}/{len(error_test_cases)} correct responses"
        
        print(f"{'✅' if success else '❌'} {message}")
        
        return SmokeTestResult(
            'error_handling',
            success,
            message,
            duration,
            {
                'total_tests': len(error_test_cases),
                'correct_responses': correct_error_responses,
                'error_results': error_results
            },
            datetime.now()
        )
    
    def test_security_headers(self) -> SmokeTestResult:
        """Test security headers are present"""
        print("🔍 Testing security headers...")
        
        start_time = time.time()
        result = self.make_request('api/health')
        duration = time.time() - start_time
        
        if not result['success']:
            return SmokeTestResult(
                'security_headers',
                False,
                "Could not test security headers - health endpoint failed",
                duration,
                {},
                datetime.now()
            )
        
        headers = result.get('headers', {})
        
        # Check for important security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
            'X-XSS-Protection': '1',
            'Strict-Transport-Security': None  # Just check presence
        }
        
        present_headers = []
        missing_headers = []
        
        for header_name, expected_value in security_headers.items():
            header_present = any(header_name.lower() == k.lower() for k in headers.keys())
            
            if header_present:
                present_headers.append(header_name)
            else:
                missing_headers.append(header_name)
        
        security_score = len(present_headers) / len(security_headers)
        success = security_score >= 0.5  # At least 50% of security headers
        
        message = f"Security headers: {len(present_headers)}/{len(security_headers)} present ({security_score:.1%})"
        
        print(f"{'✅' if success else '⚠️'} {message}")
        
        return SmokeTestResult(
            'security_headers',
            success,
            message,
            duration,
            {
                'present_headers': present_headers,
                'missing_headers': missing_headers,
                'security_score': security_score,
                'all_headers': list(headers.keys())
            },
            datetime.now()
        )
    
    def run_all_smoke_tests(self) -> bool:
        """Run all smoke tests"""
        print(f"🚀 Starting smoke tests for: {self.base_url}")
        print("=" * 60)
        
        # Define test methods
        test_methods = [
            self.test_health_endpoint,
            self.test_cors_configuration,
            self.test_api_endpoints_accessibility,
            self.test_response_times,
            self.test_error_handling,
            self.test_security_headers
        ]
        
        # Run all tests
        for test_method in test_methods:
            try:
                result = test_method()
                self.results.append(result)
                print()  # Add spacing
            except Exception as e:
                print(f"❌ Test {test_method.__name__} failed with exception: {e}")
                self.results.append(SmokeTestResult(
                    test_method.__name__,
                    False,
                    f"Test failed with exception: {e}",
                    0,
                    {'exception': str(e)},
                    datetime.now()
                ))
                print()
        
        # Print summary
        self.print_summary()
        
        # Return overall success
        successful_tests = sum(1 for r in self.results if r.success)
        return successful_tests >= len(self.results) * 0.8  # 80% success rate
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.success)
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        print("=" * 60)
        print("📊 SMOKE TEST SUMMARY")
        print("=" * 60)
        print(f"🔗 Base URL: {self.base_url}")
        print(f"🧪 Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {total_tests - successful_tests}")
        print(f"📈 Success Rate: {success_rate:.1%}")
        
        # Show failed tests
        failed_tests = [r for r in self.results if not r.success]
        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   • {test.name}: {test.message}")
        
        # Overall result
        if success_rate >= 0.8:
            print(f"\n🎉 SMOKE TESTS PASSED!")
        else:
            print(f"\n🚨 SMOKE TESTS FAILED!")
        
        print("=" * 60)
    
    def save_results(self, filename: str = None):
        """Save test results to JSON file"""
        if not filename:
            timestamp = int(time.time())
            filename = f"smoke-test-results-{timestamp}.json"
        
        results_data = {
            'base_url': self.base_url,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': len(self.results),
                'successful_tests': sum(1 for r in self.results if r.success),
                'success_rate': sum(1 for r in self.results if r.success) / len(self.results) if self.results else 0
            },
            'results': [
                {
                    'name': r.name,
                    'success': r.success,
                    'message': r.message,
                    'duration': r.duration,
                    'timestamp': r.timestamp.isoformat(),
                    'details': r.details
                }
                for r in self.results
            ]
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(results_data, f, indent=2)
            print(f"📄 Results saved to: {filename}")
        except Exception as e:
            print(f"⚠️ Failed to save results: {e}")


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python smoke_tests.py <base_url>")
        print("Example: python smoke_tests.py https://func-atomsec-sfdc-dev.azurewebsites.net")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    try:
        runner = SmokeTestRunner(base_url)
        success = runner.run_all_smoke_tests()
        runner.save_results()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Smoke tests interrupted")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Smoke tests failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()