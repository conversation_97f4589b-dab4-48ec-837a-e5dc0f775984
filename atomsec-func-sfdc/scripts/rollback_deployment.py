#!/usr/bin/env python3
"""
Automated Rollback Script for SFDC Function App

This script provides automated rollback capabilities for failed deployments.
It can be triggered manually or automatically based on health check failures.
"""

import sys
import json
import time
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import argparse


class RollbackManager:
    """Manages automated rollback operations"""
    
    def __init__(self, function_app_name: str, resource_group: str, 
                 subscription_id: str = None, environment: str = "dev"):
        self.function_app_name = function_app_name
        self.resource_group = resource_group
        self.subscription_id = subscription_id
        self.environment = environment
        self.staging_slot = "staging"
        
    def run_az_command(self, command: List[str]) -> Dict[str, Any]:
        """Run Azure CLI command and return result"""
        try:
            print(f"🔄 Running: az {' '.join(command)}")
            
            result = subprocess.run(
                ['az'] + command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Command timed out after 5 minutes',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def check_azure_cli(self) -> bool:
        """Check if Azure CLI is available and authenticated"""
        print("🔍 Checking Azure CLI availability...")
        
        # Check if az command exists
        result = self.run_az_command(['--version'])
        if not result['success']:
            print("❌ Azure CLI not found. Please install Azure CLI.")
            return False
        
        print("✅ Azure CLI found")
        
        # Check authentication
        result = self.run_az_command(['account', 'show'])
        if not result['success']:
            print("❌ Not authenticated with Azure. Please run 'az login'.")
            return False
        
        print("✅ Azure CLI authenticated")
        
        # Set subscription if provided
        if self.subscription_id:
            result = self.run_az_command(['account', 'set', '--subscription', self.subscription_id])
            if not result['success']:
                print(f"❌ Failed to set subscription: {result['stderr']}")
                return False
            print(f"✅ Subscription set to: {self.subscription_id}")
        
        return True
    
    def get_current_deployment_info(self) -> Dict[str, Any]:
        """Get current deployment information"""
        print("🔍 Getting current deployment information...")
        
        # Get function app details
        result = self.run_az_command([
            'functionapp', 'show',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--output', 'json'
        ])
        
        if not result['success']:
            print(f"❌ Failed to get function app info: {result['stderr']}")
            return {}
        
        try:
            app_info = json.loads(result['stdout'])
            
            # Get slot information
            slot_result = self.run_az_command([
                'functionapp', 'deployment', 'slot', 'list',
                '--name', self.function_app_name,
                '--resource-group', self.resource_group,
                '--output', 'json'
            ])
            
            slots = []
            if slot_result['success']:
                try:
                    slots = json.loads(slot_result['stdout'])
                except json.JSONDecodeError:
                    pass
            
            return {
                'app_info': app_info,
                'slots': slots,
                'current_state': app_info.get('state', 'Unknown'),
                'default_hostname': app_info.get('defaultHostName', ''),
                'has_staging_slot': any(slot.get('name') == self.staging_slot for slot in slots)
            }
            
        except json.JSONDecodeError:
            print("❌ Failed to parse function app information")
            return {}
    
    def create_pre_rollback_backup(self) -> bool:
        """Create backup before rollback"""
        print("💾 Creating pre-rollback backup...")
        
        # Get current app settings
        result = self.run_az_command([
            'functionapp', 'config', 'appsettings', 'list',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--output', 'json'
        ])
        
        if not result['success']:
            print(f"⚠️ Failed to backup app settings: {result['stderr']}")
            return False
        
        # Save backup to file
        timestamp = int(time.time())
        backup_filename = f"pre-rollback-backup-{self.function_app_name}-{timestamp}.json"
        
        try:
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'function_app_name': self.function_app_name,
                'resource_group': self.resource_group,
                'environment': self.environment,
                'app_settings': json.loads(result['stdout']) if result['stdout'] else []
            }
            
            with open(backup_filename, 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            print(f"✅ Backup saved to: {backup_filename}")
            return True
            
        except Exception as e:
            print(f"⚠️ Failed to save backup: {e}")
            return False
    
    def perform_slot_swap_rollback(self) -> bool:
        """Perform rollback by swapping slots back"""
        print("🔄 Performing slot swap rollback...")
        
        # Check if staging slot exists
        deployment_info = self.get_current_deployment_info()
        if not deployment_info.get('has_staging_slot'):
            print("❌ No staging slot found for rollback")
            return False
        
        # Swap staging slot to production
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'swap',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.staging_slot
        ])
        
        if result['success']:
            print("✅ Slot swap rollback completed")
            return True
        else:
            print(f"❌ Slot swap rollback failed: {result['stderr']}")
            return False
    
    def verify_rollback(self) -> bool:
        """Verify rollback was successful"""
        print("🔍 Verifying rollback...")
        
        # Wait for rollback to take effect
        print("⏳ Waiting for rollback to take effect...")
        time.sleep(30)
        
        # Get updated deployment info
        deployment_info = self.get_current_deployment_info()
        if not deployment_info:
            print("❌ Could not verify rollback - unable to get deployment info")
            return False
        
        # Check if app is running
        app_state = deployment_info.get('current_state', '').lower()
        if app_state != 'running':
            print(f"⚠️ App state after rollback: {app_state}")
            return False
        
        # Test health endpoint
        hostname = deployment_info.get('default_hostname')
        if hostname:
            health_url = f"https://{hostname}/api/health"
            print(f"🔍 Testing health endpoint: {health_url}")
            
            # Simple health check using curl (fallback if urllib not available)
            try:
                import urllib.request
                with urllib.request.urlopen(health_url, timeout=30) as response:
                    if response.getcode() == 200:
                        print("✅ Health check passed after rollback")
                        return True
                    else:
                        print(f"⚠️ Health check returned status: {response.getcode()}")
                        return False
            except Exception as e:
                print(f"⚠️ Health check failed: {e}")
                # Try with curl as fallback
                curl_result = subprocess.run(
                    ['curl', '-f', '-s', health_url],
                    capture_output=True,
                    timeout=30
                )
                if curl_result.returncode == 0:
                    print("✅ Health check passed after rollback (via curl)")
                    return True
                else:
                    print("❌ Health check failed after rollback")
                    return False
        
        print("✅ Rollback verification completed")
        return True
    
    def send_rollback_notification(self, success: bool, reason: str = ""):
        """Send rollback notification (placeholder for actual implementation)"""
        print("📧 Sending rollback notification...")
        
        status = "SUCCESS" if success else "FAILED"
        message = f"""
        🚨 ROLLBACK {status} - {self.function_app_name}
        
        Environment: {self.environment}
        Function App: {self.function_app_name}
        Resource Group: {self.resource_group}
        Timestamp: {datetime.now().isoformat()}
        Reason: {reason}
        Status: {status}
        
        Please check the deployment logs for more details.
        """
        
        print(message)
        
        # In a real implementation, this would:
        # 1. Send email notifications
        # 2. Post to Slack/Teams
        # 3. Create incident tickets
        # 4. Update monitoring dashboards
        
        print("📧 Notification sent (placeholder)")
    
    def execute_rollback(self, reason: str = "Manual rollback") -> bool:
        """Execute complete rollback process"""
        print(f"🚨 Starting rollback process for {self.function_app_name}")
        print(f"📝 Reason: {reason}")
        print("=" * 60)
        
        # Step 1: Check prerequisites
        if not self.check_azure_cli():
            return False
        
        # Step 2: Get current deployment info
        deployment_info = self.get_current_deployment_info()
        if not deployment_info:
            print("❌ Could not get deployment information")
            return False
        
        print(f"📊 Current app state: {deployment_info.get('current_state')}")
        print(f"🌐 Current hostname: {deployment_info.get('default_hostname')}")
        
        # Step 3: Create backup
        backup_success = self.create_pre_rollback_backup()
        if not backup_success:
            print("⚠️ Backup failed, but continuing with rollback...")
        
        # Step 4: Perform rollback
        rollback_success = self.perform_slot_swap_rollback()
        if not rollback_success:
            print("❌ Rollback failed")
            self.send_rollback_notification(False, f"Rollback failed: {reason}")
            return False
        
        # Step 5: Verify rollback
        verification_success = self.verify_rollback()
        if not verification_success:
            print("⚠️ Rollback completed but verification failed")
            self.send_rollback_notification(False, f"Rollback verification failed: {reason}")
            return False
        
        # Step 6: Send success notification
        print("✅ Rollback completed successfully!")
        self.send_rollback_notification(True, reason)
        
        return True


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Automated rollback for SFDC Function App deployments",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python rollback_deployment.py --app func-atomsec-sfdc-dev --resource-group atomsec-dev-backend
  python rollback_deployment.py --app func-atomsec-sfdc-prod --resource-group atomsec-prod-backend --subscription 12345678-1234-1234-1234-123456789012
  python rollback_deployment.py --app func-atomsec-sfdc-staging --resource-group atomsec-staging-backend --reason "High error rate detected"
        """
    )
    
    parser.add_argument('--app', '-a', required=True,
                       help='Function App name')
    parser.add_argument('--resource-group', '-g', required=True,
                       help='Resource group name')
    parser.add_argument('--subscription', '-s',
                       help='Azure subscription ID (optional)')
    parser.add_argument('--environment', '-e', default='dev',
                       choices=['dev', 'staging', 'prod'],
                       help='Environment (default: dev)')
    parser.add_argument('--reason', '-r', default='Manual rollback',
                       help='Reason for rollback (default: Manual rollback)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without executing')
    parser.add_argument('--force', action='store_true',
                       help='Skip confirmation prompts')
    
    args = parser.parse_args()
    
    # Confirmation prompt (unless forced or dry-run)
    if not args.force and not args.dry_run:
        print(f"🚨 WARNING: This will rollback the deployment for {args.app}")
        print(f"📝 Reason: {args.reason}")
        print(f"🌍 Environment: {args.environment}")
        
        confirmation = input("\nAre you sure you want to proceed? (yes/no): ").lower().strip()
        if confirmation not in ['yes', 'y']:
            print("❌ Rollback cancelled by user")
            sys.exit(0)
    
    try:
        # Create rollback manager
        rollback_manager = RollbackManager(
            args.app,
            args.resource_group,
            args.subscription,
            args.environment
        )
        
        if args.dry_run:
            print("🔍 DRY RUN MODE - No actual changes will be made")
            print(f"Would rollback: {args.app}")
            print(f"Resource group: {args.resource_group}")
            print(f"Environment: {args.environment}")
            print(f"Reason: {args.reason}")
            sys.exit(0)
        
        # Execute rollback
        success = rollback_manager.execute_rollback(args.reason)
        
        if success:
            print("\n🎉 Rollback completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Rollback failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n❌ Rollback interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Rollback failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()