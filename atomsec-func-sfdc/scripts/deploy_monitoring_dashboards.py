#!/usr/bin/env python3
"""
Deploy Monitoring Dashboards Script

This script deploys Application Insights workbooks and alert rules for the SFDC service.
It reads the configuration from the monitoring config files and creates the necessary
resources in Azure.

Usage:
    python deploy_monitoring_dashboards.py --resource-group <rg> --app-insights <name>
"""

import json
import os
import sys
import argparse
import logging
from typing import Dict, Any, List
from azure.identity import DefaultAzureCredential
from azure.mgmt.applicationinsights import ApplicationInsightsManagementClient
from azure.mgmt.monitor import MonitorManagementClient

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)


class DashboardDeployer:
    """Deploy monitoring dashboards and alerts to Azure"""
    
    def __init__(self, subscription_id: str, resource_group: str, app_insights_name: str):
        """Initialize dashboard deployer"""
        self.subscription_id = subscription_id
        self.resource_group = resource_group
        self.app_insights_name = app_insights_name
        
        # Initialize Azure clients
        credential = DefaultAzureCredential()
        self.app_insights_client = ApplicationInsightsManagementClient(
            credential, subscription_id
        )
        self.monitor_client = MonitorManagementClient(credential, subscription_id)
        
    def load_workbook_config(self, config_path: str) -> Dict[str, Any]:
        """Load workbook configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading workbook config: {e}")
            raise
    
    def deploy_workbooks(self, config: Dict[str, Any]) -> List[str]:
        """Deploy Application Insights workbooks"""
        deployed_workbooks = []
        
        for workbook_config in config.get('workbooks', []):
            try:
                workbook_name = workbook_config['name']
                logger.info(f"Deploying workbook: {workbook_name}")
                
                # Create workbook parameters
                workbook_params = {
                    'location': self._get_app_insights_location(),
                    'display_name': workbook_name,
                    'description': workbook_config.get('description', ''),
                    'serialized_data': json.dumps(workbook_config['template']),
                    'category': 'workbook',
                    'kind': 'shared',
                    'tags': {
                        'service': 'sfdc',
                        'type': 'monitoring',
                        'auto_deployed': 'true'
                    }
                }
                
                # Deploy workbook
                result = self.app_insights_client.workbooks.create_or_update(
                    resource_group_name=self.resource_group,
                    resource_name=self._generate_workbook_id(workbook_name),
                    workbook_properties=workbook_params
                )
                
                deployed_workbooks.append(workbook_name)
                logger.info(f"Successfully deployed workbook: {workbook_name}")
                
            except Exception as e:
                logger.error(f"Error deploying workbook {workbook_name}: {e}")
                continue
        
        return deployed_workbooks
    
    def deploy_alert_rules(self, config: Dict[str, Any]) -> List[str]:
        """Deploy Application Insights alert rules"""
        deployed_alerts = []
        
        for alert_config in config.get('alert_rules', []):
            try:
                alert_name = alert_config['name']
                logger.info(f"Deploying alert rule: {alert_name}")
                
                # Get Application Insights resource ID
                app_insights_resource_id = self._get_app_insights_resource_id()
                
                # Create alert rule parameters
                alert_params = {
                    'location': self._get_app_insights_location(),
                    'description': alert_config.get('description', ''),
                    'severity': alert_config.get('severity', 2),
                    'enabled': True,
                    'scopes': [app_insights_resource_id],
                    'evaluation_frequency': alert_config.get('frequency', 'PT5M'),
                    'window_size': alert_config.get('window_size', 'PT5M'),
                    'criteria': {
                        'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria',
                        'allOf': [
                            {
                                'name': 'condition1',
                                'metric_name': 'customMetrics',
                                'operator': alert_config['threshold']['operator'],
                                'threshold': alert_config['threshold']['value'],
                                'time_aggregation': 'Average'
                            }
                        ]
                    },
                    'actions': [],
                    'tags': {
                        'service': 'sfdc',
                        'type': 'alert',
                        'auto_deployed': 'true'
                    }
                }
                
                # Deploy alert rule
                result = self.monitor_client.metric_alerts.create_or_update(
                    resource_group_name=self.resource_group,
                    rule_name=alert_name,
                    parameters=alert_params
                )
                
                deployed_alerts.append(alert_name)
                logger.info(f"Successfully deployed alert rule: {alert_name}")
                
            except Exception as e:
                logger.error(f"Error deploying alert rule {alert_name}: {e}")
                continue
        
        return deployed_alerts
    
    def _get_app_insights_location(self) -> str:
        """Get Application Insights resource location"""
        try:
            app_insights = self.app_insights_client.components.get(
                resource_group_name=self.resource_group,
                resource_name=self.app_insights_name
            )
            return app_insights.location
        except Exception as e:
            logger.warning(f"Could not get App Insights location: {e}")
            return 'East US'  # Default location
    
    def _get_app_insights_resource_id(self) -> str:
        """Get Application Insights resource ID"""
        return f"/subscriptions/{self.subscription_id}/resourceGroups/{self.resource_group}/providers/Microsoft.Insights/components/{self.app_insights_name}"
    
    def _generate_workbook_id(self, workbook_name: str) -> str:
        """Generate unique workbook ID"""
        import uuid
        # Create deterministic UUID based on workbook name
        namespace = uuid.UUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')
        return str(uuid.uuid5(namespace, workbook_name))
    
    def validate_deployment(self) -> Dict[str, Any]:
        """Validate that resources were deployed correctly"""
        validation_results = {
            'workbooks': [],
            'alerts': [],
            'errors': []
        }
        
        try:
            # Check workbooks
            workbooks = self.app_insights_client.workbooks.list_by_resource_group(
                resource_group_name=self.resource_group
            )
            
            for workbook in workbooks:
                if workbook.tags and workbook.tags.get('service') == 'sfdc':
                    validation_results['workbooks'].append({
                        'name': workbook.display_name,
                        'id': workbook.id,
                        'status': 'deployed'
                    })
            
            # Check alert rules
            alerts = self.monitor_client.metric_alerts.list_by_resource_group(
                resource_group_name=self.resource_group
            )
            
            for alert in alerts:
                if alert.tags and alert.tags.get('service') == 'sfdc':
                    validation_results['alerts'].append({
                        'name': alert.name,
                        'id': alert.id,
                        'enabled': alert.enabled,
                        'status': 'deployed'
                    })
            
        except Exception as e:
            validation_results['errors'].append(str(e))
        
        return validation_results


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description='Deploy SFDC monitoring dashboards')
    parser.add_argument('--subscription-id', required=True, help='Azure subscription ID')
    parser.add_argument('--resource-group', required=True, help='Resource group name')
    parser.add_argument('--app-insights', required=True, help='Application Insights name')
    parser.add_argument('--config-path', default='config/monitoring/app_insights_workbooks.json',
                       help='Path to workbook configuration file')
    parser.add_argument('--validate-only', action='store_true', 
                       help='Only validate existing deployment')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize deployer
        deployer = DashboardDeployer(
            subscription_id=args.subscription_id,
            resource_group=args.resource_group,
            app_insights_name=args.app_insights
        )
        
        if args.validate_only:
            # Validate existing deployment
            logger.info("Validating existing deployment...")
            validation_results = deployer.validate_deployment()
            
            print("\n=== Validation Results ===")
            print(f"Workbooks deployed: {len(validation_results['workbooks'])}")
            for workbook in validation_results['workbooks']:
                print(f"  - {workbook['name']}: {workbook['status']}")
            
            print(f"\nAlert rules deployed: {len(validation_results['alerts'])}")
            for alert in validation_results['alerts']:
                print(f"  - {alert['name']}: {alert['status']} (enabled: {alert['enabled']})")
            
            if validation_results['errors']:
                print(f"\nErrors: {len(validation_results['errors'])}")
                for error in validation_results['errors']:
                    print(f"  - {error}")
            
        else:
            # Deploy dashboards and alerts
            logger.info("Starting dashboard deployment...")
            
            # Load configuration
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                args.config_path
            )
            config = deployer.load_workbook_config(config_path)
            
            # Deploy workbooks
            deployed_workbooks = deployer.deploy_workbooks(config)
            logger.info(f"Deployed {len(deployed_workbooks)} workbooks")
            
            # Deploy alert rules
            deployed_alerts = deployer.deploy_alert_rules(config)
            logger.info(f"Deployed {len(deployed_alerts)} alert rules")
            
            # Validate deployment
            validation_results = deployer.validate_deployment()
            
            print("\n=== Deployment Summary ===")
            print(f"Workbooks deployed: {len(deployed_workbooks)}")
            for workbook in deployed_workbooks:
                print(f"  - {workbook}")
            
            print(f"\nAlert rules deployed: {len(deployed_alerts)}")
            for alert in deployed_alerts:
                print(f"  - {alert}")
            
            print(f"\nValidation: {len(validation_results['workbooks'])} workbooks, {len(validation_results['alerts'])} alerts found")
            
            if validation_results['errors']:
                print(f"\nValidation errors: {len(validation_results['errors'])}")
                for error in validation_results['errors']:
                    print(f"  - {error}")
        
        logger.info("Dashboard deployment completed successfully")
        
    except Exception as e:
        logger.error(f"Dashboard deployment failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()