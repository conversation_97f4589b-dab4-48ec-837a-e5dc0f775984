#!/usr/bin/env python3
"""
Canary Deployment Manager for SFDC Function App

This script manages canary deployments with gradual traffic shifting
and automatic rollback based on metrics monitoring.
"""

import sys
import json
import time
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import argparse
import urllib.request
import urllib.error


class CanaryDeploymentManager:
    """Manages canary deployments with traffic splitting and monitoring"""
    
    def __init__(self, function_app_name: str, resource_group: str, 
                 subscription_id: str = None, environment: str = "dev"):
        self.function_app_name = function_app_name
        self.resource_group = resource_group
        self.subscription_id = subscription_id
        self.environment = environment
        
        # Deployment configuration
        self.canary_slot = "canary"
        self.production_slot = "production"
        
        # Canary configuration
        self.traffic_increments = [10, 25, 50, 75, 100]  # Percentage increments
        self.monitoring_duration_per_increment = 300  # 5 minutes per increment
        self.health_check_interval = 30  # 30 seconds
        
        # Success criteria
        self.max_error_rate = 0.02  # 2%
        self.max_response_time = 3000  # 3 seconds
        self.min_success_rate = 0.98  # 98%
        
    def run_az_command(self, command: List[str]) -> Dict[str, Any]:
        """Run Azure CLI command and return result"""
        try:
            print(f"🔄 Running: az {' '.join(command)}")
            
            result = subprocess.run(
                ['az'] + command,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Command timed out',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def check_prerequisites(self) -> bool:
        """Check deployment prerequisites"""
        print("🔍 Checking canary deployment prerequisites...")
        
        # Check Azure CLI
        result = self.run_az_command(['--version'])
        if not result['success']:
            print("❌ Azure CLI not found")
            return False
        
        # Check authentication
        result = self.run_az_command(['account', 'show'])
        if not result['success']:
            print("❌ Not authenticated with Azure")
            return False
        
        # Set subscription
        if self.subscription_id:
            result = self.run_az_command(['account', 'set', '--subscription', self.subscription_id])
            if not result['success']:
                print(f"❌ Failed to set subscription: {result['stderr']}")
                return False
        
        # Check function app exists
        result = self.run_az_command([
            'functionapp', 'show',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group
        ])
        if not result['success']:
            print(f"❌ Function app not found: {self.function_app_name}")
            return False
        
        print("✅ Prerequisites check passed")
        return True
    
    def create_canary_slot(self) -> bool:
        """Create canary slot if it doesn't exist"""
        print(f"🔧 Creating canary slot: {self.canary_slot}")
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'create',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.canary_slot
        ])
        
        if result['success']:
            print("✅ Canary slot created successfully")
            return True
        else:
            print(f"❌ Failed to create canary slot: {result['stderr']}")
            return False
    
    def deploy_to_canary(self, package_path: str) -> bool:
        """Deploy package to canary slot"""
        print(f"📦 Deploying to canary slot: {package_path}")
        
        if not os.path.exists(package_path):
            print(f"❌ Package not found: {package_path}")
            return False
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'source', 'config-zip',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.canary_slot,
            '--src', package_path
        ])
        
        if result['success']:
            print("✅ Deployment to canary completed")
            return True
        else:
            print(f"❌ Deployment to canary failed: {result['stderr']}")
            return False
    
    def set_traffic_distribution(self, canary_percentage: int) -> bool:
        """Set traffic distribution between production and canary"""
        print(f"🚦 Setting traffic distribution: {canary_percentage}% to canary")
        
        # Azure Function Apps use traffic routing rules
        result = self.run_az_command([
            'functionapp', 'traffic-routing', 'set',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--distribution', f'{self.canary_slot}={canary_percentage}'
        ])
        
        if result['success']:
            print(f"✅ Traffic distribution set: {canary_percentage}% to canary")
            return True
        else:
            print(f"❌ Failed to set traffic distribution: {result['stderr']}")
            # Try alternative approach for older Azure CLI versions
            return self.set_traffic_distribution_alternative(canary_percentage)
    
    def set_traffic_distribution_alternative(self, canary_percentage: int) -> bool:
        """Alternative method for setting traffic distribution"""
        print("🔄 Trying alternative traffic distribution method...")
        
        # Use slot settings to configure traffic routing
        result = self.run_az_command([
            'functionapp', 'config', 'appsettings', 'set',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.canary_slot,
            '--settings', f'WEBSITE_TRAFFIC_ROUTING={canary_percentage}'
        ])
        
        if result['success']:
            print(f"✅ Alternative traffic distribution set: {canary_percentage}% to canary")
            return True
        else:
            print(f"⚠️ Traffic distribution may not be supported - continuing with slot-based testing")
            return True  # Continue anyway for slot-based testing
    
    def health_check(self, url: str, timeout: int = 30) -> Dict[str, Any]:
        """Perform health check on given URL"""
        try:
            start_time = time.time()
            
            req = urllib.request.Request(url + '/api/health')
            req.add_header('User-Agent', 'CanaryDeployment/1.0')
            
            with urllib.request.urlopen(req, timeout=timeout) as response:
                response_time = (time.time() - start_time) * 1000
                status_code = response.getcode()
                
                body = response.read().decode('utf-8')
                try:
                    body = json.loads(body)
                except json.JSONDecodeError:
                    pass
                
                return {
                    'success': status_code == 200,
                    'status_code': status_code,
                    'response_time': response_time,
                    'body': body
                }
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
            return {
                'success': False,
                'status_code': 0,
                'response_time': response_time,
                'error': str(e)
            }
    
    def monitor_canary_metrics(self, canary_url: str, production_url: str, 
                              duration: int) -> Dict[str, Any]:
        """Monitor canary and production metrics"""
        print(f"📊 Monitoring canary metrics for {duration}s...")
        
        start_time = time.time()
        canary_metrics = []
        production_metrics = []
        
        while time.time() - start_time < duration:
            # Check canary
            canary_result = self.health_check(canary_url)
            canary_metrics.append(canary_result)
            
            # Check production
            production_result = self.health_check(production_url)
            production_metrics.append(production_result)
            
            time.sleep(self.health_check_interval)
        
        # Analyze metrics
        canary_analysis = self.analyze_metrics(canary_metrics, "canary")
        production_analysis = self.analyze_metrics(production_metrics, "production")
        
        # Compare canary vs production
        comparison = self.compare_metrics(canary_analysis, production_analysis)
        
        return {
            'canary': canary_analysis,
            'production': production_analysis,
            'comparison': comparison,
            'canary_healthy': self.is_deployment_healthy(canary_analysis),
            'comparison_acceptable': comparison['acceptable']
        }
    
    def analyze_metrics(self, metrics: List[Dict], deployment_type: str) -> Dict[str, Any]:
        """Analyze metrics for a deployment"""
        if not metrics:
            return {
                'success_rate': 0,
                'avg_response_time': 0,
                'error_rate': 1,
                'total_requests': 0
            }
        
        successful_requests = sum(1 for m in metrics if m['success'])
        total_requests = len(metrics)
        success_rate = successful_requests / total_requests
        error_rate = 1 - success_rate
        
        response_times = [m['response_time'] for m in metrics if m['success']]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        
        analysis = {
            'deployment_type': deployment_type,
            'success_rate': success_rate,
            'error_rate': error_rate,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'total_requests': total_requests,
            'successful_requests': successful_requests
        }
        
        print(f"📊 {deployment_type.title()} metrics:")
        print(f"   Success rate: {success_rate:.1%}")
        print(f"   Error rate: {error_rate:.1%}")
        print(f"   Avg response time: {avg_response_time:.1f}ms")
        print(f"   Max response time: {max_response_time:.1f}ms")
        
        return analysis
    
    def compare_metrics(self, canary: Dict, production: Dict) -> Dict[str, Any]:
        """Compare canary metrics against production"""
        # Calculate relative differences
        success_rate_diff = canary['success_rate'] - production['success_rate']
        error_rate_diff = canary['error_rate'] - production['error_rate']
        response_time_diff = canary['avg_response_time'] - production['avg_response_time']
        
        # Define acceptable thresholds for comparison
        max_success_rate_degradation = 0.05  # 5% worse than production
        max_error_rate_increase = 0.03  # 3% more errors than production
        max_response_time_increase = 1000  # 1 second slower than production
        
        # Check if canary is acceptable compared to production
        acceptable = (
            success_rate_diff >= -max_success_rate_degradation and
            error_rate_diff <= max_error_rate_increase and
            response_time_diff <= max_response_time_increase
        )
        
        comparison = {
            'success_rate_diff': success_rate_diff,
            'error_rate_diff': error_rate_diff,
            'response_time_diff': response_time_diff,
            'acceptable': acceptable
        }
        
        print(f"📊 Canary vs Production comparison:")
        print(f"   Success rate diff: {success_rate_diff:+.1%}")
        print(f"   Error rate diff: {error_rate_diff:+.1%}")
        print(f"   Response time diff: {response_time_diff:+.1f}ms")
        print(f"   Acceptable: {'✅' if acceptable else '❌'}")
        
        return comparison
    
    def is_deployment_healthy(self, metrics: Dict) -> bool:
        """Check if deployment metrics meet health criteria"""
        return (
            metrics['success_rate'] >= self.min_success_rate and
            metrics['error_rate'] <= self.max_error_rate and
            metrics['avg_response_time'] <= self.max_response_time
        )
    
    def rollback_canary(self) -> bool:
        """Rollback canary deployment"""
        print("🔄 Rolling back canary deployment...")
        
        # Reset traffic to 0% for canary
        result = self.run_az_command([
            'functionapp', 'traffic-routing', 'clear',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group
        ])
        
        if result['success']:
            print("✅ Canary traffic cleared")
        else:
            print(f"⚠️ Failed to clear canary traffic: {result['stderr']}")
        
        # Delete canary slot
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'delete',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.canary_slot
        ])
        
        if result['success']:
            print("✅ Canary slot deleted")
            return True
        else:
            print(f"❌ Failed to delete canary slot: {result['stderr']}")
            return False
    
    def promote_canary(self) -> bool:
        """Promote canary to production"""
        print("🚀 Promoting canary to production...")
        
        # Swap canary to production
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'swap',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.canary_slot
        ])
        
        if result['success']:
            print("✅ Canary promoted to production")
            
            # Clear traffic routing
            self.run_az_command([
                'functionapp', 'traffic-routing', 'clear',
                '--name', self.function_app_name,
                '--resource-group', self.resource_group
            ])
            
            return True
        else:
            print(f"❌ Failed to promote canary: {result['stderr']}")
            return False
    
    def execute_canary_deployment(self, package_path: str) -> bool:
        """Execute complete canary deployment"""
        print(f"🚀 Starting canary deployment for {self.function_app_name}")
        print(f"📦 Package: {package_path}")
        print(f"🌍 Environment: {self.environment}")
        print(f"📊 Traffic increments: {self.traffic_increments}")
        print("=" * 60)
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Step 2: Create canary slot
        if not self.create_canary_slot():
            return False
        
        # Step 3: Deploy to canary
        if not self.deploy_to_canary(package_path):
            return False
        
        # Get URLs
        canary_url = f"https://{self.function_app_name}-{self.canary_slot}.azurewebsites.net"
        production_url = f"https://{self.function_app_name}.azurewebsites.net"
        
        print(f"🔗 Canary URL: {canary_url}")
        print(f"🔗 Production URL: {production_url}")
        
        # Step 4: Wait for canary to be healthy
        print("⏳ Waiting for canary to be healthy...")
        time.sleep(60)  # Give deployment time to stabilize
        
        canary_health = self.health_check(canary_url)
        if not canary_health['success']:
            print("❌ Canary deployment is not healthy")
            self.rollback_canary()
            return False
        
        print("✅ Canary deployment is healthy")
        
        # Step 5: Gradual traffic shifting
        for traffic_percentage in self.traffic_increments:
            print(f"\n🚦 Testing with {traffic_percentage}% traffic to canary...")
            
            # Set traffic distribution
            if not self.set_traffic_distribution(traffic_percentage):
                print("⚠️ Traffic distribution failed, continuing with direct slot testing")
            
            # Monitor metrics
            metrics = self.monitor_canary_metrics(
                canary_url, 
                production_url, 
                self.monitoring_duration_per_increment
            )
            
            # Check if canary is healthy and acceptable
            if not metrics['canary_healthy']:
                print(f"❌ Canary unhealthy at {traffic_percentage}% traffic")
                self.rollback_canary()
                return False
            
            if not metrics['comparison_acceptable']:
                print(f"❌ Canary performance unacceptable at {traffic_percentage}% traffic")
                self.rollback_canary()
                return False
            
            print(f"✅ Canary successful at {traffic_percentage}% traffic")
            
            # If this is not the final increment, continue
            if traffic_percentage < 100:
                print(f"⏳ Proceeding to next traffic increment...")
                time.sleep(30)  # Brief pause between increments
        
        # Step 6: Promote canary to production
        if not self.promote_canary():
            print("❌ Failed to promote canary to production")
            self.rollback_canary()
            return False
        
        # Step 7: Final validation
        print("🔍 Final validation of promoted deployment...")
        time.sleep(30)  # Allow time for promotion to take effect
        
        final_health = self.health_check(production_url)
        if not final_health['success']:
            print("❌ Final validation failed")
            return False
        
        print("🎉 Canary deployment completed successfully!")
        return True


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Canary deployment for SFDC Function App",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python canary_deployment.py --app func-atomsec-sfdc-dev --resource-group atomsec-dev-backend --package build.zip
  python canary_deployment.py --app func-atomsec-sfdc-prod --resource-group atomsec-prod-backend --package build.zip --increments 5,15,30,60,100
        """
    )
    
    parser.add_argument('--app', '-a', required=True,
                       help='Function App name')
    parser.add_argument('--resource-group', '-g', required=True,
                       help='Resource group name')
    parser.add_argument('--package', '-p', required=True,
                       help='Deployment package path')
    parser.add_argument('--subscription', '-s',
                       help='Azure subscription ID')
    parser.add_argument('--environment', '-e', default='dev',
                       choices=['dev', 'staging', 'prod'],
                       help='Environment (default: dev)')
    parser.add_argument('--increments', 
                       help='Traffic increments (comma-separated percentages, e.g., 10,25,50,100)')
    parser.add_argument('--monitoring-duration', type=int, default=300,
                       help='Monitoring duration per increment in seconds (default: 300)')
    
    args = parser.parse_args()
    
    try:
        # Create deployment manager
        manager = CanaryDeploymentManager(
            args.app,
            args.resource_group,
            args.subscription,
            args.environment
        )
        
        # Configure traffic increments if provided
        if args.increments:
            try:
                increments = [int(x.strip()) for x in args.increments.split(',')]
                manager.traffic_increments = sorted(increments)
                print(f"Using custom traffic increments: {manager.traffic_increments}")
            except ValueError:
                print("❌ Invalid traffic increments format")
                sys.exit(1)
        
        # Configure monitoring duration
        manager.monitoring_duration_per_increment = args.monitoring_duration
        
        # Execute deployment
        success = manager.execute_canary_deployment(args.package)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Canary deployment interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Canary deployment failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()