"""
Create PoliciesResult Table

This script creates the PoliciesResult table in Azurite and inserts test data.
"""

import os
import sys
import logging
import signal
from datetime import datetime
from azure.data.tables import TableServiceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('create_policies_result')

def create_policies_result_table():
    """Create PoliciesResult table and insert test data"""
    logger.info("Creating PoliciesResult table...")

    # Get connection string from environment variable or use default
    connection_string = os.environ.get(
        "AzureWebJobsStorage",
        "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
    )

    logger.info(f"Using connection string: {connection_string}")

    try:
        # Create table service client
        logger.info("Creating table service client...")
        table_service = TableServiceClient.from_connection_string(connection_string)
        logger.info("Successfully created table service client")

        # Create table
        table_client = table_service.create_table_if_not_exists("PoliciesResult")
        logger.info("Successfully created PoliciesResult table")

        # Insert test data
        test_entities = [
            {
                "PartitionKey": "test-integration-1",
                "RowKey": f"setting1-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                "OrgValue": "false",
                "OWASPCategory": "A1",
                "StandardValue": "true",
                "IssueDescription": "Test issue 1",
                "Recommendations": "Test recommendation 1",
                "Severity": "High",
                "Weakness": "Test weakness 1",
                "IntegrationId": "test-integration-1",
                "CreatedAt": datetime.now().isoformat(),
                "Setting": "TestSetting1"
            },
            {
                "PartitionKey": "test-integration-1",
                "RowKey": f"setting2-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                "OrgValue": "true",
                "OWASPCategory": "A2",
                "StandardValue": "true",
                "IssueDescription": "Test issue 2",
                "Recommendations": "Test recommendation 2",
                "Severity": "Medium",
                "Weakness": "Test weakness 2",
                "IntegrationId": "test-integration-1",
                "CreatedAt": datetime.now().isoformat(),
                "Setting": "TestSetting2"
            }
        ]

        for entity in test_entities:
            table_client.upsert_entity(entity)
            logger.info(f"Inserted test entity: {entity}")

        # Verify entities were inserted
        entities = list(table_client.query_entities(""))
        logger.info(f"Found {len(entities)} entities in PoliciesResult table")

        return True
    except Exception as e:
        logger.error(f"Error creating PoliciesResult table: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_policies_result_table()
    if success:
        logger.info("PoliciesResult table creation completed successfully")
    else:
        logger.error("PoliciesResult table creation failed")
        sys.exit(1)
