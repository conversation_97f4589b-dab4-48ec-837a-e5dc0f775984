#!/usr/bin/env python3
"""
Simple test for QueueMessageProcessor
"""

print("Starting simple test...")

try:
    # Test basic functionality without complex mocking
    import json
    import uuid
    from datetime import datetime
    
    # Create a simple test message
    test_message = {
        "task_id": str(uuid.uuid4()),
        "execution_log_id": str(uuid.uuid4()),
        "task_type": "Integration_Scan",
        "org_id": "test_org",
        "user_id": "test_user",
        "priority": "high",
        "params": {"test_param": "test_value"}
    }
    
    print("✓ Test message created successfully")
    
    # Test JSON serialization (used in queue messages)
    serialized = json.dumps(test_message)
    deserialized = json.loads(serialized)
    
    assert deserialized["task_id"] == test_message["task_id"]
    assert deserialized["execution_log_id"] == test_message["execution_log_id"]
    
    print("✓ JSON serialization/deserialization test passed")
    
    # Test execution context extraction logic (without actual class)
    def extract_execution_context_simple(message):
        execution_log_id = message.get("execution_log_id")
        if not execution_log_id:
            return None
        
        return {
            "execution_log_id": execution_log_id,
            "task_type": message.get("task_type"),
            "org_id": message.get("org_id"),
            "user_id": message.get("user_id"),
            "priority": message.get("priority", "medium"),
            "params": message.get("params", {})
        }
    
    context = extract_execution_context_simple(test_message)
    assert context is not None
    assert context["execution_log_id"] == test_message["execution_log_id"]
    
    print("✓ Execution context extraction logic test passed")
    
    print("🎉 All simple tests passed!")
    
except Exception as e:
    print(f"❌ Test failed: {str(e)}")
    import traceback
    traceback.print_exc()
    exit(1)

print("Simple test completed successfully!")