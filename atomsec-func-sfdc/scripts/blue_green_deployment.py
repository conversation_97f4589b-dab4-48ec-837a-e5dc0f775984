#!/usr/bin/env python3
"""
Blue-Green Deployment Manager for SFDC Function App

This script manages blue-green deployments using Azure Function App deployment slots.
It provides safe deployment with automatic rollback capabilities.
"""

import sys
import json
import time
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import argparse
import urllib.request
import urllib.error


class BlueGreenDeploymentManager:
    """Manages blue-green deployments with monitoring and rollback"""
    
    def __init__(self, function_app_name: str, resource_group: str, 
                 subscription_id: str = None, environment: str = "dev"):
        self.function_app_name = function_app_name
        self.resource_group = resource_group
        self.subscription_id = subscription_id
        self.environment = environment
        
        # Deployment configuration
        self.staging_slot = "staging"
        self.production_slot = "production"  # This is the default slot
        self.health_check_timeout = 300  # 5 minutes
        self.health_check_interval = 10  # 10 seconds
        self.monitoring_duration = 300  # 5 minutes post-deployment monitoring
        
        # Performance thresholds
        self.max_response_time = 5000  # 5 seconds
        self.min_success_rate = 0.95  # 95%
        self.max_error_rate = 0.05  # 5%
        
    def run_az_command(self, command: List[str]) -> Dict[str, Any]:
        """Run Azure CLI command and return result"""
        try:
            print(f"🔄 Running: az {' '.join(command)}")
            
            result = subprocess.run(
                ['az'] + command,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Command timed out',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def check_prerequisites(self) -> bool:
        """Check deployment prerequisites"""
        print("🔍 Checking deployment prerequisites...")
        
        # Check Azure CLI
        result = self.run_az_command(['--version'])
        if not result['success']:
            print("❌ Azure CLI not found")
            return False
        
        # Check authentication
        result = self.run_az_command(['account', 'show'])
        if not result['success']:
            print("❌ Not authenticated with Azure")
            return False
        
        # Set subscription
        if self.subscription_id:
            result = self.run_az_command(['account', 'set', '--subscription', self.subscription_id])
            if not result['success']:
                print(f"❌ Failed to set subscription: {result['stderr']}")
                return False
        
        # Check function app exists
        result = self.run_az_command([
            'functionapp', 'show',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group
        ])
        if not result['success']:
            print(f"❌ Function app not found: {self.function_app_name}")
            return False
        
        print("✅ Prerequisites check passed")
        return True
    
    def get_deployment_slots(self) -> Dict[str, Any]:
        """Get information about deployment slots"""
        print("🔍 Getting deployment slot information...")
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'list',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--output', 'json'
        ])
        
        if not result['success']:
            print(f"❌ Failed to get slot information: {result['stderr']}")
            return {}
        
        try:
            slots = json.loads(result['stdout']) if result['stdout'] else []
            
            slot_info = {
                'slots': slots,
                'has_staging': any(slot.get('name') == self.staging_slot for slot in slots),
                'staging_url': None,
                'production_url': None
            }
            
            # Get URLs
            for slot in slots:
                if slot.get('name') == self.staging_slot:
                    slot_info['staging_url'] = f"https://{slot.get('defaultHostName', '')}"
            
            # Get production URL
            app_result = self.run_az_command([
                'functionapp', 'show',
                '--name', self.function_app_name,
                '--resource-group', self.resource_group,
                '--output', 'json'
            ])
            
            if app_result['success']:
                app_info = json.loads(app_result['stdout'])
                slot_info['production_url'] = f"https://{app_info.get('defaultHostName', '')}"
            
            return slot_info
            
        except json.JSONDecodeError:
            print("❌ Failed to parse slot information")
            return {}
    
    def create_staging_slot(self) -> bool:
        """Create staging slot if it doesn't exist"""
        print(f"🔧 Creating staging slot: {self.staging_slot}")
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'create',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.staging_slot
        ])
        
        if result['success']:
            print("✅ Staging slot created successfully")
            return True
        else:
            print(f"❌ Failed to create staging slot: {result['stderr']}")
            return False
    
    def deploy_to_staging(self, package_path: str) -> bool:
        """Deploy package to staging slot"""
        print(f"📦 Deploying to staging slot: {package_path}")
        
        if not os.path.exists(package_path):
            print(f"❌ Package not found: {package_path}")
            return False
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'source', 'config-zip',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.staging_slot,
            '--src', package_path
        ])
        
        if result['success']:
            print("✅ Deployment to staging completed")
            return True
        else:
            print(f"❌ Deployment to staging failed: {result['stderr']}")
            return False
    
    def health_check(self, url: str, timeout: int = 30) -> Dict[str, Any]:
        """Perform health check on given URL"""
        try:
            start_time = time.time()
            
            req = urllib.request.Request(url + '/api/health')
            req.add_header('User-Agent', 'BlueGreenDeployment/1.0')
            
            with urllib.request.urlopen(req, timeout=timeout) as response:
                response_time = (time.time() - start_time) * 1000
                status_code = response.getcode()
                
                body = response.read().decode('utf-8')
                try:
                    body = json.loads(body)
                except json.JSONDecodeError:
                    pass
                
                return {
                    'success': status_code == 200,
                    'status_code': status_code,
                    'response_time': response_time,
                    'body': body
                }
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
            return {
                'success': False,
                'status_code': 0,
                'response_time': response_time,
                'error': str(e)
            }
    
    def wait_for_staging_health(self, staging_url: str) -> bool:
        """Wait for staging deployment to be healthy"""
        print(f"⏳ Waiting for staging to be healthy: {staging_url}")
        
        start_time = time.time()
        attempts = 0
        
        while time.time() - start_time < self.health_check_timeout:
            attempts += 1
            health_result = self.health_check(staging_url)
            
            if health_result['success']:
                print(f"✅ Staging healthy after {attempts} attempts ({time.time() - start_time:.1f}s)")
                return True
            
            print(f"⏳ Attempt {attempts}: {health_result.get('error', 'Health check failed')}")
            time.sleep(self.health_check_interval)
        
        print(f"❌ Staging health check timeout after {self.health_check_timeout}s")
        return False
    
    def run_staging_validation(self, staging_url: str) -> bool:
        """Run comprehensive validation on staging"""
        print("🧪 Running staging validation...")
        
        validation_tests = [
            ('Health Check', lambda: self.health_check(staging_url)),
            ('Performance Test', lambda: self.performance_test(staging_url)),
            ('API Endpoints Test', lambda: self.api_endpoints_test(staging_url))
        ]
        
        passed_tests = 0
        total_tests = len(validation_tests)
        
        for test_name, test_func in validation_tests:
            print(f"🔍 Running {test_name}...")
            try:
                result = test_func()
                if result.get('success', False):
                    print(f"✅ {test_name} passed")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
        
        success_rate = passed_tests / total_tests
        print(f"📊 Validation results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% success rate required
    
    def performance_test(self, url: str) -> Dict[str, Any]:
        """Run performance test on URL"""
        response_times = []
        successful_requests = 0
        total_requests = 10
        
        for i in range(total_requests):
            result = self.health_check(url)
            if result['success']:
                successful_requests += 1
                response_times.append(result['response_time'])
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            success_rate = successful_requests / total_requests
            
            performance_ok = (
                avg_response_time < self.max_response_time and
                success_rate >= self.min_success_rate
            )
            
            return {
                'success': performance_ok,
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'success_rate': success_rate
            }
        else:
            return {
                'success': False,
                'error': 'No successful requests'
            }
    
    def api_endpoints_test(self, url: str) -> Dict[str, Any]:
        """Test API endpoints accessibility"""
        endpoints = [
            '/api/health',
            '/api/organizations',
            '/api/integrations'
        ]
        
        accessible_endpoints = 0
        
        for endpoint in endpoints:
            try:
                req = urllib.request.Request(url + endpoint)
                req.add_header('User-Agent', 'BlueGreenDeployment/1.0')
                
                with urllib.request.urlopen(req, timeout=10) as response:
                    # 200, 401, 403 are acceptable (not 404/500)
                    if response.getcode() in [200, 401, 403]:
                        accessible_endpoints += 1
            except urllib.error.HTTPError as e:
                if e.code in [401, 403]:
                    accessible_endpoints += 1
            except Exception:
                pass
        
        success_rate = accessible_endpoints / len(endpoints)
        return {
            'success': success_rate >= 0.8,
            'accessible_endpoints': accessible_endpoints,
            'total_endpoints': len(endpoints),
            'success_rate': success_rate
        }
    
    def swap_slots(self) -> bool:
        """Swap staging and production slots"""
        print("🔄 Swapping staging to production...")
        
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'swap',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.staging_slot
        ])
        
        if result['success']:
            print("✅ Slot swap completed")
            return True
        else:
            print(f"❌ Slot swap failed: {result['stderr']}")
            return False
    
    def monitor_production(self, production_url: str) -> bool:
        """Monitor production after deployment"""
        print(f"📊 Monitoring production for {self.monitoring_duration}s...")
        
        start_time = time.time()
        health_checks = []
        
        while time.time() - start_time < self.monitoring_duration:
            health_result = self.health_check(production_url)
            health_checks.append(health_result)
            
            if not health_result['success']:
                print(f"⚠️ Production health check failed: {health_result.get('error')}")
            
            time.sleep(self.health_check_interval)
        
        # Analyze results
        successful_checks = sum(1 for check in health_checks if check['success'])
        success_rate = successful_checks / len(health_checks) if health_checks else 0
        
        if health_checks:
            response_times = [check['response_time'] for check in health_checks if check['success']]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        else:
            avg_response_time = 0
        
        print(f"📊 Production monitoring results:")
        print(f"   Success rate: {success_rate:.1%}")
        print(f"   Average response time: {avg_response_time:.1f}ms")
        
        # Check if rollback is needed
        rollback_needed = (
            success_rate < self.min_success_rate or
            avg_response_time > self.max_response_time
        )
        
        if rollback_needed:
            print("🚨 Production monitoring indicates issues - rollback recommended")
            return False
        else:
            print("✅ Production monitoring successful")
            return True
    
    def rollback_deployment(self) -> bool:
        """Rollback deployment by swapping slots back"""
        print("🔄 Rolling back deployment...")
        
        # Swap back to previous version
        result = self.run_az_command([
            'functionapp', 'deployment', 'slot', 'swap',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--slot', self.staging_slot
        ])
        
        if result['success']:
            print("✅ Rollback completed")
            return True
        else:
            print(f"❌ Rollback failed: {result['stderr']}")
            return False
    
    def execute_blue_green_deployment(self, package_path: str, auto_rollback: bool = True) -> bool:
        """Execute complete blue-green deployment"""
        print(f"🚀 Starting blue-green deployment for {self.function_app_name}")
        print(f"📦 Package: {package_path}")
        print(f"🌍 Environment: {self.environment}")
        print(f"🔄 Auto-rollback: {auto_rollback}")
        print("=" * 60)
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Step 2: Get slot information
        slot_info = self.get_deployment_slots()
        if not slot_info:
            return False
        
        # Step 3: Create staging slot if needed
        if not slot_info['has_staging']:
            if not self.create_staging_slot():
                return False
            # Refresh slot info
            slot_info = self.get_deployment_slots()
        
        staging_url = slot_info.get('staging_url')
        production_url = slot_info.get('production_url')
        
        if not staging_url or not production_url:
            print("❌ Could not determine slot URLs")
            return False
        
        print(f"🔗 Staging URL: {staging_url}")
        print(f"🔗 Production URL: {production_url}")
        
        # Step 4: Deploy to staging
        if not self.deploy_to_staging(package_path):
            return False
        
        # Step 5: Wait for staging to be healthy
        if not self.wait_for_staging_health(staging_url):
            return False
        
        # Step 6: Run staging validation
        if not self.run_staging_validation(staging_url):
            print("❌ Staging validation failed")
            return False
        
        # Step 7: Swap slots (blue-green deployment)
        if not self.swap_slots():
            return False
        
        # Step 8: Monitor production
        production_healthy = self.monitor_production(production_url)
        
        # Step 9: Handle rollback if needed
        if not production_healthy and auto_rollback:
            print("🚨 Production issues detected - initiating automatic rollback")
            rollback_success = self.rollback_deployment()
            
            if rollback_success:
                print("✅ Automatic rollback completed")
                return False  # Deployment failed but rollback succeeded
            else:
                print("❌ Automatic rollback failed - manual intervention required")
                return False
        
        if production_healthy:
            print("🎉 Blue-green deployment completed successfully!")
            return True
        else:
            print("⚠️ Deployment completed but production monitoring shows issues")
            return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Blue-Green deployment for SFDC Function App",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python blue_green_deployment.py --app func-atomsec-sfdc-dev --resource-group atomsec-dev-backend --package build.zip
  python blue_green_deployment.py --app func-atomsec-sfdc-prod --resource-group atomsec-prod-backend --package build.zip --no-auto-rollback
        """
    )
    
    parser.add_argument('--app', '-a', required=True,
                       help='Function App name')
    parser.add_argument('--resource-group', '-g', required=True,
                       help='Resource group name')
    parser.add_argument('--package', '-p', required=True,
                       help='Deployment package path')
    parser.add_argument('--subscription', '-s',
                       help='Azure subscription ID')
    parser.add_argument('--environment', '-e', default='dev',
                       choices=['dev', 'staging', 'prod'],
                       help='Environment (default: dev)')
    parser.add_argument('--no-auto-rollback', action='store_true',
                       help='Disable automatic rollback on failure')
    parser.add_argument('--monitoring-duration', type=int, default=300,
                       help='Production monitoring duration in seconds (default: 300)')
    
    args = parser.parse_args()
    
    try:
        # Create deployment manager
        manager = BlueGreenDeploymentManager(
            args.app,
            args.resource_group,
            args.subscription,
            args.environment
        )
        
        # Configure monitoring duration
        manager.monitoring_duration = args.monitoring_duration
        
        # Execute deployment
        success = manager.execute_blue_green_deployment(
            args.package,
            not args.no_auto_rollback
        )
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Deployment interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Deployment failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()