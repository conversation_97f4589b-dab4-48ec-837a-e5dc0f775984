#!/usr/bin/env python3
"""
Deployment Monitoring and Alerting for SFDC Function App

This script provides continuous monitoring of deployments with automatic
alerting and rollback triggers based on performance and error metrics.
"""

import sys
import json
import time
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import argparse
import urllib.request
import urllib.error
import threading
import queue
import statistics


class DeploymentMonitor:
    """Monitors deployment health and triggers alerts/rollbacks"""
    
    def __init__(self, function_app_name: str, resource_group: str, 
                 subscription_id: str = None, environment: str = "dev"):
        self.function_app_name = function_app_name
        self.resource_group = resource_group
        self.subscription_id = subscription_id
        self.environment = environment
        
        # Monitoring configuration
        self.monitoring_interval = 30  # 30 seconds
        self.metrics_window = 300  # 5 minutes rolling window
        self.alert_cooldown = 600  # 10 minutes between alerts
        
        # Health thresholds
        self.error_rate_warning = 0.05  # 5%
        self.error_rate_critical = 0.10  # 10%
        self.response_time_warning = 3000  # 3 seconds
        self.response_time_critical = 5000  # 5 seconds
        self.availability_warning = 0.95  # 95%
        self.availability_critical = 0.90  # 90%
        
        # Rollback thresholds
        self.rollback_error_rate = 0.15  # 15%
        self.rollback_availability = 0.85  # 85%
        self.rollback_consecutive_failures = 5
        
        # State tracking
        self.metrics_history = []
        self.alerts_sent = {}
        self.consecutive_failures = 0
        self.monitoring_active = False
        self.rollback_triggered = False
        
    def run_az_command(self, command: List[str]) -> Dict[str, Any]:
        """Run Azure CLI command and return result"""
        try:
            result = subprocess.run(
                ['az'] + command,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Command timed out',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def get_function_app_url(self) -> Optional[str]:
        """Get the function app URL"""
        result = self.run_az_command([
            'functionapp', 'show',
            '--name', self.function_app_name,
            '--resource-group', self.resource_group,
            '--output', 'json'
        ])
        
        if result['success']:
            try:
                app_info = json.loads(result['stdout'])
                hostname = app_info.get('defaultHostName')
                if hostname:
                    return f"https://{hostname}"
            except json.JSONDecodeError:
                pass
        
        return None
    
    def health_check(self, url: str) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        start_time = time.time()
        
        try:
            req = urllib.request.Request(url + '/api/health')
            req.add_header('User-Agent', 'DeploymentMonitor/1.0')
            req.add_header('Accept', 'application/json')
            
            with urllib.request.urlopen(req, timeout=30) as response:
                response_time = (time.time() - start_time) * 1000
                status_code = response.getcode()
                
                body = response.read().decode('utf-8')
                try:
                    body = json.loads(body)
                except json.JSONDecodeError:
                    pass
                
                return {
                    'timestamp': datetime.now(),
                    'success': status_code == 200,
                    'status_code': status_code,
                    'response_time': response_time,
                    'body': body,
                    'error': None
                }
                
        except urllib.error.HTTPError as e:
            response_time = (time.time() - start_time) * 1000
            return {
                'timestamp': datetime.now(),
                'success': False,
                'status_code': e.code,
                'response_time': response_time,
                'body': None,
                'error': f"HTTP {e.code}: {e.reason}"
            }
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return {
                'timestamp': datetime.now(),
                'success': False,
                'status_code': 0,
                'response_time': response_time,
                'body': None,
                'error': str(e)
            }
    
    def collect_metrics(self, url: str) -> Dict[str, Any]:
        """Collect comprehensive metrics"""
        # Perform multiple health checks for better accuracy
        health_checks = []
        for _ in range(3):
            health_result = self.health_check(url)
            health_checks.append(health_result)
            time.sleep(1)  # Brief pause between checks
        
        # Calculate metrics
        successful_checks = sum(1 for check in health_checks if check['success'])
        total_checks = len(health_checks)
        availability = successful_checks / total_checks
        error_rate = 1 - availability
        
        response_times = [check['response_time'] for check in health_checks if check['success']]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        
        metrics = {
            'timestamp': datetime.now(),
            'availability': availability,
            'error_rate': error_rate,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'successful_checks': successful_checks,
            'total_checks': total_checks,
            'health_checks': health_checks
        }
        
        return metrics
    
    def update_metrics_history(self, metrics: Dict[str, Any]):
        """Update rolling metrics history"""
        self.metrics_history.append(metrics)
        
        # Keep only metrics within the window
        cutoff_time = datetime.now() - timedelta(seconds=self.metrics_window)
        self.metrics_history = [
            m for m in self.metrics_history 
            if m['timestamp'] > cutoff_time
        ]
    
    def calculate_rolling_metrics(self) -> Dict[str, Any]:
        """Calculate rolling window metrics"""
        if not self.metrics_history:
            return {
                'availability': 0,
                'error_rate': 1,
                'avg_response_time': 0,
                'sample_count': 0
            }
        
        # Calculate averages over the window
        availabilities = [m['availability'] for m in self.metrics_history]
        error_rates = [m['error_rate'] for m in self.metrics_history]
        response_times = [m['avg_response_time'] for m in self.metrics_history if m['avg_response_time'] > 0]
        
        rolling_metrics = {
            'availability': statistics.mean(availabilities),
            'error_rate': statistics.mean(error_rates),
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'max_response_time': max(m['max_response_time'] for m in self.metrics_history),
            'sample_count': len(self.metrics_history),
            'window_duration': self.metrics_window
        }
        
        return rolling_metrics
    
    def check_alert_conditions(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions"""
        alerts = []
        current_time = datetime.now()
        
        # Error rate alerts
        if metrics['error_rate'] >= self.error_rate_critical:
            alert_key = 'error_rate_critical'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'critical',
                    'category': 'error_rate',
                    'message': f"Critical error rate: {metrics['error_rate']:.1%} (threshold: {self.error_rate_critical:.1%})",
                    'value': metrics['error_rate'],
                    'threshold': self.error_rate_critical
                })
        elif metrics['error_rate'] >= self.error_rate_warning:
            alert_key = 'error_rate_warning'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'warning',
                    'category': 'error_rate',
                    'message': f"High error rate: {metrics['error_rate']:.1%} (threshold: {self.error_rate_warning:.1%})",
                    'value': metrics['error_rate'],
                    'threshold': self.error_rate_warning
                })
        
        # Response time alerts
        if metrics['avg_response_time'] >= self.response_time_critical:
            alert_key = 'response_time_critical'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'critical',
                    'category': 'response_time',
                    'message': f"Critical response time: {metrics['avg_response_time']:.1f}ms (threshold: {self.response_time_critical}ms)",
                    'value': metrics['avg_response_time'],
                    'threshold': self.response_time_critical
                })
        elif metrics['avg_response_time'] >= self.response_time_warning:
            alert_key = 'response_time_warning'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'warning',
                    'category': 'response_time',
                    'message': f"Slow response time: {metrics['avg_response_time']:.1f}ms (threshold: {self.response_time_warning}ms)",
                    'value': metrics['avg_response_time'],
                    'threshold': self.response_time_warning
                })
        
        # Availability alerts
        if metrics['availability'] <= self.availability_critical:
            alert_key = 'availability_critical'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'critical',
                    'category': 'availability',
                    'message': f"Critical availability: {metrics['availability']:.1%} (threshold: {self.availability_critical:.1%})",
                    'value': metrics['availability'],
                    'threshold': self.availability_critical
                })
        elif metrics['availability'] <= self.availability_warning:
            alert_key = 'availability_warning'
            if self.should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'warning',
                    'category': 'availability',
                    'message': f"Low availability: {metrics['availability']:.1%} (threshold: {self.availability_warning:.1%})",
                    'value': metrics['availability'],
                    'threshold': self.availability_warning
                })
        
        return alerts
    
    def should_send_alert(self, alert_key: str, current_time: datetime) -> bool:
        """Check if alert should be sent (respecting cooldown)"""
        last_sent = self.alerts_sent.get(alert_key)
        if last_sent is None:
            self.alerts_sent[alert_key] = current_time
            return True
        
        time_since_last = (current_time - last_sent).total_seconds()
        if time_since_last >= self.alert_cooldown:
            self.alerts_sent[alert_key] = current_time
            return True
        
        return False
    
    def send_alert(self, alert: Dict[str, Any]):
        """Send alert notification"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n🚨 ALERT [{alert['type'].upper()}] - {timestamp}")
        print(f"📱 Function App: {self.function_app_name}")
        print(f"🌍 Environment: {self.environment}")
        print(f"📊 Category: {alert['category']}")
        print(f"💬 Message: {alert['message']}")
        print("=" * 60)
        
        # In a real implementation, this would:
        # 1. Send email notifications
        # 2. Post to Slack/Teams
        # 3. Create incident tickets
        # 4. Send SMS for critical alerts
        # 5. Update monitoring dashboards
    
    def check_rollback_conditions(self, metrics: Dict[str, Any]) -> bool:
        """Check if automatic rollback should be triggered"""
        # Track consecutive failures
        if metrics['availability'] == 0:
            self.consecutive_failures += 1
        else:
            self.consecutive_failures = 0
        
        # Rollback conditions
        rollback_needed = (
            metrics['error_rate'] >= self.rollback_error_rate or
            metrics['availability'] <= self.rollback_availability or
            self.consecutive_failures >= self.rollback_consecutive_failures
        )
        
        if rollback_needed and not self.rollback_triggered:
            print(f"\n🚨 ROLLBACK TRIGGER ACTIVATED!")
            print(f"   Error rate: {metrics['error_rate']:.1%} (threshold: {self.rollback_error_rate:.1%})")
            print(f"   Availability: {metrics['availability']:.1%} (threshold: {self.rollback_availability:.1%})")
            print(f"   Consecutive failures: {self.consecutive_failures} (threshold: {self.rollback_consecutive_failures})")
            
            self.rollback_triggered = True
            return True
        
        return False
    
    def trigger_rollback(self):
        """Trigger automatic rollback"""
        print("🔄 Triggering automatic rollback...")
        
        # In a real implementation, this would:
        # 1. Call the rollback script
        # 2. Swap deployment slots
        # 3. Send critical notifications
        # 4. Create incident tickets
        # 5. Update deployment status
        
        rollback_command = f"""
        # Automatic rollback would execute:
        python scripts/rollback_deployment.py \\
            --app {self.function_app_name} \\
            --resource-group {self.resource_group} \\
            --reason "Automatic rollback triggered by monitoring" \\
            --force
        """
        
        print(rollback_command)
        print("📧 Critical notifications would be sent")
        print("🎫 Incident ticket would be created")
    
    def print_status(self, metrics: Dict[str, Any]):
        """Print current monitoring status"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        # Status indicators
        availability_status = "🟢" if metrics['availability'] >= self.availability_warning else "🟡" if metrics['availability'] >= self.availability_critical else "🔴"
        error_rate_status = "🟢" if metrics['error_rate'] <= self.error_rate_warning else "🟡" if metrics['error_rate'] <= self.error_rate_critical else "🔴"
        response_time_status = "🟢" if metrics['avg_response_time'] <= self.response_time_warning else "🟡" if metrics['avg_response_time'] <= self.response_time_critical else "🔴"
        
        print(f"\r[{timestamp}] {availability_status} Avail: {metrics['availability']:.1%} | "
              f"{error_rate_status} Errors: {metrics['error_rate']:.1%} | "
              f"{response_time_status} RT: {metrics['avg_response_time']:.0f}ms | "
              f"Samples: {metrics['sample_count']}", end="", flush=True)
    
    def start_monitoring(self, duration: int = None) -> bool:
        """Start continuous monitoring"""
        print(f"🚀 Starting deployment monitoring for {self.function_app_name}")
        print(f"🌍 Environment: {self.environment}")
        print(f"⏱️  Monitoring interval: {self.monitoring_interval}s")
        print(f"📊 Metrics window: {self.metrics_window}s")
        
        if duration:
            print(f"⏰ Duration: {duration}s")
        else:
            print("⏰ Duration: Continuous (Ctrl+C to stop)")
        
        print("=" * 60)
        
        # Get function app URL
        app_url = self.get_function_app_url()
        if not app_url:
            print("❌ Could not determine function app URL")
            return False
        
        print(f"🔗 Monitoring URL: {app_url}")
        print()
        
        self.monitoring_active = True
        start_time = time.time()
        
        try:
            while self.monitoring_active:
                # Check duration limit
                if duration and (time.time() - start_time) >= duration:
                    print(f"\n⏰ Monitoring duration ({duration}s) completed")
                    break
                
                # Collect metrics
                current_metrics = self.collect_metrics(app_url)
                self.update_metrics_history(current_metrics)
                
                # Calculate rolling metrics
                rolling_metrics = self.calculate_rolling_metrics()
                
                # Print status
                self.print_status(rolling_metrics)
                
                # Check for alerts
                alerts = self.check_alert_conditions(rolling_metrics)
                for alert in alerts:
                    self.send_alert(alert)
                
                # Check for rollback conditions
                if self.check_rollback_conditions(rolling_metrics):
                    self.trigger_rollback()
                    break
                
                # Wait for next check
                time.sleep(self.monitoring_interval)
                
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
            self.monitoring_active = False
        
        # Final summary
        self.print_monitoring_summary()
        
        return not self.rollback_triggered
    
    def print_monitoring_summary(self):
        """Print monitoring session summary"""
        if not self.metrics_history:
            return
        
        print(f"\n\n📊 MONITORING SUMMARY")
        print("=" * 60)
        
        # Calculate overall metrics
        all_availabilities = [m['availability'] for m in self.metrics_history]
        all_error_rates = [m['error_rate'] for m in self.metrics_history]
        all_response_times = [m['avg_response_time'] for m in self.metrics_history if m['avg_response_time'] > 0]
        
        if all_availabilities:
            print(f"📈 Overall Availability: {statistics.mean(all_availabilities):.1%}")
            print(f"📉 Overall Error Rate: {statistics.mean(all_error_rates):.1%}")
            
        if all_response_times:
            print(f"⚡ Average Response Time: {statistics.mean(all_response_times):.1f}ms")
            print(f"⚡ Max Response Time: {max(all_response_times):.1f}ms")
        
        print(f"📊 Total Samples: {len(self.metrics_history)}")
        print(f"🚨 Rollback Triggered: {'Yes' if self.rollback_triggered else 'No'}")
        print(f"📧 Alerts Sent: {len(self.alerts_sent)}")
        
        if self.alerts_sent:
            print(f"\n🚨 Alert Categories:")
            for alert_key in self.alerts_sent:
                print(f"   • {alert_key}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Deployment monitoring and alerting for SFDC Function App",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python deployment_monitoring.py --app func-atomsec-sfdc-dev --resource-group atomsec-dev-backend
  python deployment_monitoring.py --app func-atomsec-sfdc-prod --resource-group atomsec-prod-backend --duration 3600
  python deployment_monitoring.py --app func-atomsec-sfdc-staging --resource-group atomsec-staging-backend --interval 60
        """
    )
    
    parser.add_argument('--app', '-a', required=True,
                       help='Function App name')
    parser.add_argument('--resource-group', '-g', required=True,
                       help='Resource group name')
    parser.add_argument('--subscription', '-s',
                       help='Azure subscription ID')
    parser.add_argument('--environment', '-e', default='dev',
                       choices=['dev', 'staging', 'prod'],
                       help='Environment (default: dev)')
    parser.add_argument('--duration', '-d', type=int,
                       help='Monitoring duration in seconds (default: continuous)')
    parser.add_argument('--interval', '-i', type=int, default=30,
                       help='Monitoring interval in seconds (default: 30)')
    parser.add_argument('--error-rate-warning', type=float, default=0.05,
                       help='Error rate warning threshold (default: 0.05)')
    parser.add_argument('--error-rate-critical', type=float, default=0.10,
                       help='Error rate critical threshold (default: 0.10)')
    parser.add_argument('--response-time-warning', type=int, default=3000,
                       help='Response time warning threshold in ms (default: 3000)')
    parser.add_argument('--response-time-critical', type=int, default=5000,
                       help='Response time critical threshold in ms (default: 5000)')
    
    args = parser.parse_args()
    
    try:
        # Create monitor
        monitor = DeploymentMonitor(
            args.app,
            args.resource_group,
            args.subscription,
            args.environment
        )
        
        # Configure thresholds
        monitor.monitoring_interval = args.interval
        monitor.error_rate_warning = args.error_rate_warning
        monitor.error_rate_critical = args.error_rate_critical
        monitor.response_time_warning = args.response_time_warning
        monitor.response_time_critical = args.response_time_critical
        
        # Start monitoring
        success = monitor.start_monitoring(args.duration)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Monitoring interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Monitoring failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()