"""
Queue Creation Script

This script creates the required Azure Storage queues for the AtomSec application,
including the main task queues and their poison queue counterparts.

Usage:
    python create_queues.py
"""

import os
import sys
import logging
from azure.storage.queue import QueueServiceClient, QueueClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('create_queues')

# Use the connection string from local.settings.json for local development
# For Azurite emulator
LOCAL_CONNECTION_STRING = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;"

# Queue names to create
QUEUE_NAMES = [
    "task-queue",  # Default queue
    "task-queue-high",
    "task-queue-medium",
    "task-queue-low"
]

def get_connection_string():
    """Get the appropriate connection string based on environment"""
    # Check if we're running in Azure
    if 'AZURE_FUNCTIONS_ENVIRONMENT' in os.environ:
        # Use the Azure WebJobs storage connection string
        conn_str = os.environ.get('AzureWebJobsStorage')
        if not conn_str:
            logger.error("AzureWebJobsStorage connection string not found")
            sys.exit(1)
        return conn_str

    # Check if local.settings.json exists and try to read it
    try:
        import json
        with open('local.settings.json', 'r') as f:
            settings = json.load(f)
            conn_str = settings.get('Values', {}).get('AzureWebJobsStorage')
            if conn_str:
                logger.info("Using connection string from local.settings.json")
                return conn_str
    except Exception as e:
        logger.warning(f"Could not read local.settings.json: {str(e)}")

    # Fall back to the hardcoded local connection string
    logger.info("Using default local connection string for Azurite")
    return LOCAL_CONNECTION_STRING

def create_queue(queue_service, queue_name):
    """Create a queue if it doesn't exist"""
    try:
        logger.info(f"Creating queue: {queue_name}")
        # Get a queue client
        queue_client = queue_service.get_queue_client(queue_name)

        # Create the queue
        try:
            queue_client.create_queue()
            logger.info(f"Queue created: {queue_name}")
        except Exception as create_error:
            # If the queue already exists, that's fine
            if "QueueAlreadyExists" in str(create_error) or "ResourceExistsError" in str(create_error):
                logger.info(f"Queue already exists: {queue_name}")
            else:
                logger.error(f"Error creating queue {queue_name}: {str(create_error)}")
                return False

        # Verify the queue exists by getting properties
        try:
            properties = queue_client.get_queue_properties()
            logger.info(f"Queue {queue_name} properties: {properties.approximate_message_count} messages")
        except Exception as prop_error:
            logger.error(f"Error getting queue properties for {queue_name}: {str(prop_error)}")
            # Continue anyway, as the queue might still be accessible

        return True
    except Exception as e:
        logger.error(f"Error initializing queue client for {queue_name}: {str(e)}")
        return False

def create_all_queues():
    """Create all required queues"""
    # Get connection string
    connection_string = get_connection_string()

    # Create queue service client
    try:
        queue_service = QueueServiceClient.from_connection_string(connection_string)
        logger.info("Connected to Queue service")
    except Exception as e:
        logger.error(f"Error connecting to Queue service: {str(e)}")
        return False

    # Create main queues
    success = True
    for queue_name in QUEUE_NAMES:
        if not create_queue(queue_service, queue_name):
            success = False

        # Create poison queue (Azure Functions convention is to append "-poison")
        poison_queue_name = f"{queue_name}-poison"
        if not create_queue(queue_service, poison_queue_name):
            success = False

    return success

def list_queues():
    """List all queues in the storage account"""
    # Get connection string
    connection_string = get_connection_string()

    # Create queue service client
    try:
        queue_service = QueueServiceClient.from_connection_string(connection_string)
    except Exception as e:
        logger.error(f"Error connecting to Queue service: {str(e)}")
        return

    # List queues
    logger.info("Listing all queues:")
    try:
        queues = queue_service.list_queues()
        for queue in queues:
            # Get the queue client
            queue_client = queue_service.get_queue_client(queue.name)

            # Get queue properties
            properties = queue_client.get_queue_properties()
            logger.info(f"Queue: {queue.name}, Messages: {properties.approximate_message_count}")
    except Exception as e:
        logger.error(f"Error listing queues: {str(e)}")

if __name__ == "__main__":
    logger.info("Starting queue creation process...")

    if create_all_queues():
        logger.info("All queues created successfully")
    else:
        logger.warning("Some queues could not be created")

    # List all queues to verify
    list_queues()

    logger.info("Queue creation process completed")
