"""
Service Bus Event Processor for SFDC Service

This module handles incoming events from Azure Service Bus and processes them
based on the event type. This replaces the queue-based polling mechanism
with an event-driven approach.
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, Any, Optional

from src.shared.background_processor import (
    BackgroundProcessor,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_PROFILES,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_STATUS_RUNNING,
    TASK_STATUS_FAILED
)
from src.shared.utils import create_json_response
from src.shared.cors_middleware import cors_middleware
from src.task_processor import (
    process_metadata_extraction_task,
    process_health_check_task,
    process_overview_task,
    process_profiles_task,
    process_sfdc_authenticate_task
)

logger = logging.getLogger(__name__)

# Create blueprint for Service Bus functions
bp = func.Blueprint()

# Only register Service Bus trigger if connection string is available
# For local development without Service Bus, we'll use the mock endpoint instead
import os
if os.getenv('AzureServiceBusConnectionString'):
    @bp.service_bus_topic_trigger(
        arg_name="msg",
        topic_name="atomsec-tasks",
        subscription_name="sfdc-service",
        connection="AzureServiceBusConnectionString"
    )
    def process_service_bus_event(msg: func.ServiceBusMessage) -> None:
        """
        Process events from Service Bus topic

        Args:
            msg: Service Bus message containing event data
        """
        try:
            # Parse message body
            message_body = msg.get_body().decode('utf-8')
            event_data = json.loads(message_body)

            logger.info(f"Received Service Bus event: {event_data.get('eventType')}")

            # Extract event information
            event_type = event_data.get('eventType')
            event_id = event_data.get('eventId')
            data = event_data.get('data', {})

            # Process based on event type
            if event_type == "TaskCreated":
                process_task_created_event(data)
            elif event_type == "TaskUpdated":
                process_task_updated_event(data)
            elif event_type == "TaskCompleted":
                process_task_completed_event(data)
            else:
                logger.warning(f"Unknown event type: {event_type}")

        except Exception as e:
            logger.error(f"Error processing Service Bus event: {str(e)}")
            # Don't re-raise to avoid message retry loops
            # The message will be moved to dead letter queue after max retries
else:
    logger.info("Service Bus connection string not found. Service Bus trigger disabled for local development.")

def process_task_created_event(data: Dict[str, Any]) -> None:
    """
    Process TaskCreated event - MONITORING ONLY for local development

    This function only logs events for monitoring purposes.
    Actual task processing is handled by Azure Storage Queue triggers to prevent duplicates.

    Args:
        data: Event data containing task information
    """
    task_id = data.get('task_id')
    task_type = data.get('task_type')
    org_id = data.get('org_id')
    priority = data.get('priority', 'medium')

    logger.info(f"Service Bus Mock: Received TaskCreated event for task {task_id}")
    logger.info(f"  Task Type: {task_type}")
    logger.info(f"  Org ID: {org_id}")
    logger.info(f"  Priority: {priority}")
    logger.info(f"Service Bus Mock: Event logged - actual processing handled by queue triggers")

    # Only log the event for monitoring - do not process the task
    # Tasks are processed by the queue trigger functions (task_processor_high, etc.)
    return

def process_task_updated_event(data: Dict[str, Any]) -> None:
    """
    Process TaskUpdated event

    Args:
        data: Event data containing task update information
    """
    try:
        task_id = data.get('task_id')
        status = data.get('status')

        logger.info(f"Processing TaskUpdated event for task {task_id}, status: {status}")

        # Handle task updates if needed
        # For now, just log the event

    except Exception as e:
        logger.error(f"Error in process_task_updated_event: {str(e)}")

def process_task_completed_event(data: Dict[str, Any]) -> None:
    """
    Process TaskCompleted event

    Args:
        data: Event data containing task completion information
    """
    try:
        task_id = data.get('task_id')
        result = data.get('result')

        logger.info(f"Processing TaskCompleted event for task {task_id}")

        # Handle task completion if needed
        # For now, just log the event

    except Exception as e:
        logger.error(f"Error in process_task_completed_event: {str(e)}")

# Mock Service Bus endpoint for local testing
@bp.route(route="servicebus/mock", methods=["POST"])
def mock_service_bus_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """
    Mock Service Bus endpoint for local testing

    This endpoint receives HTTP requests from the mock Service Bus client
    and processes them as if they were Service Bus messages.

    Args:
        req: HTTP request containing event data

    Returns:
        func.HttpResponse: Processing result
    """
    try:
        # Parse request body
        try:
            event_data = req.get_json()
        except ValueError:
            logger.error("Invalid JSON in mock Service Bus request")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid JSON"}),
                mimetype="application/json",
                status_code=400
            )

        if not event_data:
            logger.error("Empty request body in mock Service Bus request")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Empty request body"}),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Mock Service Bus: Received event - {event_data.get('eventType')}")

        # Process the event using the same logic as the real Service Bus trigger
        event_type = event_data.get('eventType')
        data = event_data.get('data', {})

        if event_type == "TaskCreated":
            process_task_created_event(data)
        elif event_type == "TaskUpdated":
            process_task_updated_event(data)
        elif event_type == "TaskCompleted":
            process_task_completed_event(data)
        else:
            logger.warning(f"Unknown event type in mock Service Bus: {event_type}")

        response_data = {
            "success": True,
            "message": "Event processed successfully",
            "eventType": event_type,
            "timestamp": datetime.now().isoformat()
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        return cors_middleware(req, response)

    except Exception as e:
        logger.error(f"Error in mock Service Bus endpoint: {str(e)}")

        error_data = {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

        response = func.HttpResponse(
            json.dumps(error_data),
            mimetype="application/json",
            status_code=500
        )

        return cors_middleware(req, response)

# Health check endpoint for Service Bus processing
@bp.route(route="servicebus/health", methods=["GET"])
def service_bus_health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for Service Bus processing

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: Health status response
    """
    try:
        health_data = {
            "service": "SFDC Service Bus Processor",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }

        response = func.HttpResponse(
            json.dumps(create_json_response(health_data, 200)),
            mimetype="application/json",
            status_code=200
        )

        return cors_middleware(req, response)

    except Exception as e:
        logger.error(f"Error in service bus health check: {str(e)}")

        error_data = {
            "service": "SFDC Service Bus Processor",
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

        response = func.HttpResponse(
            json.dumps(create_json_response(error_data, 500)),
            mimetype="application/json",
            status_code=500
        )

        return cors_middleware(req, response)
