# PowerShell deployment script for SFDC Function App infrastructure
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$SubscriptionId,
    
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "Starting deployment for environment: $Environment" -ForegroundColor Green

try {
    # Set Azure context
    Write-Host "Setting Azure subscription context..." -ForegroundColor Yellow
    az account set --subscription $SubscriptionId
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to set Azure subscription context"
    }
    
    # Create resource group if it doesn't exist
    Write-Host "Ensuring resource group exists..." -ForegroundColor Yellow
    $rgExists = az group exists --name $ResourceGroupName --output tsv
    
    if ($rgExists -eq "false") {
        Write-Host "Creating resource group: $ResourceGroupName" -ForegroundColor Yellow
        az group create --name $ResourceGroupName --location $Location --tags Environment=$Environment Service=sfdc Application=atomsec
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create resource group"
        }
    }
    
    # Build deployment command
    $deploymentName = "sfdc-infrastructure-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $parameterFile = "parameters/$Environment.bicepparam"
    
    $deployCommand = @(
        "az", "deployment", "group", "create"
        "--resource-group", $ResourceGroupName
        "--name", $deploymentName
        "--template-file", "main.bicep"
        "--parameters", $parameterFile
        "--verbose"
    )
    
    if ($WhatIf) {
        $deployCommand += "--what-if"
        Write-Host "Running what-if deployment..." -ForegroundColor Yellow
    } else {
        Write-Host "Running actual deployment..." -ForegroundColor Yellow
    }
    
    # Execute deployment
    & $deployCommand[0] $deployCommand[1..($deployCommand.Length-1)]
    
    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed"
    }
    
    if (-not $WhatIf) {
        # Get deployment outputs
        Write-Host "Retrieving deployment outputs..." -ForegroundColor Yellow
        $outputs = az deployment group show --resource-group $ResourceGroupName --name $deploymentName --query "properties.outputs" --output json | ConvertFrom-Json
        
        Write-Host "Deployment completed successfully!" -ForegroundColor Green
        Write-Host "Deployment outputs:" -ForegroundColor Cyan
        
        if ($outputs.functionAppName) {
            Write-Host "  Function App Name: $($outputs.functionAppName.value)" -ForegroundColor White
        }
        if ($outputs.functionAppUrl) {
            Write-Host "  Function App URL: $($outputs.functionAppUrl.value)" -ForegroundColor White
        }
        if ($outputs.keyVaultName) {
            Write-Host "  Key Vault Name: $($outputs.keyVaultName.value)" -ForegroundColor White
        }
        if ($outputs.storageAccountName) {
            Write-Host "  Storage Account Name: $($outputs.storageAccountName.value)" -ForegroundColor White
        }
        if ($outputs.appInsightsName) {
            Write-Host "  Application Insights Name: $($outputs.appInsightsName.value)" -ForegroundColor White
        }
        if ($outputs.serviceBusNamespaceName) {
            Write-Host "  Service Bus Namespace: $($outputs.serviceBusNamespaceName.value)" -ForegroundColor White
        }
        
        # Save outputs to file for CI/CD pipeline
        $outputsFile = "deployment-outputs-$Environment.json"
        $outputs | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputsFile -Encoding UTF8
        Write-Host "Deployment outputs saved to: $outputsFile" -ForegroundColor Cyan
    }
}
catch {
    Write-Host "Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Deployment script completed successfully!" -ForegroundColor Green