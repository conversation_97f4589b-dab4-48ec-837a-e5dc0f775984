#!/bin/bash

# Bash deployment script for SFDC Function App infrastructure
set -e

# Function to display usage
usage() {
    echo "Usage: $0 -e <environment> -s <subscription-id> -g <resource-group> [-l <location>] [-w]"
    echo "  -e, --environment    Environment (dev, staging, prod)"
    echo "  -s, --subscription   Azure subscription ID"
    echo "  -g, --resource-group Resource group name"
    echo "  -l, --location       Azure region (default: East US)"
    echo "  -w, --what-if        Run what-if deployment"
    echo "  -h, --help           Show this help message"
    exit 1
}

# Default values
LOCATION="East US"
WHAT_IF=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -s|--subscription)
            SUBSCRIPTION_ID="$2"
            shift 2
            ;;
        -g|--resource-group)
            RESOURCE_GROUP="$2"
            shift 2
            ;;
        -l|--location)
            LOCATION="$2"
            shift 2
            ;;
        -w|--what-if)
            WHAT_IF=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option $1"
            usage
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ENVIRONMENT" || -z "$SUBSCRIPTION_ID" || -z "$RESOURCE_GROUP" ]]; then
    echo "Error: Missing required parameters"
    usage
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "Error: Environment must be dev, staging, or prod"
    exit 1
fi

echo "Starting deployment for environment: $ENVIRONMENT"

# Set Azure context
echo "Setting Azure subscription context..."
az account set --subscription "$SUBSCRIPTION_ID"

# Create resource group if it doesn't exist
echo "Ensuring resource group exists..."
if ! az group exists --name "$RESOURCE_GROUP" --output tsv | grep -q "true"; then
    echo "Creating resource group: $RESOURCE_GROUP"
    az group create \
        --name "$RESOURCE_GROUP" \
        --location "$LOCATION" \
        --tags Environment="$ENVIRONMENT" Service=sfdc Application=atomsec
fi

# Build deployment command
DEPLOYMENT_NAME="sfdc-infrastructure-$(date +%Y%m%d-%H%M%S)"
PARAMETER_FILE="parameters/$ENVIRONMENT.bicepparam"

DEPLOY_ARGS=(
    "az" "deployment" "group" "create"
    "--resource-group" "$RESOURCE_GROUP"
    "--name" "$DEPLOYMENT_NAME"
    "--template-file" "main.bicep"
    "--parameters" "$PARAMETER_FILE"
    "--verbose"
)

if [[ "$WHAT_IF" == true ]]; then
    DEPLOY_ARGS+=("--what-if")
    echo "Running what-if deployment..."
else
    echo "Running actual deployment..."
fi

# Execute deployment
"${DEPLOY_ARGS[@]}"

if [[ "$WHAT_IF" == false ]]; then
    # Get deployment outputs
    echo "Retrieving deployment outputs..."
    OUTPUTS=$(az deployment group show \
        --resource-group "$RESOURCE_GROUP" \
        --name "$DEPLOYMENT_NAME" \
        --query "properties.outputs" \
        --output json)
    
    echo "Deployment completed successfully!"
    echo "Deployment outputs:"
    
    # Parse and display outputs
    FUNCTION_APP_NAME=$(echo "$OUTPUTS" | jq -r '.functionAppName.value // empty')
    FUNCTION_APP_URL=$(echo "$OUTPUTS" | jq -r '.functionAppUrl.value // empty')
    KEY_VAULT_NAME=$(echo "$OUTPUTS" | jq -r '.keyVaultName.value // empty')
    STORAGE_ACCOUNT_NAME=$(echo "$OUTPUTS" | jq -r '.storageAccountName.value // empty')
    APP_INSIGHTS_NAME=$(echo "$OUTPUTS" | jq -r '.appInsightsName.value // empty')
    SERVICE_BUS_NAME=$(echo "$OUTPUTS" | jq -r '.serviceBusNamespaceName.value // empty')
    
    [[ -n "$FUNCTION_APP_NAME" ]] && echo "  Function App Name: $FUNCTION_APP_NAME"
    [[ -n "$FUNCTION_APP_URL" ]] && echo "  Function App URL: $FUNCTION_APP_URL"
    [[ -n "$KEY_VAULT_NAME" ]] && echo "  Key Vault Name: $KEY_VAULT_NAME"
    [[ -n "$STORAGE_ACCOUNT_NAME" ]] && echo "  Storage Account Name: $STORAGE_ACCOUNT_NAME"
    [[ -n "$APP_INSIGHTS_NAME" ]] && echo "  Application Insights Name: $APP_INSIGHTS_NAME"
    [[ -n "$SERVICE_BUS_NAME" ]] && echo "  Service Bus Namespace: $SERVICE_BUS_NAME"
    
    # Save outputs to file for CI/CD pipeline
    OUTPUT_FILE="deployment-outputs-$ENVIRONMENT.json"
    echo "$OUTPUTS" > "$OUTPUT_FILE"
    echo "Deployment outputs saved to: $OUTPUT_FILE"
fi

echo "Deployment script completed successfully!"