// Main Bicep template for SFDC Function App infrastructure
// This template provisions all required Azure resources for the SFDC service

@description('Environment name (dev, staging, prod)')
@allowed(['dev', 'staging', 'prod'])
param environment string = 'dev'

@description('Location for all resources')
param location string = resourceGroup().location

@description('Application name prefix')
param appNamePrefix string = 'atomsec'

@description('Service name')
param serviceName string = 'sfdc'

@description('Tags to apply to all resources')
param tags object = {
  Environment: environment
  Service: serviceName
  Application: appNamePrefix
  ManagedBy: 'Bicep'
}

// Variables
var functionAppName = 'func-${appNamePrefix}-${serviceName}-${environment}'
var appServicePlanName = 'asp-${appNamePrefix}-${serviceName}-${environment}'
var storageAccountName = 'st${appNamePrefix}${serviceName}${environment}'
var keyVaultName = 'akv-${appNamePrefix}-${serviceName}-${environment}'
var appInsightsName = 'ai-${appNamePrefix}-${serviceName}-${environment}'
var logAnalyticsName = 'law-${appNamePrefix}-${serviceName}-${environment}'
var serviceBusNamespaceName = 'sb-${appNamePrefix}-${serviceName}-${environment}'

// Key Vault module
module keyVault 'modules/keyvault.bicep' = {
  name: 'keyVault'
  params: {
    keyVaultName: keyVaultName
    location: location
    tags: tags
    environment: environment
    functionAppPrincipalId: functionApp.outputs.principalId
  }
}

// Storage Account module
module storage 'modules/storage.bicep' = {
  name: 'storage'
  params: {
    storageAccountName: storageAccountName
    location: location
    tags: tags
    environment: environment
  }
}

// Application Insights module
module appInsights 'modules/appinsights.bicep' = {
  name: 'appInsights'
  params: {
    appInsightsName: appInsightsName
    logAnalyticsName: logAnalyticsName
    location: location
    tags: tags
    environment: environment
  }
}

// Service Bus module
module serviceBus 'modules/servicebus.bicep' = {
  name: 'serviceBus'
  params: {
    serviceBusNamespaceName: serviceBusNamespaceName
    location: location
    tags: tags
    environment: environment
  }
}

// Function App module
module functionApp 'modules/functionapp.bicep' = {
  name: 'functionApp'
  params: {
    functionAppName: functionAppName
    appServicePlanName: appServicePlanName
    location: location
    tags: tags
    environment: environment
    storageAccountConnectionString: storage.outputs.connectionString
    appInsightsInstrumentationKey: appInsights.outputs.instrumentationKey
    appInsightsConnectionString: appInsights.outputs.connectionString
    keyVaultUri: keyVault.outputs.keyVaultUri
    serviceBusConnectionString: serviceBus.outputs.connectionString
  }
}

// Outputs
output functionAppName string = functionApp.outputs.functionAppName
output functionAppUrl string = functionApp.outputs.functionAppUrl
output keyVaultName string = keyVault.outputs.keyVaultName
output storageAccountName string = storage.outputs.storageAccountName
output appInsightsName string = appInsights.outputs.appInsightsName
output serviceBusNamespaceName string = serviceBus.outputs.serviceBusNamespaceName