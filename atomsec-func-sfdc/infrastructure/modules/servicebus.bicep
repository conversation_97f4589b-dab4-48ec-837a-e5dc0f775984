// Service Bus Bicep module
@description('Service Bus namespace name')
param serviceBusNamespaceName string

@description('Location for resources')
param location string

@description('Environment name')
param environment string

@description('Resource tags')
param tags object

// Service Bus Namespace
resource serviceBusNamespace 'Microsoft.ServiceBus/namespaces@2022-10-01-preview' = {
  name: serviceBusNamespaceName
  location: location
  tags: tags
  sku: {
    name: environment == 'prod' ? 'Standard' : 'Basic'
    tier: environment == 'prod' ? 'Standard' : 'Basic'
  }
  properties: {
    minimumTlsVersion: '1.2'
    publicNetworkAccess: 'Enabled'
    disableLocalAuth: false
    zoneRedundant: environment == 'prod' ? true : false
  }
}

// Authorization rules
resource serviceBusAuthRule 'Microsoft.ServiceBus/namespaces/authorizationRules@2022-10-01-preview' = {
  parent: serviceBusNamespace
  name: 'RootManageSharedAccessKey'
  properties: {
    rights: [
      'Listen'
      'Manage'
      'Send'
    ]
  }
}

// Queues
resource taskProcessingQueue 'Microsoft.ServiceBus/namespaces/queues@2022-10-01-preview' = {
  parent: serviceBusNamespace
  name: 'task-processing'
  properties: {
    maxSizeInMegabytes: environment == 'prod' ? 5120 : 1024
    requiresDuplicateDetection: true
    duplicateDetectionHistoryTimeWindow: 'PT10M'
    enableBatchedOperations: true
    deadLetteringOnMessageExpiration: true
    enablePartitioning: environment == 'prod' ? true : false
    maxDeliveryCount: 3
    defaultMessageTimeToLive: 'P14D'
    lockDuration: 'PT5M'
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    enableExpress: false
    forwardTo: ''
    forwardDeadLetteredMessagesTo: ''
  }
}

resource scanResultsQueue 'Microsoft.ServiceBus/namespaces/queues@2022-10-01-preview' = {
  parent: serviceBusNamespace
  name: 'scan-results'
  properties: {
    maxSizeInMegabytes: environment == 'prod' ? 5120 : 1024
    requiresDuplicateDetection: true
    duplicateDetectionHistoryTimeWindow: 'PT10M'
    enableBatchedOperations: true
    deadLetteringOnMessageExpiration: true
    enablePartitioning: environment == 'prod' ? true : false
    maxDeliveryCount: 3
    defaultMessageTimeToLive: 'P14D'
    lockDuration: 'PT5M'
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    enableExpress: false
    forwardTo: ''
    forwardDeadLetteredMessagesTo: ''
  }
}

resource notificationQueue 'Microsoft.ServiceBus/namespaces/queues@2022-10-01-preview' = {
  parent: serviceBusNamespace
  name: 'notifications'
  properties: {
    maxSizeInMegabytes: environment == 'prod' ? 2048 : 512
    requiresDuplicateDetection: false
    enableBatchedOperations: true
    deadLetteringOnMessageExpiration: true
    enablePartitioning: environment == 'prod' ? true : false
    maxDeliveryCount: 5
    defaultMessageTimeToLive: 'P7D'
    lockDuration: 'PT1M'
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    enableExpress: true
    forwardTo: ''
    forwardDeadLetteredMessagesTo: ''
  }
}

// Topics for pub/sub scenarios
resource taskCompletionTopic 'Microsoft.ServiceBus/namespaces/topics@2022-10-01-preview' = if (environment == 'prod') {
  parent: serviceBusNamespace
  name: 'task-completion'
  properties: {
    maxSizeInMegabytes: 1024
    requiresDuplicateDetection: true
    duplicateDetectionHistoryTimeWindow: 'PT10M'
    enableBatchedOperations: true
    enablePartitioning: true
    defaultMessageTimeToLive: 'P14D'
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    enableExpress: false
    supportOrdering: false
  }
}

resource dbServiceSubscription 'Microsoft.ServiceBus/namespaces/topics/subscriptions@2022-10-01-preview' = if (environment == 'prod') {
  parent: taskCompletionTopic
  name: 'db-service'
  properties: {
    maxDeliveryCount: 3
    deadLetteringOnMessageExpiration: true
    deadLetteringOnFilterEvaluationExceptions: true
    defaultMessageTimeToLive: 'P14D'
    lockDuration: 'PT5M'
    enableBatchedOperations: true
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    forwardTo: ''
    forwardDeadLetteredMessagesTo: ''
  }
}

resource frontendSubscription 'Microsoft.ServiceBus/namespaces/topics/subscriptions@2022-10-01-preview' = if (environment == 'prod') {
  parent: taskCompletionTopic
  name: 'frontend'
  properties: {
    maxDeliveryCount: 5
    deadLetteringOnMessageExpiration: true
    deadLetteringOnFilterEvaluationExceptions: true
    defaultMessageTimeToLive: 'P7D'
    lockDuration: 'PT1M'
    enableBatchedOperations: true
    autoDeleteOnIdle: 'P10675199DT2H48M5.4775807S'
    forwardTo: ''
    forwardDeadLetteredMessagesTo: ''
  }
}

// Diagnostic settings
resource serviceBusDiagnostics 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
  name: '${serviceBusNamespaceName}-diagnostics'
  scope: serviceBusNamespace
  properties: {
    logs: [
      {
        categoryGroup: 'allLogs'
        enabled: true
        retentionPolicy: {
          enabled: true
          days: environment == 'prod' ? 90 : 30
        }
      }
    ]
    metrics: [
      {
        category: 'AllMetrics'
        enabled: true
        retentionPolicy: {
          enabled: true
          days: environment == 'prod' ? 90 : 30
        }
      }
    ]
  }
}

// Outputs
output serviceBusNamespaceName string = serviceBusNamespace.name
output serviceBusNamespaceId string = serviceBusNamespace.id
output connectionString string = serviceBusAuthRule.listKeys().primaryConnectionString
output primaryKey string = serviceBusAuthRule.listKeys().primaryKey