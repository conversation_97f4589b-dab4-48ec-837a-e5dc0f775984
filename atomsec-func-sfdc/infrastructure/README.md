# SFDC Function App Infrastructure

This directory contains Infrastructure as Code (IaC) templates for deploying the AtomSec SFDC Function App and its supporting Azure resources.

## Architecture Overview

The infrastructure includes:

- **Azure Function App** - Main application hosting
- **App Service Plan** - Compute resources for the Function App
- **Azure Storage Account** - Function App storage, blob storage, and queues
- **Azure Key Vault** - Secure secrets management
- **Application Insights** - Application monitoring and telemetry
- **Log Analytics Workspace** - Centralized logging
- **Service Bus Namespace** - Message queuing and pub/sub
- **Auto-scaling** - Automatic scaling based on metrics (production only)
- **Staging Slots** - Blue-green deployment support

## File Structure

```
infrastructure/
├── main.bicep                    # Main Bicep template
├── modules/                      # Bicep modules
│   ├── functionapp.bicep        # Function App and App Service Plan
│   ├── keyvault.bicep           # Key Vault and secrets
│   ├── storage.bicep            # Storage Account and containers
│   ├── appinsights.bicep        # Application Insights and alerting
│   └── servicebus.bicep         # Service Bus namespace and queues
├── parameters/                   # Environment-specific parameters
│   ├── dev.bicepparam           # Development environment
│   ├── staging.bicepparam       # Staging environment
│   └── prod.bicepparam          # Production environment
├── deploy.ps1                   # PowerShell deployment script
├── deploy.sh                    # Bash deployment script
└── README.md                    # This file
```

## Prerequisites

1. **Azure CLI** - Install and configure Azure CLI
2. **Bicep CLI** - Install Bicep CLI extension
3. **Permissions** - Contributor access to the target subscription/resource group
4. **PowerShell** (for Windows) or **Bash** (for Linux/macOS)

### Install Prerequisites

```bash
# Install Azure CLI (if not already installed)
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install Bicep CLI
az bicep install

# Login to Azure
az login

# Set default subscription (optional)
az account set --subscription "your-subscription-id"
```

## Deployment

### Using Bash Script (Recommended for Linux/macOS)

```bash
# Make script executable
chmod +x deploy.sh

# Deploy to development environment
./deploy.sh -e dev -s "your-subscription-id" -g "atomsec-dev-backend"

# Deploy to staging environment
./deploy.sh -e staging -s "your-subscription-id" -g "atomsec-staging-backend"

# Deploy to production environment
./deploy.sh -e prod -s "your-subscription-id" -g "atomsec-prod-backend"

# Run what-if deployment (preview changes)
./deploy.sh -e dev -s "your-subscription-id" -g "atomsec-dev-backend" --what-if
```

### Using PowerShell Script (Windows)

```powershell
# Deploy to development environment
.\deploy.ps1 -Environment dev -SubscriptionId "your-subscription-id" -ResourceGroupName "atomsec-dev-backend"

# Deploy to staging environment
.\deploy.ps1 -Environment staging -SubscriptionId "your-subscription-id" -ResourceGroupName "atomsec-staging-backend"

# Deploy to production environment
.\deploy.ps1 -Environment prod -SubscriptionId "your-subscription-id" -ResourceGroupName "atomsec-prod-backend"

# Run what-if deployment (preview changes)
.\deploy.ps1 -Environment dev -SubscriptionId "your-subscription-id" -ResourceGroupName "atomsec-dev-backend" -WhatIf
```

### Using Azure CLI Directly

```bash
# Deploy to development environment
az deployment group create \
  --resource-group "atomsec-dev-backend" \
  --template-file main.bicep \
  --parameters parameters/dev.bicepparam \
  --verbose

# Run what-if deployment
az deployment group what-if \
  --resource-group "atomsec-dev-backend" \
  --template-file main.bicep \
  --parameters parameters/dev.bicepparam
```

## Environment Configuration

### Development Environment
- **SKU**: Consumption plan (Y1)
- **Scaling**: Basic auto-scaling
- **Retention**: 30 days for logs and metrics
- **Alerting**: Basic error and performance alerts
- **Security**: Standard Key Vault, basic encryption

### Staging Environment
- **SKU**: Consumption plan (Y1)
- **Scaling**: Enhanced auto-scaling
- **Retention**: 60 days for logs and metrics
- **Alerting**: Enhanced alerting with multiple thresholds
- **Security**: Standard Key Vault, enhanced encryption

### Production Environment
- **SKU**: Premium plan (P1v3)
- **Scaling**: Advanced auto-scaling with multiple metrics
- **Retention**: 90 days for logs and metrics
- **Alerting**: Comprehensive alerting with action groups
- **Security**: Premium Key Vault, full encryption, purge protection
- **Redundancy**: Zone-redundant storage and Service Bus

## Post-Deployment Configuration

After successful deployment, you need to:

1. **Update Key Vault Secrets**:
   ```bash
   # Set SQL connection string
   az keyvault secret set --vault-name "akv-atomsec-sfdc-dev" --name "SQL-CONNECTION-STRING" --value "your-sql-connection-string"
   
   # Set JWT secret key
   az keyvault secret set --vault-name "akv-atomsec-sfdc-dev" --name "JWT-SECRET-KEY" --value "your-jwt-secret-key"
   
   # Set Azure AD client secret
   az keyvault secret set --vault-name "akv-atomsec-sfdc-dev" --name "AZURE-AD-CLIENT-SECRET" --value "your-azure-ad-client-secret"
   
   # Set DB service API key
   az keyvault secret set --vault-name "akv-atomsec-sfdc-dev" --name "DB-SERVICE-API-KEY" --value "your-db-service-api-key"
   ```

2. **Configure Application Settings** (if needed):
   ```bash
   # Add additional app settings
   az functionapp config appsettings set \
     --name "func-atomsec-sfdc-dev" \
     --resource-group "atomsec-dev-backend" \
     --settings "CUSTOM_SETTING=value"
   ```

3. **Set up Continuous Deployment** (if using Azure DevOps):
   - Configure service connections
   - Update pipeline variables with deployment outputs
   - Test deployment pipeline

## Monitoring and Alerting

The infrastructure includes comprehensive monitoring:

### Application Insights
- **Custom Metrics**: Request duration, error rates, dependency calls
- **Live Metrics**: Real-time performance monitoring
- **Distributed Tracing**: End-to-end request tracking

### Alert Rules
- **High Error Rate**: Triggers when error rate exceeds threshold
- **High Response Time**: Alerts on slow response times
- **Low Availability**: Critical alert for availability issues

### Action Groups
- **Critical Alerts**: Immediate notification for critical issues
- **Warning Alerts**: Standard notifications for warnings

## Security Features

### Key Vault
- **Managed Identity**: Function App uses system-assigned managed identity
- **Access Policies**: Least privilege access to secrets
- **Audit Logging**: All secret access is logged
- **Soft Delete**: Protection against accidental deletion

### Function App Security
- **HTTPS Only**: All traffic forced to HTTPS
- **TLS 1.2**: Minimum TLS version enforced
- **Security Headers**: HSTS, X-Frame-Options, etc.
- **CORS**: Configured for specific origins only

### Storage Security
- **Encryption**: All data encrypted at rest and in transit
- **Access Control**: No public blob access
- **Soft Delete**: Protection for blobs and containers
- **Audit Logging**: All storage operations logged

## Troubleshooting

### Common Issues

1. **Deployment Fails with Permission Error**:
   - Ensure you have Contributor access to the subscription/resource group
   - Check if resource providers are registered

2. **Key Vault Access Denied**:
   - Verify managed identity is properly configured
   - Check Key Vault access policies

3. **Function App Won't Start**:
   - Check Application Insights for error logs
   - Verify all required app settings are configured
   - Ensure storage account connection string is valid

### Useful Commands

```bash
# Check deployment status
az deployment group show --resource-group "atomsec-dev-backend" --name "deployment-name"

# View Function App logs
az functionapp log tail --name "func-atomsec-sfdc-dev" --resource-group "atomsec-dev-backend"

# Test Function App health
curl https://func-atomsec-sfdc-dev.azurewebsites.net/api/health

# Check Key Vault secrets
az keyvault secret list --vault-name "akv-atomsec-sfdc-dev"
```

## Cost Optimization

### Development Environment
- Uses Consumption plan for cost efficiency
- Shorter retention periods
- Basic monitoring and alerting

### Production Environment
- Premium plan for performance and reliability
- Auto-scaling to optimize costs during low usage
- Zone redundancy for high availability

### Cost Monitoring
- Set up budget alerts in Azure Cost Management
- Monitor resource usage through Azure portal
- Review and optimize resource SKUs regularly

## Maintenance

### Regular Tasks
1. **Update Bicep Templates**: Keep templates updated with latest API versions
2. **Review Security**: Regular security reviews and updates
3. **Monitor Costs**: Regular cost analysis and optimization
4. **Update Secrets**: Rotate secrets according to security policies
5. **Review Alerts**: Ensure alerting rules are still relevant

### Backup and Recovery
- **Key Vault**: Soft delete and purge protection enabled
- **Storage**: Soft delete for blobs and containers
- **Function App**: Source code in version control
- **Configuration**: Infrastructure as Code for reproducibility

## Support

For issues with the infrastructure deployment:
1. Check the troubleshooting section above
2. Review Azure Activity Log for detailed error messages
3. Contact the DevOps team with deployment logs
4. Create an issue in the project repository