#!/usr/bin/env python3
"""
Test script for QueueMessageProcessor

This script tests the QueueMessageProcessor functionality including:
- Message processing
- Execution context extraction
- Error handling
- Message acknowledgment
"""

import json
import uuid
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# Import the module to test
from src.shared.queue_message_processor import QueueMessageProcessor, get_queue_message_processor


def test_queue_message_processor_initialization():
    """Test QueueMessageProcessor initialization"""
    print("Testing QueueMessageProcessor initialization...")
    
    try:
        # Mock all the dependencies to avoid actual Azure connections
        with patch('shared.queue_message_processor.get_task_status_service') as mock_task_service, \
             patch('shared.queue_message_processor.get_storage_connection_string') as mock_conn_string, \
             patch('azure.storage.queue.QueueClient') as mock_queue_client_class:
            
            mock_task_service.return_value = Mock()
            mock_conn_string.return_value = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test"
            
            # Mock QueueClient creation
            mock_queue_client = Mock()
            mock_queue_client.get_queue_properties.return_value = Mock()
            mock_queue_client_class.from_connection_string.return_value = mock_queue_client
            
            processor = QueueMessageProcessor()
            
            assert processor is not None
            assert processor.task_status_service is not None
            assert processor.connection_string is not None
            
            print("✓ QueueMessageProcessor initialization test passed")
            return True
            
    except Exception as e:
        print(f"✗ QueueMessageProcessor initialization test failed: {str(e)}")
        return False


def test_extract_execution_context():
    """Test execution context extraction"""
    print("Testing execution context extraction...")
    
    try:
        with patch('shared.queue_message_processor.get_task_status_service') as mock_task_service, \
             patch('shared.queue_message_processor.get_storage_connection_string') as mock_conn_string, \
             patch('azure.storage.queue.QueueClient'):
            
            mock_task_service.return_value = Mock()
            mock_conn_string.return_value = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test"
            
            processor = QueueMessageProcessor()
            
            # Test message with valid execution context
            test_message = {
                "task_id": str(uuid.uuid4()),
                "execution_log_id": str(uuid.uuid4()),
                "parent_execution_log_id": str(uuid.uuid4()),
                "task_type": "Integration_Scan",
                "org_id": "test_org",
                "user_id": "test_user",
                "priority": "high",
                "created_at": datetime.now().isoformat(),
                "retry_count": 0,
                "params": {"test_param": "test_value"}
            }
            
            context = processor.extract_execution_context(test_message)
            
            assert context is not None
            assert context["execution_log_id"] == test_message["execution_log_id"]
            assert context["parent_execution_log_id"] == test_message["parent_execution_log_id"]
            assert context["task_type"] == test_message["task_type"]
            assert context["org_id"] == test_message["org_id"]
            assert context["user_id"] == test_message["user_id"]
            
            print("✓ Execution context extraction test passed")
            return True
            
    except Exception as e:
        print(f"✗ Execution context extraction test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests"""
    print("Running QueueMessageProcessor tests...\n")
    
    tests = [
        test_queue_message_processor_initialization,
        test_extract_execution_context
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {str(e)}")
            failed += 1
        print()
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    print("Starting test execution...")
    success = run_all_tests()
    print(f"Test execution completed with success: {success}")
    exit(0 if success else 1)