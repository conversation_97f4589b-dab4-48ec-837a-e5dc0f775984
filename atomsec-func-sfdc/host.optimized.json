{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 50, "excludedTypes": "Request", "includedTypes": "Dependency;Event;PageView;Trace;Exception"}, "enableLiveMetrics": true, "enableDependencyTracking": true, "enablePerformanceCountersCollection": true, "httpAutoCollectionOptions": {"enableHttpTriggerExtendedInfoCollection": true, "enableW3CDistributedTracing": true, "enableResponseHeaderInjection": true}}, "logLevel": {"default": "Information", "Worker.Rpc": "Warning", "Function": "Information", "Host.Triggers.Queue": "Warning", "Azure.Storage": "Warning", "Azure.Core": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "console": {"isEnabled": true}, "fileLoggingMode": "debugOnly"}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:15:00", "concurrency": {"dynamicConcurrencyEnabled": true, "snapshotPersistenceEnabled": true}, "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}, "singleton": {"lockPeriod": "00:00:15", "listenerLockPeriod": "00:01:00", "listenerLockRecoveryPollingInterval": "00:01:00", "lockAcquisitionTimeout": "00:01:00", "lockAcquisitionPollingInterval": "00:00:05"}, "watchDirectories": ["shared"], "extensions": {"http": {"routePrefix": "api", "maxOutstandingRequests": 1000, "maxConcurrentRequests": 400, "dynamicThrottlesEnabled": true, "hsts": {"isEnabled": true, "maxAge": "365"}, "customHeaders": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin"}}, "queues": {"maxPollingInterval": "00:00:05", "visibilityTimeout": "00:10:00", "batchSize": 64, "maxDequeueCount": 5, "newBatchThreshold": 32, "messageEncoding": "base64"}, "blobs": {"maxDegreeOfParallelism": 16}, "serviceBus": {"prefetchCount": 200, "messageHandlerOptions": {"autoComplete": false, "maxConcurrentCalls": 64, "maxAutoRenewDuration": "00:05:00"}, "sessionHandlerOptions": {"autoComplete": false, "messageWaitTimeout": "00:00:30", "maxAutoRenewDuration": "00:05:00", "maxConcurrentSessions": 32}}, "cosmosDB": {"connectionMode": "Direct", "protocol": "Tcp", "leaseOptions": {"leasePrefix": "sfdc-service"}}, "eventHubs": {"batchCheckpointFrequency": 10, "eventProcessorOptions": {"maxBatchSize": 128, "prefetchCount": 512, "receiveTimeout": "00:01:00"}}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 5, "minimumInterval": "00:00:01", "maximumInterval": "00:01:00"}, "customHandler": {"description": {"defaultExecutablePath": "python", "workingDirectory": "", "arguments": []}, "enableForwardingHttpRequest": false}, "managedDependency": {"enabled": true}, "aggregator": {"batchSize": 1000, "flushTimeout": "00:00:30"}}