/* Reset and base styles */
html {
  font-size: 16px; /* Base font size for rem calculations */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Font imports */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Lato:wght@400;500;600&display=swap');

/* Main container */
.login-container {
  display: flex;
  min-height: 100vh;
  /* padding: 5rem 6.25rem; */
  /* gap: 5rem; */
  box-sizing: border-box;
}

/* Left side */
.login-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 1;
  padding: 4rem;
  height: 100vh;
  justify-content: center;
}

.login-logo {
  display: flex;
  align-items: center;
  gap: 1.625rem;
}

.login-logo img {
  width: 25%;
}

.login-card {
  background: #FFFFFF;
  border-radius: 0.25rem;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  opacity: 0.95;
}

.login-header {
  display: flex;
  flex-direction: column;
  /* gap: 0.5rem; */
}

.login-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1.6;
  color: #020A07;
}

.login-subtitle {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
}

/* Form styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  /* gap: 0.25rem; */
}

.form-group label {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-container input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 0.0625rem solid rgba(0, 0, 0, 0.12);
  border-radius: 0.25rem;
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
  box-sizing: border-box;
}

.input-container input::placeholder {
  color: #666666;
}

.input-container input:focus {
  outline: none;
  border-color: #51D59C;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #51D59C;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.forgot-password {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #155B55;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 0.5rem 1.5rem;
  border: none;
  border-radius: 0.25rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.6;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.5rem;
}

.azure-button {
  background: #51D59C;
  color: #020A07;
}

.azure-button:hover {
  background: #3DC488;
}

.azure-button:disabled {
  background: #A8E9CE;
  cursor: not-allowed;
}

.email-button {
  background: #51D59C;
  color: #020A07;
}

.email-button:hover {
  background: #3DC488;
}

.email-button:disabled {
  background: #A8E9CE;
  cursor: not-allowed;
}

.login-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1rem 0;
  color: #666666;
}

.login-divider::before,
.login-divider::after {
  content: '';
  flex: 1;
  border-bottom: 0.0625rem solid #E0E0E0;
}

.login-divider span {
  padding: 0 0.625rem;
  font-size: 0.875rem;
  font-family: 'Lato', sans-serif;
}

.login-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
}

.signup-link {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.6;
  color: #155B55;
  text-decoration: none;
}

.signup-link:hover {
  text-decoration: underline;
}

/* Right side */
.login-right {
  background-color: #F1FCF7;
  border-radius: 1rem;
  padding: 4rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 2.5rem;
  height: 100vh;
}

.right-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1.6;
  color: #020A07;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.feature-card {
  background-color: #CBF2E2;
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 2.0625rem;
  /* height: 11.5625rem; */
}

.feature-plus {
  font-family: 'Poppins', sans-serif;
  font-weight: 300;
  font-size: 2.5rem;
  line-height: 1.6;
  color: #020A07;
}

.feature-text {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 1.125rem;
  line-height: 1.6;
  color: #020A07;
}

/* Error message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 59, 48, 0.1);
  border-radius: 0.25rem;
  color: #FF3B30;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Responsive design */
@media screen and (max-width: 75rem) { /* 1200px */
  .login-container {
    /* padding: 3.75rem; */
    gap: 3.75rem;
  }

  .login-card {
    padding: 2.5rem;
  }

  .login-right {
    /* padding: 2.5rem; */
  }
}

@media screen and (max-width: 62rem) { /* 992px */
  .login-container {
    flex-direction: column;
    /* padding: 2.5rem 1.25rem; */
    gap: 2.5rem;
  }

  .login-left {
    gap: 2.5rem;
  }

  .login-right {
    /* padding: 2.5rem 1.25rem; */
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .login-title {
    font-size: 2.25rem;
  }

  .right-title {
    font-size: 2.25rem;
  }
}

@media screen and (max-width: 48rem) { /* 768px */
  .login-container {
    /* padding: 1.875rem 0.9375rem; */
    gap: 1.875rem;
  }

  .login-card {
    padding: 1.875rem 1.25rem;
  }

  .login-title {
    font-size: 2rem;
  }

  .right-title {
    font-size: 2rem;
  }

  .feature-card {
    height: auto;
    min-height: 9.375rem;
  }

  .login-logo img {
    height: 2.5rem;
  }

  .login-left {
    gap: 1.875rem;
  }
}

@media screen and (max-width: 36rem) { /* 576px */
  .login-container {
    /* padding: 1.25rem 0.625rem; */
  }

  .login-card {
    padding: 1.5rem 1rem;
  }

  .login-title {
    font-size: 1.75rem;
  }

  .login-subtitle {
    font-size: 0.875rem;
  }

  .form-group label {
    font-size: 0.875rem;
  }

  .input-container input {
    font-size: 0.875rem;
    padding: 0.5rem 0.625rem;
  }

  .password-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .login-button {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .login-footer {
    font-size: 0.875rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .right-title {
    font-size: 1.5rem;
  }

  .feature-text {
    font-size: 1rem;
  }

  .feature-plus {
    font-size: 2rem;
  }

  .login-right {
    /* padding: 1.875rem 0.9375rem; */
  }
}

@media screen and (max-width: 30rem) { /* 480px */
  .login-container {
    /* padding: 0.9375rem 0.5rem; */
  }

  .login-card {
    padding: 1.25rem 0.75rem;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .login-subtitle {
    font-size: 0.8125rem;
  }

  .login-logo img {
    height: 1.875rem;
  }

  .login-left {
    gap: 1.25rem;
  }

  .form-group {
    gap: 0.125rem;
  }

  .login-form {
    gap: 0.75rem;
  }

  .feature-card {
    min-height: 7.5rem;
    gap: 1.25rem;
    padding: 0.75rem;
  }
}