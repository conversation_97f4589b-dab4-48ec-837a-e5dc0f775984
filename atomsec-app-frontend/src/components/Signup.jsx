import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { signup as apiSignup } from '../api';
import './Signup.css';
import logo from '../assets/logo.svg';
import { Visibility, VisibilityOff } from '@mui/icons-material';

const Signup = () => {
  const [firstName, setFirstName] = useState('');
  const [middleName, setMiddleName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [organization, setOrganization] = useState('');
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSignup = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    // Validate password match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      // Construct the full name
      const name = `${firstName} ${middleName} ${lastName}`.replace(/\s+/g, ' ').trim();

      // Prepare user data object for the API
      const userData = {
        email,
        password,
        name,
        organization
      };

      // Call the signup API function
      const response = await apiSignup(userData);

      if (!response.success) {
        setError(response.error || 'Signup failed. Please try again.');
        setIsLoading(false);
        return;
      }

      // Store tokens if they're returned
      if (response.data && response.data.access_token) {
        localStorage.setItem('accessToken', response.data.access_token);
      }

      if (response.data && response.data.refresh_token) {
        localStorage.setItem('refreshToken', response.data.refresh_token);
      }

      if (response.data && response.data.user && response.data.user.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }

      // Set success state instead of redirecting
      setSuccess(true);
      setIsLoading(false);

      // Clear form fields
      setFirstName('');
      setMiddleName('');
      setLastName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setOrganization('');
    } catch (error) {
      setError(
        error.response?.data?.error ||
        'Signup failed. Please try again.'
      );
      setIsLoading(false);
    }
  };

  return (
    <div className="signup-container">
      <div className="signup-left-panel">
        <div className="logo-container">
          <img src={logo} alt="Atom Security Logo" className="logo" />
        </div>
        <div className="signup-card">
          <div className="signup-header">
            <h1 className="signup-title">Create your account</h1>
            <p className="signup-subtitle">Fill your details to complete your account setup</p>
          </div>

          <form onSubmit={handleSignup} className="signup-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="firstName">First Name</label>
                <input
                  type="text"
                  id="firstName"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Enter here"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="middleName">Middle Name</label>
                <input
                  type="text"
                  id="middleName"
                  value={middleName}
                  onChange={(e) => setMiddleName(e.target.value)}
                  placeholder="Enter here"
                />
              </div>

              <div className="form-group">
                <label htmlFor="lastName">Last Name</label>
                <input
                  type="text"
                  id="lastName"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Enter here"
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="organization">Organization</label>
              <input
                type="text"
                id="organization"
                value={organization}
                onChange={(e) => setOrganization(e.target.value)}
                placeholder="Enter your organization name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Work Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email address here"
                required
              />
            </div>

            <div className="form-row">
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <div className="password-input">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Create a password"
                  minLength="6"
                  required
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </button>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="confirmPassword">Confirm Password</label>
              <div className="password-input">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Re-type password"
                  minLength="6"
                  required
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                </button>
              </div>
            </div>
            </div>

            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            {success && (
              <div className="success-message">
                Account created successfully! <Link to="/login">Sign in now</Link>
              </div>
            )}

            <button
              type="submit"
              className="signup-button"
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Sign Up'}
            </button>

            <div className="login-link">
              Already have an account? <Link to="/login">Sign In</Link>
            </div>
          </form>
        </div>
      </div>

      <div className="right-panel">
        <h2 className="right-panel-title">
          Secure your<br /><span className="brand-color">Digital World.</span>
        </h2>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">+</div>
            <div className="feature-text">Detailed Vulnerability Reports</div>
          </div>
          <div className="feature-card">
            <div className="feature-icon">+</div>
            <div className="feature-text">Guided Remediation Steps</div>
          </div>
          <div className="feature-card">
            <div className="feature-icon">+</div>
            <div className="feature-text">Comprehensive Security Planning</div>
          </div>
          <div className="feature-card">
            <div className="feature-icon">+</div>
            <div className="feature-text">Compliance Monitoring</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;