import React, { useEffect, useState } from 'react';
import './SettingsDetailPage.css';
import { fetchProfilesPermissions } from '../api';

export default function SettingsDetailPage({ orgId, taskType, executionLogId }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Add sorting state and logic
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
  };

  useEffect(() => {
    setLoading(true);
    setError(null);
    setData(null);
    fetchProfilesPermissions(orgId, executionLogId)
      .then(json => {
        setData(json);
        setLoading(false);
      })
      .catch(err => {
        setError('Failed to fetch details');
        setLoading(false);
      });
  }, [orgId, taskType, executionLogId]);

  // Extract all discrepancies (flattened)
  let rows = [];
  if (data && data.policies) {
    data.policies.forEach(policy => {
      let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
      if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
        profileName = policy.RowKey.split('-')[0];
      }
      try {
        const arr = JSON.parse(policy.OrgValue);
        arr.forEach(s => {
          rows.push({
            profileName,
            ...s
          });
        });
      } catch {}
    });
  }

  // Instead of grouping by profile, flatten to one row per profile+setting, and show Setting Name = Value in Org/Standard columns
  const detailRows = rows.map(row => ({
    profileName: row.profileName,
    settingName: row.SalesforceSetting,
    orgValue: row.OrgValue,
    standardValue: row.StandardValue,
    owasp: row.OWASP,
    risk: row.Risk || 'High',
    isRisk: row.Issue && row.Issue.toLowerCase().includes('missing') || row.Issue && row.Issue.toLowerCase().includes('discrepancy') || row.Issue && row.Issue.toLowerCase().includes('false')
  }));

  // Sort detailRows based on sortConfig
  const sortedRows = React.useMemo(() => {
    if (!sortConfig.key) return detailRows;
    const sorted = [...detailRows].sort((a, b) => {
      const aVal = (a[sortConfig.key] || '').toString().toLowerCase();
      const bVal = (b[sortConfig.key] || '').toString().toLowerCase();
      if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [detailRows, sortConfig]);

  // Pagination logic (10 rows per page)
  const [page, setPage] = useState(1);
  const perPage = 10;
  const totalPages = Math.ceil(sortedRows.length / perPage);
  const paginatedRows = sortedRows.slice((page - 1) * perPage, page * perPage);

  return (
    <div className="settings-detail-main-content">
      <div className="settings-detail-card">
        <div className="settings-detail-title">{taskType.replace(/_/g, ' ').toUpperCase()} - Details</div>
        {loading && <div className="settings-detail-loading">Loading...</div>}
        {error && <div className="settings-detail-error">{error}</div>}
        {!loading && !error && (
          <table className="settings-detail-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('profileName')} style={{ cursor: 'pointer' }}>
                  Profile Name {sortConfig.key === 'profileName' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                </th>
                <th onClick={() => handleSort('orgValue')} style={{ cursor: 'pointer' }}>
                  Org Value {sortConfig.key === 'orgValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                </th>
                <th onClick={() => handleSort('standardValue')} style={{ cursor: 'pointer' }}>
                  Standard Value {sortConfig.key === 'standardValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                </th>
                <th onClick={() => handleSort('owasp')} style={{ cursor: 'pointer' }}>
                  OWASP Category {sortConfig.key === 'owasp' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                </th>
                <th onClick={() => handleSort('risk')} style={{ cursor: 'pointer' }}>
                  Risk {sortConfig.key === 'risk' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedRows.length === 0 && (
                <tr><td colSpan={5} className="settings-detail-empty">No discrepancies found.</td></tr>
              )}
              {paginatedRows.map((row, i) => (
                <tr key={i}>
                  <td style={{ wordBreak: 'break-word', maxWidth: 180 }}>{row.profileName}</td>
                  <td style={{ wordBreak: 'break-word', maxWidth: 260 }}>
                    {row.orgValue == null || row.orgValue === '' ? (
                      <span>-</span>
                    ) : (
                      <>
                        <span style={{ fontWeight: 600 }}>{row.settingName}</span>
                        <span style={{ fontWeight: 600, color: '#888', margin: '0 4px' }}>=</span>
                        <span style={{ color: row.isRisk ? '#D32F2F' : '#222', fontWeight: row.isRisk ? 700 : 400 }}>{row.orgValue}</span>
                      </>
                    )}
                  </td>
                  <td style={{ wordBreak: 'break-word', maxWidth: 260 }}>
                    <span style={{ fontWeight: 600 }}>{row.settingName}</span>
                    <span style={{ fontWeight: 600, color: '#888', margin: '0 4px' }}>=</span>
                    <span>{row.standardValue}</span>
                  </td>
                  <td style={{ wordBreak: 'break-word', maxWidth: 180 }}>{row.owasp}</td>
                  <td style={{ wordBreak: 'break-word', maxWidth: 80 }}>{row.risk}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
        {/* Pagination controls */}
        {totalPages > 1 && (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: 16, gap: 16 }}>
            <button onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1} style={{ padding: '6px 16px', borderRadius: 4, border: '1px solid #E0E0E0', background: page === 1 ? '#f1f1f1' : '#fff', color: '#393E3C', cursor: page === 1 ? 'not-allowed' : 'pointer' }}>Previous</button>
            <span style={{ fontSize: 14, color: '#393E3C' }}>Page {page} of {totalPages}</span>
            <button onClick={() => setPage(p => Math.min(totalPages, p + 1))} disabled={page === totalPages} style={{ padding: '6px 16px', borderRadius: 4, border: '1px solid #E0E0E0', background: page === totalPages ? '#f1f1f1' : '#fff', color: '#393E3C', cursor: page === totalPages ? 'not-allowed' : 'pointer' }}>Next</button>
          </div>
        )}
      </div>
    </div>
  );
} 