/* Filters row */
.settings-risk-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}
.settings-risk-reset-btn {
  padding: 8px 16px;
  font-size: 16px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background: #fff;
  color: #393E3C;
  cursor: pointer;
  min-width: 120px;
}
.settings-risk-reset-btn:hover {
  background: #F8FDFB;
}

/* Pagination styles */
.settings-risk-pagination-btn {
  padding: 8px 16px;
  font-size: 14px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background: #fff;
  color: #393E3C;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-risk-pagination-btn:hover:not(:disabled) {
  background: #F8FDFB;
  border-color: #51D59C;
  color: #51D59C;
}

.settings-risk-pagination-btn:disabled {
  background: #f1f1f1;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Table styling */
.settings-risk-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  font-size: 15px;
  overflow: hidden;
}
.settings-risk-table th, .settings-risk-table td {
  padding: 14px 8px 14px 16px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  text-align: left;
}
.settings-risk-table th {
  background: rgba(0,0,0,0.04);
  color: #393E3C;
  font-weight: 600;
}
.settings-risk-table tr:last-child td {
  border-bottom: none;
}
.settings-risk-empty {
  text-align: center;
  color: #888;
  padding: 32px 0;
}
.settings-risk-view-link {
  color: #51D59C;
  text-decoration: underline;
  font-weight: 500;
  margin-left: 8px;
  transition: color 0.2s;
}
.settings-risk-view-link:hover {
  color: #393E3C;
}

.settings-risk-table-wrapper {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  padding: 32px 32px 24px 32px;
  margin: 0;
}
.settings-risk-table-title {
  font-weight: 700;
  font-size: 28px;
  color: #181C1A;
  margin-bottom: 24px;
  margin-left: 0;
  margin-top: 0;
  text-align: left;
}
.settings-risk-filters-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  margin-left: 0;
  flex-wrap: nowrap;
  width: fit-content;
}
.settings-risk-filters-row select {
  min-width: 180px;
  max-width: 240px;
  width: auto;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 16px;
  color: #393E3C;
  background: #fff;
  appearance: none;
  outline: none;
}
.settings-risk-filter-label {
  font-weight: 600;
  font-size: 16px;
  color: #181C1A;
  margin-right: 8px;
  white-space: nowrap;
}
.settings-risk-reset-btn {
  padding: 8px 24px;
  font-size: 16px;
  border: 1.5px solid #51D59C;
  border-radius: 8px;
  background: #fff;
  color: #51D59C;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.2s, color 0.2s;
  margin-left: auto;
}
.settings-risk-reset-btn:hover {
  background: #F8FDFB;
  color: #155b55;
}
.settings-risk-reset-icon {
  width: 18px;
  height: 18px;
}
.settings-risk-table th {
  background: rgba(0,0,0,0.04);
  color: #393E3C;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  padding: 14px 8px 14px 16px;
}
.settings-risk-table td {
  padding: 14px 8px 14px 16px;
  font-size: 15px;
  color: #181C1A;
}
.settings-risk-filters-row .figma-dropdown {
  min-width: 180px;
  max-width: 240px;
  width: auto;
}

.settings-risk-org-value-col {
  max-width: 120px;
  min-width: 80px;
  white-space: normal;
  word-break: break-word;
  vertical-align: top;
}

.settings-risk-standard-value-col {
  max-width: 120px;
  min-width: 80px;
  white-space: normal;
  word-break: break-word;
  vertical-align: top;
} 