/* Signup Component Styles */
html {
  font-size: 16px; /* Base font size for rem calculations */
}

.signup-container {
  display: flex;
  justify-content: space-between;
  /* gap: 5rem; */
  /* padding: 5rem 6.25rem; */
  min-height: 100vh;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

/* Left Panel Styles */
.signup-left-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 1;
  padding: 4rem;
  height: 100vh;
  justify-content: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1.625rem;
}

.logo {
  width: 25%;
}

.signup-card {
  background-color: #FFFFFF;
  border-radius: 0.25rem;
  padding: 3rem;
  box-shadow: 0px 0.25rem 1.25rem rgba(0, 0, 0, 0.1);
  opacity: 0.95;
  height: max-content;
    overflow: scroll;
}

.signup-header {
  margin-bottom: 3rem;
}

.signup-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 2rem;
  line-height: 1.6;
  color: #020A07;
  margin: 0;
}

.signup-subtitle {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
  /* margin: 0.5rem 0 0; */
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.form-group label {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
}

.form-group input {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #666666;
  padding: 0.5rem 0.75rem;
  border: 0.0625rem solid rgba(0, 0, 0, 0.12);
  border-radius: 0.25rem;
  background-color: #FFFFFF;
  width: 100%;
  box-sizing: border-box;
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 2.5rem;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #51D59C;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signup-button {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.6;
  color: #FFFFFF;
  background-color: #51D59C;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1.5rem;
  cursor: pointer;
  width: 100%;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-link {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #020A07;
  text-align: center;
  margin-top: 1rem;
}

.login-link a {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.6;
  color: #155B55;
  text-decoration: none;
}

/* Right Panel Styles */
.right-panel {
  background-color: #F1FCF7;
  border-radius: 1rem;
  padding: 4rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 2.5rem;
  height: 100vh;
}

.right-panel-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1.6;
  color: #020A07;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.feature-card {
  background-color: #CBF2E2;
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 2.0625rem;
  /* height: 11.5625rem; */
}

.feature-card:nth-child(2) {
  background-color: #A8E9CE;
}

.feature-card:nth-child(3) {
  background-color: #FFFFFF;
}

.feature-icon {
  font-family: 'Poppins', sans-serif;
  font-weight: 300;
  font-size: 2.5rem;
  line-height: 1.6;
  color: #020A07;
}

.feature-text {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.6;
  color: #020A07;
}

.error-message {
  color: #FF0000;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.brand-color{
  color: #51D59C;
}

/* Responsive Adjustments */
@media screen and (max-width: 75rem) { /* 1200px */
  .signup-container {
    /* padding: 3.75rem; */
    gap: 3.75rem;
  }

  .signup-card {
    padding: 2.5rem;
  }

  .right-panel {
    /* padding: 2.5rem; */
  }

  .signup-title {
    font-size: 2.25rem;
  }

  .right-panel-title {
    font-size: 2.25rem;
  }
}

@media screen and (max-width: 62rem) { /* 992px */
  .signup-container {
    flex-direction: column;
    /* padding: 2.5rem; */
    gap: 2.5rem;
  }

  .signup-card {
    max-width: 100%;
  }

  .right-panel {
    min-height: 25rem;
  }

  .signup-left-panel {
    gap: 2.5rem;
  }
}

@media screen and (max-width: 48rem) { /* 768px */
  .signup-container {
    /* padding: 1.875rem 1.25rem; */
  }

  .form-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .signup-title {
    font-size: 2rem;
  }

  .right-panel-title {
    font-size: 2rem;
  }

  .signup-left-panel {
    gap: 1.875rem;
  }

  .signup-card {
    padding: 1.875rem 1.25rem;
  }

  .feature-card {
    height: auto;
    min-height: 9.375rem;
  }

  .logo {
    height: 2.1875rem;
  }
}

@media screen and (max-width: 36rem) { /* 576px */
  .signup-container {
    /* padding: 1.25rem 0.625rem; */
  }

  .signup-card {
    padding: 1.5rem 1rem;
  }

  .signup-title {
    font-size: 1.75rem;
  }

  .signup-subtitle {
    font-size: 0.875rem;
  }

  .form-group label {
    font-size: 0.875rem;
  }

  .form-group input {
    font-size: 0.875rem;
    padding: 0.5rem 0.625rem;
  }

  .signup-button {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    height: 2.25rem;
  }

  .login-link {
    font-size: 0.875rem;
  }

  .login-link a {
    font-size: 0.875rem;
  }

  .right-panel {
    /* padding: 1.875rem 0.9375rem; */
  }

  .right-panel-title {
    font-size: 1.5rem;
  }

  .feature-text {
    font-size: 1rem;
  }

  .feature-icon {
    font-size: 2rem;
  }

  .signup-left-panel {
    gap: 1.25rem;
  }

  .logo {
    height: 1.875rem;
  }
}

@media screen and (max-width: 30rem) { /* 480px */
  .signup-container {
    /* padding: 0.9375rem 0.5rem; */
  }

  .signup-card {
    padding: 1.25rem 0.75rem;
  }

  .signup-title {
    font-size: 1.5rem;
  }

  .signup-subtitle {
    font-size: 0.8125rem;
  }

  .form-group {
    gap: 0.125rem;
  }

  .signup-form {
    gap: 0.75rem;
  }

  .form-row {
    gap: 0.5rem;
  }

  .feature-card {
    min-height: 7.5rem;
    gap: 1.25rem;
    padding: 0.75rem;
  }

  .signup-header {
    margin-bottom: 1.5rem;
  }

  .logo {
    height: 1.5625rem;
  }
}