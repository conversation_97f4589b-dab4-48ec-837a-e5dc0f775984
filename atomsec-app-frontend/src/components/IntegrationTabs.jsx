import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Routes, Route, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import './IntegrationTabs.css';
import {
  fetchIntegrationOverviewById,
  fetchIntegrationHealthCheckById,
  fetchIntegrationProfilesById,
  fetchIntegrationPMDById,
  fetchIntegrations,
  fetchPoliciesResultByIntegrationId
} from '../api';
import DataStatusMessage from './DataStatusMessage';
import ProfilesAndPermissionsTab from './ProfilesAndPermissionsTab';
import PMDIssuesTab from './PMDIssuesTab';
import { Pie, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const IntegrationTabs = () => {
  // Get integration ID from URL
  const { integrationId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { taskType, executionLogId } = useParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [integration, setIntegration] = useState(null);

  // Tab data states
  const [overviewData, setOverviewData] = useState(null);
  const [overviewStatus, setOverviewStatus] = useState('loading');
  const [healthCheckData, setHealthCheckData] = useState(null);
  const [healthCheckStatus, setHealthCheckStatus] = useState('loading');
  const [profilesData, setProfilesData] = useState(null);
  const [profilesStatus, setProfilesStatus] = useState('loading');
  const [pmdData, setPmdData] = useState(null);
  const [pmdStatus, setPmdStatus] = useState('loading');
  const [riskFilter, setRiskFilter] = useState('all');

  // Pie chart filter state
  const [pieFilter, setPieFilter] = React.useState(null);

  // Bar chart filter state
  const [barFilter, setBarFilter] = React.useState(null); // { group: string, risk: string } or null

  // Sorting state for table columns
  const [sortConfig, setSortConfig] = React.useState({ key: null, direction: 'asc' });

  // Add state for new filters
  const [settingGroupFilter, setSettingGroupFilter] = React.useState('');
  const [riskTypeFilter, setRiskTypeFilter] = React.useState('');

  // Policies result state for health check
  const [policiesResult, setPoliciesResult] = useState([]);
  const [policiesResultStatus, setPoliciesResultStatus] = useState('loading');

  // Pagination state for details table
  const [detailsPage, setDetailsPage] = React.useState(1);
  const detailsPerPage = 10;

  // Refresh flags
  const [isRefreshing, setIsRefreshing] = useState({
    overview: false,
    healthCheck: false,
    profiles: false,
    pmd: false,
  });

  // Global rescan state
  const [isRescanning, setIsRescanning] = useState(false);

  // Fetch integration details using the integration ID
  useEffect(() => {
    const fetchIntegrationDetails = async () => {
      if (!integrationId) {
        setError('No integration ID provided');
        toast.error('No integration ID provided');
        navigate('/integrations');
        return;
      }

      try {
        setLoading(true);
        // Fetch all integrations to find the one with matching ID
        const response = await fetchIntegrations({ include_inactive: true }); // Include inactive integrations

        // Handle different response structures
        let integrations = [];
        if (response && response.data && response.data.integrations) {
          integrations = response.data.integrations;
        } else if (response && response.integrations) {
          integrations = response.integrations;
        } else if (Array.isArray(response)) {
          integrations = response;
        }

        if (integrations && integrations.length > 0) {
          // Find the integration with the matching ID
          const foundIntegration = integrations.find(
            int => (int.id === integrationId || int.Id === integrationId || int.RowKey === integrationId)
          );

          if (foundIntegration) {
            setIntegration(foundIntegration);
            console.log(`Found integration with ID ${integrationId}`);
            console.log('Integration object properties:', Object.keys(foundIntegration));
            console.log('Integration object:', foundIntegration);
          } else {
            setError(`No integration found with ID: ${integrationId}`);
            toast.error(`No integration found with ID: ${integrationId}`);
            navigate('/integrations');
          }
        } else {
          setError('Failed to fetch integrations or no integrations found');
          toast.error('Failed to fetch integrations or no integrations found');
          navigate('/integrations');
        }
      } catch (error) {
        console.error('Error fetching integration details:', error);
        setError(`Error fetching integration details: ${error.message || 'Unknown error'}`);
        toast.error(`Error fetching integration details: ${error.message || 'Unknown error'}`);
        navigate('/integrations');
      } finally {
        setLoading(false);
      }
    };

    fetchIntegrationDetails();
  }, [integrationId, navigate]);

  // Function to fetch data for the active tab
  const fetchTabData = (tab, forceRefresh = false) => {
    setLoading(true);
    setError(null);

    switch (tab) {
      case 'overview':
        fetchOverviewData(forceRefresh);
        break;
      case 'healthCheck':
        fetchHealthCheckData(forceRefresh);
        break;
      case 'profilesPermissions':
        fetchProfilesData(forceRefresh);
        break;
      case 'pmdIssues':
        fetchPMDData(forceRefresh);
        break;
      default:
        handleTabChange('overview');
        fetchOverviewData(forceRefresh);
    }
  };

  // Fetch tab data when integration object is available
  useEffect(() => {
    if (integration && integrationId) {
      fetchTabData(activeTab);
    }
  }, [integration, integrationId, activeTab]);

  // Fetch overview data
  const fetchOverviewData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setOverviewStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: true }));
      }

      setOverviewStatus('loading');
      console.log(`Fetching overview data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      // Use the integration ID directly instead of tenant URL
      console.log('Using integration ID:', integrationId);

      const response = await fetchIntegrationOverviewById(integrationId, forceRefresh);
      console.log('Overview response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('Overview data status: pending');
        setOverviewStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchOverviewData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('Overview data status: empty');
        setOverviewStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('Overview data status: available');
        setOverviewData(response);
        setOverviewStatus('available');
      } else if (response.dataStatus === 'error') {
        console.log('Overview data status: error');
        setOverviewStatus('error');
        setError(response.message || 'Failed to fetch overview data');
      } else {
        console.log(`Unknown overview data status: ${response.dataStatus}`);
        setOverviewStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching overview data:', error);
      setError(`Failed to fetch overview data: ${error.message || 'Unknown error'}`);
      setOverviewStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: false }));
      }

      // Show error toast
      toast.error(`Error fetching overview data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch policies result data for health check
  const fetchPoliciesData = async (forceRefresh = false) => {
    setPoliciesResultStatus('loading');
    try {
      // 1. Fetch latest completed health_check task for this integration
      const { getTaskStatus } = await import('../api');
      const taskStatusData = await getTaskStatus(integrationId, 'completed', 1, 'health_check');
      console.log('[DEBUG] taskStatusData:', taskStatusData);
      const latestTask = taskStatusData?.data?.[0];
      console.log('[DEBUG] latestTask:', latestTask);

      if (!latestTask || !latestTask.execution_log_id) {
        console.log('[DEBUG] No latestTask or execution_log_id found');
        setPoliciesResultStatus('empty');
        setPoliciesResult([]);
        return;
      }

      // 2. Fetch policies result for the latest execution_log_id
      console.log('[DEBUG] Calling fetchPoliciesResultByIntegrationId with', integrationId, latestTask.execution_log_id, 'HealthCheck');
      const result = await fetchPoliciesResultByIntegrationId(integrationId, latestTask.execution_log_id, 'HealthCheck');
      console.log('[DEBUG] fetchPoliciesResultByIntegrationId result:', result);
      if (Array.isArray(result)) {
        setPoliciesResult(result);
        setPoliciesResultStatus('available');
      } else if (result && result.data && Array.isArray(result.data)) {
        setPoliciesResult(result.data);
        setPoliciesResultStatus('available');
      } else if (result && result.length === 0) {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      } else {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      }
    } catch (err) {
      console.error('[DEBUG] Error in fetchPoliciesData:', err);
      setPoliciesResultStatus('error');
      setPoliciesResult([]);
    }
  };

  // Reset to page 1 if policiesResult changes
  React.useEffect(() => {
    setDetailsPage(1);
  }, [policiesResult]);

  // Fetch health check data
  const fetchHealthCheckData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setHealthCheckStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: true }));
        // Show a toast notification that we're syncing data
        toast.info('Syncing health check data from Salesforce. This may take a moment...');
      }

      // Use policies data instead of health check data
      await fetchPoliciesData(forceRefresh);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching health check data:', error);
      setError(`Failed to fetch health check data: ${error.message || 'Unknown error'}`);
      setHealthCheckStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: false }));
      }

      // Show error toast
      toast.error(`Error fetching health check data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch profiles data
  const fetchProfilesData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setProfilesStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: true }));
      }

      setProfilesStatus('loading');
      console.log(`Fetching profiles data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      // Use the integration ID directly instead of tenant URL
      console.log('Using integration ID:', integrationId);

      const response = await fetchIntegrationProfilesById(integrationId, forceRefresh);
      console.log('Profiles response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('Profiles data status: pending');
        setProfilesStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchProfilesData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('Profiles data status: empty');
        setProfilesStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('Profiles data status: available');
        console.log('Profiles data:', response);

        // Log detailed information about the profiles and permission sets
        if (response.profiles) {
          console.log(`Profiles count: ${response.profiles.length}`);
          if (response.profiles.length > 0) {
            console.log('Sample profile:', response.profiles[0]);
            console.log('Sample profile system permissions:', response.profiles[0].systemPermissions);
          }
        } else {
          console.warn('No profiles array found in response');
        }

        if (response.permissionSets) {
          console.log(`Permission sets count: ${response.permissionSets.length}`);
          if (response.permissionSets.length > 0) {
            console.log('Sample permission set:', response.permissionSets[0]);
            console.log('Sample permission set system permissions:', response.permissionSets[0].systemPermissions);
          }
        } else {
          console.warn('No permissionSets array found in response');
        }

        setProfilesData(response);
        setProfilesStatus('available');
      } else if (response.dataStatus === 'error') {
        console.log('Profiles data status: error');
        setProfilesStatus('error');
        setError(response.message || 'Failed to fetch profiles data');
      } else {
        console.log(`Unknown profiles data status: ${response.dataStatus}`);
        setProfilesStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching profiles data:', error);
      setError(`Failed to fetch profiles data: ${error.message || 'Unknown error'}`);
      setProfilesStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }

      // Show error toast
      toast.error(`Error fetching profiles data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch PMD data
  const fetchPMDData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setPmdStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: true }));
      }

      setPmdStatus('loading');
      console.log(`Fetching PMD data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      const response = await fetchIntegrationPMDById(integrationId, forceRefresh);
      console.log('PMD response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('PMD data status: pending');
        setPmdStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchPMDData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('PMD data status: empty');
        setPmdStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('PMD data status: available');
        console.log('PMD data:', response);

        setPmdData(response);
        setPmdStatus('available');
      } else {
        console.warn('Unknown PMD data status:', response.dataStatus);
        setPmdStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching PMD data:', error);
      setError(`Failed to fetch PMD data: ${error.message || 'Unknown error'}`);
      setPmdStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: false }));
      }

      // Show error toast
      toast.error(`Error fetching PMD data: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // This function is used in the tab components
  const handleRefresh = () => {
    fetchTabData(activeTab, true);
  };

  // Handle global rescan button click
  const handleRescan = async () => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        toast.error('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      setIsRescanning(true);
      const loadingToast = toast.info('Rescanning Salesforce org. This may take a few moments...', {
        autoClose: false
      });

      // Rescan the org using the integration ID
      console.log('Attempting to rescan org with integration ID:', integrationId);

      let response;

      try {
        console.log('Using scanIntegration with the integration ID');
        const { scanIntegration } = await import('../api');

        console.log('Using integration ID for scan:', integrationId);

        // Update toast to show progress
        toast.update(loadingToast, {
          render: 'Scanning integration... This may take up to 2 minutes.',
          type: 'info',
          autoClose: false
        });

        response = await scanIntegration(integrationId);
        console.log('Scan response:', response);
      } catch (scanError) {
        console.error('Scan failed:', scanError);
        response = {
          success: false,
          error: scanError.message || 'Unknown error during scan'
        };
      }

      console.log('Final rescan response:', response);

      if (response && response.success === false) {
        // Handle different types of errors
        if (response.isTimeout) {
          // Special handling for timeout errors
          toast.update(loadingToast, {
            render: 'Scan is taking longer than expected. The scan is still running in the background. Please check back in a few minutes.',
            type: 'warning',
            autoClose: 8000
          });

          // Set all tabs to pending status since scan might still be running
          setOverviewStatus('pending');
          setHealthCheckStatus('pending');
          setProfilesStatus('pending');
          setPmdStatus('pending');

          // Schedule refreshes to check for completed data
          setTimeout(() => fetchTabData(activeTab), 30000);  // 30 seconds
          setTimeout(() => fetchTabData(activeTab), 60000);  // 1 minute
          setTimeout(() => fetchTabData(activeTab), 120000); // 2 minutes
        } else {
          // Handle other error responses
          toast.update(loadingToast, {
            render: `Failed to initiate rescan: ${response.error || 'Unknown error'}`,
            type: 'error',
            autoClose: 5000
          });
        }
      } else if (response && (response.success || response.data)) {
        toast.update(loadingToast, {
          render: 'Rescan completed successfully. Data will be updated shortly.',
          type: 'success',
          autoClose: 5000
        });

        // Set all tabs to pending status
        setOverviewStatus('pending');
        setHealthCheckStatus('pending');
        setProfilesStatus('pending');
        setPmdStatus('pending');

        // Schedule a refresh of the current tab after a delay
        setTimeout(() => {
          fetchTabData(activeTab);
        }, 5000);

        // Schedule additional refreshes to catch delayed data
        setTimeout(() => {
          fetchTabData(activeTab);
        }, 15000);

        setTimeout(() => {
          fetchTabData(activeTab);
        }, 30000);
      } else {
        toast.update(loadingToast, {
          render: 'Failed to initiate rescan. Please try again.',
          type: 'error',
          autoClose: 5000
        });
      }
    } catch (error) {
      console.error('Error rescanning org:', error);
      toast.error(`Failed to rescan: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRescanning(false);
    }
  };

  // Render overview tab content
  const renderOverviewTab = () => {
    console.log(`Rendering overview tab with status: ${overviewStatus}`);

    if (overviewStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading overview data...</p>
        </div>
      );
    }

    if (overviewStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load overview data'}</p>
          <button className="refresh-button" onClick={() => fetchOverviewData(true)}>
            Try Again
          </button>
        </div>
      );
    }

    if (overviewStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Overview data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchOverviewData()}
        />
      );
    }

    if (overviewStatus === 'empty') {
      return (
        <div className="empty-state">
          <div className="empty-icon">📊</div>
          <h3>No Overview Data Available</h3>
          <p>Overview data is not available. Please perform a full rescan from the integrations page if needed, or check back later.</p>
        </div>
      );
    }

    if (overviewStatus === 'available' && overviewData) {
      // Calculate percentages safely to avoid NaN or division by zero
      const totalRisks = overviewData.totalRisks || 0;
      const highRiskPercent = totalRisks > 0 ? (overviewData.highRisks / totalRisks) * 100 : 0;
      const mediumRiskPercent = totalRisks > 0 ? (overviewData.mediumRisks / totalRisks) * 100 : 0;
      const lowRiskPercent = totalRisks > 0 ? (overviewData.lowRisks / totalRisks) * 100 : 0;

      return (
        <div className="overview-content">
          <div className="overview-header">
            <h3>Security Overview</h3>
          </div>

          <div className="overview-stats">
            <div className="stat-card">
              <h4>Health Score</h4>
              <div className={`score-pill ${
                overviewData.healthScore >= 80 ? 'high' :
                overviewData.healthScore >= 60 ? 'medium' : 'low'
              }`}>
                {overviewData.healthScore}%
              </div>
            </div>

            <div className="stat-card">
              <h4>Profiles</h4>
              <div className="stat-value">{overviewData.totalProfiles || 0}</div>
            </div>

            <div className="stat-card">
              <h4>Permission Sets</h4>
              <div className="stat-value">{overviewData.totalPermissionSets || 0}</div>
            </div>

            <div className="stat-card">
              <h4>Total Risks</h4>
              <div className="stat-value">{overviewData.totalRisks || 0}</div>
            </div>
          </div>

          <div className="risk-summary">
            <h4>Risk Summary</h4>
            <div className="risk-bars">
              <div className="risk-bar">
                <span className="risk-label">High</span>
                <div className="bar-container">
                  <div
                    className="bar high"
                    style={{ width: `${highRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.highRisks || 0}</span>
              </div>

              <div className="risk-bar">
                <span className="risk-label">Medium</span>
                <div className="bar-container">
                  <div
                    className="bar medium"
                    style={{ width: `${mediumRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.mediumRisks || 0}</span>
              </div>

              <div className="risk-bar">
                <span className="risk-label">Low</span>
                <div className="bar-container">
                  <div
                    className="bar low"
                    style={{ width: `${lowRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.lowRisks || 0}</span>
              </div>
            </div>
          </div>

          <div className="last-updated">
            Last updated: {new Date(overviewData.lastUpdated).toLocaleString()}
          </div>
        </div>
      );
    }

    // Fallback for any other state
    return (
      <div className="no-data-state">
        <p>No overview data available. Status: {overviewStatus}</p>
        <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch data from Salesforce.</p>
      </div>
    );
  };

  // Process policies result data for filtering and charts
  const processedPoliciesResult = React.useMemo(() => {
    if (!policiesResult || !Array.isArray(policiesResult)) return [];
    return policiesResult.map(risk => ({
      Weakness: risk.Weakness || risk.riskType || risk.RiskType || 'UNKNOWN_RISK',
      Setting: risk.Setting || risk.setting || 'Unknown Setting',
      SettingGroup: risk.SettingGroup || risk.settingGroup || 'Unknown Group',
      OrgValue: risk.OrgValue || risk.orgValue || 'Unknown',
      StandardValue: risk.StandardValue || risk.standardValue || 'Unknown',
      OWASPCategory: risk.OWASPCategory || risk.owaspCategory || 'Unknown'
    }));
  }, [policiesResult]);

  // Color mapping for risk types
  const riskColorMap = React.useMemo(() => ({
    'HIGH_RISK': '#E57373',
    'MEDIUM_RISK': '#FFD54F',
    'LOW_RISK': '#51D59C',
    'INFORMATIONAL': '#64B5F6',
    'MEETS_STANDARD': '#81C784',
  }), []);

  // Get unique SettingGroup and Weakness values for picklists
  const settingGroupOptions = React.useMemo(() => {
    const set = new Set();
    processedPoliciesResult.forEach(risk => {
      if (risk.SettingGroup) set.add(risk.SettingGroup);
    });
    return Array.from(set);
  }, [processedPoliciesResult]);

  const riskTypeOptions = React.useMemo(() => {
    const set = new Set();
    processedPoliciesResult.forEach(risk => {
      if (risk.Weakness) set.add(risk.Weakness);
    });
    return Array.from(set);
  }, [processedPoliciesResult]);

  // Filtered policies for all charts/tables based on picklist filters
  const filteredPoliciesAll = React.useMemo(() => {
    return processedPoliciesResult.filter(risk => {
      const groupMatch = settingGroupFilter ? (risk.SettingGroup === settingGroupFilter) : true;
      const riskMatch = riskTypeFilter ? (risk.Weakness === riskTypeFilter) : true;
      return groupMatch && riskMatch;
    });
  }, [processedPoliciesResult, settingGroupFilter, riskTypeFilter]);

  // Pie chart data
  const pieData = React.useMemo(() => {
    const riskCounts = {};
    filteredPoliciesAll.forEach(risk => {
      if (!riskCounts[risk.Weakness]) riskCounts[risk.Weakness] = 0;
      riskCounts[risk.Weakness]++;
    });
    const labels = Object.keys(riskCounts);
    const data = labels.map(label => riskCounts[label]);
    const backgroundColor = labels.map(label => riskColorMap[label] || '#BDBDBD');
    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor,
          borderColor: backgroundColor,
          borderWidth: 2,
        },
      ],
    };
  }, [filteredPoliciesAll, riskColorMap]);

  const pieOptions = React.useMemo(() => ({
    plugins: {
      legend: { display: false },
    },
    responsive: true,
    maintainAspectRatio: false,
  }), []);

  // Dynamic risk types (excluding MEETS_STANDARD)
  const riskTypes = React.useMemo(() => {
    const set = new Set();
    filteredPoliciesAll.forEach(risk => {
      if (risk.Weakness && risk.Weakness !== 'MEETS_STANDARD') set.add(risk.Weakness);
    });
    return Array.from(set);
  }, [filteredPoliciesAll]);

  // Dynamic bar chart data
  const barData = React.useMemo(() => {
    // Group by SettingGroup and RiskType (excluding MEETS_STANDARD)
    const groupMap = {};
    filteredPoliciesAll.forEach(risk => {
      if (risk.Weakness === 'MEETS_STANDARD') return;
      const group = risk.SettingGroup || 'Other';
      const riskType = risk.Weakness;
      if (!groupMap[group]) groupMap[group] = {};
      if (!groupMap[group][riskType]) groupMap[group][riskType] = 0;
      groupMap[group][riskType]++;
    });
    const labels = Object.keys(groupMap);
    // For each riskType, build a dataset
    const datasets = riskTypes.map(riskType => ({
      label: riskType,
      data: labels.map(group => groupMap[group][riskType] || 0),
      backgroundColor: riskColorMap[riskType] || '#BDBDBD',
      borderColor: riskColorMap[riskType] || '#BDBDBD',
      borderWidth: 1,
      stack: 'Stack 0',
    }));
    return { labels, datasets };
  }, [filteredPoliciesAll, riskTypes, riskColorMap]);

  const barOptions = React.useMemo(() => ({
    indexAxis: 'y',
    plugins: {
      legend: { display: false },
      title: { display: true, text: 'Misaligned settings' },
    },
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: { beginAtZero: true, ticks: { stepSize: 1, precision: 0 } },
      y: { ticks: { font: { size: 14 } } },
    },
  }), []);

  // Pie chart click handler for react-chartjs-2 v4
  const pieChartRef = React.useRef();
  const handlePieClick = (event) => {
    if (!pieChartRef.current) return;
    const chart = pieChartRef.current;
    const points = chart.getElementsAtEventForMode(event.nativeEvent, 'nearest', { intersect: true }, true);
    if (points && points.length > 0) {
      const idx = points[0].index;
      const label = pieData.labels[idx];
      setPieFilter(label);
    }
  };

  // Bar chart click handler
  const barChartRef = React.useRef();
  const handleBarClick = (event) => {
    if (!barChartRef.current) return;
    const chart = barChartRef.current;
    const points = chart.getElementsAtEventForMode(event.nativeEvent, 'nearest', { intersect: true }, true);
    if (points && points.length > 0) {
      const { datasetIndex, index } = points[0];
      const group = barData.labels[index];
      const risk = barData.datasets[datasetIndex].label;
      setBarFilter({ group, risk });
    }
  };

  // Filtered policies for the table below (combine with pie filter if needed)
  const filteredPoliciesForTable = React.useMemo(() => {
    let filtered = filteredPoliciesAll;
    if (barFilter && barFilter.risk) {
      filtered = filtered.filter(risk => risk.Weakness === barFilter.risk);
    }
    if (pieFilter) {
      filtered = filtered.filter(risk => risk.Weakness === pieFilter);
    }
    return filtered;
  }, [filteredPoliciesAll, barFilter, pieFilter]);

  // Pagination for filteredPoliciesForTable
  const totalDetailsPages = Math.ceil(filteredPoliciesForTable.length / detailsPerPage);
  const paginatedPolicies = React.useMemo(() => {
    const start = (detailsPage - 1) * detailsPerPage;
    return filteredPoliciesForTable.slice(start, start + detailsPerPage);
  }, [filteredPoliciesForTable, detailsPage]);

  // Sort handler
  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
  };

  // Reset handler
  const handleResetFilters = () => {
    setSettingGroupFilter('');
    setRiskTypeFilter('');
    setPieFilter(null);
    setBarFilter(null);
  };

  // Render health check tab content
  const renderHealthCheckTab = () => {
    console.log(`Rendering health check tab with policiesResultStatus: ${policiesResultStatus}`);

    if (policiesResultStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading health check data...</p>
        </div>
      );
    }

    if (policiesResultStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load health check data'}</p>
          <button className="refresh-button" onClick={() => fetchHealthCheckData(true)}>
            Try Again
          </button>
        </div>
      );
    }

    if (policiesResultStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Health check data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchHealthCheckData()}
        />
      );
    }

    if (policiesResultStatus === 'empty' || !policiesResult || policiesResult.length === 0) {
      return (
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No Health Check Data Available</h3>
          <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch security health check data from Salesforce.</p>
          <button className="refresh-button" onClick={() => fetchHealthCheckData(true)}>
            Rescan
          </button>
        </div>
      );
    }

    // Available state: show health check results from policiesResult
    if (policiesResultStatus === 'available' && policiesResult && policiesResult.length > 0) {
      return (
        <div className="health-check-figma-ui" style={{ background: '#F8FAF9', padding: '32px', borderRadius: '16px', fontFamily: 'Lato, Arial, sans-serif', maxWidth: 1200, margin: '0 auto' }}>
          {/* Filter Bar */}
          <div className="filter-bar">
            <span className="filter-label">Filter By:</span>
            <select
              className="filter-select"
              value={settingGroupFilter}
              onChange={e => setSettingGroupFilter(e.target.value)}
            >
              <option value="" disabled>Setting Group</option>
              {settingGroupOptions.map(group => (
                <option key={group} value={group}>{group}</option>
              ))}
            </select>
            <select
              className="filter-select"
              value={riskTypeFilter}
              onChange={e => setRiskTypeFilter(e.target.value)}
            >
              <option value="" disabled>Risk Type</option>
              {riskTypeOptions.map(risk => (
                <option key={risk} value={risk}>{risk}</option>
              ))}
            </select>
            <button className="filter-reset-btn" onClick={handleResetFilters}>
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none" style={{ marginRight: 8 }}><path d="M12 5V2L7 7l5 5V8c3.31 0 6 2.69 6 6 0 1.3-.42 2.5-1.13 3.47l1.46 1.46C19.07 17.07 20 15.13 20 13c0-4.42-3.58-8-8-8z" fill="#51D59C"/></svg>
              Reset
            </button>
          </div>

          {/* Charts Row: Pie left, Bar right */}
          <div style={{ display: 'flex', gap: 32, alignItems: 'flex-start', marginBottom: 32 }}>
            {/* Pie Chart Section */}
            <div style={{ width: 340, background: '#fff', border: '1px solid #E0E0E0', borderRadius: 8, padding: 24, minHeight: 460, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
              <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 16, color: '#000', marginBottom: 16, textAlign: 'center' }}>Distribution of Risk Types</div>
              <div style={{ width: 260, height: 260, margin: '0 auto', cursor: 'pointer' }}>
                <Pie ref={pieChartRef} data={pieData} options={pieOptions} onClick={handlePieClick} />
              </div>
              {/* Dynamic Legend */}
              <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 24, marginTop: 24, maxHeight: 80, overflowY: 'auto', overflowX: 'auto', whiteSpace: 'nowrap' }}>
                {pieData.labels.map((label, idx) => (
                  <div key={label} style={{ display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer', fontWeight: pieFilter === label ? 700 : 400 }} onClick={() => setPieFilter(label)}>
                    <span style={{ width: 16, height: 16, borderRadius: 8, background: pieData.datasets[0].backgroundColor[idx], display: 'inline-block', border: pieFilter === label ? '2px solid #393E3C' : 'none' }}></span>
                    <span style={{ fontSize: 14, color: '#393E3C' }}>{label.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}</span>
                  </div>
                ))}
                {pieFilter && (
                  <button style={{ marginLeft: 16, background: '#fff', border: '1px solid #51D59C', color: '#51D59C', borderRadius: 4, padding: '4px 12px', cursor: 'pointer', fontWeight: 500 }} onClick={() => setPieFilter(null)}>
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
            {/* Bar Chart Section */}
            <div className="bar-chart-container" style={{ flex: 1, minWidth: 400, background: '#fff', border: '1px solid #E0E0E0', borderRadius: 8, padding: 24, minHeight: 460, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
              <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 16, color: '#000', marginBottom: 16, textAlign: 'center' }}>Misaligned Settings</div>
              <div style={{ width: '100%', height: 260 }}>
                <Bar ref={barChartRef} data={barData} options={barOptions} onClick={handleBarClick} />
              </div>
              {/* Dynamic, clickable legend for Bar Chart (by risk type, like pie chart) */}
              <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 24, marginTop: 16, maxHeight: 80, overflowY: 'auto', overflowX: 'auto', whiteSpace: 'nowrap' }}>
                {riskTypes.map((riskType, idx) => (
                  <div
                    key={riskType}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      cursor: 'pointer',
                      fontWeight: barFilter && barFilter.risk === riskType ? 700 : 400
                    }}
                    onClick={() => {
                      setRiskTypeFilter(riskType);
                      setBarFilter({ risk: riskType });
                    }}
                  >
                    <span style={{ width: 16, height: 16, borderRadius: 8, background: riskColorMap[riskType] || '#BDBDBD', display: 'inline-block', border: barFilter && barFilter.risk === riskType ? '2px solid #393E3C' : 'none' }}></span>
                    <span style={{ fontSize: 14, color: '#393E3C' }}>{riskType.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}</span>
                  </div>
                ))}
                {barFilter && (
                  <button style={{ marginLeft: 16, background: '#fff', border: '1px solid #51D59C', color: '#51D59C', borderRadius: 4, padding: '4px 12px', cursor: 'pointer', fontWeight: 500 }} onClick={() => { setBarFilter(null); setRiskTypeFilter(''); }}>
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div style={{ background: '#fff', border: '1px solid #E0E0E0', borderRadius: 8, padding: 24 }}>
            <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 16, color: '#000', marginBottom: 16 }}>Security Risk Details</div>
            <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
              <thead>
                <tr style={{ background: '#F1FCF7', borderBottom: '1px solid #B8D8CB' }}>
                  <th onClick={() => handleSort('Weakness')} style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left', cursor: 'pointer' }}>
                    Risk {sortConfig.key === 'Weakness' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                  </th>
                  <th onClick={() => handleSort('Setting')} style={{ fontWeight: 500, fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px', textAlign: 'left', cursor: 'pointer' }}>
                    Setting {sortConfig.key === 'Setting' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                  </th>
                  <th onClick={() => handleSort('OrgValue')} style={{ fontWeight: 500, fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px', textAlign: 'left', cursor: 'pointer' }}>
                    Org Value {sortConfig.key === 'OrgValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                  </th>
                  <th onClick={() => handleSort('StandardValue')} style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left', cursor: 'pointer' }}>
                    Standard Value {sortConfig.key === 'StandardValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                  </th>
                  <th onClick={() => handleSort('OWASPCategory')} style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left', cursor: 'pointer' }}>
                    OWASP Category {sortConfig.key === 'OWASPCategory' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedPolicies.map((risk, index) => (
                  <tr key={index} style={{ borderBottom: '1px solid #E0E0E0' }}>
                    <td style={{ padding: '14px 8px 14px 16px', fontSize: 14, color: '#393E3C' }}>{risk.Weakness.replace(/_/g, ' ')}</td>
                    <td style={{ padding: '14px 8px 14px 16px', fontSize: 14, color: '#1D2433' }}>{risk.Setting}</td>
                    <td style={{ padding: '14px 8px 14px 16px', fontSize: 14, color: '#1D2433' }}>{risk.OrgValue}</td>
                    <td style={{ padding: '14px 8px 14px 16px', fontSize: 14, color: '#393E3C' }}>{risk.StandardValue}</td>
                    <td style={{ padding: '14px 8px 14px 16px', fontSize: 14, color: '#393E3C' }}>{risk.OWASPCategory}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {totalDetailsPages > 1 && (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: 16, gap: 16 }}>
              <button onClick={() => setDetailsPage(p => Math.max(1, p - 1))} disabled={detailsPage === 1} style={{ padding: '6px 16px', borderRadius: 4, border: '1px solid #E0E0E0', background: detailsPage === 1 ? '#f1f1f1' : '#fff', color: '#393E3C', cursor: detailsPage === 1 ? 'not-allowed' : 'pointer' }}>Previous</button>
              <span style={{ fontSize: 14, color: '#393E3C' }}>Page {detailsPage} of {totalDetailsPages}</span>
              <button onClick={() => setDetailsPage(p => Math.min(totalDetailsPages, p + 1))} disabled={detailsPage === totalDetailsPages} style={{ padding: '6px 16px', borderRadius: 4, border: '1px solid #E0E0E0', background: detailsPage === totalDetailsPages ? '#f1f1f1' : '#fff', color: '#393E3C', cursor: detailsPage === totalDetailsPages ? 'not-allowed' : 'pointer' }}>Next</button>
            </div>
          )}
        </div>
      );
    }

    // Fallback for any other state
    return (
      <div className="no-data-state">
        <p>No health check data available. Status: {healthCheckStatus}</p>
        <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch data from Salesforce.</p>
      </div>
    );
  };

  // Render profiles tab content
  const renderProfilesTab = () => {
    console.log(`Rendering profiles tab with status: ${profilesStatus}`);

    return <ProfilesAndPermissionsTab orgId={integrationId} />;
  };

  // Render PMD tab content
  const renderPMDTab = () => {
    console.log(`Rendering PMD tab with status: ${pmdStatus}`);

    return (
      <PMDIssuesTab
        data={pmdData}
        status={pmdStatus}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing?.pmd || false}
      />
    );
  };

  return (
    <div className="integration-tabs">
      <div className="page-header">
        <h2>Salesforce Org Details</h2>
        {loading ? (
          <h3>Loading integration details...</h3>
        ) : error ? (
          <h3>Error: {error}</h3>
        ) : (
          <h3>{integration ? (integration.name || integration.Name || 'Unnamed Integration') : 'Integration Details'}</h3>
        )}
      </div>

      <div className="integration-header">
        <div className="tabs">
          <button
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => handleTabChange('overview')}
          >
            Overview
          </button>
          <button
            className={`tab-button ${activeTab === 'healthCheck' ? 'active' : ''}`}
            onClick={() => handleTabChange('healthCheck')}
          >
            Health Check
          </button>
          <button
            className={`tab-button ${activeTab === 'profilesPermissions' ? 'active' : ''}`}
            onClick={() => handleTabChange('profilesPermissions')}
          >
            Profiles and Permission Sets
          </button>
          <button
            className={`tab-button ${activeTab === 'pmdIssues' ? 'active' : ''}`}
            onClick={() => handleTabChange('pmdIssues')}
          >
            PMD Issues
          </button>
        </div>
        <button
          className="rescan-button"
          onClick={handleRescan}
          disabled={isRescanning}
        >
          {isRescanning ? 'Rescanning...' : 'Rescan'}
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'healthCheck' && renderHealthCheckTab()}
        {activeTab === 'profilesPermissions' && renderProfilesTab()}
        {activeTab === 'pmdIssues' && renderPMDTab()}
      </div>
    </div>
  );
};

export default IntegrationTabs;
