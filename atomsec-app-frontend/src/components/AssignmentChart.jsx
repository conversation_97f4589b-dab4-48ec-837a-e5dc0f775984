import React from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const COLORS = [
  '#51D59C', '#FFD54F', '#6C63FF', '#A393E6', '#FFB400', '#B5EAD7', '#FFB7B2', '#A0CED9', '#C7CEEA', '#FFDAC1',
  '#E2F0CB', '#B5EAD7', '#FFB400', '#FFD6A5', '#FFFFB5', '#B5EAD7', '#FFB7B2', '#A0CED9', '#C7CEEA', '#FFDAC1', '#E2F0CB'
];

export default function AssignmentChart({ data }) {
  if (!Array.isArray(data) || data.length === 0) {
    return <div className='assignment-chart-card' style={{ minHeight: 320, display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#fff', borderRadius: 4, boxShadow: '0px 4px 20px 0px rgba(0,0,0,0.1)', padding: 24 }}>No assignment data available</div>;
  }
  // Sort by userCount descending
  const sorted = [...data].sort((a, b) => (b.userCount || 0) - (a.userCount || 0));
  const labels = sorted.map(d => d.name);
  const userCounts = sorted.map(d => d.userCount);
  const backgroundColors = labels.map((_, i) => COLORS[i % COLORS.length]);
  const chartData = {
    labels,
    datasets: [
      {
        label: 'Users Assigned',
        data: userCounts,
        backgroundColor: backgroundColors,
        borderRadius: 8,
        barThickness: 24,
        maxBarThickness: 32,
      }
    ]
  };
  const options = {
    indexAxis: 'y',
    responsive: true,
    plugins: {
      legend: { display: false },
      title: { display: false },
      tooltip: { enabled: true }
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: { color: '#F0F0F0' },
        ticks: { color: '#393E3C', font: { size: 14 } }
      },
      y: {
        grid: { display: false },
        ticks: { color: '#393E3C', font: { size: 14 } }
      }
    }
  };
  return (
    <div className='assignment-chart-card' style={{ background: '#fff', borderRadius: 4, boxShadow: '0px 4px 20px 0px rgba(0,0,0,0.1)', padding: 24, minHeight: 320 }}>
      <div style={{ fontWeight: 600, fontSize: 18, marginBottom: 16, color: '#393E3C' }}>Assignments</div>
      <Bar data={chartData} options={options} height={320} />
    </div>
  );
} 