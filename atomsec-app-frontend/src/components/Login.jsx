import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Login.css';
import logo from '../assets/logo.svg';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import AuthDebugInfo from './AuthDebugInfo';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { isAuthenticated, login, loading, setUser, setIsAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from the location state or default to home
  const from = location.state?.from?.pathname || '/';

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Handle Azure AD login with redirect - using useCallback to prevent unnecessary re-renders
  const handleAzureLoginRedirect = useCallback(async () => {
    setError(null);
    setIsLoading(true);

    try {
      console.log('Starting Azure login process with redirect, path:', from);

      // Save the current path to localStorage
      localStorage.setItem('loginRedirectPath', from);

      // Set auth in progress flag
      localStorage.setItem('authInProgress', 'true');

      // Directly call login with immediate redirect
      login(from);

      // The redirect will happen automatically
      console.log('Login redirect called');

    } catch (error) {
      console.error('Azure login redirect error:', error);
      setError('Failed to sign in with Microsoft. Please try again.');
      setIsLoading(false);
      localStorage.removeItem('authInProgress');
    }
  }, [from, login, setError, setIsLoading]);

  // Email/password login method - using useCallback to prevent unnecessary re-renders
  const handleEmailLogin = useCallback(async () => {
    setError(null);
    setIsLoading(true);

    try {
      // Validate inputs
      if (!email || !password) {
        setError('Please enter both email and password');
        setIsLoading(false);
        return;
      }

      // Import API functions
      const { login: apiLogin } = await import('../api');

      // Prepare credentials object for the API
      const credentials = { email, password };

      // Call the login API
      const response = await apiLogin(credentials);

      // Check for success
      if (response && response.success) {
        // Store tokens and user info
        localStorage.setItem('accessToken', response.data.access_token);
        localStorage.setItem('refreshToken', response.data.refresh_token);
        localStorage.setItem('userEmail', response.data.user.email);
        localStorage.setItem('userName', response.data.user.name || '');

        // Update auth context state
        setUser({
          accessToken: response.data.access_token,
          email: response.data.user.email,
          name: response.data.user.name || ''
        });
        setIsAuthenticated(true);

        // Update auth context and redirect using navigate instead of window.location
        // This prevents a full page refresh
        navigate(from);
      } else {
        // SECURITY FIX: Ensure authentication state is completely cleared on login failure
        console.error('Login failed:', response?.error || 'Unknown error');

        // Clear any existing authentication data
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userName');

        // Clear auth context
        setUser(null);
        setIsAuthenticated(false);

        // Handle error
        setError(response?.error || 'Invalid email or password. Please try again.');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Email login error:', error);

      // SECURITY FIX: Clear authentication state on any login error
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userName');
      setUser(null);
      setIsAuthenticated(false);

      setError(error.response?.data?.error || 'Invalid email or password. Please try again.');
      setIsLoading(false);
    }
  }, [email, password, navigate, from, setUser, setIsAuthenticated]);

  return (
    <div className="login-container">
      {/* Debug info - remove in production */}
      {process.env.NODE_ENV === 'development' && <AuthDebugInfo />}

      <div className="login-left">
        <div className="login-logo">
        <img src={logo} alt="Atom Security Logo" className="logo" />
        </div>
        <div className="login-card">
          <div className="login-header">
            <h1 className="login-title">Welcome Back!</h1>
            <p className="login-subtitle">Sign in to your account to continue</p>
          </div>

          <form onSubmit={(e) => e.preventDefault()} className="login-form">
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <div className="input-container">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter email address here"
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <div className="password-header">
                <label htmlFor="password">Password</label>
                <Link to="/forgot-password" className="forgot-password">Forgot Password?</Link>
              </div>
              <div className="input-container">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password here"
                  required
                  autoComplete="current-password" // Added autocomplete attribute
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </button>
              </div>
            </div>

            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <button
              type="button"
              className="login-button email-button"
              onClick={(e) => { e.preventDefault(); handleEmailLogin(); }}
              disabled={isLoading}
            >
              {isLoading ? 'Signing in...' : 'Sign In with Email'}
            </button>

            <div className="login-divider">
              <span>or</span>
            </div>

            <button
              type="button"
              className="login-button azure-button"
              onClick={(e) => { e.preventDefault(); handleAzureLoginRedirect(); }}
              disabled={isLoading || loading}
            >
              {isLoading || loading ? 'Signing in...' : 'Sign in with Microsoft'}
            </button>
          </form>

          <div className="login-footer">
            <p>Don't have an account?</p>
            <Link to="/signup" className="signup-link">Sign Up</Link>
          </div>
        </div>
      </div>

      <div className="login-right">
        <h2 className="right-title">Secure your <br /><span className="brand-color">Digital World.</span></h2>
        <div className="features-grid">
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Comprehensive Security Planning</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Detailed Vulnerability Reports</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Guided Remediation Steps</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Compliance Monitoring</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;