import React, { useState, useEffect, useRef } from 'react';
import './settings-risk-table.css';
import ResetIcon from '../assets/icons/ResetIcon.svg';
import { getTaskStatus, fetchProfilesPermissions } from '../api';

const TASK_TYPES = [
  'device_activation',
  'login_ip_ranges',
  'mfa_enforcement',
  'password_policy',
  'session_timeout',
];

const FILTER_OPTIONS = [
  { label: 'Profiles/Permission Sets', value: 'profiles' },
  { label: 'Settings', value: 'settings' },
];
const RISK_TYPE_OPTIONS = [
  { label: 'All', value: 'all' },
  { label: 'High', value: 'high' },
  { label: 'Medium', value: 'medium' },
  { label: 'Low', value: 'low' },
];

export default function SettingsRiskTable({ orgId, setDetailView }) {
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const scrollTopRef = useRef(null);
  const [settingNameFilter, setSettingNameFilter] = useState('All');
  const [riskFilter, setRiskFilter] = useState('All');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;

  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
  };

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    setError(null);
    const fetchAll = async () => {
      try {
        const results = await Promise.all(
          TASK_TYPES.map(async (taskType) => {
            // 1. Fetch latest execution_log_id for this task type
            const statusJson = await getTaskStatus(orgId, 'completed', 1, taskType);
            const arr = Array.isArray(statusJson.data) ? statusJson.data : [];
            const latest = arr.length > 0 ? arr[0] : null;
            const executionLogId = latest && (latest.execution_log_id || latest.ExecutionLogId);
            if (!executionLogId) return null;
            // 2. Fetch the summary/settings data for this execution_log_id
            const dataJson = await fetchProfilesPermissions(orgId, executionLogId);

            // Special handling for MFA Enforcement
            if (taskType === 'mfa_enforcement' && Array.isArray(dataJson.policies)) {
              // 1. Org Value: custom aggregation for issues, unique per profile
              let orgValue = '';
              // Map: { profileName: {missing: bool, mismatch: bool} }
              const profileIssueMap = {};
              // Define allSettings to collect all settings for standardValue aggregation
              const allSettings = [];
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                // Try to extract profile name from RowKey if needed
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    if (!profileIssueMap[profileName]) profileIssueMap[profileName] = { missing: false, mismatch: false };
                    if (s.Issue === 'Setting missing from profile') profileIssueMap[profileName].missing = true;
                    if (s.Issue === 'Value does not match best practice') profileIssueMap[profileName].mismatch = true;
                  });
                } catch {}
              });
              const missingCount = Object.values(profileIssueMap).filter(v => v.missing).length;
              const mismatchCount = Object.values(profileIssueMap).filter(v => v.mismatch).length;
              const orgValueParts = [];
              if (missingCount > 0) {
                orgValueParts.push(`${missingCount === 1 ? '1 profile is' : missingCount + ' profiles are'} lacking MFA Enforcement`);
              }
              if (mismatchCount > 0) {
                orgValueParts.push(`${mismatchCount === 1 ? '1 profile has a discrepancy' : mismatchCount + ' profiles have discrepancies'} in MFA implementation`);
              }
              orgValue = orgValueParts.join('; ');
              // 2. OWASP Category: unique OWASP
              const uniqueOwasp = Array.from(new Set(dataJson.policies.map(p => p.OWASP).filter(Boolean)));
              const owaspCategory = uniqueOwasp.join(', ');
              // 3. Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key)) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'MFA Enforcement',
                orgValue,
                standardValue,
                owaspCategory,
                risk: 'High',
                raw: dataJson,
              };
            }
            // Special handling for Device Activation
            if (taskType === 'device_activation' && Array.isArray(dataJson.policies)) {
              let orgValue = '';
              let missingCount = 0;
              let misconfiguredCount = 0;
              const allSettings = [];
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    if (s.OrgValue === null || s.OrgValue === undefined) missingCount++;
                    else if (String(s.OrgValue).toLowerCase() !== String(s.StandardValue).toLowerCase()) misconfiguredCount++;
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch {}
              });
              if (missingCount > 0) {
                orgValue += `Device Activation is not enabled on ${missingCount === 1 ? '1 profile' : missingCount + ' profiles'}`;
              }
              if (misconfiguredCount > 0) {
                if (orgValue) orgValue += '; ';
                orgValue += `Device Activation feature is misconfigured on ${misconfiguredCount === 1 ? '1 profile' : misconfiguredCount + ' profiles'}`;
              }
              // Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key)) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'Device Activation',
                orgValue,
                standardValue,
                owaspCategory,
                risk: 'High',
                raw: dataJson,
              };
            }
            // Special handling for Login IP Ranges
            if (taskType === 'login_ip_ranges' && Array.isArray(dataJson.policies)) {
              let riskProfiles = 0;
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  if (Array.isArray(arr) && arr.length > 0) {
                    // If any issue present, count this profile
                    if (arr.some(s => s.Issue)) riskProfiles++;
                  }
                  arr.forEach(s => { if (s.OWASP) uniqueOwasp.add(s.OWASP); });
                } catch {}
              });
              let orgValue = '';
              if (riskProfiles > 0) {
                orgValue = `${riskProfiles} ${riskProfiles === 1 ? 'profile has' : 'profiles have'} a risky configuration for whitelisted IP's`;
              }
              const standardValue = "IP Ranges should be restricted";
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'Login IP Ranges',
                orgValue,
                standardValue,
                owaspCategory,
                risk: 'High',
                raw: dataJson,
              };
            }
            // Special handling for Password Policy
            if (taskType === 'password_policy' && Array.isArray(dataJson.policies)) {
              const missingProfiles = new Set();
              const misconfiguredProfiles = new Set();
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  arr.forEach(s => {
                    if (s.Issue === 'Password policy file missing for profile') missingProfiles.add(profileName);
                    if (s.Issue === 'Value does not match best practice') misconfiguredProfiles.add(profileName);
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch {}
              });
              let orgValue = '';
              if (missingProfiles.size > 0) {
                orgValue += `${missingProfiles.size} ${missingProfiles.size === 1 ? 'profile lacks' : 'profiles lack'} best practices for password policies`;
              }
              if (misconfiguredProfiles.size > 0) {
                if (orgValue) orgValue += ': ';
                orgValue += `${misconfiguredProfiles.size} ${misconfiguredProfiles.size === 1 ? 'profile has' : 'profiles have'} misconfigured password policies`;
              }
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'Password Policy',
                orgValue,
                standardValue: '',
                owaspCategory,
                risk: 'High',
                raw: dataJson,
              };
            }
            // Default: just return summary row per task
            return {
              taskType,
              executionLogId,
              ...dataJson.summary, // or customize as per your backend response
              raw: dataJson,
            };
          })
        );
        if (isMounted) {
          setTableData(results.filter(Boolean));
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError('Failed to fetch settings risk data');
          setLoading(false);
        }
      }
    };
    fetchAll();
    return () => { isMounted = false; };
  }, [orgId]);

  const safeData = Array.isArray(tableData) ? tableData : [];

  // Get unique setting names and risks from the data
  const settingNameOptions = React.useMemo(() => {
    const set = new Set();
    safeData.forEach(row => {
      if (row.settingName) set.add(row.settingName);
      else if (row.taskType) set.add(row.taskType);
    });
    return ['All', ...Array.from(set)];
  }, [safeData]);
  const riskOptions = React.useMemo(() => {
    const set = new Set();
    safeData.forEach(row => {
      if (row.risk) set.add(row.risk);
    });
    return ['All', ...Array.from(set)];
  }, [safeData]);

  // Filter data based on filters
  const filteredData = React.useMemo(() => {
    return safeData.filter(row => {
      const settingMatch = settingNameFilter === 'All' || row.settingName === settingNameFilter || row.taskType === settingNameFilter;
      const riskMatch = riskFilter === 'All' || row.risk === riskFilter;
      return settingMatch && riskMatch;
    });
  }, [safeData, settingNameFilter, riskFilter]);

  // Sort filteredData based on sortConfig
  const sortedFilteredData = React.useMemo(() => {
    if (!sortConfig.key) return filteredData;
    const sorted = [...filteredData].sort((a, b) => {
      const aVal = (a[sortConfig.key] || '').toString().toLowerCase();
      const bVal = (b[sortConfig.key] || '').toString().toLowerCase();
      if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [filteredData, sortConfig]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedFilteredData.length / rowsPerPage);
  const paginatedData = sortedFilteredData.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  // Reset handler
  const handleResetFilters = () => {
    setSettingNameFilter('All');
    setRiskFilter('All');
    setCurrentPage(1);
  };

  if (loading) return <div style={{ padding: 32 }}>Loading...</div>;
  if (error) return <div style={{ padding: 32, color: 'red' }}>{error}</div>;

  // Main table UI only
  return (
    <>
      <div className="settings-risk-table-title">Other Settings - Profiles & Permissions</div>
      <div className="settings-risk-filters-row">
        <span className="settings-risk-filter-label">Filter By:</span>
        <select value={settingNameFilter} onChange={e => {
          setSettingNameFilter(e.target.value);
          setCurrentPage(1);
        }}>
          {settingNameOptions.map(opt => (
            <option key={opt} value={opt}>{opt}</option>
          ))}
        </select>
        <select value={riskFilter} onChange={e => {
          setRiskFilter(e.target.value);
          setCurrentPage(1);
        }}>
          {riskOptions.map(opt => (
            <option key={opt} value={opt}>{opt}</option>
          ))}
        </select>
        <button className="settings-risk-reset-btn" onClick={handleResetFilters}>
          <img src={ResetIcon} alt="Reset" className="settings-risk-reset-icon" />
          Reset
        </button>
      </div>
      <div ref={scrollTopRef}></div>
      <table className="settings-risk-table bubble-chart-section">
        <thead>
          <tr>
            <th onClick={() => handleSort('settingName')} style={{ cursor: 'pointer' }}>
              Setting Name {sortConfig.key === 'settingName' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
            </th>
            <th className="settings-risk-org-value-col" onClick={() => handleSort('orgValue')} style={{ cursor: 'pointer' }}>
              Org Value {sortConfig.key === 'orgValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
            </th>
            <th className="settings-risk-standard-value-col" onClick={() => handleSort('standardValue')} style={{ cursor: 'pointer' }}>
              Standard Value {sortConfig.key === 'standardValue' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
            </th>
            <th className="owasp-category-col" onClick={() => handleSort('owaspCategory')} style={{ cursor: 'pointer' }}>
              OWASP Category {sortConfig.key === 'owaspCategory' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
            </th>
            <th onClick={() => handleSort('risk')} style={{ cursor: 'pointer' }}>
              Risk {sortConfig.key === 'risk' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
            </th>
          </tr>
        </thead>
        <tbody>
          {paginatedData.map(row => (
            <tr key={row.taskType}>
              <td>{row.settingName || row.taskType}</td>
              <td className="settings-risk-org-value-col">
                {row.orgValue || '-'}
                {row.executionLogId && setDetailView && (
                  <span
                    className="settings-risk-view-link"
                    style={{ marginLeft: 8, cursor: 'pointer', color: '#51D59C', textDecoration: 'underline' }}
                    onClick={() => {
                      window.scrollTo({ top: 0, behavior: 'smooth' });
                      setDetailView({ taskType: row.taskType, executionLogId: row.executionLogId });
                    }}
                  >
                    View
                  </span>
                )}
              </td>
              <td className="settings-risk-standard-value-col">{row.standardValue || '-'}</td>
              <td className="owasp-category-col">
                {row.owaspCategory
                  ? row.owaspCategory.split(',').map((part, idx) => (
                      <span key={idx} style={{ display: 'block' }}>{part.trim()}</span>
                    ))
                  : '-'}
              </td>
              <td>{row.risk || '-'}</td>
            </tr>
          ))}
        </tbody>
      </table>
      {totalPages > 1 && (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: 16, gap: 16 }}>
          <button 
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))} 
            disabled={currentPage === 1}
            className="settings-risk-pagination-btn"
            style={{ 
              padding: '6px 16px', 
              borderRadius: 4, 
              border: '1px solid #E0E0E0', 
              background: currentPage === 1 ? '#f1f1f1' : '#fff', 
              color: '#393E3C', 
              cursor: currentPage === 1 ? 'not-allowed' : 'pointer' 
            }}
          >
            Previous
          </button>
          <span style={{ fontSize: 14, color: '#393E3C' }}>
            Page {currentPage} of {totalPages}
          </span>
          <button 
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} 
            disabled={currentPage === totalPages}
            className="settings-risk-pagination-btn"
            style={{ 
              padding: '6px 16px', 
              borderRadius: 4, 
              border: '1px solid #E0E0E0', 
              background: currentPage === totalPages ? '#f1f1f1' : '#fff', 
              color: '#393E3C', 
              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer' 
            }}
          >
            Next
          </button>
        </div>
      )}
    </>
  );
} 