import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setUser, setIsAuthenticated } = useAuth();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get tokens from URL parameters
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const email = searchParams.get('email');
        const name = searchParams.get('name');

        if (accessToken && email) {
          // Store tokens in localStorage
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('refreshToken', refreshToken || '');
          localStorage.setItem('userEmail', email);
          localStorage.setItem('userName', name || '');

          // Update auth context
          setUser({
            accessToken,
            email,
            name: name || '',
            roles: [],
            isAdmin: false
          });
          setIsAuthenticated(true);

          // Get redirect path or default to home
          const redirectPath = localStorage.getItem('loginRedirectPath') || '/';
          localStorage.removeItem('loginRedirectPath');

          // Navigate to the intended destination
          navigate(redirectPath);
        } else {
          console.error('Missing authentication tokens');
          navigate('/login?error=auth_failed');
        }
      } catch (error) {
        console.error('Error handling auth callback:', error);
        navigate('/login?error=auth_failed');
      }
    };

    handleAuthCallback();
  }, [searchParams, navigate, setUser, setIsAuthenticated]);

  return (
    <div className="loading-indicator">
      <h2>Completing authentication...</h2>
      <p>Please wait while we complete your login.</p>
    </div>
  );
};

export default AuthCallback;
