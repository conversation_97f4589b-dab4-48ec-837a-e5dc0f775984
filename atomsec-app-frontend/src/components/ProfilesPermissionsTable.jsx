import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

// Custom Checkbox Renderer for boolean columns
const CheckboxRenderer = (props) => {
  const val = props.value;
  const norm = val !== undefined && val !== null ? val.toString().toLowerCase() : '';
  if (norm === 'true') {
    // Green checkmark SVG
    return (
      <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: 24, height: 24 }}>
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="20" height="20" rx="6" fill="#51D59C"/>
          <path d="M6 10.5L9 13.5L14 7.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </span>
    );
  } else if (norm === 'false') {
    // Empty box SVG
    return (
      <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: 24, height: 24 }}>
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="20" height="20" rx="6" fill="#F5F7FA" stroke="#B7C2C7" strokeWidth="1.5"/>
        </svg>
      </span>
    );
  } else if (val !== undefined && val !== null && val !== '') {
    // Show value as text
    return <span>{val}</span>;
  } else {
    return null;
  }
};

// Custom Tooltip Renderer
const CustomTooltip = (props) => (
  <div style={{ padding: 10 }}>
    <strong>{props.colDef.headerName}</strong>
    <div>{props.value}</div>
  </div>
);

// Normalize field names for columns and row keys
function normalizeFieldName(name) {
  return (name || '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
}

const ProfilesPermissionsTable = forwardRef(({ rowData, settings, profileAssignments, bubbleFilter, handleResetFilters }, ref) => {
  const [gridApi, setGridApi] = useState(null);
  const [columnApi, setColumnApi] = useState(null);
  const [filterMode, setFilterMode] = useState('and'); // 'and' or 'or'

  useImperativeHandle(ref, () => ({
    resetFilters: () => {
      if (gridApi) gridApi.setFilterModel(null);
    }
  }), [gridApi]);

  // Helper to get type label
  function getTypeLabel(type) {
    if (type === 'ProfilePermissions') return 'Profile';
    if (type === 'ProfilePermissionSetAssignment') return 'Permission Set';
    return type || '';
  }

  // Group and transform data for table
  function transformAndGroupRows(rowData, profileAssignments, settings) {
    // First, parse OrgValue and map permissions to normalized fields for each row
    const parsedRows = (rowData || []).map(row => {
      let newRow = { ...row };
      if (row.OrgValue) {
        let orgSettings = [];
        try {
          orgSettings = JSON.parse(row.OrgValue);
        } catch (e) {
          orgSettings = [];
        }
        const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
        (orgSettings || []).forEach(setting => {
          const colKey = normalizeFieldName(setting.SalesforceSetting);
          // For Permission Set, use PermissionSetValue-UserPermissions if present
          let value;
          if (type === 'ProfilePermissionSetAssignment' || type === 'PermissionSetPermissions' || type === 'Permission Set') {
            if (setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            } else if (setting.ProfileValue !== undefined) {
              value = setting.ProfileValue;
            } else if (setting.profileValue !== undefined) {
              value = setting.profileValue;
            } else {
              value = setting.Match;
            }
          } else {
            // For Profile, use ProfileValue, then PermissionSetValue-UserPermissions, then profileValue, then Match
            value = setting.ProfileValue;
            if (value === undefined && setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            }
            if (value === undefined && setting.profileValue !== undefined) {
              value = setting.profileValue;
            }
            if (value === undefined) value = setting.Match;
          }
          // Set as 'true' or 'false' string
          newRow[colKey] = (value === true || value === 'true') ? 'true' : 'false';
          newRow[`${colKey}_desc`] = setting.Description;
        });
      }
      return newRow;
    });
    console.log('DEBUG parsedRows:', parsedRows);
    // Map for summing assignments by API Name + Type
    const groupMap = {};
    (parsedRows || []).forEach(row => {
      const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
      const typeLabel = getTypeLabel(type);
      // For permission sets, use PermissionSetName as API Name, else ProfileName
      const apiName = typeLabel === 'Permission Set' ? (row.PermissionSetName || row.ProfileName || row.APIName) : (row.ProfileName || row.APIName);
      if (!apiName) return; // skip if no name
      const groupKey = `${apiName}__${typeLabel}`;
      if (!groupMap[groupKey]) {
        groupMap[groupKey] = {
          APIName: apiName,
          Type: typeLabel,
          AssignmentCount: 0,
          rawRows: [],
        };
      }
      // Sum assignments
      let assignCount = 0;
      if (row.AssignmentCount !== undefined) assignCount = Number(row.AssignmentCount) || 0;
      // For profileAssignments, sum if present
      if (profileAssignments && typeLabel === 'Profile') {
        const pa = profileAssignments.find(a => a.ProfileName === apiName);
        if (pa && pa.AssignmentCount !== undefined) assignCount = Number(pa.AssignmentCount) || assignCount;
      }
      groupMap[groupKey].AssignmentCount += assignCount;
      groupMap[groupKey].rawRows.push(row);
    });
    // Merge settings columns (show true if any row in group is true)
    const result = Object.values(groupMap).map(group => {
      const row = {
        APIName: group.APIName,
        Type: group.Type,
        AssignmentCount: group.AssignmentCount,
      };
      (settings || []).forEach(setting => {
        const colKey = setting.field;
        // If any row in group has this permission true, set 'true' as string, else 'false'
        const hasPermission = group.rawRows.some(r => r[colKey] === 'true');
        row[colKey] = hasPermission ? 'true' : 'false';
        // Use first non-empty description
        const desc = group.rawRows.map(r => r[`${colKey}_desc`]).find(d => d);
        if (desc) row[`${colKey}_desc`] = desc;
      });
      return row;
    });
    return result;
  }

  // Toolbar for column show/hide, CSV export, and pagination controls
  const onBtnExport = () => {
    if (gridApi) gridApi.exportDataAsCsv();
  };
  const onShowColumns = () => {
    console.log('gridApi:', gridApi);
    if (gridApi) {
      gridApi.setSideBarVisible(true);
      gridApi.openToolPanel('columns');
    }
  };

  const baseCols = [
    { headerName: 'API Name', field: 'APIName', pinned: 'left', filter: 'agTextColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'left', fontWeight: 600 }, checkboxSelection: true, headerCheckboxSelection: true, rowDrag: true, floatingFilter: true },
    { headerName: 'Type', field: 'Type', filter: 'agSetColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'center', fontWeight: 500 }, floatingFilter: true },
    { headerName: 'No of Assignments', field: 'AssignmentCount', filter: 'agNumberColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'center' }, floatingFilter: true }
  ];
  const dynamicCols = (settings || []).map(setting => ({
    headerName: setting.label,
    field: setting.field,
    cellRenderer: 'CheckboxRenderer',
    tooltipField: `${setting.field}_desc`,
    filter: 'agSetColumnFilter',
    filterParams: { values: null },
    sortable: true,
    editable: false,
    resizable: true,
    minWidth: 120,
    cellStyle: { textAlign: 'center', verticalAlign: 'middle' },
    headerClass: 'ag-center-cols-header',
    floatingFilter: true,
    comparator: (a, b) => {
      // Sort true (checkmark) > false (empty) > other values
      const norm = v => {
        if (v === true || v === 'true' || (typeof v === 'string' && v.toLowerCase() === 'true')) return 2;
        if (v === false || v === 'false' || (typeof v === 'string' && v.toLowerCase() === 'false')) return 1;
        return 0;
      };
      return norm(b) - norm(a); // descending: true > false > other
    },
  }));
  const columnDefs = [...baseCols, ...dynamicCols];

  const gridRows = transformAndGroupRows(rowData, profileAssignments, settings);

  // Apply bubbleFilter if present (supports both AND and OR filtering)
  let filteredGridRows = gridRows;
  if (Array.isArray(bubbleFilter) && bubbleFilter.length > 0) {
    console.log(`Filtering rows with bubbleFilter (${filterMode.toUpperCase()} mode):`, bubbleFilter);
    
    if (filterMode === 'and') {
      // Use AND logic - row must have ALL selected permissions set to true
      filteredGridRows = gridRows.filter(row => {
        // Check if row has ALL of the selected permissions
        for (const permission of bubbleFilter) {
          // Normalize the permission key to match row property names
          const normKey = permission.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          
          const val = row[normKey];
          
          // If any permission is false, exclude this row (AND logic)
          if (!(val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true'))) {
            return false;
          }
        }
        
        // If we get here, all permissions matched
        return true;
      });
      
      console.log(`Filtered from ${gridRows.length} to ${filteredGridRows.length} rows using AND logic`);
    } else {
      // Use OR logic - row must have ANY selected permissions set to true
      filteredGridRows = gridRows.filter(row => {
        // Check if row has ANY of the selected permissions
        for (const permission of bubbleFilter) {
          // Normalize the permission key to match row property names
          const normKey = permission.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          
          const val = row[normKey];
          
          if (val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true')) {
            return true;
          }
        }
        return false;
      });
      
      console.log(`Filtered from ${gridRows.length} to ${filteredGridRows.length} rows using OR logic`);
    }
  }

  // Debug: log settings and first row of gridRows
  console.log('DEBUG settings:', settings);
  if (filteredGridRows && filteredGridRows.length > 0) {
    console.log('DEBUG first gridRow:', filteredGridRows[0]);
  }

  if (!filteredGridRows || filteredGridRows.length === 0) {
    return <div>No profile permissions data available.</div>;
  }

  // Debug logs
  console.log('DEBUG columnDefs:', columnDefs);
  console.log('DEBUG gridRows[0]:', filteredGridRows[0]);

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12, gap: '16px' }}>
        {/* Export CSV Button */}
        <button
          onClick={onBtnExport}
          style={{
            background: '#F8FDFB',
            border: '1px solid #51D59C',
            color: '#51D59C',
            borderRadius: 6,
            padding: '8px 20px',
            fontWeight: 600,
            cursor: 'pointer',
            minWidth: '150px',
            textAlign: 'center'
          }}
        >
          Export CSV
        </button>
        
        {/* Filter Mode Dropdown */}
        {Array.isArray(bubbleFilter) && bubbleFilter.length > 1 && (
          <>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '12px'
            }}>
              <span style={{ 
                color: '#393E3C', 
                fontSize: '14px',
                fontWeight: 500,
                whiteSpace: 'nowrap'
              }}>
                Filter Mode:
              </span>
              <select
                value={filterMode}
                onChange={(e) => setFilterMode(e.target.value)}
                style={{
                  padding: '8px 16px',
                  borderRadius: 6,
                  border: '1px solid #51D59C',
                  background: '#F8FDFB',
                  color: '#51D59C',
                  fontSize: '14px',
                  fontWeight: 600,
                  cursor: 'pointer',
                  minWidth: '200px',
                  appearance: 'none',
                  backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2351D59C%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.4-12.8z%22%2F%3E%3C%2Fsvg%3E")',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'right 12px center',
                  backgroundSize: '12px auto',
                  paddingRight: '32px'
                }}
              >
                <option value="and">Match ALL (AND)</option>
                <option value="or">Match ANY (OR)</option>
              </select>
            </div>
            <button
              onClick={handleResetFilters}
              style={{
                padding: '8px 16px',
                background: '#51D59C',
                border: 'none',
                borderRadius: 6,
                color: '#fff',
                fontSize: '14px',
                fontWeight: 600,
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              Reset Filters
            </button>
          </>
        )}
      </div>
      <div className="ag-theme-quartz" style={{ 
        height: 500, // Reduced from 600 to show pagination
        minHeight: 400, 
        width: '100%', 
        borderRadius: 16, 
        boxShadow: '0 4px 24px rgba(0,0,0,0.07)', 
        overflow: 'hidden' 
      }}>
        <style>{`
          .ag-theme-quartz .ag-row-hover { background: #f8fdfb !important; }
          .ag-theme-quartz .ag-header-cell, .ag-theme-quartz .ag-header-group-cell { background: #f5f7fa; font-weight: 600; }
          .ag-theme-quartz .ag-cell { vertical-align: middle; }
          .ag-theme-quartz .ag-paging-panel { 
            height: 60px;
            padding: 12px;
            border-top: 1px solid #E0E0E0;
          }
        `}</style>
        <AgGridReact
          rowData={filteredGridRows}
          columnDefs={columnDefs}
          domLayout="normal"
          defaultColDef={{
            sortable: true,
            resizable: true,
            editable: false,
            tooltipComponent: 'CustomTooltip',
            enableRowGroup: true,
            enablePivot: false,
            enableValue: true,
            floatingFilter: true,
            suppressMenu: false,
          }}
          rowSelection="multiple"
          rowDragManaged={true}
          animateRows={true}
          pagination={true}
          paginationPageSize={10} // Changed from 20 to 10
          paginationPageSizeSelector={[10, 20, 50, 100]} // Added page size selector
          suppressRowClickSelection={false}
          clipboard={true}
          enableCellTextSelection={true}
          suppressAggFuncInHeader={true}
          suppressDragLeaveHidesColumns={true}
          suppressRowGroupHidesColumns={true}
          suppressColumnVirtualisation={false}
          suppressScrollOnNewData={false}
          enableRangeSelection={true}
          enableBrowserTooltips={true}
          onGridReady={params => {
            setGridApi(params.api);
            setColumnApi(params.columnApi);
          }}
          components={{
            CheckboxRenderer,
            CustomTooltip,
          }}
        />
      </div>
    </div>
  );
});

export default ProfilesPermissionsTable;
