import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Home,
  Inventory2,
  Insights,
  CalendarViewDay,
  Settings,
  ArrowDropDown,
  Menu as MenuIcon,
  Security
} from '@mui/icons-material';
import './Sidebar.css';

const Sidebar = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('tools');

  // Initialize active tab from localStorage or default to 'tools'
  useEffect(() => {
    const savedActiveTab = localStorage.getItem('activeTab');
    if (savedActiveTab) {
      setActiveTab(savedActiveTab);
    }
  }, []);

  // Update active tab when location changes
  useEffect(() => {
    const path = location.pathname.split('/')[1] || 'tools';
    setActiveTab(path);
    localStorage.setItem('activeTab', path);
  }, [location]);

  const toggleSidebar = () => {
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);

    // Add or remove the sidebar-is-collapsed class from the app-container
    const appContainer = document.querySelector('.app-container');
    if (appContainer) {
      if (newCollapsedState) {
        appContainer.classList.add('sidebar-is-collapsed');
      } else {
        appContainer.classList.remove('sidebar-is-collapsed');
      }
    }
  };

  // Set initial collapsed state class on app-container
  useEffect(() => {
    const appContainer = document.querySelector('.app-container');
    if (appContainer && collapsed) {
      appContainer.classList.add('sidebar-is-collapsed');
    }

    // Cleanup function
    return () => {
      if (appContainer) {
        appContainer.classList.remove('sidebar-is-collapsed');
      }
    };
  }, [collapsed]);

  // Navigation items based on actual routes in the application
  const navItems = [
    {
      path: '/tools',
      name: 'Security Tools',
      icon: <Security />,
      hasDropdown: false,
      id: 'tools'
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      icon: <Home />,
      hasDropdown: false,
      id: 'dashboard'
    },
    {
      path: '/integrations',
      name: 'Integrations',
      icon: <Inventory2 />,
      hasDropdown: false,
      id: 'integrations'
    },
    {
      path: '/insights',
      name: 'Insights',
      icon: <Insights />,
      hasDropdown: false,
      id: 'insights'
    },
    {
      path: '/configurations',
      name: 'Configurations',
      icon: <CalendarViewDay />,
      hasDropdown: false,
      id: 'configurations'
    },
    {
      path: '/settings',
      name: 'Settings',
      icon: <Settings />,
      hasDropdown: false,
      id: 'settings'
    }
  ];

  return (
    <div className={`sidebar ${collapsed ? 'sidebar-collapsed' : ''}`}>
      <nav aria-label="Main navigation">
        <ul className="nav-menu">
          {navItems.map((item) => (
            <li
              className={`nav-item ${activeTab === item.id ? 'active' : ''}`}
              key={item.path}
            >
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  isActive ? 'nav-link active' : 'nav-link'
                }
                aria-current={activeTab === item.id ? 'page' : undefined}
              >
                <div className="nav-icon">{item.icon}</div>
                {!collapsed && (
                  <>
                    <span className="nav-text">{item.name}</span>
                    {item.hasDropdown && <ArrowDropDown className="nav-dropdown" />}
                  </>
                )}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      <div className="sidebar-footer">
        {!collapsed && (
          <div className="help-section">
            <h4>Need Help?</h4>
            <p>Contact <NAME_EMAIL></p>
          </div>
        )}
        <button
          className="collapse-button"
          onClick={toggleSidebar}
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <MenuIcon className="collapse-icon" />
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
