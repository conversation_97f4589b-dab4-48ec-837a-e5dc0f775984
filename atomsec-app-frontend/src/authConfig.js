/**
 * Configuration for Azure AD Authentication
 *
 * This file contains the configuration settings for Azure AD authentication.
 * Supports both local development (direct function app calls) and production (Azure APIM).
 *
 * For Web app type registrations, the authentication flow is handled by the backend,
 * which exchanges the authorization code for tokens using a client secret.
 */

// Determine if we're running in production or development
const isProduction = window.location.hostname !== 'localhost';

// Base URL for API calls - Supports both local and APIM
// For local development: use direct function app calls
// For production: use Azure APIM
export const API_BASE_URL = isProduction 
  ? (process.env.REACT_APP_APIM_BASE_URL || 'https://apim-atomsec-dev.azure-api.net/db')
  : (process.env.REACT_APP_DB_SERVICE_URL || 'http://localhost:7072');

// API version (only used for APIM)
export const API_VERSION = process.env.REACT_APP_API_VERSION || 'v1';

// APIM subscription key for authentication (only used in production)
export const APIM_SUBSCRIPTION_KEY = process.env.REACT_APP_APIM_SUBSCRIPTION_KEY || '';

// Helper function to get the correct URL based on environment
const getServiceUrl = (endpoint = '') => {
  if (isProduction) {
    // Production: Use APIM with version
    const baseUrl = API_BASE_URL;
    const version = API_VERSION;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/${version}/${cleanEndpoint}`;
  } else {
    // Development: Use direct function app calls
    const baseUrl = API_BASE_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/api/db/${cleanEndpoint}`;
  }
};

// Azure AD configuration
export const azureConfig = {
  clientId: process.env.REACT_APP_AZURE_CLIENT_ID,
  tenantId: process.env.REACT_APP_AZURE_TENANT_ID,
  // Use the correct redirect URI based on the environment
  redirectUri: isProduction
    ? (process.env.REACT_APP_REDIRECT_URI || "https://app-atomsec-dev01.azurewebsites.net/.auth/login/aad/callback")
    : (process.env.REACT_APP_REDIRECT_URI || "http://localhost:3000"),
  scopes: ["openid", "profile", "email", "User.Read"],
};

// Microsoft Graph API endpoints
export const graphConfig = {
  graphMeEndpoint: "https://graph.microsoft.com/v1.0/me",
};

// Authentication endpoints - Supports both local and APIM
export const authEndpoints = {
  // General auth endpoints
  login: getServiceUrl('/auth/login'),
  signup: getServiceUrl('/auth/signup'),
  verifyLogin: getServiceUrl('/users/login/verify'),

  // Azure AD specific endpoints
  azure: {
    login: getServiceUrl('/auth/azure/login'),
    callback: getServiceUrl('/auth/azure/callback'),
    me: getServiceUrl('/auth/azure/me')
  }
};

// Legacy configuration for backward compatibility
// This can be removed once all components are updated to use authEndpoints
export const azureAuthConfig = {
  loginEndpoint: authEndpoints.azure.login,
  callbackEndpoint: authEndpoints.azure.callback,
  meEndpoint: authEndpoints.azure.me,
  tokenRefreshEndpoint: authEndpoints.verifyLogin,
};
