import axios from 'axios';
import API_CONFIG, { getServiceURL, getEndpoint, getTimeout, isFeatureEnabled } from './config';

// Create axios instances for APIM
const createApiInstance = (baseURL, serviceName) => {
  const instance = axios.create({
    baseURL,
    timeout: getTimeout('request'),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor for authentication and APIM headers
  instance.interceptors.request.use(
    (config) => {
      // Add APIM subscription key if available
      if (API_CONFIG.apimSubscriptionKey) {
        config.headers['Ocp-Apim-Subscription-Key'] = API_CONFIG.apimSubscriptionKey;
      }

      // Add authentication token if available
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add user ID and organization ID from localStorage
      const userId = localStorage.getItem('userId');
      const orgId = localStorage.getItem('organizationId');
      
      if (userId) {
        config.headers['X-User-ID'] = userId;
      }
      if (orgId) {
        config.headers['X-Organization-ID'] = orgId;
      }

      // Add service-specific headers
      config.headers['X-Service'] = serviceName;
      config.headers['X-Client'] = 'atomsec-frontend';

      // Log request in development
      if (API_CONFIG.isDevelopment && isFeatureEnabled('enableApiLogging')) {
        console.log(`[${serviceName}] Request:`, {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: config.headers,
          data: config.data
        });
      }

      return config;
    },
    (error) => {
      console.error(`[${serviceName}] Request error:`, error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response) => {
      // Log response in development
      if (API_CONFIG.isDevelopment && isFeatureEnabled('enableApiLogging')) {
        console.log(`[${serviceName}] Response:`, {
          status: response.status,
          url: response.config.url,
          data: response.data
        });
      }

      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 Unauthorized - try to refresh token
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken) {
            const refreshResponse = await refreshAuthToken(refreshToken);
            if (refreshResponse.success) {
              // Retry the original request with new token
              originalRequest.headers.Authorization = `Bearer ${refreshResponse.accessToken}`;
              return instance(originalRequest);
            }
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Redirect to login
          window.location.href = '/login';
        }
      }

      // Handle other errors
      console.error(`[${serviceName}] Response error:`, {
        status: error.response?.status,
        url: error.config?.url,
        message: error.message,
        data: error.response?.data
      });

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create API instance for APIM
const api = createApiInstance(getServiceURL('apim'), 'apim');

// Authentication API
export const authAPI = {
  // Login with username/password
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  // Sign up new user
  signup: async (userData) => {
    const response = await api.post('/auth/signup', userData);
    return response.data;
  },

  // Verify login
  verifyLogin: async () => {
    const response = await api.post('/users/login/verify');
    return response.data;
  },

  // Azure AD authentication
  azureLogin: async () => {
    const response = await api.get('/auth/azure/login');
    return response.data;
  },

  // Azure AD callback
  azureCallback: async (code) => {
    const response = await api.get(`/auth/azure/callback?code=${code}`);
    return response.data;
  },

  // Get current user info
  getCurrentUser: async () => {
    const response = await api.get('/auth/azure/me');
    return response.data;
  }
};

// User Management API
export const userAPI = {
  // Get all users
  getUsers: async (params = {}) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  // Get user by ID
  getUser: async (userId) => {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  // Get user by email
  getUserByEmail: async (email) => {
    const response = await api.get(`/users/email/${email}`);
    return response.data;
  },

  // Create new user
  createUser: async (userData) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  // Update user
  updateUser: async (userId, userData) => {
    const response = await api.put(`/users/${userId}`, userData);
    return response.data;
  },

  // Delete user
  deleteUser: async (userId) => {
    const response = await api.delete(`/users/${userId}`);
    return response.data;
  },

  // User profile management
  getUserProfile: async () => {
    const response = await api.get('/user/profile');
    return response.data;
  },

  updateUserProfile: async (profileData) => {
    const response = await api.put('/user/profile', profileData);
    return response.data;
  }
};

// Account Management API
export const accountAPI = {
  // Get all accounts
  getAccounts: async (params = {}) => {
    const response = await api.get('/accounts', { params });
    return response.data;
  },

  // Get account by ID
  getAccount: async (accountId) => {
    const response = await api.get(`/accounts/${accountId}`);
    return response.data;
  },

  // Create new account
  createAccount: async (accountData) => {
    const response = await api.post('/accounts', accountData);
    return response.data;
  },

  // Update account
  updateAccount: async (accountId, accountData) => {
    const response = await api.put(`/accounts/${accountId}`, accountData);
    return response.data;
  },

  // Delete account
  deleteAccount: async (accountId) => {
    const response = await api.delete(`/accounts/${accountId}`);
    return response.data;
  }
};

// Integration Management API
export const integrationAPI = {
  // Get all integrations
  getIntegrations: async (params = {}) => {
    const response = await api.get('/integrations', { params });
    return response.data;
  },

  // Get integration by ID
  getIntegration: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}`);
    return response.data;
  },

  // Create new integration
  createIntegration: async (integrationData) => {
    const response = await api.post('/integrations', integrationData);
    return response.data;
  },

  // Update integration
  updateIntegration: async (integrationId, integrationData) => {
    const response = await api.put(`/integrations/${integrationId}`, integrationData);
    return response.data;
  },

  // Delete integration
  deleteIntegration: async (integrationId) => {
    const response = await api.delete(`/integrations/${integrationId}`);
    return response.data;
  },

  // Integration-specific endpoints
  getIntegrationOverview: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}/overview`);
    return response.data;
  },

  getIntegrationHealthCheck: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}/health-check`);
    return response.data;
  },

  getIntegrationProfiles: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}/profiles`);
    return response.data;
  },

  getIntegrationCredentials: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}/credentials`);
    return response.data;
  },

  getIntegrationPmdIssues: async (integrationId) => {
    const response = await api.get(`/integrations/${integrationId}/pmd-issues`);
    return response.data;
  },

  // Integration actions
  scanIntegration: async (integrationId, scanData = {}) => {
    const response = await api.post(`/integration/scan/${integrationId}`, scanData);
    return response.data;
  },

  testConnection: async (connectionData) => {
    const response = await api.post('/integration/test-connection', connectionData);
    return response.data;
  },

  // Legacy function signature for backward compatibility
  testIntegrationConnection: async (tenantUrl, clientId, clientSecret, integrationType, environment, orgName, description, integrationId, useJwt, username, privateKey) => {
    // Get user information from localStorage, with fallbacks for development
    let userEmail = localStorage.getItem('userEmail');
    let userId = localStorage.getItem('userId');

    // In development, if no user info in localStorage, use development defaults
    const isDevelopment = window.location.hostname === 'localhost';
    if (isDevelopment && (!userEmail || !userId)) {
      console.log('Development mode: Using default user information for connection test');
      userEmail = '<EMAIL>';
      userId = 'pranav-kumar-dev';

      // Store in localStorage for consistency
      localStorage.setItem('userEmail', userEmail);
      localStorage.setItem('userId', userId);
    }

    // Convert individual parameters to the expected object format
    const connectionData = {
      tenantUrl,
      clientId,
      clientSecret,
      type: integrationType,
      environment,
      orgName,
      description,
      integrationId,
      useJwt: useJwt || false,
      username,
      privateKey,
      // Include user information for logging/tracking purposes
      userEmail,
      userId
    };

    // Remove null/undefined values to clean up the request
    Object.keys(connectionData).forEach(key => {
      if (connectionData[key] === null || connectionData[key] === undefined) {
        delete connectionData[key];
      }
    });

    const response = await api.post('/integration/test-connection', connectionData);
    return response.data;
  },

  connectIntegration: async (connectionData) => {
    const response = await api.post('/integration/connect', connectionData);
    return response.data;
  },

  // Legacy function signature for backward compatibility
  connectIntegrationLegacy: async (orgName, tenantUrl, clientId, clientSecret, description, integrationType, environment, integrationId, isActive, useJwt, username, privateKey) => {
    // Get user information from localStorage, with fallbacks for development
    let userEmail = localStorage.getItem('userEmail');
    let userId = localStorage.getItem('userId');

    // In development, if no user info in localStorage, use development defaults
    const isDevelopment = window.location.hostname === 'localhost';
    if (isDevelopment && (!userEmail || !userId)) {
      console.log('Development mode: Using default user information for integration creation');
      userEmail = '<EMAIL>';
      userId = 'pranav-kumar-dev';

      // Store in localStorage for consistency
      localStorage.setItem('userEmail', userEmail);
      localStorage.setItem('userId', userId);
    }

    // Convert individual parameters to the expected object format
    const connectionData = {
      orgName,
      tenantUrl,
      clientId,
      clientSecret,
      description,
      type: integrationType,
      environment,
      integrationId,
      isActive: isActive !== undefined ? isActive : true,
      useJwt: useJwt || false,
      username,
      privateKey,
      // Include user information for proper association
      userEmail,
      userId
    };

    // Remove null/undefined values to clean up the request
    Object.keys(connectionData).forEach(key => {
      if (connectionData[key] === null || connectionData[key] === undefined) {
        delete connectionData[key];
      }
    });

    const response = await api.post('/integration/connect', connectionData);
    return response.data;
  }
};

// Security API
export const securityAPI = {
  // Security health checks
  getHealthChecks: async (params = {}) => {
    const response = await api.get('/security/health-checks', { params });
    return response.data;
  },

  storeHealthChecks: async (healthCheckData) => {
    const response = await api.post('/security/health-checks', healthCheckData);
    return response.data;
  },

  // Policies results
  getPoliciesResults: async (params = {}) => {
    const response = await api.get('/security/policies-result', { params });
    return response.data;
  },

  storePoliciesResults: async (policiesData) => {
    const response = await api.post('/security/policies-result', policiesData);
    return response.data;
  },

  // Profile assignment counts
  getProfileAssignmentCounts: async (params = {}) => {
    const response = await api.get('/security/profile-assignment-counts', { params });
    return response.data;
  },

  storeProfileAssignmentCounts: async (countData) => {
    const response = await api.post('/security/profile-assignment-counts', countData);
    return response.data;
  }
};

// Task Management API
export const taskAPI = {
  // Get all tasks
  getTasks: async (params = {}) => {
    const response = await api.get('/tasks', { params });
    return response.data;
  },

  // Get task by ID
  getTask: async (taskId) => {
    const response = await api.get(`/tasks/${taskId}`);
    return response.data;
  },

  // Create new task
  createTask: async (taskData) => {
    const response = await api.post('/tasks', taskData);
    return response.data;
  },

  // Update task status
  updateTaskStatus: async (taskId, statusData) => {
    const response = await api.put(`/tasks/${taskId}/status`, statusData);
    return response.data;
  }
};

// System API
export const systemAPI = {
  // Health checks
  getHealth: async () => {
    const response = await api.get('/health');
    return response.data;
  },

  // System info
  getInfo: async () => {
    const response = await api.get('/info');
    return response.data;
  }
};

// Helper function to refresh authentication token
const refreshAuthToken = async (refreshToken) => {
  try {
    const response = await authAPI.refreshToken(refreshToken);
    if (response.success) {
      localStorage.setItem('accessToken', response.accessToken);
      if (response.refreshToken) {
        localStorage.setItem('refreshToken', response.refreshToken);
      }
    }
    return response;
  } catch (error) {
    console.error('Token refresh failed:', error);
    // Clear stored tokens
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    throw error;
  }
};

// Export all APIs
export default {
  auth: authAPI,
  users: userAPI,
  accounts: accountAPI,
  integrations: integrationAPI,
  security: securityAPI,
  tasks: taskAPI,
  system: systemAPI
};

// Individual function exports for backward compatibility
// These functions maintain the same interface as the old API

// Authentication functions
export const login = authAPI.login;
export const signup = authAPI.signup;
export const verifyLogin = authAPI.verifyLogin;
export const azureLogin = authAPI.azureLogin;
export const azureCallback = authAPI.azureCallback;
export const getCurrentUser = authAPI.getCurrentUser;

// User management functions
export const getUsers = userAPI.getUsers;
export const getUser = userAPI.getUser;
export const getUserByEmail = userAPI.getUserByEmail;
export const createUser = userAPI.createUser;
export const updateUser = userAPI.updateUser;
export const deleteUser = userAPI.deleteUser;
export const getUserProfile = userAPI.getUserProfile;
export const updateUserProfile = userAPI.updateUserProfile;

// Account management functions
export const getAccounts = accountAPI.getAccounts;
export const getAccount = accountAPI.getAccount;
export const createAccount = accountAPI.createAccount;
export const updateAccount = accountAPI.updateAccount;
export const deleteAccount = accountAPI.deleteAccount;

// Integration management functions
export const getIntegrations = integrationAPI.getIntegrations;
export const getIntegration = integrationAPI.getIntegration;
export const createIntegration = integrationAPI.createIntegration;
export const updateIntegration = integrationAPI.updateIntegration;
export const deleteIntegration = integrationAPI.deleteIntegration;
export const getIntegrationOverview = integrationAPI.getIntegrationOverview;
export const getIntegrationHealthCheck = integrationAPI.getIntegrationHealthCheck;
export const getIntegrationProfiles = integrationAPI.getIntegrationProfiles;
export const getIntegrationCredentials = integrationAPI.getIntegrationCredentials;
export const getIntegrationPmdIssues = integrationAPI.getIntegrationPmdIssues;
export const scanIntegration = integrationAPI.scanIntegration;
export const testConnection = integrationAPI.testConnection;
export const connectIntegration = integrationAPI.connectIntegrationLegacy;

// Security functions
export const getHealthChecks = securityAPI.getHealthChecks;
export const storeHealthChecks = securityAPI.storeHealthChecks;
export const getPoliciesResults = securityAPI.getPoliciesResults;
export const storePoliciesResults = securityAPI.storePoliciesResults;
export const getProfileAssignmentCounts = securityAPI.getProfileAssignmentCounts;
export const storeProfileAssignmentCounts = securityAPI.storeProfileAssignmentCounts;

// Task management functions
export const getTasks = taskAPI.getTasks;
export const getTask = taskAPI.getTask;
export const createTask = taskAPI.createTask;
export const updateTaskStatus = taskAPI.updateTaskStatus;

// System functions
export const getHealth = systemAPI.getHealth;
export const getInfo = systemAPI.getInfo;

// Legacy function names for backward compatibility
export const fetchAccounts = getAccounts;
export const fetchUsers = getUsers;
export const fetchIntegrations = getIntegrations;
export const fetchUserProfile = getUserProfile;
export const testIntegrationConnection = integrationAPI.testIntegrationConnection;
export const fetchScanAccounts = getAccounts; // Assuming this is the same as getAccounts

// Legacy functions for backward compatibility
export const fetchOrgDetails = async (instanceUrl, forceRefresh = false) => {
  try {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('forceRefresh', 'true');
    }
    if (instanceUrl) {
      params.append('instanceUrl', instanceUrl);
    }

    const response = await api.get(`/integrations/org-details?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching org details:', error);
    throw error;
  }
};

export const fetchHealthScore = async (instanceUrl, forceRefresh = false) => {
  try {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('forceRefresh', 'true');
    }
    if (instanceUrl) {
      params.append('instanceUrl', instanceUrl);
    }

    const response = await api.get(`/security/health-score?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching health score:', error);
    throw error;
  }
};

export const fetchHealthRisks = async (instanceUrl, forceRefresh = false) => {
  try {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('forceRefresh', 'true');
    }
    if (instanceUrl) {
      params.append('instanceUrl', instanceUrl);
    }

    const response = await api.get(`/security/health-risks?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching health risks:', error);
    throw error;
  }
};

export const fetchProfiles = async (instanceUrl, forceRefresh = false) => {
  try {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('forceRefresh', 'true');
    }
    if (instanceUrl) {
      params.append('instanceUrl', instanceUrl);
    }

    const response = await api.get(`/security/profiles?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching profiles:', error);
    throw error;
  }
};

export const fetchPermissionSets = async (instanceUrl, forceRefresh = false) => {
  try {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('forceRefresh', 'true');
    }
    if (instanceUrl) {
      params.append('instanceUrl', instanceUrl);
    }

    const response = await api.get(`/security/permission-sets?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching permission sets:', error);
    throw error;
  }
};

export const fetchProfilesPermissions = async (orgId, executionLogId = null) => {
  try {
    const params = new URLSearchParams();
    if (orgId) {
      params.append('orgId', orgId);
    }
    if (executionLogId) {
      params.append('executionLogId', executionLogId);
    }

    const response = await api.get(`/security/profiles-permissions?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching profiles permissions:', error);
    throw error;
  }
};

export const changePassword = async (currentPassword, newPassword) => {
  try {
    const response = await api.post('/users/change-password', {
      currentPassword,
      newPassword
    });
    return response.data;
  } catch (error) {
    console.error('Error changing password:', error);
    throw error;
  }
};

export const fetchRoles = async (accountId) => {
  // This function doesn't exist in the new API, so we'll create a placeholder
  // You may need to implement this based on your requirements
  console.warn('fetchRoles function is not implemented in the new API structure');
  return [];
};
export const assignUserRole = async (userId, roleId) => {
  // This function doesn't exist in the new API, so we'll create a placeholder
  // You may need to implement this based on your requirements
  console.warn('assignUserRole function is not implemented in the new API structure');
  return { success: true };
};

// Backward compatibility: rescanOrg is an alias for scanIntegration
export const rescanOrg = scanIntegration;

// Backward compatibility: Integration details fetchers
export const fetchIntegrationOverviewById = async (integrationId, forceRefresh = false) => {
  // Optionally pass forceRefresh as a query param if backend supports it
  return await integrationAPI.getIntegrationOverview(integrationId);
};

export const fetchIntegrationHealthCheckById = async (integrationId, forceRefresh = false) => {
  return await integrationAPI.getIntegrationHealthCheck(integrationId);
};

export const fetchIntegrationProfilesById = async (integrationId, forceRefresh = false) => {
  return await integrationAPI.getIntegrationProfiles(integrationId);
};

export const fetchIntegrationPMDById = async (integrationId, forceRefresh = false) => {
  return await integrationAPI.getIntegrationPmdIssues(integrationId);
};

export const fetchPoliciesResultByIntegrationId = async (integrationId, executionLogId = null, type = null) => {
  // The security API expects org_id parameter, not integrationId
  const params = { org_id: integrationId };
  
  if (executionLogId) {
    params.execution_log_id = executionLogId;
  }
  
  if (type) {
    params.type = type;
  }
  
  console.log('fetchPoliciesResultByIntegrationId - Parameters being sent:', params);
  
  return await securityAPI.getPoliciesResults(params);
};

// Backward compatibility: getTaskStatus for task status queries
export const getTaskStatus = async (integrationId, status, page = 1, type = '') => {
  // Use the correct task-status endpoint instead of tasks endpoint
  const params = {
    integration_id: integrationId,  // Use integration_id parameter
    status: status,                 // Keep status as is
    task_type: type,                // Map type to task_type
    limit: 10                       // Add limit parameter
  };
  
  try {
    const response = await api.get('/tasks', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching task status:', error);
    throw error;
  }
};
