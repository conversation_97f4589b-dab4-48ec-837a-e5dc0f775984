import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate, useParams } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ScanWizard from './components/ScanWizard';
import Login from './components/Login';
import Signup from './components/Signup';
import Accounts from './components/Accounts';
import Dashboard from './components/Dashboard';
import Reports from './components/Reports';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Footer from './components/Footer';
import HealthCheckDetails from './components/HealthCheckDetails';
import IntegrationTabs from './components/IntegrationTabs';
import Integrations from './components/Integrations';
import Insights from './components/Insights';
import Configurations from './components/Configurations';
import Settings from './components/Settings';
import TaskManagement from './components/TaskManagement';
import AccountManagement from './components/AccountManagement';
import AuthTest from './components/AuthTest';
import AzureSetupGuide from './components/AzureSetupGuide';
import AuthCallback from './components/AuthCallback';
import PlatformAuthHandler from './components/PlatformAuthHandler';
import SecurityToolsSelector from './components/SecurityToolsSelector';

import './App.css';

// Redirect component for integration routes
const IntegrationRedirect = () => {
  const { integrationId } = useParams();
  return <Navigate to={`/integration/${integrationId}`} replace />;
};

// Removed MSAL instance logic

// Protected route component that uses the AuthContext
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <div className="loading-indicator">Loading...</div>;
  }

  if (!isAuthenticated) {
    // Redirect to login page but save the intended destination
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return children;
};

// Main application content with routes
function AppContent() {
  const { user, logout, loading, error, isAuthenticated } = useAuth();
  const userEmail = user?.username || '';
  const navigate = useNavigate();
  const location = useLocation();

  // Fallback redirect mechanism
  useEffect(() => {
    // If authenticated and on login page, redirect to tools page or saved path
    if (isAuthenticated && location.pathname === '/login') {
      const redirectPath = localStorage.getItem('loginRedirectPath') || '/tools';
      localStorage.removeItem('loginRedirectPath');
      navigate(redirectPath);
    }
  }, [isAuthenticated, location.pathname, navigate]);

  return (
    <div className="App">
      {error && <div className="error-message">{error}</div>}
      {loading && <div className="loading-indicator">Loading...</div>}

      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<Signup />} />
        <Route path="/auth-test" element={<AuthTest />} />
        <Route path="/azure-setup" element={<AzureSetupGuide />} />
        <Route path="/auth-callback" element={<AuthCallback />} />
        <Route path="/platform-auth" element={<PlatformAuthHandler />} />

        {/* Security Tools Selector Route - Post-login landing page */}
        <Route
          path="/tools"
          element={
            <ProtectedRoute>
              <SecurityToolsSelector />
            </ProtectedRoute>
          }
        />


        {/* Dashboard Route with Layout */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Dashboard />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Root route redirects to tools */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Navigate to="/tools" replace />
            </ProtectedRoute>
          }
        />

        <Route
          path="/scan"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <div className="page-header">
                      <h1 className="page-title">New Scan</h1>
                      <p className="page-description">Configure and run a new security scan</p>
                    </div>
                    <ScanWizard userEmail={userEmail} />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Accounts route removed - now using /integrations instead */}

        {/* Reports Route */}
        <Route
          path="/reports"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Reports />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Integration Details Route (by ID) */}
        <Route
          path="/integration/:integrationId"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <IntegrationTabs />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Integrations Route */}
        <Route
          path="/integrations"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Integrations />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Insights Route */}
        <Route
          path="/insights"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Insights />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Configurations Route */}
        <Route
          path="/configurations"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Configurations />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Settings Route */}
        <Route
          path="/settings"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <Settings />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Task Management Routes */}
        <Route
          path="/tasks/:orgId"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <div className="page-header">
                      <h1 className="page-title">Task Management</h1>
                      <p className="page-description">Monitor and manage background tasks</p>
                    </div>
                    <TaskManagement orgId={window.location.pathname.split('/')[2]} />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        <Route
          path="/tasks"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <div className="page-header">
                      <h1 className="page-title">Task Management</h1>
                      <p className="page-description">Please select an organization to view tasks</p>
                    </div>
                    <div className="card">
                      <div className="empty-state">
                        <h3>No Organization Selected</h3>
                        <p>Please select an organization from the sidebar or go to the Integrations page to create a new integration.</p>
                      </div>
                    </div>
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Account Management Route */}
        <Route
          path="/account-management"
          element={
            <ProtectedRoute>
              <>
                <Header userEmail={userEmail} onLogout={logout} />
                <div className="app-container">
                  <Sidebar />
                  <main className="main-content">
                    <AccountManagement />
                  </main>
                </div>
                <Footer />
              </>
            </ProtectedRoute>
          }
        />

        {/* Legacy route redirect - redirect old URL format to integrations page */}
        <Route
          path="/org/:instanceUrl"
          element={
            <ProtectedRoute>
              <Navigate to="/integrations" replace />
            </ProtectedRoute>
          }
        />

        {/* Redirect from plural to singular integration route */}
        <Route
          path="/integrations/:integrationId"
          element={
            <ProtectedRoute>
              <IntegrationRedirect />
            </ProtectedRoute>
          }
        />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/tools" replace />} />
      </Routes>
    </div>
  );
}

// Main App component that wraps everything with Auth provider
function App() {
  return (
    <Router>
      <AuthProvider>
        <ToastContainer position="top-right" autoClose={5000} />
        <AppContent />
      </AuthProvider>
    </Router>
  );
}

export default App;
