.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main Layout */
.app-container {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 60px); /* Adjust based on header height */
  position: relative;
  margin-top: 60px; /* Match header height */
}

/* Header */
.app-header {
  height: 60px;
  background-color: #000000;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.app-logo {
  height: 40px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-profile {
  position: relative;
}

.user-profile-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);

  color: #FFFFFF;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  margin-top: var(--spacing-sm);
  overflow: hidden;
  animation: fadeIn var(--transition-fast);
}

.user-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-dropdown li {
  border-bottom: 1px solid var(--color-gray-200);
}

.user-dropdown li:last-child {
  border-bottom: none;
}

.user-dropdown a,
.user-dropdown button {
  display: block;
  padding: var(--spacing-md);
  text-decoration: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  text-align: left;
  width: 100%;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.user-dropdown a:hover,
.user-dropdown button:hover {
  background-color: var(--color-gray-100);
}

.notification-button {
  position: relative;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-danger);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar - App.css version (not used) */
/* These styles are overridden by Sidebar.css */
.app-sidebar {
  width: 250px;
  background-color: var(--color-bg-main);
  border-right: 1px solid var(--color-border);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  overflow-x: hidden;
}

.app-sidebar-collapsed {
  width: 70px;
  padding: var(--spacing-md) 8px;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
  width: 100%;
}

.nav-item {
  margin-bottom: var(--spacing-sm);
  width: 100%;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  width: 100%;
  white-space: nowrap;
}

.nav-link:hover {
  background-color: var(--color-gray-100);
  color: #51D59C;
}

.nav-link.active {
  background-color: #F1FCF7;
  color: var(--color-text-primary);
}

.nav-icon {
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-sm);
}

.sidebar-collapsed .nav-link {
  padding: var(--spacing-sm);
  justify-content: center;
}

.sidebar-collapsed .nav-icon {
  margin-right: 0;
}

/* Adjust main content when sidebar is collapsed */
.app-container.sidebar-is-collapsed .main-content {
  margin-left: 64px; /* Match collapsed sidebar width */
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-lg);
  background-color: var(--color-bg-light);
  overflow-y: auto;
  margin-left: 250px; /* Match sidebar width */
  padding-bottom: 100px; /* Add padding to prevent content from being hidden behind footer */
}

/* Page Header */
.page-header {
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
}

/* Cards */
.card {
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  /* Sidebar styles are in Sidebar.css */

  .main-content,
  .app-container.sidebar-is-collapsed .main-content {
    padding: var(--spacing-md);
    margin-left: 0;
    margin-top: 60px; /* Add space for the mobile sidebar */
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}

/* Buttons */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border: none;
  text-align: center;
}

/* Messages */
.error-message {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.success-message {
  color: #2e7d32;
  background-color: #e8f5e9;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: #3DC488;
}

.btn-secondary {
  background-color: var(--color-gray-300);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  background-color: var(--color-gray-400);
}
