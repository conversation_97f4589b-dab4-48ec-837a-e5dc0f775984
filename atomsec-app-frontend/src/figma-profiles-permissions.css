@import './settings-risk-table.css';

/* Main content area */
.figma-main-content {
  padding: 32px;
  background: #fff;
  min-width: 1136px;
}

/* Card styling */
.figma-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  padding: 40px 32px 32px 32px;
  margin-bottom: 24px;
  overflow: hidden;
}

.figma-card .ag-root-wrapper {
  border-radius: 16px;
  overflow: hidden;
}

/* AG Grid overrides for Figma look */
.ag-theme-quartz {
  --ag-background-color: #fff;
  --ag-header-background-color: #fff;
  --ag-header-foreground-color: #222;
  --ag-row-hover-color: #f5f7fa;
  --ag-font-family: 'Inter', sans-serif;
  --ag-border-radius: 4px;
  --ag-card-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  font-size: 15px;
}

.ag-theme-quartz .ag-header-cell-label {
  font-weight: 600;
  font-size: 16px;
}

.ag-theme-quartz .ag-cell {
  border-radius: 4px;
}

.bubble-chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 24px;
  justify-content: left;
  align-items: stretch;
  width: 100%;
  min-height: 0;
  margin-left: auto;
  margin-right: auto;
}

.bubble-chart-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  padding: 24px;
  min-width: 220px;
  max-width: 33%;
  min-height: 0;
  flex: 1 1 31%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: auto;
}

.bubble-chart-card-inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bubble-chart-title {
  font-weight: 900;
  font-size: 20px;
  margin-bottom: 12px;
  text-align: center;
}

.bubble-chart-svg {
  width: 100%;
  max-width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
}

.bubble-chart-svg text {
  white-space: pre;
  text-anchor: middle;
  dominant-baseline: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.highlight-row {
  background: #e6f7f0 !important;
}

@media (max-width: 1000px) {
  .bubble-chart-card {
    max-width: 100%;
    min-width: 90vw;
    flex: 1 1 100%;
  }
  .bubble-chart-row {
    flex-direction: column;
    gap: 16px;
  }
}
