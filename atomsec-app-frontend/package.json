{"name": "atomsec-app-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@azure/msal-browser": "^4.11.0", "@azure/msal-node": "^3.5.1", "@azure/msal-react": "^3.0.10", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ag-grid-community": "^33.3.2", "ag-grid-react": "^33.3.1", "antd": "^5.24.8", "axios": "^1.8.4", "bootstrap": "^5.3.2", "chart.js": "^4.4.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "eslint-config-react-app": "^7.0.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "react-toastify": "^9.1.3", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "http-proxy-middleware": "^2.0.6"}, "scripts": {"start": "react-scripts start", "build": "npx update-browserslist-db@latest && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}