# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

steps:
- script: echo Starting the atomsec react deployment script
  displayName: 'Starting the atomsec react deployment script'

- script: |
    echo Add other tasks to build, test, and deploy your project.
    echo See https://aka.ms/yaml
    npm install
    npx update-browserslist-db@latest
    npm install --save-dev @babel/plugin-proposal-private-property-in-object@7.21.11
    # Set environment variables for production build
    echo "Setting production environment variables..."
    echo "# Production environment settings" > .env.production
    echo "# Azure APIM Configuration" >> .env.production
    echo "REACT_APP_APIM_BASE_URL=**************************************/db" >> .env.production
    echo "REACT_APP_API_VERSION=v1" >> .env.production
    echo "REACT_APP_APIM_SUBSCRIPTION_KEY=bd50cc1018444ae987a04c465534e428" >> .env.production
    echo "# Azure AD Authentication" >> .env.production
    echo "REACT_APP_AZURE_CLIENT_ID=2d313c1a-d62d-492c-869e-cf8cb9258204" >> .env.production
    echo "REACT_APP_AZURE_TENANT_ID=41b676db-bf6f-46ae-a354-a83a1362533f" >> .env.production
    echo "REACT_APP_REDIRECT_URI=https://app-atomsec-dev01.azurewebsites.net/.auth/login/aad/callback" >> .env.production
    echo "# Feature Flags" >> .env.production
    echo "REACT_APP_ENABLE_SERVICE_BUS=true" >> .env.production
    echo "REACT_APP_ENABLE_KEY_VAULT=true" >> .env.production
    echo "REACT_APP_ENABLE_REAL_TIME=true" >> .env.production
    echo "REACT_APP_ENABLE_ADVANCED_SECURITY=true" >> .env.production
    echo "# Logging Configuration" >> .env.production
    echo "REACT_APP_LOG_LEVEL=info" >> .env.production
    echo "REACT_APP_ENABLE_CONSOLE_LOGS=false" >> .env.production
    echo "REACT_APP_ENABLE_REMOTE_LOGS=true" >> .env.production
    echo "REACT_APP_REPORT_ERRORS=true" >> .env.production
    echo "# Security Settings" >> .env.production
    echo "REACT_APP_ENABLE_CSP=true" >> .env.production
    echo "REACT_APP_ENABLE_HSTS=true" >> .env.production
    echo "# Performance Monitoring" >> .env.production
    echo "REACT_APP_ENABLE_METRICS=true" >> .env.production
    echo "REACT_APP_ENABLE_PROFILING=false" >> .env.production
    echo "REACT_APP_LANGUAGE=en" >> .env.production
    echo "REACT_APP_DATE_FORMAT=MM/DD/YYYY" >> .env.production
    echo "REACT_APP_TIME_FORMAT=HH:mm:ss" >> .env.production
    echo "REACT_APP_TIMEZONE=UTC" >> .env.production
    echo "# Development Tools (disabled in production)" >> .env.production
    echo "REACT_APP_ENABLE_REDUX_DEVTOOLS=false" >> .env.production
    echo "REACT_APP_ENABLE_REACT_DEVTOOLS=false" >> .env.production
    echo "REACT_APP_ENABLE_API_LOGGING=false" >> .env.production
    echo "REACT_APP_ENABLE_PERFORMANCE_MONITORING=true" >> .env.production
    echo "# External Tools URLs" >> .env.production
    echo "REACT_APP_OPEN_SEARCH_URL=https://opensearch.atomsec.ai" >> .env.production
    echo "REACT_APP_GREY_LOG_URL=https://greylog.atomsec.ai" >> .env.production
    echo "REACT_APP_WAZUH_URL=https://wazuh.atomsec.ai" >> .env.production
    echo "REACT_APP_NMAP_URL=https://nmap.atomsec.ai" >> .env.production
    cat .env.production
    npm run build
    # Copy configuration files to the build directory
    cp authentication.json build/
    cp web.config build/
  displayName: 'npm install and build'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: 'build'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true
    verbose: true

- task: AzureRmWebAppDeployment@4
  inputs:
    ConnectionType: 'AzureRM'
    azureSubscription: 'sc-atomsec-dev-frontend'
    appType: 'webApp'
    WebAppName: 'app-atomsec-dev01'
    packageForLinux: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'