{"platform": {"enabled": true}, "globalValidation": {"unauthenticatedClientAction": "AllowAnonymous", "redirectToProvider": "azureActiveDirectory", "excludedPaths": ["/manifest.json", "/favicon.ico", "/static/*", "/*.json", "/*.png", "/*.jpg", "/*.svg", "/*.ico", "/*.js", "/*.css", "/*.txt", "/.well-known/*", "/login", "/platform-auth", "/"]}, "identityProviders": {"azureActiveDirectory": {"enabled": true, "registration": {"openIdIssuer": "https://login.microsoftonline.com/41b676db-bf6f-46ae-a354-a83a1362533f/v2.0", "clientId": "2d313c1a-d62d-492c-869e-cf8cb9258204", "clientSecretSettingName": "MICROSOFT_PROVIDER_AUTHENTICATION_SECRET"}, "login": {"loginParameters": ["response_type=code id_token", "response_mode=form_post"], "postLoginRedirectUrl": "/platform-auth"}}}, "login": {"tokenStore": {"enabled": true}, "preserveUrlFragmentsForLogins": true, "routes": {"loginSuccessUrl": "/platform-auth", "logoutSuccessUrl": "/.auth/login/aad"}, "allowedExternalRedirectUrls": ["https://app-atomsec-dev01.azurewebsites.net", "https://app-atomsec-dev01.azurewebsites.net/", "https://app-atomsec-dev01.azurewebsites.net/platform-auth", "https://app-atomsec-dev01.azurewebsites.net/dashboard", "https://app-atomsec-dev01.azurewebsites.net/integrations"]}}