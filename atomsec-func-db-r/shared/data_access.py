"""
Data Access Module

This module provides repository classes for accessing various Azure data services.
It implements the repository pattern to abstract data access logic from business logic.

Best practices implemented:
- Repository pattern for data access abstraction
- Proper error handling and logging
- Consistent interface across different storage types
- Automatic container/table/queue creation
- Support for local development with Azurite
"""

import json
import logging
import struct
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from shared.azure_services import (
    get_blob_client,
    get_table_client,
    get_queue_client,
    get_credential,
    is_local_dev
)

# Configure module-level logger
logger = logging.getLogger(__name__)

class BlobStorageRepository:
    """Repository for accessing Blob Storage"""

    def __init__(self, container_name: str = "data"):
        """
        Initialize the blob storage repository

        Args:
            container_name: Name of the blob container
        """
        self.blob_service = get_blob_client()
        self.container_name = container_name

        # Ensure container exists
        try:
            self.container_client = self.blob_service.get_container_client(container_name)
            # Check if container exists and create if it doesn't
            try:
                self.container_client.get_container_properties()
                logger.debug(f"Using existing blob container: {container_name}")
            except Exception:
                self.container_client = self.blob_service.create_container(container_name)
                logger.info(f"Created blob container: {container_name}")
        except Exception as e:
            logger.error(f"Error initializing blob container: {str(e)}")
            raise

    def save_json(self, blob_name: str, data: Any) -> bool:
        """
        Save JSON data to a blob

        Args:
            blob_name: Name of the blob
            data: Data to save (will be converted to JSON)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            json_data = json.dumps(data)
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.upload_blob(json_data, overwrite=True)
            logger.info(f"Saved JSON data to blob: {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Error saving JSON to blob {blob_name}: {str(e)}")
            return False

    def get_json(self, blob_name: str) -> Optional[Any]:
        """
        Get JSON data from a blob

        Args:
            blob_name: Name of the blob

        Returns:
            Any: Parsed JSON data or None if error
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            data = blob_client.download_blob().readall()
            logger.debug(f"Retrieved data from blob: {blob_name}")
            return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting JSON from blob {blob_name}: {str(e)}")
            return None

    def list_blobs(self, name_starts_with: Optional[str] = None) -> List[str]:
        """
        List blobs in the container

        Args:
            name_starts_with: Optional prefix to filter blobs

        Returns:
            List[str]: List of blob names
        """
        try:
            blobs = [blob.name for blob in self.container_client.list_blobs(name_starts_with=name_starts_with)]
            logger.debug(f"Listed {len(blobs)} blobs in container {self.container_name}")
            return blobs
        except Exception as e:
            logger.error(f"Error listing blobs: {str(e)}")
            return []

    def delete_blob(self, blob_name: str) -> bool:
        """
        Delete a blob

        Args:
            blob_name: Name of the blob to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.delete_blob()
            logger.info(f"Deleted blob: {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting blob {blob_name}: {str(e)}")
            return False


class TableStorageRepository:
    """Repository for accessing Table Storage"""

    def __init__(self, table_name: str):
        """
        Initialize the table storage repository

        Args:
            table_name: Name of the table
        """
        self.table_service = get_table_client()
        self.table_name = table_name

        # Ensure table exists
        try:
            logger.info(f"Initializing table: {table_name}")
            logger.info(f"Table service type: {type(self.table_service)}")

            # Check if table exists first
            try:
                tables = list(self.table_service.list_tables())
                table_names = [table.name for table in tables]
                logger.info(f"Existing tables: {table_names}")

                if table_name in table_names:
                    logger.info(f"Table {table_name} already exists")
                    self.table_client = self.table_service.get_table_client(table_name)
                else:
                    logger.info(f"Creating table {table_name}")
                    # Azure Data Tables uses a different method name
                    self.table_client = self.table_service.create_table_if_not_exists(table_name)
            except Exception as list_e:
                logger.warning(f"Error listing tables: {str(list_e)}, proceeding with create_table_if_not_exists")
                # Azure Data Tables uses a different method name
                self.table_client = self.table_service.create_table_if_not_exists(table_name)

            logger.info(f"Table initialized: {table_name}")
            logger.info(f"Table client type: {type(self.table_client)}")
        except Exception as e:
            logger.error(f"Error initializing table: {str(e)}")
            raise

    def insert_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Insert an entity into the table

        Args:
            entity: Entity to insert (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure partition key and row key
            if 'PartitionKey' not in entity:
                entity['PartitionKey'] = datetime.now().strftime("%Y%m%d")
                logger.info(f"Added default PartitionKey: {entity['PartitionKey']}")
            if 'RowKey' not in entity:
                entity['RowKey'] = datetime.now().strftime("%H%M%S%f")
                logger.info(f"Added default RowKey: {entity['RowKey']}")

            logger.info(f"Inserting entity into table {self.table_name}: {entity}")

            # Azure Data Tables uses upsert_entity instead of create_entity
            try:
                self.table_client.upsert_entity(entity)
                logger.info(f"Successfully inserted entity with RowKey {entity.get('RowKey')} into table {self.table_name}")

                # Verify the entity was inserted by trying to retrieve it
                try:
                    partition_key = entity.get('PartitionKey')
                    row_key = entity.get('RowKey')
                    filter_query = f"PartitionKey eq '{partition_key}' and RowKey eq '{row_key}'"
                    verification_entities = list(self.table_client.query_entities(query_filter=filter_query))
                    if verification_entities:
                        logger.info(f"Verified entity exists in table {self.table_name}: {verification_entities[0]}")
                    else:
                        logger.warning(f"Entity verification failed - could not find entity with PartitionKey={partition_key}, RowKey={row_key} in table {self.table_name}")
                except Exception as ve:
                    logger.warning(f"Error verifying entity insertion: {str(ve)}")

                return True
            except Exception as ue:
                logger.error(f"Error in upsert_entity operation: {str(ue)}")
                return False
        except Exception as e:
            logger.error(f"Error inserting entity: {str(e)}")
            return False

    def query_entities(self, filter_query: Optional[str] = None, field_filter: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Query entities from the table

        Args:
            filter_query: Optional OData filter query
            field_filter: Optional dictionary of field name to value for filtering

        Returns:
            List[Dict[str, Any]]: List of entities
        """
        try:
            # Log the query parameters
            logger.info(f"Querying table {self.table_name} with filter: {filter_query}")

            # Azure Data Tables uses query_entities with different parameters
            # Handle different versions of the Azure Data Tables SDK
            try:
                if filter_query:
                    logger.info(f"Using filter query: {filter_query}")
                    entities = self.table_client.query_entities(query_filter=filter_query)
                else:
                    # Try with empty string as query_filter (newer SDK versions)
                    logger.info("Using empty filter query")
                    entities = self.table_client.query_entities(query_filter="")
            except TypeError as te:
                logger.warning(f"TypeError in query_entities: {str(te)}")
                # For older SDK versions that don't require query_filter
                try:
                    logger.info("Falling back to query_entities without parameters")
                    entities = self.table_client.query_entities()
                except Exception as inner_e:
                    logger.error(f"Failed to query entities with older SDK approach: {str(inner_e)}")
                    return []

            # Convert to list for further processing
            result = list(entities)
            logger.info(f"Retrieved {len(result)} entities from table {self.table_name}")

            # Log the first few entities for debugging
            if result:
                for i, entity in enumerate(result[:3]):  # Log up to 3 entities
                    logger.info(f"Entity {i+1}: {entity}")
                if len(result) > 3:
                    logger.info(f"... and {len(result) - 3} more entities")

            # Apply field filtering if provided
            if field_filter and result:
                filtered_result = []
                for entity in result:
                    include = True
                    for field, value in field_filter.items():
                        if field in entity:
                            entity_value = str(entity[field]).lower()
                            filter_value = str(value).lower()
                            if filter_value not in entity_value:
                                include = False
                                break
                    if include:
                        filtered_result.append(entity)
                logger.debug(f"Filtered from {len(result)} to {len(filtered_result)} entities")
                return filtered_result

            return result
        except Exception as e:
            logger.error(f"Error querying entities: {str(e)}")
            return []

    def update_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Update an entity in the table

        Args:
            entity: Entity to update (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure entity has partition key and row key
            if 'PartitionKey' not in entity or 'RowKey' not in entity:
                logger.error("Entity must have PartitionKey and RowKey")
                return False

            # Azure Data Tables uses upsert_entity for updates
            self.table_client.upsert_entity(entity)
            logger.debug(f"Updated entity with RowKey {entity.get('RowKey')} in table {self.table_name}")
            return True
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False

    def insert_or_update_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Insert or update an entity in the table (upsert operation)

        Args:
            entity: Entity to insert or update (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure partition key and row key
            if 'PartitionKey' not in entity:
                entity['PartitionKey'] = datetime.now().strftime("%Y%m%d")
                logger.info(f"Added default PartitionKey: {entity['PartitionKey']}")
            if 'RowKey' not in entity:
                entity['RowKey'] = datetime.now().strftime("%H%M%S%f")
                logger.info(f"Added default RowKey: {entity['RowKey']}")

            logger.info(f"Upserting entity into table {self.table_name}: {entity}")

            # Azure Data Tables uses upsert_entity for insert or update
            self.table_client.upsert_entity(entity)
            logger.info(f"Successfully upserted entity with RowKey {entity.get('RowKey')} into table {self.table_name}")
            return True
        except Exception as e:
            logger.error(f"Error upserting entity: {str(e)}")
            return False

    def get_entity(self, partition_key: str, row_key: str) -> Optional[Dict[str, Any]]:
        """
        Get a single entity from the table by partition key and row key

        Args:
            partition_key: Partition key of the entity
            row_key: Row key of the entity

        Returns:
            Optional[Dict[str, Any]]: Entity if found, None otherwise
        """
        try:
            # Create a filter query for the specific entity
            filter_query = f"PartitionKey eq '{partition_key}' and RowKey eq '{row_key}'"
            entities = self.query_entities(filter_query)

            if not entities:
                logger.warning(f"Entity with PartitionKey {partition_key} and RowKey {row_key} not found")
                return None

            # Return the first (and should be only) entity
            logger.debug(f"Retrieved entity with PartitionKey {partition_key} and RowKey {row_key}")
            return entities[0]
        except Exception as e:
            logger.error(f"Error getting entity: {str(e)}")
            return None

    def delete_entity(self, partition_key: str, row_key: str) -> bool:
        """
        Delete an entity from the table

        Args:
            partition_key: Partition key of the entity
            row_key: Row key of the entity

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.table_client.delete_entity(partition_key, row_key)
            logger.info(f"Deleted entity with PartitionKey {partition_key} and RowKey {row_key}")
            return True
        except Exception as e:
            logger.error(f"Error deleting entity: {str(e)}")
            return False


class QueueStorageRepository:
    """Repository for accessing Queue Storage"""

    def __init__(self, queue_name: str):
        """
        Initialize the queue storage repository

        Args:
            queue_name: Name of the queue
        """
        self.queue_service = get_queue_client()
        self.queue_name = queue_name

        # Ensure queue exists
        try:
            # Get queue client for specific queue
            self.queue_client = self.queue_service.get_queue_client(queue_name)

            # Create queue if it doesn't exist
            try:
                self.queue_client.create_queue()
                logger.info(f"Created queue: {queue_name}")
            except Exception as create_error:
                # Queue might already exist, which is fine
                if "already exists" in str(create_error).lower() or "queuealreadyexists" in str(create_error):
                    logger.info(f"Queue already exists: {queue_name}")
                else:
                    logger.warning(f"Queue creation warning for {queue_name}: {str(create_error)}")

            logger.info(f"Queue initialized: {queue_name}")
        except Exception as e:
            logger.error(f"Error initializing queue: {str(e)}")
            raise

    def send_message(self, message_content: Union[str, Dict[str, Any]]) -> bool:
        """
        Send a message to the queue

        Args:
            message_content: Message content (string or dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if isinstance(message_content, dict):
                message_content = json.dumps(message_content)
            self.queue_client.send_message(message_content)
            logger.debug(f"Sent message to queue {self.queue_name}")
            return True
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return False

    def receive_messages(self, max_messages: int = 5) -> List[Any]:
        """
        Receive messages from the queue

        Args:
            max_messages: Maximum number of messages to receive

        Returns:
            List[Any]: List of messages
        """
        try:
            messages = self.queue_client.receive_messages(max_messages=max_messages)
            result = list(messages)
            logger.debug(f"Received {len(result)} messages from queue {self.queue_name}")
            return result
        except Exception as e:
            logger.error(f"Error receiving messages: {str(e)}")
            return []

    def delete_message(self, message: Any) -> bool:
        """
        Delete a message from the queue

        Args:
            message: Message to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.queue_client.delete_message(message)
            logger.debug(f"Deleted message from queue {self.queue_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting message: {str(e)}")
            return False


class SqlDatabaseRepository:
    """Repository for accessing SQL Database"""

    def __init__(self, table_name: str):
        """
        Initialize the SQL database repository

        Args:
            table_name: Name of the table
        """
        self.table_name = table_name

    def _get_connection(self):
        """
        Get a connection to the SQL database

        Returns:
            Connection object or None if in local development
        """
        if is_local_dev():
            # In local development, we don't connect to the actual SQL database
            logger.info('Using Azure Table Storage for local development instead of SQL')
            logger.info(f'Table name: {self.table_name}')
            return None
        else:
            # In production, connect to the actual SQL database
            try:
                # Import pyodbc only when needed in production
                import pyodbc
                from shared.azure_services import get_credential
                credential = get_credential()

                connection_db_string = (
                    'Driver={ODBC Driver 17 for SQL Server};'
                    'Server=tcp:sqldb-atomsec-dev.database.windows.net,1433;'
                    'Database=sql-atomsec-dev;Encrypt=yes;TrustServerCertificate=no;'
                    'Connection Timeout=120'
                )
                token_bytes = credential.get_token("https://database.windows.net/.default").token.encode("UTF-16-LE")
                token_struct = struct.pack(f'<I{len(token_bytes)}s', len(token_bytes), token_bytes)
                SQL_COPT_SS_ACCESS_TOKEN = 1256  # Connection option defined by Microsoft
                conn = pyodbc.connect(connection_db_string, attrs_before={SQL_COPT_SS_ACCESS_TOKEN: token_struct})
                logger.info('Database connected.')
                return conn
            except ImportError as e:
                logger.error(f'pyodbc module not available: {str(e)}')
                return None
            except Exception as e:
                logger.error(f'Error connecting to SQL database: {str(e)}')
                return None

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[tuple]:
        """
        Execute a query and return results

        Args:
            query: SQL query to execute
            params: Optional parameters for the query

        Returns:
            List[tuple]: Query results
        """
        # In local development, return empty results
        if is_local_dev():
            logger.info(f"Local development: Not executing SQL query: {query}")
            return []

        conn = self._get_connection()
        if not conn:
            logger.warning("No SQL connection available")
            return []

        try:
            cursor = conn.cursor()
            if params:
                logger.debug(f"Executing query with params: {query}, {params}")
                cursor.execute(query, params)
            else:
                logger.debug(f"Executing query: {query}")
                cursor.execute(query)

            results = cursor.fetchall()
            logger.debug(f"Executed query, returned {len(results)} rows")
            return results
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return []
        finally:
            conn.close()

    def execute_non_query(self, query: str, params: Optional[tuple] = None) -> bool:
        """
        Execute a non-query statement (INSERT, UPDATE, DELETE)

        Args:
            query: SQL query to execute
            params: Optional parameters for the query

        Returns:
            bool: True if successful, False otherwise
        """
        # In local development, return success without executing
        if is_local_dev():
            logger.info(f"Local development: Not executing SQL non-query: {query}")
            return True

        conn = self._get_connection()
        if not conn:
            logger.warning("No SQL connection available")
            return False

        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            conn.commit()
            logger.debug(f"Executed non-query, affected {cursor.rowcount} rows")
            return True
        except Exception as e:
            logger.error(f"Error executing non-query: {str(e)}")
            conn.rollback()
            return False
        finally:
            conn.close()


# Global repository instances (lazy initialized)
_table_storage_repos = {}
_sql_database_repos = {}


def get_table_storage_repository(table_name: str) -> Optional[TableStorageRepository]:
    """
    Get a table storage repository for the specified table name

    This function implements lazy initialization of table storage repositories
    and caches them for reuse.

    Args:
        table_name: Name of the table

    Returns:
        TableStorageRepository: Repository instance or None if error
    """
    global _table_storage_repos

    # Check if repository already exists
    if table_name in _table_storage_repos and _table_storage_repos[table_name] is not None:
        return _table_storage_repos[table_name]

    # Create new repository
    try:
        repo = TableStorageRepository(table_name=table_name)
        _table_storage_repos[table_name] = repo
        logger.info(f"Initialized table storage repository for {table_name}")
        return repo
    except Exception as e:
        logger.error(f"Failed to initialize table storage repository for {table_name}: {str(e)}")
        _table_storage_repos[table_name] = None
        return None


def get_sql_database_repository(table_name: str = None) -> Optional[SqlDatabaseRepository]:
    """
    Get a SQL database repository for the specified table name

    This function implements lazy initialization of SQL database repositories
    and caches them for reuse.

    Args:
        table_name: Optional name of the table (can be None for queries spanning multiple tables)

    Returns:
        SqlDatabaseRepository: Repository instance or None if error
    """
    global _sql_database_repos

    # Use empty string as key for default repository
    key = table_name or ""

    # Check if repository already exists
    if key in _sql_database_repos and _sql_database_repos[key] is not None:
        return _sql_database_repos[key]

    # Create new repository
    try:
        repo = SqlDatabaseRepository(table_name=table_name or "")
        _sql_database_repos[key] = repo
        logger.info(f"Initialized SQL database repository for {table_name or 'default'}")
        return repo
    except Exception as e:
        logger.error(f"Failed to initialize SQL database repository for {table_name or 'default'}: {str(e)}")
        _sql_database_repos[key] = None
        return None


# Policy and Rule Creation Functions
import os
import uuid
import yaml

CONFIG_PATH = os.path.join(os.path.dirname(__file__), '../config/default_policies_and_rules.yaml')

def create_default_policies_and_rules_for_integration(user_id: str, integration_id: str) -> bool:
    """
    Create default policies and rules for a new integration, loading from YAML config.
    This should be called after a new integration is created or after metadata extraction.

    Args:
        user_id: User ID who owns the integration
        integration_id: Integration ID to create policies for

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Load YAML configuration
        with open(CONFIG_PATH, 'r') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Could not load default policies and rules YAML: {e}")
        return False

    if not config or not isinstance(config, list):
        logger.error("Default policies and rules YAML is empty or invalid.")
        return False

    # Import policy endpoints functions
    from api.policy_endpoints import store_policy_data, store_rule_data
    # Import PMD repository for subtask/individual rule creation
    from repositories.pmd_repository import PMDRepository
    pmd_repo = PMDRepository()

    logger.info(f"Creating default policies and rules for integration {integration_id} and user {user_id}")

    try:
        for policy in config:
            logger.debug(f"Processing policy: {policy}")
            policy_id = str(uuid.uuid4())  # Always generate a UUID for PolicyId
            policy_entity = {
                "PolicyId": policy_id,  # UUID
                "Name": policy.get("name", "Unnamed Policy"),  # Human-readable name
                "UserId": user_id,
                "IntegrationId": integration_id,
                "CreatedAt": datetime.now().isoformat(),
            }

            # Store policy
            success = store_policy_data(policy_entity)
            if not success:
                logger.error(f"Failed to store policy {policy_entity['Name']} for integration {integration_id}")
                continue

            logger.info(f"Created policy: {policy_entity['Name']} (ID: {policy_id}) for integration {integration_id}")

            # Create rules for this policy
            for rule in policy.get("rules", []):
                logger.debug(f"Processing rule: {rule}")
                rule_id = str(uuid.uuid4())
                rule_entity = {
                    "RuleId": rule_id,
                    "PolicyId": policy_id,  # Foreign key to Policy table
                    "TaskType": rule.get("task_type"),
                    "Enabled": int(bool(rule.get("enabled", False))),
                    "CreatedAt": datetime.now().isoformat(),
                }

                # Store rule
                success = store_rule_data(rule_entity)
                if success:
                    logger.info(f"Created rule: {rule.get('task_type')} (enabled: {rule.get('enabled')}) for policy {policy_entity['Name']}")
                else:
                    logger.error(f"Failed to store rule {rule.get('task_type')} for policy {policy_id}")

                # --- NEW: Handle PMD subtasks and individual rules ---
                if rule.get("task_type") == "pmd_apex_security":
                    logger.debug(f"Checking for subtasks in rule: {rule}")
                    if "subtasks" in rule:
                        logger.debug(f"Found subtasks in rule: {rule['subtasks']}")
                        for subtask in rule.get("subtasks", []):
                            logger.debug(f"Processing subtask: {subtask}")
                            subtask_id = str(uuid.uuid4())
                            subtask_entity = {
                                "SubtaskId": subtask_id,
                                "RuleId": rule_id,
                                "TaskType": "pmd_apex_security",
                                "SubtaskName": subtask.get("name", ""),
                                "SubtaskDescription": subtask.get("description", ""),
                                "Enabled": int(bool(subtask.get("enabled", False))),
                                "CreatedAt": datetime.now().isoformat(),
                            }
                            try:
                                pmd_repo.insert_pmd_subtask(subtask_entity)
                                logger.info(f"Created PMD subtask: {subtask_entity['SubtaskName']} (ID: {subtask_id}) for rule {rule_id}")
                            except Exception as e:
                                logger.error(f"Error inserting PMD subtask: {e}")

                            # Handle individual rules within subtasks
                            if "rules" in subtask:
                                logger.debug(f"Found individual rules in subtask: {subtask['rules']}")
                                for individual_rule in subtask.get("rules", []):
                                    logger.debug(f"Processing individual rule: {individual_rule}")
                                    individual_rule_id = str(uuid.uuid4())
                                    individual_rule_entity = {
                                        "IndividualRuleId": individual_rule_id,
                                        "SubtaskId": subtask_id,
                                        "RuleName": individual_rule.get("name", ""),
                                        "RuleDescription": individual_rule.get("description", ""),
                                        "Enabled": int(bool(individual_rule.get("enabled", False))),
                                        "CreatedAt": datetime.now().isoformat(),
                                    }
                                    try:
                                        pmd_repo.insert_pmd_individual_rule(individual_rule_entity)
                                        logger.info(f"Created PMD individual rule: {individual_rule_entity['RuleName']} (ID: {individual_rule_id}) for subtask {subtask_id}")
                                    except Exception as e:
                                        logger.error(f"Error inserting PMD individual rule: {e}")
                    else:
                        logger.warning(f"No subtasks found in pmd_apex_security rule: {rule}")

        logger.info(f"Successfully created default policies, rules, PMD subtasks, and individual rules for integration {integration_id}")
        return True

    except Exception as e:
        logger.error(f"Error creating default policies and rules for integration {integration_id}: {str(e)}")
        return False
