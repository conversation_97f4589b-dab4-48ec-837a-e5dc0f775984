# Default policies and rules for new integrations
# You can update this file to evolve the rule set without code changes

- name: Profiles and Permissions
  rules:
    - task_type: profiles_permission_sets
      enabled: true
    - task_type: mfa_enforcement
      enabled: true
    - task_type: device_activation
      enabled: true
    - task_type: login_ip_ranges
      enabled: true
    - task_type: login_hours
      enabled: true
    - task_type: session_timeout
      enabled: true
    - task_type: api_whitelisting
      enabled: true
    - task_type: password_policy
      enabled: true

- name: Health Check
  rules:
    - task_type: health_check
      enabled: true

- name: Static Code Analysis (PMD)
  rules:
    - task_type: pmd_apex_security
      enabled: true
      subtasks:
        - name: security
          description: Security-related PMD rules
          enabled: true
          rules:
            - name: ApexBadCrypto
              description: Bad crypto practices
              enabled: true
            - name: ApexCRUDViolation
              description: CRUD permission violations
              enabled: true
            - name: ApexDangerousMethods
              description: Dangerous method usage
              enabled: true
            - name: ApexInsecureEndpoint
              description: Insecure endpoint configurations
              enabled: true
            - name: ApexOpenRedirect
              description: Open redirect vulnerabilities
              enabled: true
            - name: ApexSharingViolations
              description: Sharing and security violations
              enabled: true
            - name: ApexSOQLInjection
              description: SOQL injection vulnerabilities
              enabled: true
            - name: ApexSuggestUsingNamedCred
              description: Suggest using named credentials
              enabled: true
            - name: ApexXSSFromEscapeFalse
              description: XSS from escape=false
              enabled: true
            - name: ApexXSSFromURLParam
              description: XSS from URL parameters
              enabled: true
        - name: design
          description: Design-related PMD rules
          enabled: true
          rules:
            - name: AvoidDeeplyNestedIfStmts
              description: Avoid deeply nested if statements
              enabled: true
            - name: UnusedMethod
              description: Unused methods
              enabled: true
            - name: CyclomaticComplexity
              description: Cyclomatic complexity
              enabled: true
            - name: CognitiveComplexity
              description: Cognitive complexity
              enabled: true
            - name: ExcessiveClassLength
              description: Excessive class length
              enabled: true
            - name: ExcessiveParameterList
              description: Excessive parameter list
              enabled: true
            - name: ExcessivePublicCount
              description: Excessive public count
              enabled: true
            - name: NcssConstructorCount
              description: NCSS constructor count
              enabled: true
            - name: NcssMethodCount
              description: NCSS method count
              enabled: true
            - name: NcssTypeCount
              description: NCSS type count
              enabled: true
            - name: StdCyclomaticComplexity
              description: Standard cyclomatic complexity
              enabled: true
            - name: TooManyFields
              description: Too many fields
              enabled: true
        - name: performance
          description: Performance-related PMD rules
          enabled: true
          rules:
            - name: AvoidDebugStatements
              description: Avoid debug statements
              enabled: true
            - name: AvoidNonRestrictiveQueries
              description: Avoid non-restrictive queries
              enabled: true
            - name: EagerlyLoadedDescribeSObjectResult
              description: Eagerly loaded describe SObject result
              enabled: true
            - name: OperationWithHighCostInLoop
              description: Operation with high cost in loop
              enabled: true
            - name: OperationWithLimitsInLoop
              description: Operation with limits in loop
              enabled: true
        - name: bestpractices
          description: Best practices PMD rules
          enabled: true
          rules:
            - name: ApexAssertionsShouldIncludeMessage
              description: Apex assertions should include message
              enabled: true
            - name: ApexUnitTestClassShouldHaveAsserts
              description: Apex unit test class should have asserts
              enabled: true
            - name: ApexUnitTestClassShouldHaveRunAs
              description: Apex unit test class should have run as
              enabled: true
            - name: ApexUnitTestMethodShouldHaveIsTestAnnotation
              description: Apex unit test method should have isTest annotation
              enabled: true
            - name: ApexUnitTestShouldNotUseSeeAllDataTrue
              description: Apex unit test should not use seeAllData true
              enabled: true
            - name: AvoidGlobalModifier
              description: Avoid global modifier
              enabled: true
            - name: AvoidLogicInTrigger
              description: Avoid logic in trigger
              enabled: true
            - name: DebugsShouldUseLoggingLevel
              description: Debugs should use logging level
              enabled: true
            - name: UnusedLocalVariable
              description: Unused local variable
              enabled: true
            - name: QueueableWithoutFinalizer
              description: Queueable without finalizer
              enabled: true
        - name: codestyle
          description: Code style PMD rules
          enabled: true
          rules:
            - name: ClassNamingConventions
              description: Class naming conventions
              enabled: true
            - name: IfElseStmtsMustUseBraces
              description: If-else statements must use braces
              enabled: true
            - name: IfStmtsMustUseBraces
              description: If statements must use braces
              enabled: true
            - name: FieldDeclarationsShouldBeAtStart
              description: Field declarations should be at start
              enabled: true
            - name: FieldNamingConventions
              description: Field naming conventions
              enabled: true
            - name: ForLoopsMustUseBraces
              description: For loops must use braces
              enabled: true
            - name: FormalParameterNamingConventions
              description: Formal parameter naming conventions
              enabled: true
            - name: LocalVariableNamingConventions
              description: Local variable naming conventions
              enabled: true
            - name: MethodNamingConventions
              description: Method naming conventions
              enabled: true
            - name: OneDeclarationPerLine
              description: One declaration per line
              enabled: true
            - name: PropertyNamingConventions
              description: Property naming conventions
              enabled: true
            - name: WhileLoopsMustUseBraces
              description: While loops must use braces
              enabled: true
# Add more policies and rules as needed
# - name: Another Policy
#   rules:
#     - task_type: some_task
#       enabled: false 