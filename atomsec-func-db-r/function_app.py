"""
AtomSec Database Function App

This is a dedicated Azure Function App for handling all database operations.
It provides a clean API for CRUD operations on various entities.

Features:
- Centralized database access layer
- Repository pattern implementation
- Support for both Azure Table Storage (local dev) and SQL Database (production)
- RESTful API endpoints for all database operations
- Event publishing to Service Bus for async operations
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import API endpoints
from api.user_endpoints import bp as user_bp
from api.account_endpoints import bp as account_bp
from api.organization_endpoints import bp as organization_bp
from api.integration_endpoints import bp as integration_bp
from api.security_endpoints import bp as security_bp
from api.task_endpoints import bp as task_bp
from api.auth_endpoints import bp as auth_bp
from api.policy_endpoints import bp as policy_bp
from api.cors_handler import bp as cors_bp
from api.sfdc_proxy_endpoints import bp as sfdc_proxy_bp
from api.user_profile_endpoints import bp as user_profile_bp
from api.key_vault_endpoints import bp as key_vault_bp
from api.general_endpoints import bp as general_bp
from api.pmd_endpoints import bp as pmd_bp
from api.task_processor_endpoints import bp as task_processor_bp
from api.scan_endpoints import bp as scan_bp

# Create the Function App
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Register all blueprints
try:
    app.register_functions(user_bp)
    app.register_functions(account_bp)
    app.register_functions(organization_bp)
    app.register_functions(integration_bp)
    app.register_functions(security_bp)
    app.register_functions(task_bp)
    app.register_functions(auth_bp)
    app.register_functions(policy_bp)
    app.register_functions(cors_bp)
    app.register_functions(sfdc_proxy_bp)
    app.register_functions(user_profile_bp)
    app.register_functions(key_vault_bp)
    app.register_functions(general_bp)
    app.register_functions(pmd_bp)
    app.register_functions(task_processor_bp)
    app.register_functions(scan_bp)
    logger.info("Successfully registered all database API blueprints")
except Exception as e:
    logger.error(f"Error registering blueprints: {str(e)}")
    raise

# Health check endpoint
@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for the database function app

    Returns:
        JSON response with health status
    """
    try:
        # Test database connectivity
        from shared.data_access import get_table_storage_repository
        from shared.azure_services import is_local_dev

        health_status = {
            "status": "healthy",
            "service": "atomsec-func-db",
            "environment": "local" if is_local_dev() else "production",
            "checks": {}
        }

        # Test Table Storage connectivity
        try:
            test_repo = get_table_storage_repository("HealthCheck")
            if test_repo:
                health_status["checks"]["table_storage"] = "connected"
            else:
                health_status["checks"]["table_storage"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["table_storage"] = f"error: {str(e)}"
            health_status["status"] = "unhealthy"

        # Test SQL Database connectivity (only in production)
        if not is_local_dev():
            try:
                from shared.data_access import get_sql_database_repository
                test_sql_repo = get_sql_database_repository("App_Account")
                if test_sql_repo:
                    health_status["checks"]["sql_database"] = "connected"
                else:
                    health_status["checks"]["sql_database"] = "failed"
                    health_status["status"] = "degraded"
            except Exception as e:
                health_status["checks"]["sql_database"] = f"error: {str(e)}"
                health_status["status"] = "unhealthy"

        # Test Service Bus connectivity
        try:
            from shared.service_bus_client import get_service_bus_client
            sb_client = get_service_bus_client()
            if sb_client:
                health_status["checks"]["service_bus"] = "connected"
            else:
                health_status["checks"]["service_bus"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["service_bus"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        status_code = 200 if health_status["status"] == "healthy" else 503

        return func.HttpResponse(
            json.dumps(health_status),
            mimetype="application/json",
            status_code=status_code
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=503
        )

# Info endpoint
@app.route(route="info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """
    Information endpoint for the database function app

    Returns:
        JSON response with service information
    """
    info_data = {
        "service": "atomsec-func-db",
        "version": "1.0.0",
        "description": "AtomSec Database Service - Centralized database operations",
        "endpoints": {
            "users": {
                "base": "/api/users",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "accounts": {
                "base": "/api/accounts",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "organizations": {
                "base": "/api/organizations",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "integrations": {
                "base": "/api/integrations",
                "operations": ["GET", "POST", "PUT", "DELETE"],
                "sub_endpoints": ["/overview", "/health-check", "/profiles", "/credentials", "/pmd-issues"]
            },
            "security": {
                "base": "/api/security",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/health-checks", "/profiles", "/overview"]
            },
            "tasks": {
                "base": "/api/tasks",
                "operations": ["GET", "POST", "PUT"],
                "sub_endpoints": ["/status", "/results"]
            },
            "auth": {
                "base": "/api/auth",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/login", "/signup", "/token/refresh", "/azure/login", "/azure/callback", "/azure/me"]
            },
            "policies": {
                "base": "/api/policies",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/rules", "/policy-rule-settings", "/policy-rule-settings/enabled-tasks"]
            },
            "user_profile": {
                "base": "/api/user",
                "operations": ["GET", "PUT"],
                "sub_endpoints": ["/profile", "/password"]
            },
            "key_vault": {
                "base": "/api/key-vault",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/secrets", "/create", "/access-policy", "/client-credentials"]
            },
            "sfdc_proxy": {
                "base": "/v1",
                "description": "Proxy endpoints to SFDC service",
                "operations": ["GET", "POST"],
                "sub_endpoints": [
                    "/integration/scan/{id}",
                    "/task-status",
                    "/tasks/cancel",
                    "/tasks/schedule",
                    "/health-score",
                    "/health-risks",
                    "/profiles",
                    "/permission-sets",
                    "/scan/accounts",
                    "/scan/history",
                    "/sfdc/health",
                    "/sfdc/info"
                ]
            }
        },
        "features": [
            "Dual storage support (Azure Table Storage + SQL Database)",
            "Environment-aware (local dev + production)",
            "Repository pattern implementation",
            "Comprehensive error handling",
            "RESTful API design",
            "Service Bus event publishing",
            "Event-driven architecture"
        ]
    }

    return func.HttpResponse(
        json.dumps(info_data),
        mimetype="application/json",
        status_code=200
    )

def handle_error(e: Exception, operation: str) -> func.HttpResponse:
    """
    Standard error handler for database operations

    Args:
        e: Exception that occurred
        operation: Description of the operation that failed

    Returns:
        func.HttpResponse: Error response
    """
    logger.error(f"Error in {operation}: {str(e)}")
    return func.HttpResponse(
        json.dumps({
            "success": False,
            "error": f"Database operation failed: {str(e)}"
        }),
        mimetype="application/json",
        status_code=500
    )

logger.info("AtomSec Database Function App initialized successfully")