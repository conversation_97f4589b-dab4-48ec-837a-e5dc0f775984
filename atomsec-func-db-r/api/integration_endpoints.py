"""
Integration Management Endpoints

This module provides integration-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Consolidated API Endpoints:

Resource Management (CRUD):
- GET /api/integrations - List integrations with optional filtering
- POST /api/integrations - Create new integration
- GET /api/integrations/{id} - Get integration by ID
- PUT /api/integrations/{id} - Update integration
- DELETE /api/integrations/{id} - Delete integration (soft delete)

Integration Data:
- GET /api/integrations/{id}/overview - Get integration overview
- GET /api/integrations/{id}/health-check - Get health check data
- GET /api/integrations/{id}/profiles - Get profiles and permission sets
- GET /api/integrations/{id}/pmd-issues - Get PMD scan issues

Integration Actions:
- POST /api/integration/test-connection - Test Salesforce connection
- POST /api/integration/connect - Connect and store credentials
- POST /api/integration/scan/{id} - Enqueue scan task for integration
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository, create_default_policies_and_rules_for_integration
from shared.database_models_new import Organization, Credentials
from shared.auth_utils import get_user_from_request_or_default, get_current_user
from shared.execution_log_service import get_execution_log_service

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_integration_table_repo = None
_integration_sql_repo = None
_credentials_table_repo = None
_credentials_sql_repo = None


def get_integration_table_repo() -> Optional[TableStorageRepository]:
    """Get integration table repository for local development"""
    global _integration_table_repo
    if _integration_table_repo is None:
        try:
            _integration_table_repo = TableStorageRepository(table_name="Integrations")
            logger.info("Initialized integration table repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration table repository: {str(e)}")
            _integration_table_repo = None
    return _integration_table_repo


def get_integration_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get integration SQL repository for production"""
    global _integration_sql_repo
    if _integration_sql_repo is None and not is_local_dev():
        try:
            _integration_sql_repo = SqlDatabaseRepository(table_name="App_Integration")
            logger.info("Initialized integration SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration SQL repository: {str(e)}")
            _integration_sql_repo = None
    return _integration_sql_repo


def get_credentials_table_repo() -> Optional[TableStorageRepository]:
    """Get credentials table repository for local development"""
    global _credentials_table_repo
    if _credentials_table_repo is None:
        try:
            _credentials_table_repo = TableStorageRepository(table_name="Credentials")
            logger.info("Initialized credentials table repository")
        except Exception as e:
            logger.error(f"Failed to initialize credentials table repository: {str(e)}")
            _credentials_table_repo = None
    return _credentials_table_repo


def get_credentials_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get credentials SQL repository for production"""
    global _credentials_sql_repo
    if _credentials_sql_repo is None and not is_local_dev():
        try:
            _credentials_sql_repo = SqlDatabaseRepository(table_name="App_Credentials")
            logger.info("Initialized credentials SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize credentials SQL repository: {str(e)}")
            _credentials_sql_repo = None
    return _credentials_sql_repo


def create_integration_record(name: str, tenant_url: str, integration_type: str = "Salesforce",
                             description: str = "", environment: str = "production",
                             user_email: str = None, user_id: str = None, account_id: int = None,
                             is_active: bool = True) -> Optional[str]:
    """
    Create a new integration record

    Args:
        name: Integration name
        tenant_url: Tenant URL
        integration_type: Integration type (default: Salesforce)
        description: Integration description
        environment: Environment (production or sandbox)
        user_email: User email
        user_id: User ID
        account_id: Account ID
        is_active: Whether the integration is active

    Returns:
        Integration ID if successful, None otherwise
    """
    try:
        # Generate unique integration ID
        integration_id = str(uuid.uuid4())

        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_integration_table_repo()
            if not repo:
                logger.error("Integration table repository not available")
                return None

            # Create integration entity
            integration_entity = {
                "PartitionKey": "integration",
                "RowKey": integration_id,
                "Name": name,
                "TenantUrl": tenant_url,
                "Type": integration_type,
                "Description": description,
                "Environment": environment,
                "IsActive": is_active,
                "LastScan": "",
                "LastScanAttempt": "",
                "HealthScore": None,
                "CreatedAt": datetime.now().isoformat(),
                "UserEmail": user_email,
                "UserId": user_id,
                "AccountId": account_id
            }

            if repo.insert_entity(integration_entity):
                logger.info(f"Created integration: {name} with ID {integration_id}")
                return integration_id
            else:
                logger.error("Failed to insert integration entity")
                return None

        else:
            # Use SQL Database for production
            repo = get_integration_sql_repo()
            if not repo:
                logger.error("Integration SQL repository not available")
                return None

            query = """
            INSERT INTO App_Integration (Id, Name, TenantUrl, Type, Description, Environment,
                                       IsActive, LastScan, LastScanAttempt, HealthScore,
                                       CreatedAt, UserEmail, UserId, AccountId)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                integration_id,
                name,
                tenant_url,
                integration_type,
                description,
                environment,
                is_active,
                "",
                "",
                None,
                datetime.now(),
                user_email,
                user_id,
                account_id
            )

            if repo.execute_non_query(query, params):
                logger.info(f"Created integration: {name} with ID {integration_id}")
                return integration_id
            else:
                logger.error("Failed to create integration in SQL database")
                return None

    except Exception as e:
        logger.error(f"Error creating integration: {str(e)}")
        return None


def get_integration_by_id_db(integration_id: str) -> Optional[Dict[str, Any]]:
    """
    Get integration by ID from database

    Args:
        integration_id: Integration ID

    Returns:
        Integration data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_integration_table_repo()
            if not repo:
                logger.error("Integration table repository not available")
                return None

            integration = repo.get_entity("integration", integration_id)

            if integration:
                return {
                    'id': integration.get('RowKey'),
                    'name': integration.get('Name'),
                    'tenant_url': integration.get('TenantUrl'),
                    'type': integration.get('Type'),
                    'description': integration.get('Description'),
                    'environment': integration.get('Environment'),
                    'is_active': integration.get('IsActive'),
                    'last_scan': integration.get('LastScan'),
                    'last_scan_attempt': integration.get('LastScanAttempt'),
                    'health_score': integration.get('HealthScore'),
                    'created_at': integration.get('CreatedAt'),
                    'user_email': integration.get('UserEmail'),
                    'user_id': integration.get('UserId'),
                    'account_id': integration.get('AccountId')
                }
            else:
                logger.warning(f"Integration not found: {integration_id}")
                return None

        else:
            # Use SQL Database for production
            repo = get_integration_sql_repo()
            if not repo:
                logger.error("Integration SQL repository not available")
                return None

            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive,
                   LastScan, LastScanAttempt, HealthScore, CreatedAt, UserEmail, UserId, AccountId
            FROM App_Integration
            WHERE Id = ?
            """

            results = repo.execute_query(query, (integration_id,))

            if results:
                row = results[0]
                return {
                    'id': row[0],
                    'name': row[1],
                    'tenant_url': row[2],
                    'type': row[3],
                    'description': row[4],
                    'environment': row[5],
                    'is_active': row[6],
                    'last_scan': row[7],
                    'last_scan_attempt': row[8],
                    'health_score': row[9],
                    'created_at': row[10].isoformat() if row[10] else None,
                    'user_email': row[11],
                    'user_id': row[12],
                    'account_id': row[13]
                }
            else:
                logger.warning(f"Integration not found: {integration_id}")
                return None

    except Exception as e:
        logger.error(f"Error getting integration by ID: {str(e)}")
        return None


def update_integration_record(integration_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update integration record

    Args:
        integration_id: Integration ID
        update_data: Dictionary containing fields to update

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_integration_table_repo()
            if not repo:
                logger.error("Integration table repository not available")
                return False

            # Get existing integration
            integration = repo.get_entity("integration", integration_id)
            if not integration:
                logger.warning(f"Integration not found: {integration_id}")
                return False

            # Update fields
            for key, value in update_data.items():
                if key in ['name', 'tenant_url', 'description', 'environment', 'is_active', 'health_score', 'last_scan', 'last_scan_attempt']:
                    field_name = {
                        'name': 'Name',
                        'tenant_url': 'TenantUrl',
                        'description': 'Description',
                        'environment': 'Environment',
                        'is_active': 'IsActive',
                        'health_score': 'HealthScore',
                        'last_scan': 'LastScan',
                        'last_scan_attempt': 'LastScanAttempt'
                    }.get(key, key)
                    integration[field_name] = value

            integration['UpdatedAt'] = datetime.now().isoformat()

            return repo.update_entity(integration)

        else:
            # Use SQL Database for production
            repo = get_integration_sql_repo()
            if not repo:
                logger.error("Integration SQL repository not available")
                return False

            # Build update query
            update_fields = []
            params = []

            field_mapping = {
                'name': 'Name',
                'tenant_url': 'TenantUrl',
                'description': 'Description',
                'environment': 'Environment',
                'is_active': 'IsActive',
                'health_score': 'HealthScore',
                'last_scan': 'LastScan',
                'last_scan_attempt': 'LastScanAttempt'
            }

            for key, value in update_data.items():
                if key in field_mapping:
                    update_fields.append(f"{field_mapping[key]} = ?")
                    params.append(value)

            if not update_fields:
                return True  # Nothing to update

            update_fields.append("UpdatedAt = ?")
            params.append(datetime.now())
            params.append(integration_id)

            query = f"UPDATE App_Integration SET {', '.join(update_fields)} WHERE Id = ?"

            return repo.execute_non_query(query, tuple(params))

    except Exception as e:
        logger.error(f"Error updating integration: {str(e)}")
        return False


def delete_integration_record(integration_id: str) -> bool:
    """
    Delete an integration (soft delete by setting IsActive = False)

    Args:
        integration_id: Integration ID

    Returns:
        True if successful, False otherwise
    """
    return update_integration_record(integration_id, {'is_active': False})


def get_integrations_list(user_email: str = None, user_id: str = None, account_id: str = None,
                         include_inactive: bool = False, integration_type: str = None) -> List[Dict[str, Any]]:
    """
    Get integrations with optional filtering

    Args:
        user_email: Filter by user email
        user_id: Filter by user ID
        account_id: Filter by account ID
        include_inactive: Include inactive integrations
        integration_type: Filter by integration type

    Returns:
        List of integration dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_integration_table_repo()
            if not repo:
                logger.error("Integration table repository not available")
                return []

            filter_parts = ["PartitionKey eq 'integration'"]

            # Apply filters
            if user_email:
                filter_parts.append(f"UserEmail eq '{user_email}'")
            if user_id:
                filter_parts.append(f"UserId eq '{user_id}'")
            if account_id:
                filter_parts.append(f"AccountId eq '{account_id}'")
            if not include_inactive:
                filter_parts.append("IsActive eq true")
            if integration_type:
                filter_parts.append(f"Type eq '{integration_type}'")

            filter_query = " and ".join(filter_parts)
            entities = list(repo.query_entities(filter_query))

            integrations = []
            for entity in entities:
                integrations.append({
                    'id': entity.get('RowKey'),
                    'name': entity.get('Name'),
                    'tenant_url': entity.get('TenantUrl'),
                    'type': entity.get('Type'),
                    'description': entity.get('Description'),
                    'environment': entity.get('Environment'),
                    'is_active': entity.get('IsActive'),
                    'last_scan': entity.get('LastScan'),
                    'last_scan_attempt': entity.get('LastScanAttempt'),
                    'health_score': entity.get('HealthScore'),
                    'created_at': entity.get('CreatedAt'),
                    'user_email': entity.get('UserEmail'),
                    'user_id': entity.get('UserId'),
                    'account_id': entity.get('AccountId')
                })

            return integrations

        else:
            # Use SQL Database for production
            repo = get_integration_sql_repo()
            if not repo:
                logger.error("Integration SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive,
                   LastScan, LastScanAttempt, HealthScore, CreatedAt, UserEmail, UserId, AccountId
            FROM App_Integration WHERE 1=1
            """
            params = []

            if user_email:
                query += " AND UserEmail = ?"
                params.append(user_email)
            if user_id:
                query += " AND UserId = ?"
                params.append(user_id)
            if account_id:
                query += " AND AccountId = ?"
                params.append(account_id)
            if not include_inactive:
                query += " AND IsActive = ?"
                params.append(True)
            if integration_type:
                query += " AND Type = ?"
                params.append(integration_type)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            integrations = []
            for row in results:
                integrations.append({
                    'id': row[0],
                    'name': row[1],
                    'tenant_url': row[2],
                    'type': row[3],
                    'description': row[4],
                    'environment': row[5],
                    'is_active': row[6],
                    'last_scan': row[7],
                    'last_scan_attempt': row[8],
                    'health_score': row[9],
                    'created_at': row[10].isoformat() if row[10] else None,
                    'user_email': row[11],
                    'user_id': row[12],
                    'account_id': row[13]
                })

            return integrations

    except Exception as e:
        logger.error(f"Error getting integrations: {str(e)}")
        return []


def store_integration_credentials(integration_id: str, credentials_data: Dict[str, Any]) -> bool:
    """
    Store integration credentials

    Args:
        integration_id: Integration ID
        credentials_data: Dictionary containing credential information

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_credentials_table_repo()
            if not repo:
                logger.error("Credentials table repository not available")
                return False

            # Create credentials entity
            credentials_entity = {
                "PartitionKey": f"salesforce-{integration_id}",
                "RowKey": credentials_data.get('credential_type', 'client-id'),
                "Value": credentials_data.get('value', ''),
                "AuthFlow": credentials_data.get('auth_flow', 'client_credentials'),
                "CreatedAt": datetime.now().isoformat(),
                "UpdatedAt": datetime.now().isoformat()
            }

            return repo.insert_or_update_entity(credentials_entity)

        else:
            # Use SQL Database for production
            repo = get_credentials_sql_repo()
            if not repo:
                logger.error("Credentials SQL repository not available")
                return False

            # Check if credentials already exist
            query = "SELECT Id FROM App_Credentials WHERE OrgId = ?"
            results = repo.execute_query(query, (integration_id,))

            if results:
                # Update existing credentials
                update_query = """
                UPDATE App_Credentials
                SET ClientId = ?, ClientSecret = ?, KeyVaultId = ?, UpdatedAt = ?
                WHERE OrgId = ?
                """
                params = (
                    credentials_data.get('client_id'),
                    credentials_data.get('client_secret'),
                    credentials_data.get('key_vault_id'),
                    datetime.now(),
                    integration_id
                )
            else:
                # Insert new credentials
                update_query = """
                INSERT INTO App_Credentials (OrgId, ClientId, ClientSecret, KeyVaultId, CreatedAt, UpdatedAt)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (
                    integration_id,
                    credentials_data.get('client_id'),
                    credentials_data.get('client_secret'),
                    credentials_data.get('key_vault_id'),
                    datetime.now(),
                    datetime.now()
                )

            return repo.execute_non_query(update_query, params)

    except Exception as e:
        logger.error(f"Error storing integration credentials: {str(e)}")
        return False


def get_integration_credentials(integration_id: str) -> Optional[Dict[str, Any]]:
    """
    Get integration credentials

    Args:
        integration_id: Integration ID

    Returns:
        Credentials data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_credentials_table_repo()
            if not repo:
                logger.error("Credentials table repository not available")
                return None

            # Get credentials by service name
            service_name = f"salesforce-{integration_id}"
            filter_query = f"PartitionKey eq '{service_name}'"
            entities = list(repo.query_entities(filter_query))

            if entities:
                credentials = {}
                for entity in entities:
                    credential_type = entity.get('RowKey')
                    credentials[credential_type] = {
                        'value': entity.get('Value'),
                        'auth_flow': entity.get('AuthFlow'),
                        'created_at': entity.get('CreatedAt'),
                        'updated_at': entity.get('UpdatedAt')
                    }
                return credentials
            else:
                return None

        else:
            # Use SQL Database for production
            repo = get_credentials_sql_repo()
            if not repo:
                logger.error("Credentials SQL repository not available")
                return None

            query = """
            SELECT ClientId, ClientSecret, KeyVaultId, CreatedAt, UpdatedAt
            FROM App_Credentials
            WHERE OrgId = ?
            """

            results = repo.execute_query(query, (integration_id,))

            if results:
                row = results[0]
                return {
                    'client_id': row[0],
                    'client_secret': row[1],
                    'key_vault_id': row[2],
                    'created_at': row[3].isoformat() if row[3] else None,
                    'updated_at': row[4].isoformat() if row[4] else None
                }
            else:
                return None

    except Exception as e:
        logger.error(f"Error getting integration credentials: {str(e)}")
        return None


# Create blueprint
bp = func.Blueprint()

@bp.route(route="integrations", methods=["GET"])
def list_integrations(req: func.HttpRequest) -> func.HttpResponse:
    """List integrations with optional filtering"""
    try:
        # Get current user from authentication headers
        current_user = get_current_user(req)

        # Get query parameters
        user_email = req.params.get('user_email')
        user_id = req.params.get('user_id')
        account_id = req.params.get('account_id')
        include_inactive = req.params.get('include_inactive', 'false').lower() == 'true'
        integration_type = req.params.get('integration_type')

        # Security: If user is authenticated, filter by their user context
        # Only allow users to see their own integrations unless explicitly overridden
        if current_user:
            # Use authenticated user's email if no specific user_email is provided
            if not user_email:
                user_email = current_user.get('email')
                logger.info(f"Filtering integrations for authenticated user: {user_email}")
            else:
                # Security check: only allow users to query their own data
                # (In the future, you might want to add admin role checks here)
                if user_email != current_user.get('email'):
                    logger.warning(f"User {current_user.get('email')} attempted to access integrations for {user_email}")
                    return func.HttpResponse(
                        json.dumps({
                            "success": False,
                            "error": "Unauthorized: Cannot access other users' integrations"
                        }),
                        mimetype="application/json",
                        status_code=403
                    )
        else:
            # For unauthenticated requests in local development, allow but log warning
            if is_local_dev():
                logger.warning("Unauthenticated request in local development - returning all integrations")
            else:
                logger.error("Unauthenticated request in production environment")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Authentication required"
                    }),
                    mimetype="application/json",
                    status_code=401
                )

        integrations = get_integrations_list(
            user_email=user_email,
            user_id=user_id,
            account_id=account_id,
            include_inactive=include_inactive,
            integration_type=integration_type
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {"integrations": integrations, "count": len(integrations)}
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error listing integrations: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations", methods=["POST"])
def create_integration(req: func.HttpRequest) -> func.HttpResponse:
    """Create new integration"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ['name', 'tenant_url']
        for field in required_fields:
            if field not in req_body:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Missing required field: {field}"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        # Create integration
        integration_id = create_integration_record(
            name=req_body['name'],
            tenant_url=req_body['tenant_url'],
            integration_type=req_body.get('integration_type', 'Salesforce'),
            description=req_body.get('description', ''),
            environment=req_body.get('environment', 'production'),
            user_email=req_body.get('user_email'),
            user_id=req_body.get('user_id'),
            account_id=req_body.get('account_id'),
            is_active=req_body.get('is_active', True)
        )

        if integration_id:
            # After successful creation, check if policies/rules already exist
            from repositories.pmd_repository import PMDRepository
            pmd_repo = PMDRepository()
            config = pmd_repo.get_complete_configuration_for_integration(integration_id)
            if not config or not config.get('policy_id'):
                # Only create if not already present
                user_id = req_body.get('user_id')
                try:
                    logger.info(f"Creating default policies/rules for new integration {integration_id} (user {user_id})")
                    create_default_policies_and_rules_for_integration(user_id, integration_id)
                except Exception as e:
                    logger.error(f"Error creating default policies/rules for integration {integration_id}: {e}")
            else:
                logger.info(f"Default policies/rules already exist for integration {integration_id}, skipping creation.")

            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {"integration_id": integration_id}
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create integration"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating integration: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}", methods=["GET"])
def get_integration(req: func.HttpRequest) -> func.HttpResponse:
    """Get integration by ID"""
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get current user from authentication headers
        current_user = get_current_user(req)

        integration = get_integration_by_id_db(integration_id)

        if integration:
            # Security check: verify user owns this integration
            if current_user:
                user_email = current_user.get('email')
                integration_user_email = integration.get('user_email')

                if integration_user_email != user_email:
                    logger.warning(f"User {user_email} attempted to access integration {integration_id} owned by {integration_user_email}")
                    return func.HttpResponse(
                        json.dumps({
                            "success": False,
                            "error": "Unauthorized: Cannot access other users' integrations"
                        }),
                        mimetype="application/json",
                        status_code=403
                    )
            else:
                # For unauthenticated requests in local development, allow but log warning
                if is_local_dev():
                    logger.warning(f"Unauthenticated request for integration {integration_id} in local development")
                else:
                    logger.error("Unauthenticated request in production environment")
                    return func.HttpResponse(
                        json.dumps({
                            "success": False,
                            "error": "Authentication required"
                        }),
                        mimetype="application/json",
                        status_code=401
                    )

            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": integration
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting integration: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}", methods=["PUT"])
def update_integration(req: func.HttpRequest) -> func.HttpResponse:
    """Update integration"""
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Update integration
        success = update_integration_record(integration_id, req_body)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Integration updated successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update integration"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error updating integration: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}", methods=["DELETE"])
def delete_integration(req: func.HttpRequest) -> func.HttpResponse:
    """Delete integration (soft delete)"""
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Delete integration (soft delete)
        success = delete_integration_record(integration_id)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Integration deleted successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to delete integration"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error deleting integration: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="integrations/{integration_id}/overview", methods=["GET"])
def get_integration_overview(req: func.HttpRequest) -> func.HttpResponse:
    from datetime import datetime
    try:
        # Extract integration_id from route parameters
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration ID is required"}),
                mimetype="application/json",
                status_code=400
            )

        from shared.azure_services import is_local_dev
        from shared.data_access import get_table_storage_repository
        overview_data = {}
        if is_local_dev():
            integration_repo = get_table_storage_repository("Integrations")
            integration = integration_repo.get_entity("integration", integration_id) if integration_repo else None
            if not integration:
                return func.HttpResponse(json.dumps({"success": False, "error": f"Integration not found for ID: {integration_id}"}), mimetype="application/json", status_code=404)
            overview_repo = get_table_storage_repository("Overview")
            if not overview_repo:
                return func.HttpResponse(json.dumps({"dataStatus": "empty", "message": "No overview data available. Please sync to fetch data.", "timestamp": datetime.now().isoformat(), "tenantUrl": integration.get("TenantUrl", "")}), mimetype="application/json", status_code=200)
            filter_query = f"PartitionKey eq '{integration_id}'"
            entities = overview_repo.query_entities(filter_query)
            if not entities:
                return func.HttpResponse(json.dumps({"dataStatus": "empty", "message": "No overview data available. Please sync to fetch data.", "timestamp": datetime.now().isoformat(), "tenantUrl": integration.get("TenantUrl", "")}), mimetype="application/json", status_code=200)
            sorted_entities = sorted(entities, key=lambda x: x.get('RowKey', ''), reverse=True)
            latest_entity = sorted_entities[0]
            overview_data = {
                "dataStatus": "available",
                "healthScore": latest_entity.get('HealthScore', 0),
                "totalProfiles": latest_entity.get('TotalProfiles', 0),
                "totalPermissionSets": latest_entity.get('TotalPermissionSets', 0),
                "totalRisks": latest_entity.get('TotalRisks', 0),
                "highRisks": latest_entity.get('HighRisks', 0),
                "mediumRisks": latest_entity.get('MediumRisks', 0),
                "lowRisks": latest_entity.get('LowRisks', 0),
                "lastUpdated": latest_entity.get('LastUpdated', datetime.now().isoformat())
            }
        else:
            # For production, use SQL database (to be implemented)
            return func.HttpResponse(
                json.dumps({
                    "dataStatus": "empty",
                    "message": "SQL database support not yet implemented for overview data.",
                    "timestamp": datetime.now().isoformat(),
                    "tenantUrl": ""
                }),
                mimetype="application/json",
                status_code=200
            )
        return func.HttpResponse(json.dumps(overview_data), mimetype="application/json", status_code=200)
    except Exception as e:
        return func.HttpResponse(json.dumps({"success": False, "error": str(e)}), mimetype="application/json", status_code=500)

@bp.route(route="integrations/{integration_id}/health-check", methods=["GET"])
def get_integration_health_check(req: func.HttpRequest) -> func.HttpResponse:
    from datetime import datetime
    try:
        # Extract integration_id from route parameters
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration ID is required"}),
                mimetype="application/json",
                status_code=400
            )

        from shared.azure_services import is_local_dev
        from shared.data_access import get_table_storage_repository
        health_check_data = {}
        if is_local_dev():
            integration_repo = get_table_storage_repository("Integrations")
            integration = integration_repo.get_entity("integration", integration_id) if integration_repo else None
            if not integration:
                return func.HttpResponse(json.dumps({"success": False, "error": f"Integration not found for ID: {integration_id}"}), mimetype="application/json", status_code=404)
            # Use the security endpoints to get health check data
            from .security_endpoints import get_security_health_check_data
            health_check_data_list = get_security_health_check_data(integration_id)

            if not health_check_data_list:
                return func.HttpResponse(json.dumps({"dataStatus": "empty", "message": "No health check data available. Please sync to fetch data.", "timestamp": datetime.now().isoformat(), "tenantUrl": integration.get("TenantUrl", "")}), mimetype="application/json", status_code=200)

            # Convert the health check data to the expected format
            risks = []
            last_updated = None
            for health_check in health_check_data_list:
                risk = {
                    "riskType": health_check.get("risk_type", "UNKNOWN_RISK"),
                    "setting": health_check.get("setting", "Unknown Setting"),
                    "settingGroup": health_check.get("setting_group", "Unknown Group"),
                    "orgValue": health_check.get("org_value", "Unknown"),
                    "standardValue": health_check.get("standard_value", "Unknown")
                }
                risks.append(risk)
                entity_timestamp = health_check.get("last_updated")
                if entity_timestamp and (last_updated is None or entity_timestamp > last_updated):
                    last_updated = entity_timestamp

            health_score = int(integration.get("HealthScore", 0)) if "HealthScore" in integration else 0
            health_check_data = {
                "dataStatus": "available",
                "score": health_score,
                "risks": risks,
                "lastUpdated": last_updated if last_updated else datetime.now().isoformat()
            }
        else:
            # For production, use SQL database (to be implemented)
            return func.HttpResponse(
                json.dumps({
                    "dataStatus": "empty",
                    "message": "SQL database support not yet implemented for health check data.",
                    "timestamp": datetime.now().isoformat(),
                    "tenantUrl": ""
                }),
                mimetype="application/json",
                status_code=200
            )
        return func.HttpResponse(json.dumps(health_check_data), mimetype="application/json", status_code=200)
    except Exception as e:
        return func.HttpResponse(json.dumps({"success": False, "error": str(e)}), mimetype="application/json", status_code=500)

@bp.route(route="integrations/{integration_id}/profiles", methods=["GET"])
def get_integration_profiles(req: func.HttpRequest) -> func.HttpResponse:
    from datetime import datetime
    try:
        # Extract integration_id from route parameters
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration ID is required"}),
                mimetype="application/json",
                status_code=400
            )

        # Get current user from authentication headers
        current_user = get_current_user(req)

        # Get integration to verify ownership
        integration = get_integration_by_id_db(integration_id)
        if not integration:
            return func.HttpResponse(
                json.dumps({"success": False, "error": f"Integration not found for ID: {integration_id}"}),
                mimetype="application/json",
                status_code=404
            )

        # Security check: verify user owns this integration
        if current_user:
            user_email = current_user.get('email')
            integration_user_email = integration.get('user_email')

            if integration_user_email != user_email:
                logger.warning(f"User {user_email} attempted to access profiles for integration {integration_id} owned by {integration_user_email}")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Unauthorized: Cannot access other users' integration profiles"
                    }),
                    mimetype="application/json",
                    status_code=403
                )
        else:
            # For unauthenticated requests in local development, allow but log warning
            if is_local_dev():
                logger.warning(f"Unauthenticated request for integration profiles {integration_id} in local development")
            else:
                logger.error("Unauthenticated request in production environment")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Authentication required"
                    }),
                    mimetype="application/json",
                    status_code=401
                )

        from shared.azure_services import is_local_dev
        from shared.data_access import get_table_storage_repository
        if is_local_dev():
            integration_repo = get_table_storage_repository("Integrations")
            integration_entity = integration_repo.get_entity("integration", integration_id) if integration_repo else None
            if not integration_entity:
                return func.HttpResponse(json.dumps({"success": False, "error": f"Integration not found for ID: {integration_id}"}), mimetype="application/json", status_code=404)
            profile_permissions_repo = get_table_storage_repository("ProfilePermissions")
            if not profile_permissions_repo:
                return func.HttpResponse(json.dumps({"dataStatus": "empty", "message": "No profiles data available. Please sync to fetch data.", "timestamp": datetime.now().isoformat(), "tenantUrl": integration.get("TenantUrl", "")}), mimetype="application/json", status_code=200)
            filter_query = f"PartitionKey eq '{integration_id}' and EntityType eq 'Profile'"
            profile_entities = profile_permissions_repo.query_entities(filter_query)
            filter_query = f"PartitionKey eq '{integration_id}' and EntityType eq 'PermissionSet'"
            permission_set_entities = profile_permissions_repo.query_entities(filter_query)
            if (not profile_entities or len(profile_entities) == 0) and (not permission_set_entities or len(permission_set_entities) == 0):
                return func.HttpResponse(json.dumps({"dataStatus": "empty", "message": "No profiles data available. Please sync to fetch data.", "timestamp": datetime.now().isoformat(), "tenantUrl": integration.get("TenantUrl", "")}), mimetype="application/json", status_code=200)
            profiles = []
            for entity in profile_entities or []:
                risk_level = "LOW_RISK"
                permissions = {}
                for key, value in entity.items():
                    if key.startswith("Permission_"):
                        perm_name = key[11:]
                        permissions[perm_name] = value == "true" or value is True
                if permissions.get("modifyAllData") or permissions.get("manageUsers") or permissions.get("manageProfilesPermissionsets") or permissions.get("manageRoles"):
                    risk_level = "HIGH_RISK"
                elif permissions.get("viewAllData") or permissions.get("resetPasswords") or permissions.get("dataExport") or permissions.get("manageSharing"):
                    risk_level = "MEDIUM_RISK"
                profile = {
                    "id": entity.get("EntityId", ""),
                    "name": entity.get("Name", ""),
                    "description": f"{entity.get('Name', '')} profile",
                    "userLicense": entity.get("UserLicense", "Salesforce"),
                    "createdDate": entity.get("CreatedDate", datetime.now().isoformat()),
                    "lastModifiedDate": entity.get("LastModifiedDate", datetime.now().isoformat()),
                    "riskLevel": risk_level,
                    "riskScore": 90 if risk_level == "HIGH_RISK" else (60 if risk_level == "MEDIUM_RISK" else 30),
                    "userCount": int(entity.get("UserCount", 0)),
                    "permissions": permissions
                }
                profiles.append(profile)
            permission_sets = []
            for entity in permission_set_entities or []:
                risk_level = "LOW_RISK"
                permissions = {}
                for key, value in entity.items():
                    if key.startswith("Permission_"):
                        perm_name = key[11:]
                        permissions[perm_name] = value == "true" or value is True
                if permissions.get("modifyAllData") or permissions.get("manageUsers") or permissions.get("manageProfilesPermissionsets") or permissions.get("manageRoles"):
                    risk_level = "HIGH_RISK"
                elif permissions.get("viewAllData") or permissions.get("resetPasswords") or permissions.get("dataExport") or permissions.get("manageSharing"):
                    risk_level = "MEDIUM_RISK"
                permission_set = {
                    "id": entity.get("EntityId", ""),
                    "name": entity.get("Name", ""),
                    "description": f"{entity.get('Name', '')} permission set",
                    "createdDate": entity.get("CreatedDate", datetime.now().isoformat()),
                    "lastModifiedDate": entity.get("LastModifiedDate", datetime.now().isoformat()),
                    "riskLevel": risk_level,
                    "riskScore": 90 if risk_level == "HIGH_RISK" else (60 if risk_level == "MEDIUM_RISK" else 30),
                    "userCount": int(entity.get("UserCount", 0)),
                    "permissions": permissions
                }
                permission_sets.append(permission_set)
            return func.HttpResponse(json.dumps({"dataStatus": "available", "profiles": profiles, "permissionSets": permission_sets}), mimetype="application/json", status_code=200)
        else:
            # For production, use SQL database (to be implemented)
            return func.HttpResponse(
                json.dumps({
                    "dataStatus": "empty",
                    "profiles": [],
                    "permissionSets": []
                }),
                mimetype="application/json",
                status_code=200
            )
    except Exception as e:
        return func.HttpResponse(json.dumps({"success": False, "error": str(e)}), mimetype="application/json", status_code=500)


@bp.route(route="integrations/{integration_id}/credentials", methods=["GET"])
def get_integration_credentials_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get integration credentials"""
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        credentials = get_integration_credentials(integration_id)

        if credentials:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": credentials
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Credentials not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting integration credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}/pmd-issues", methods=["GET"])
def get_integration_pmd_issues(req: func.HttpRequest) -> func.HttpResponse:
    """Get PMD issues for integration"""
    from datetime import datetime
    try:
        # Extract integration_id from route parameters
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration ID is required"}),
                mimetype="application/json",
                status_code=400
            )

        # Check if integration exists
        integration = get_integration_by_id_db(integration_id)
        if not integration:
            return func.HttpResponse(
                json.dumps({"success": False, "error": f"Integration not found for ID: {integration_id}"}),
                mimetype="application/json",
                status_code=404
            )

        # Get PMD findings from database using PMD repository
        from repositories.pmd_repository import PMDRepository
        pmd_repo = PMDRepository()
        
        # Get query parameters for filtering
        limit = int(req.params.get('limit', 1000))  # Default to 1000 for PMD issues
        offset = int(req.params.get('offset', 0))
        severity = req.params.get('severity')
        category = req.params.get('category')
        
        findings_data = pmd_repo.get_findings_for_integration(
            integration_id, 
            limit=limit, 
            offset=offset,
            severity=severity,
            category=category
        )
        
        findings = findings_data.get("findings", [])
        
        if not findings:
            return func.HttpResponse(
                json.dumps({
                    "dataStatus": "empty",
                    "message": "No PMD issues data available. Please sync to fetch data.",
                    "timestamp": datetime.now().isoformat(),
                    "tenantUrl": integration.get("tenant_url", ""),
                    "issues": [],
                    "summary": {
                        "total": 0,
                        "high": 0,
                        "medium": 0,
                        "low": 0
                    }
                }),
                mimetype="application/json",
                status_code=200
            )

        # Process PMD issues
        issues = []
        summary = {"total": 0, "high": 0, "medium": 0, "low": 0}

        for finding in findings:
            severity = finding.get("severity", "low").lower()
            issue = {
                "id": f"{finding.get('task_id', '')}-{finding.get('file_name', '')}-{finding.get('line_number', '')}",
                "rule": finding.get("rule_name", "Unknown Rule"),
                "severity": severity,
                "message": finding.get("issue_description", ""),
                "file": finding.get("file_name", ""),
                "line": int(finding.get("line_number", 0)),
                "column": 0,  # Not available in current schema
                "package": finding.get("package", ""),
                "class": finding.get("class", ""),
                "method": finding.get("method", ""),
                "priority": int(finding.get("issue_priority", 3)),
                "ruleSet": finding.get("pmd_rule_set", ""),
                "externalInfoUrl": "",  # Not available in current schema
                "description": finding.get("issue_description", ""),
                "timestamp": finding.get("created_at", datetime.now().isoformat())
            }
            issues.append(issue)

            # Update summary
            summary["total"] += 1
            if severity == "high":
                summary["high"] += 1
            elif severity == "medium":
                summary["medium"] += 1
            else:
                summary["low"] += 1

        return func.HttpResponse(
            json.dumps({
                "dataStatus": "available",
                "issues": issues,
                "summary": summary,
                "lastUpdated": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenant_url", ""),
                "totalCount": findings_data.get("total_count", 0),
                "hasMore": findings_data.get("has_more", False)
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error getting PMD issues: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integration/test-connection", methods=["POST"])
def test_connection(req: func.HttpRequest) -> func.HttpResponse:
    """Test Salesforce connection by proxying to SFDC service"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body is required"}),
                mimetype="application/json",
                status_code=400
            )

        if not req_body:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body is required"}),
                mimetype="application/json",
                status_code=400
            )

        # Handle case where req_body might be a string (double-encoded JSON)
        if isinstance(req_body, str):
            try:
                logger.info(f"Request body is string, attempting to parse: {req_body}")
                req_body = json.loads(req_body)
            except Exception as parse_error:
                logger.error(f"Failed to parse string body as JSON: {parse_error}")
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Request body must be a valid JSON object"}),
                    mimetype="application/json",
                    status_code=400
                )

        # Validate request body is now a dictionary
        if not isinstance(req_body, dict):
            logger.error(f"Invalid request body type after parsing: {type(req_body)}, expected dict")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body must be a JSON object"}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required parameters for validation
        tenant_url = req_body.get('tenantUrl')
        client_id = req_body.get('clientId')

        if not tenant_url or not client_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "tenantUrl and clientId are required"}),
                mimetype="application/json",
                status_code=400
            )

        # Proxy the request to SFDC service for proper separation of duties
        from shared.sfdc_service_client import get_sfdc_client

        logger.info("Proxying test-connection request to SFDC service")
        sfdc_client = get_sfdc_client()

        # Forward the entire request body to SFDC service
        response = sfdc_client.test_connection(data=req_body)

        if response is None:
            logger.error("Failed to get response from SFDC service")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Failed to communicate with SFDC service"}),
                mimetype="application/json",
                status_code=500
            )

        # Check if response contains an error from SFDC service
        if isinstance(response, dict) and response.get('error'):
            logger.error(f"SFDC service returned error: {response.get('error')}")
            return func.HttpResponse(
                json.dumps(response),
                mimetype="application/json",
                status_code=response.get('status_code', 400)
            )

        # Return successful response from SFDC service
        logger.info("Successfully proxied test-connection request to SFDC service")
        return func.HttpResponse(
            json.dumps(response),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error testing connection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integration/connect", methods=["POST"])
def connect_integration(req: func.HttpRequest) -> func.HttpResponse:
    """Connect to an integration and store credentials"""
    try:
        # Parse request body with better error handling
        try:
            req_body = req.get_json()
        except Exception as json_error:
            logger.error(f"Failed to parse JSON: {json_error}")
            # Try to get raw body and parse manually
            try:
                raw_body = req.get_body().decode('utf-8')
                req_body = json.loads(raw_body)
            except Exception as raw_error:
                logger.error(f"Failed to parse raw body: {raw_error}")
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Invalid JSON in request body"}),
                    mimetype="application/json",
                    status_code=400
                )

        if not req_body:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body is required"}),
                mimetype="application/json",
                status_code=400
            )

        # Handle case where req_body might be a string (double-encoded JSON)
        if isinstance(req_body, str):
            try:
                logger.info(f"Request body is string, attempting to parse: {req_body}")
                req_body = json.loads(req_body)
            except Exception as parse_error:
                logger.error(f"Failed to parse string body as JSON: {parse_error}")
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Request body must be a valid JSON object"}),
                    mimetype="application/json",
                    status_code=400
                )

        # Validate request body is now a dictionary
        if not isinstance(req_body, dict):
            logger.error(f"Invalid request body type after parsing: {type(req_body)}, expected dict")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body must be a JSON object"}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required parameters
        org_name = req_body.get('orgName')
        tenant_url = req_body.get('tenantUrl')
        client_id = req_body.get('clientId')
        integration_type = req_body.get('type', 'Salesforce')
        environment = req_body.get('environment', 'production')
        description = req_body.get('description', '')
        is_active = req_body.get('isActive', True)
        use_jwt = req_body.get('useJwt', False)
        integration_id = req_body.get('integrationId')  # For updates

        if not org_name or not tenant_url or not client_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "orgName, tenantUrl, and clientId are required"}),
                mimetype="application/json",
                status_code=400
            )

        # Skip automatic connection test - frontend handles this separately
        # This avoids timeout issues and redundant testing
        logger.info("Skipping automatic connection test - frontend handles connection validation")

        # Get current user for proper user association
        # First try to get user info from request body (sent by frontend)
        user_email = req_body.get('userEmail') or req_body.get('user_email')
        user_id = req_body.get('userId') or req_body.get('user_id')

        # If not provided in request body, try to get from authentication
        if not user_email or not user_id:
            current_user = get_current_user(req)
            if current_user:
                user_email = user_email or current_user.get('email')
                user_id = user_id or current_user.get('user_id') or current_user.get('id')

        # If still no user info, try to get from request headers
        if not user_email:
            user_email = req.headers.get('X-User-Email') or req.headers.get('x-user-email')
        if not user_id:
            user_id = req.headers.get('X-User-Id') or req.headers.get('x-user-id')

        logger.info(f"Creating integration for user: {user_email} (ID: {user_id})")

        if not user_email and not user_id:
            logger.warning("No user information found - integration will not be associated with a user")

        # Create or update integration record
        if integration_id:
            # Update existing integration
            update_data = {
                'name': org_name,
                'tenant_url': tenant_url,
                'description': description,
                'environment': environment,
                'is_active': is_active
            }

            if update_integration_record(integration_id, update_data):
                db_integration_id = integration_id
            else:
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Failed to update integration"}),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            # Create new integration with user association
            db_integration_id = create_integration_record(
                name=org_name,
                tenant_url=tenant_url,
                integration_type=integration_type,
                description=description,
                environment=environment,
                user_email=user_email,
                user_id=user_id,
                is_active=is_active
            )

            if not db_integration_id:
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Failed to create integration"}),
                    mimetype="application/json",
                    status_code=500
                )

            # After successful creation, check if policies/rules already exist
            from repositories.pmd_repository import PMDRepository
            pmd_repo = PMDRepository()
            config = pmd_repo.get_complete_configuration_for_integration(db_integration_id)
            if not config or not config.get('policy_id'):
                # Only create if not already present
                try:
                    logger.info(f"Creating default policies/rules for new integration {db_integration_id} (user {user_id})")
                    create_default_policies_and_rules_for_integration(user_id, db_integration_id)
                except Exception as e:
                    logger.error(f"Error creating default policies/rules for integration {db_integration_id}: {e}")
            else:
                logger.info(f"Default policies/rules already exist for integration {db_integration_id}, skipping creation.")

        # Store credentials now that timeout issues are resolved
        auth_flow = 'jwt' if use_jwt else 'client_credentials'
        credentials_stored = True

        try:
            if is_local_dev():
                # For local dev, store credentials with error handling
                logger.info(f"Storing credentials for integration {db_integration_id}")

                # Store client ID
                client_id_data = {
                    'credential_type': 'client-id',
                    'value': client_id,
                    'auth_flow': auth_flow
                }
                if not store_integration_credentials(db_integration_id, client_id_data):
                    logger.warning(f"Failed to store client ID for integration {db_integration_id}")
                    credentials_stored = False

                if use_jwt:
                    # Store username
                    username_data = {
                        'credential_type': 'username',
                        'value': req_body.get('username', ''),
                        'auth_flow': auth_flow
                    }
                    if not store_integration_credentials(db_integration_id, username_data):
                        logger.warning(f"Failed to store username for integration {db_integration_id}")
                        credentials_stored = False

                    # Store private key
                    private_key_data = {
                        'credential_type': 'private-key',
                        'value': req_body.get('privateKey', ''),
                        'auth_flow': auth_flow
                    }
                    if not store_integration_credentials(db_integration_id, private_key_data):
                        logger.warning(f"Failed to store private key for integration {db_integration_id}")
                        credentials_stored = False
                else:
                    # Store client secret
                    client_secret_data = {
                        'credential_type': 'client-secret',
                        'value': req_body.get('clientSecret', ''),
                        'auth_flow': auth_flow
                    }
                    if not store_integration_credentials(db_integration_id, client_secret_data):
                        logger.warning(f"Failed to store client secret for integration {db_integration_id}")
                        credentials_stored = False
            else:
                # For production, store all credentials in one record
                credentials_data = {
                    'client_id': client_id,
                    'auth_flow': auth_flow,
                    'tenant_url': tenant_url,
                    'environment': environment
                }

                if use_jwt:
                    credentials_data['username'] = req_body.get('username')
                    credentials_data['private_key'] = req_body.get('privateKey')
                else:
                    credentials_data['client_secret'] = req_body.get('clientSecret')

                credentials_stored = store_integration_credentials(db_integration_id, credentials_data)
                if not credentials_stored:
                    logger.warning(f"Failed to store credentials for integration {db_integration_id}")
        except Exception as cred_error:
            logger.error(f"Error storing credentials: {str(cred_error)}")
            credentials_stored = False

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "Integration connected successfully",
                "data": {
                    "integration_id": db_integration_id,
                    "name": org_name,
                    "tenant_url": tenant_url,
                    "type": integration_type,
                    "environment": environment
                }
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error connecting integration: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}/scan", methods=["POST"])
def scan_integration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Enqueue a scan task for an integration.
    This endpoint creates a unique execution log first, then creates a task in the database.
    The execution log ID is passed through the entire workflow to ensure proper isolation.
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Enqueuing scan task for integration ID: {integration_id}")

        # Get current user from authentication headers
        current_user = get_current_user(req)

        # Get integration to ensure it exists and is active
        integration = get_integration_by_id_db(integration_id)
        if not integration:
            logger.error(f"Integration with ID {integration_id} not found.")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Integration with ID {integration_id} not found."
                }),
                mimetype="application/json",
                status_code=404
            )

        # Security check: verify user owns this integration
        if current_user:
            user_email = current_user.get('email')
            integration_user_email = integration.get('user_email')

            if integration_user_email != user_email:
                logger.warning(f"User {user_email} attempted to scan integration {integration_id} owned by {integration_user_email}")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Unauthorized: Cannot scan other users' integrations"
                    }),
                    mimetype="application/json",
                    status_code=403
                )
        else:
            # For unauthenticated requests in local development, allow but log warning
            if is_local_dev():
                logger.warning(f"Unauthenticated scan request for integration {integration_id} in local development")
            else:
                logger.error("Unauthenticated scan request in production environment")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Authentication required"
                    }),
                    mimetype="application/json",
                    status_code=401
                )

        if not integration.get("is_active", False):
            logger.warning(f"Integration {integration_id} is not active. Scan aborted.")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Cannot scan inactive integration. Please fix the connection first."
                }),
                mimetype="application/json",
                status_code=400
            )

        # Import task creation function
        from .task_endpoints import create_task

        # Get user ID from current user or use default
        user_id = "system"  # Default fallback
        if current_user:
            user_id = current_user.get('user_id') or current_user.get('email') or "system"

        logger.info(f"Creating scan task for integration {integration_id} by user: {user_id}")
        logger.info(f"Current user data: {current_user}")

        # Create execution log FIRST to ensure unique ID for this scan
        execution_log_service = get_execution_log_service()
        execution_log_id = execution_log_service.create_execution_log(
            org_id=integration_id,  # Keep as string (UUID)
            execution_type="Integration_Scan",
            user_id=int(user_id) if str(user_id).isdigit() else 1,  # Handle non-numeric user IDs
            priority="High"
        )

        if not execution_log_id:
            logger.error(f"Failed to create execution log for integration {integration_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create execution log for scan"
                }),
                mimetype="application/json",
                status_code=500
            )

        logger.info(f"Created execution log {execution_log_id} for integration {integration_id}")

        # Prepare task data for scan with explicit execution_log_id
        task_data = {
            "task_type": "sfdc_authenticate",  # This will trigger the scan process
            "org_id": integration_id,
            "user_id": user_id,
            "status": "Pending",
            "priority": "High",
            "execution_log_id": execution_log_id,  # Pass execution log ID explicitly
            "data": {
                "integration_id": integration_id,
                "tenant_url": integration.get("tenant_url"),
                "environment": integration.get("environment"),
                "scan_type": "manual",
                "execution_log_id": execution_log_id  # Also include in data for SFDC service
            }
        }

        logger.info(f"Creating scan task via database service for integration {integration_id} with execution_log_id {execution_log_id}")
        task_id = create_task(task_data)

        if not task_id:
            logger.error(f"Failed to create scan task for integration {integration_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to initiate Salesforce scan"
                }),
                mimetype="application/json",
                status_code=500
            )

        logger.info(f"Successfully created scan task {task_id} for integration {integration_id}")

        # Update LastScanAttempt timestamp on the integration record
        from datetime import datetime
        now_iso = datetime.now().isoformat()

        update_data = {
            "last_scan_attempt": now_iso
        }

        if not update_integration_record(integration_id, update_data):
            logger.warning(f"Failed to update LastScanAttempt for integration {integration_id}")

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "Salesforce scan initiated. Task has been queued via database service.",
                "data": {
                    "integration_id": integration_id,
                    "task_id": task_id,
                    "execution_log_id": execution_log_id
                }
            }),
            mimetype="application/json",
            status_code=202  # Accepted
        )

    except Exception as e:
        logger.error(f"Error creating scan task for integration {integration_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Error initiating Salesforce scan: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="integrations/{integration_id}/rescan", methods=["POST"])
def rescan_integration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Rescan an integration (alias for scan_integration with force=true)
    This endpoint is called by the frontend rescan button
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Rescan requested for integration ID: {integration_id}")

        # Get current user from authentication headers
        current_user = get_current_user(req)

        # Get integration to ensure it exists and is active
        integration = get_integration_by_id_db(integration_id)
        if not integration:
            logger.error(f"Integration not found: {integration_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        # Check user authorization
        if current_user:
            user_email = current_user.get('email')
            integration_user_email = integration.get('user_email')

            if integration_user_email != user_email:
                logger.warning(f"User {user_email} attempted to rescan integration {integration_id} owned by {integration_user_email}")
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Unauthorized: Cannot rescan other users' integrations"
                    }),
                    mimetype="application/json",
                    status_code=403
                )

        # Check if integration is active
        if not integration.get("is_active", False):
            logger.warning(f"Integration {integration_id} is not active. Rescan aborted.")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Cannot rescan inactive integration. Please fix the connection first."
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get user ID from current user or use default
        user_id = "system"  # Default fallback
        if current_user:
            user_id = current_user.get('user_id') or current_user.get('email') or "system"

        logger.info(f"Creating rescan task for integration {integration_id} by user: {user_id}")

        # For rescan, we want to force a new scan even if one is running
        # First, cancel any existing pending/running tasks for this integration
        from shared.background_processor import get_background_processor
        processor = get_background_processor()

        # Get existing tasks for this integration
        existing_tasks = processor.get_tasks_by_org(integration_id, status_filter=None, limit=20)

        # Cancel any pending or running tasks
        cancelled_count = 0
        for task in existing_tasks:
            if task.get("Status") in ["pending", "running"]:
                success = processor.cancel_task(task.get("TaskId"), "Cancelled for rescan")
                if success:
                    cancelled_count += 1
                    logger.info(f"Cancelled existing task {task.get('TaskId')} for rescan")

        if cancelled_count > 0:
            logger.info(f"Cancelled {cancelled_count} existing tasks for integration {integration_id}")

        # Create execution log for the rescan
        execution_log_service = get_execution_log_service()
        execution_log_id = execution_log_service.create_execution_log(
            org_id=integration_id,
            execution_type="rescan",
            user_id=user_id,
            status="Pending"
        )

        if not execution_log_id:
            logger.error(f"Failed to create execution log for rescan of integration {integration_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create execution log"
                }),
                mimetype="application/json",
                status_code=500
            )

        # Create task data for rescan - start with authentication task
        # Rescan must begin with sfdc_authenticate task to establish proper dependency chain
        task_data = {
            "task_type": "sfdc_authenticate",  # Start with authentication for proper dependency chain
            "org_id": integration_id,
            "user_id": user_id,
            "priority": "high",  # Rescan gets high priority
            "execution_log_id": execution_log_id,
            "force": True,  # Force the task even if duplicates exist
            "data": {
                "integration_id": integration_id,
                "scan_type": "rescan",
                "execution_log_id": execution_log_id,
                "tenant_url": None,  # Will be populated from integration record
                "username": None,    # Will be populated from integration record
                "password": None     # Will be populated from integration record
            }
        }

        # Import task creation function
        try:
            from .task_endpoints import create_task
        except ImportError:
            from api.task_endpoints import create_task

        logger.info(f"Creating rescan task for integration {integration_id} with execution_log_id {execution_log_id}")
        task_id = create_task(task_data)

        if not task_id:
            logger.error(f"Failed to create rescan task for integration {integration_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to initiate rescan"
                }),
                mimetype="application/json",
                status_code=500
            )

        logger.info(f"Successfully created rescan task {task_id} for integration {integration_id}")

        # Update LastScanAttempt timestamp on the integration record
        from datetime import datetime
        now_iso = datetime.now().isoformat()

        update_data = {
            "last_scan_attempt": now_iso
        }

        if not update_integration_record(integration_id, update_data):
            logger.warning(f"Failed to update LastScanAttempt for integration {integration_id}")

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "Rescan initiated successfully. Previous tasks cancelled.",
                "task_id": task_id,
                "execution_log_id": execution_log_id,
                "cancelled_tasks": cancelled_count
            }),
            mimetype="application/json",
            status_code=202  # Accepted
        )

    except Exception as e:
        logger.error(f"Error creating rescan task for integration {integration_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )