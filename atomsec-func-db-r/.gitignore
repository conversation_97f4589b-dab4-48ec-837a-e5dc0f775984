# Python and Virtual Environment
.venv
.python_packages
__pycache__/
*.pyc
.pytest_cache

# Conflicting modules
/asyncio/

# Azure Functions
!task_processor/function.json
!WrapperFunction/function.json

# Azure Storage Emulator
.azurite/
.azurite/__azurite_db_*.json
__blobstorage__/
__queuestorage__/

# VS Code settings
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Project directories
certs/
static/
logs/

# Log files
*.log

# Documentation files (uncomment to ignore)
# API_CONSOLIDATION_SUMMARY.md
# CONSOLIDATED_API_ENDPOINTS.md

# Local settings (uncomment to ignore)
# local.settings.json
# config.py