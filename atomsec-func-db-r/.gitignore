# Python virtual environment
.venv

# Conflicting modules
/asyncio/

# Python cache files
.python_packages
__pycache__
*.pyc
.pytest_cache
api/__pycache__/*

# Azurite local DB files
.azurite/
.azurite/__azurite_db_*.json
__blobstorage__/
__queuestorage__/

# Local settings and credentials
# local.settings.json
# config.py

# VS Code settings
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

certs
static
# Documentation (keep in source control)
# references

# Generated function.json files
*/function.json
!task_processor/function.json
!WrapperFunction/function.json

logs/
function.log
*.pyc
DB-local-testing.logs
API_CONSOLIDATION_SUMMARY.md
CONSOLIDATED_API_ENDPOINTS.md
db.log
/api/__pycache__
/shared/__pycache__*
DB-local-testing.log
*.pyc
dbfunctionapp.log
shared/__pycache__/*
repositories/__pycache__/*
repositories/__pycache__/__init__.cpython-312.pyc
repositories/__pycache__/user_repository.cpython-312.pyc
shared/__pycache__/__init__.cpython-312.pyc
shared/__pycache__/azure_services.cpython-312.pyc
shared/__pycache__/common.cpython-312.pyc
shared/__pycache__/config.cpython-312.pyc
shared/__pycache__/data_access.cpython-312.pyc
shared/__pycache__/database_models_new.cpython-312.pyc
shared/__pycache__/database_models.cpython-312.pyc
