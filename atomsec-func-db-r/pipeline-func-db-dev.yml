# Azure DevOps Pipeline for AtomSec DB Function App (dev)
# Reference: pipeline-func-sfdc-dev.yml structure

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

steps:

- task: UsePythonVersion@0
  inputs:
    versionSpec: '3.12'
  displayName: 'Set up Python 3.12'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Azure CLI version:'
      az --version
      echo 'Installing Azure Functions Core Tools...'
      npm install -g azure-functions-core-tools@4 --unsafe-perm true
  displayName: 'Setup Azure CLI and Functions Tools'

- script: |
    python --version
    python -m pip install --upgrade pip setuptools wheel

    echo 'Installing system dependencies for Python packages...'
    sudo apt-get update
    sudo apt-get install -y build-essential python3-dev pkg-config

    echo 'Installing ODBC driver for SQL Server on the build agent...'
    sudo apt-get install -y curl gnupg2
    
    # Remove any existing Microsoft repository entries to avoid conflicts
    sudo rm -f /etc/apt/sources.list.d/mssql-release.list
    sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
    
    # Add Microsoft repository properly
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
    echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
    
    sudo apt-get update
    sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
    sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

    echo 'ODBC driver installation attempt on build agent complete.'

    echo 'Installing Python dependencies ...'
    head requirements.txt
    
    # Ensure we have modern build tools compatible with Python 3.12
    pip install --upgrade pip
    pip install --upgrade setuptools>=68.0.0 wheel>=0.40.0 build>=1.0.0
    
    # Install remaining dependencies first (without pyodbc)
    pip install --no-cache-dir -r requirements.txt -t .

    # Install pyodbc to the same target directory
    pip install --no-cache-dir pyodbc==5.0.1 -t .
  displayName: 'Install ODBC driver and Python dependencies'

- script: |
    echo 'Verifying package structure before archiving...'
    echo 'Files in current directory:'
    ls -la
    
    echo 'Checking if function_app.py exists:'
    if [ -f function_app.py ]; then
      echo '✓ function_app.py found'
      echo 'First 30 lines:'
      head -30 function_app.py
    else
      echo '✗ function_app.py not found'
      exit 1
    fi
    
    echo 'Checking if host.json exists:'
    if [ -f host.json ]; then
      echo '✓ host.json found'
      cat host.json
    else
      echo '✗ host.json not found'
      exit 1
    fi
    
    echo 'Checking API directory:'
    if [ -d api ]; then
      echo '✓ API directory found'
      ls -la api/
      echo 'Sample API file:'
      head -20 api/user_endpoints.py
    else
      echo '✗ API directory not found'
      exit 1
    fi
    
    echo 'Checking shared directory:'
    if [ -d shared ]; then
      echo '✓ Shared directory found'
      ls -la shared/
    else
      echo '✗ Shared directory not found'
    fi
    
    echo 'Checking repositories directory:'
    if [ -d repositories ]; then
      echo '✓ Repositories directory found'
      ls -la repositories/
    else
      echo '✗ Repositories directory not found'
    fi
    
    echo 'Testing function imports locally:'
    python -c "
    import sys
    sys.path.insert(0, '.')
    try:
        print('Testing basic imports...')
        import azure.functions as func
        print('✓ azure.functions imported successfully')
        
        print('Testing function_app import...')
        import function_app
        print('✓ function_app.py imports successfully')
        
        print('Testing API imports...')
        from api.user_endpoints import bp as user_bp
        print('✓ user_endpoints imported successfully')
        
    except Exception as e:
        print(f'✗ Import failed: {e}')
        import traceback
        traceback.print_exc()
    "
  displayName: 'Verify Package Structure'
  continueOnError: true

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true
  displayName: 'Archive DB Function App'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Creating staging slot if it does not exist...'
      az functionapp deployment slot create --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage || echo "Staging slot may already exist"
  displayName: 'Ensure Staging Slot Exists'
  continueOnError: true

- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: 'sc-atomsec-dev-data'
    appType: 'functionAppLinux'
    appName: 'func-atomsec-dbconnect-dev'
    package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.12'
    deploymentMethod: 'zipDeploy'
    resourceGroupName: 'atomsec-dev-data'
    slotName: 'stage'
  displayName: 'Deploy DB Function App to Staging Slot'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Testing staging slot deployment...'
      
      # Wait for deployment to be ready
      sleep 60
      
      # Test health endpoint with route prefix
      echo 'Testing health endpoint with route prefix...'
      curl -v "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/health" || echo "Health endpoint with prefix failed"
      
      # Test health endpoint without route prefix
      echo 'Testing health endpoint without route prefix...'
      curl -v "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/health" || echo "Health endpoint without prefix failed"
      
      # Test info endpoint  
      echo 'Testing info endpoint...'
      curl -v "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/info" || echo "Info endpoint failed"
      
      # Check function app status
      echo 'Checking staging slot status...'
      az functionapp show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage --query "state"
      
      echo 'Staging slot tests completed.'
  displayName: 'Test Staging Slot Deployment'
  continueOnError: true

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Checking deployment logs and function status...'
      
      # Check staging slot configuration
      echo 'Staging slot configuration:'
      az functionapp config show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage
      
      # Check staging slot state
      echo 'Staging slot state:'
      az functionapp show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage --query "state"
      
      # Get deployment status for staging slot
      echo 'Staging slot deployment status:'
      az webapp deployment list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage --query "[0].{status:status, message:message, author:author, deployer:deployer}" || echo "Could not get deployment status"
      
      # Check if functions are loaded by testing endpoints on staging slot
      echo 'Testing staging slot function endpoints directly...'
      
      # Test health endpoint with route prefix
      echo 'Testing: https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/health'
      curl -v -X GET "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/health" || echo "Health endpoint with prefix failed"
      
      # Test health endpoint without route prefix
      echo 'Testing: https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/health'
      curl -v -X GET "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/health" || echo "Health endpoint without prefix failed"
      
      # Test info endpoint
      echo 'Testing: https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/info'
      curl -v -X GET "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/info" || echo "Info endpoint failed"
      
      # Test users endpoint
      echo 'Testing: https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/users'
      curl -v -X GET "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net/api/db/users" || echo "Users endpoint failed"
      
      # Check staging slot application settings
      echo 'Staging slot app settings:'
      az functionapp config appsettings list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage --query "[].{name:name, value:value}" || echo "Could not get app settings"
      
      # Check if the staging slot is responding at all
      echo 'Testing basic connectivity to staging slot...'
      curl -v --connect-timeout 10 --max-time 30 "https://func-atomsec-dbconnect-dev-stage.azurewebsites.net" || echo "Basic connectivity to staging failed"
      
  displayName: 'Check Deployment Status'
  continueOnError: true

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Swapping staging slot to production...'
      az functionapp deployment slot swap --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --slot stage --target-slot production
      echo 'Slot swap completed successfully.'
      
      # Test production after swap
      echo 'Testing production after swap...'
      sleep 30
      curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health" || echo "Production health check after swap failed"
  displayName: 'Swap Staging to Production'
  condition: succeeded()

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    ArtifactName: 'drop'
    publishLocation: 'Container'
  displayName: 'Publish DB Service Artifacts' 