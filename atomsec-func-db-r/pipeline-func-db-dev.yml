# Azure DevOps Pipeline for AtomSec DB Function App (dev)
# Reference: pipeline-func-sfdc-dev.yml structure

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

steps:

- task: UsePythonVersion@0
  inputs:
    versionSpec: '3.11'
  displayName: 'Set up Python 3.11'

- script: |
    python --version
    python -m pip install --upgrade pip setuptools wheel

    echo 'Installing system dependencies for Python packages...'
    sudo apt-get update
    sudo apt-get install -y build-essential python3-dev pkg-config

    echo 'Installing ODBC driver for SQL Server on the build agent...'
    sudo apt-get install -y curl gnupg2
    
    # Remove any existing Microsoft repository entries to avoid conflicts
    sudo rm -f /etc/apt/sources.list.d/mssql-release.list
    sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
    
    # Add Microsoft repository properly
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
    echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
    
    sudo apt-get update
    sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
    sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

    echo 'ODBC driver installation attempt on build agent complete.'

    echo 'Installing Python dependencies ...'
    head requirements.txt
    
    # Ensure we have modern build tools compatible with Python 3.12
    pip install --upgrade pip
    pip install --upgrade setuptools>=68.0.0 wheel>=0.40.0 build>=1.0.0
    
    # Install remaining dependencies first (without pyodbc)
    pip install --no-cache-dir -r requirements.txt -t .
    
    # Install pyodbc
    pip install --no-cache-dir pyodbc==5.0.1
  displayName: 'Install ODBC driver and Python dependencies'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true
  displayName: 'Archive DB Function App'

- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: 'sc-atomsec-dev-data'
    appType: 'functionAppLinux'
    appName: 'func-atomsec-dbconnect-dev'
    package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.11'
    deploymentMethod: 'zipDeploy'
    resourceGroupName: 'atomsec-dev-data'
  displayName: 'Deploy DB Function App'

- script: |
    pip install pytest pytest-azurepipelines
    echo 'Set base_url for DB test--'
    export base_url="https://func-atomsec-dbconnect-dev.azurewebsites.net/"
    # If you have pytest-compatible tests, enable the next line:
    # pytest tests/
    echo 'No pytest-compatible tests found. Skipping.'
  displayName: 'Run DB Service Tests via pytest'
  continueOnError: true

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    ArtifactName: 'drop'
    publishLocation: 'Container'
  displayName: 'Publish DB Service Artifacts' 