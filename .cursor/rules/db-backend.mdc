# AtomSec DB Service Backend (Azure Functions) Structure and Routing

## Main Entry Point
- The main entry point is [function_app.py](mdc:atomsec-func-db-r/function_app.py).
- All API endpoints are registered as blueprints from [api/](mdc:atomsec-func-db-r/api/) and attached to the Azure Function App.
- The app exposes RESTful endpoints for database operations, user/account/org/integration management, security, tasks, policies, and more.

## Architectural Notes
- Follows a microservices architecture, acting as the single entry point for all frontend requests.
- Handles both direct database operations and proxying to the SFDC backend for Salesforce-specific logic.
- Implements repository pattern for data access (see [repositories/](mdc:atomsec-func-db-r/repositories/)).
- CORS and authentication are handled globally.
- **Does NOT** register or use blueprints from the SFDC backend. All blueprints are local to the DB service.

## Key API Routes (see [function_app.py](mdc:atomsec-func-db-r/function_app.py) and [README.md](mdc:atomsec-func-db-r/README.md))

### User & Account Management
- **/api/db/users**: User CRUD operations
- **/api/db/accounts**: Account CRUD operations
- **/api/db/organizations**: Organization CRUD operations
- **/api/db/user**: User profile management (GET/PUT profile, password)

### Integration Management
- **/api/db/integrations**: Integration CRUD and data endpoints (overview, health-check, profiles, pmd-issues)
- **/api/db/integration/test-connection**: Test Salesforce connection
- **/api/db/integration/connect**: Connect and store credentials
- **/api/db/integration/scan/{id}**: Enqueue scan task for integration (proxied to SFDC)

### Security & Policy
- **/api/db/security**: Security health checks, profiles, permission sets, overview
- **/api/db/policies**: Policy and rule management (policies, rules, policy-rule-settings)
- **/api/db/pmd**: PMD scan and configuration endpoints

### Task & Scan Management
- **/api/db/tasks**: Task management (list, status, schedule, cancel)
- **/api/db/scan**: Scan initiation and status endpoints

### System & Utility
- **/api/db/key-vault**: Azure Key Vault operations (secrets, credentials)
- **/api/db/stats**: Database statistics
- **/api/db/info**: Service info endpoint

### Proxy Endpoints to SFDC
- Proxy endpoints are implemented in [api/sfdc_proxy_endpoints.py](mdc:atomsec-func-db-r/api/sfdc_proxy_endpoints.py).
- These endpoints forward Salesforce-specific requests to the SFDC backend (see [SFDC_PROXY_ARCHITECTURE.md](mdc:atomsec-func-db-r/SFDC_PROXY_ARCHITECTURE.md)).
- Examples:
  - **POST /integration/scan/{id}** → Triggers scan in SFDC backend
  - **GET /task-status** → Gets task status from SFDC backend
  - **GET /health-score** → Gets health score from SFDC backend
  - **GET /profiles** → Gets profiles from SFDC backend

## Adding New Endpoints
- Add new routes as blueprints in [api/](mdc:atomsec-func-db-r/api/) and register them in [function_app.py](mdc:atomsec-func-db-r/function_app.py).
- For proxy endpoints, implement forwarding logic in [api/sfdc_proxy_endpoints.py](mdc:atomsec-func-db-r/api/sfdc_proxy_endpoints.py).

---
This rule provides a comprehensive guide to the DB Service backend structure, routing, and API endpoints for AtomSec. Reference this rule for endpoint details, architecture, and proxying behavior.
description:
globs:
alwaysApply: false
---
