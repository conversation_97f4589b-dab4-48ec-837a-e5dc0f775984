# AtomSec Frontend (React) Structure and Routing

## Main Entry Point
- The main entry point is [App.js](mdc:atomsec-app-frontend/src/App.js), which is rendered by [index.js](mdc:atomsec-app-frontend/src/index.js).
- All routing is handled using `react-router-dom` inside the `App` component.
- Authentication state is managed by [AuthContext.js](mdc:atomsec-app-frontend/src/context/AuthContext.js).

## Route Overview
- **/login**: Login page (public)
- **/signup**: Signup page (public)
- **/auth-test**: Auth test/debug page (public)
- **/azure-setup**: Azure setup guide (public)
- **/auth-callback**: Handles Azure AD login callback (public)
- **/platform-auth**: Handles platform authentication (public)
- **/**: Dashboard (protected)
- **/scan**: <PERSON>an wizard for new security scans (protected)
- **/reports**: Reports page (protected)
- **/integration/:integrationId**: Integration details (protected)
- **/integrations**: Integrations list (protected)
- **/insights**: Insights page (protected)
- **/configurations**: Configuration settings (protected)
- **/settings**: Application settings (protected)
- **/tasks/:orgId**: Task management for a specific org (protected)
- **/tasks**: Task management (no org selected) (protected)
- **/account-management**: Account management (protected)
- **/org/:instanceUrl**: Legacy route, redirects to /integrations (protected)
- **/integrations/:integrationId**: Redirects to /integration/:integrationId (protected)
- **Catch-all**: Redirects to dashboard

## Route Protection
- All routes except login, signup, auth-test, azure-setup, auth-callback, and platform-auth are protected by the `ProtectedRoute` component, which checks authentication state from `AuthContext`.

## Navigation
- The sidebar ([Sidebar.jsx](mdc:atomsec-app-frontend/src/components/Sidebar.jsx)) provides navigation to main protected routes: Dashboard, Integrations, Insights, Configurations, Settings.

## API Integration
- All API calls are routed through the DB service (see [config.js](mdc:atomsec-app-frontend/src/config.js)).
- Auth, user, integration, security, task, and policy endpoints are available under `/api/db/`.

## Auth Flow
- Login can be via username/password or Azure AD.
- Auth state is persisted in localStorage and managed by `AuthContext`.
- After login, users are redirected to their intended destination.

## Component Structure
- Each route renders a main component (e.g., Dashboard, ScanWizard, Reports, etc.) with a common layout (Header, Sidebar, Footer).

---
This rule provides a comprehensive guide to the frontend structure, routing, and authentication for AtomSec. Reference this rule for navigation, route protection, and API integration details.
description:
globs:
alwaysApply: false
---
